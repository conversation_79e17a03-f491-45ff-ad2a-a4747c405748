:root {
    --main-color: #141414;
    --border-color: #424242;
}

::selection {
    background: var(--border-color)
}

* {
    font-family: -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, Helvetica, Arial, sans-serif, Apple Color Emoji, Segoe UI Emoji !important;
    margin: 0;
    padding: 0;
    font-size: 0.9rem !important;
    -webkit-font-smoothing: antialiased;
}

.chat-window {
    background: inherit !important;
    height: 25% !important;
    left: 0% !important;
}

.chat-input {
    width: 17.6% !important;
    box-sizing: border-box;
}

.chat-input>div.input {
    background-color: var(--main-color) !important;
    border: calc(.0625rem* 1) solid var(--border-color);
    border-radius: calc(0.25rem* 1);
}


.suggestions {
    background-color: var(--main-color) !important;
    border: calc(.0625rem* 1) solid var(--border-color);
    border-radius: calc(0.25rem* 1);
    padding: 0.8vh;
}

.suggestion {
    font-family: monospace !important;
    margin-bottom: 0.7vh;
}

textarea {
    resize: none;
    font-weight: 600 !important;
    color: #c9c9c9;
    line-height: 1.83vh;
    padding-left: 4.5vh;
}

.prefix {
    height: 100%;
    vertical-align: middle;
    background: #42424238;
    padding-left: 4vh;
    display: inline-block;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 20px;
    position: absolute;
}

.help {
    color: #ebebeb61;
    font-size: 0.8rem !important;
    text-transform: lowercase;
    font-family: monospace !important;
    line-height: 1.5vh;
    border-top: 0.1vh dashed #343434;
}

.param {
    color: #fdff5dd1;
    font-weight: 500;
}

.type {
    padding: 0.3vh;
    border: calc(.0625rem* 1) solid var(--border-color);
    border-radius: calc(0.25rem* 1);
    margin-right: 0.3vh;
}

.multiline {
    margin-left: 1vh;
    text-indent: 0.3rem;
    white-space: pre-line;
}


.alert {
    background: #141414cc;
    border: calc(0.0625rem* 1) solid var(--border-color);
    border-radius: calc(0.25rem* 1);
    word-wrap: break-word;
    width: fit-content;
    min-width: 16vw;
    padding: 1.2vh;
    margin-bottom: 0.8vh;
    max-width: 16vw;
}
