{"error": {"not_online": "O jogador não está online.", "wrong_format": "Formato inválido.", "missing_args": "Não introduziste todos os argumentos. (x, y, z)", "missing_args2": "Todos os argumentos têm de ser preenchidos!", "no_access": "Não tens acesso a este comando.", "company_too_poor": "A tua empresa está falida.", "item_not_exist": "O item não existe.", "too_heavy": "Inventário cheio.", "location_not_exist": "Localização não existe.", "duplicate_license": "Licença Rockstar duplicada encontrada.", "no_valid_license": "Sem licença Rockstar válida.", "not_whitelisted": "Não estás na whitelist.", "server_already_open": "O servidor já se encontra aberto.", "server_already_closed": "O servidor já se encontra fechado.", "no_permission": "Não tens permissão para isso.", "no_waypoint": "Sem marcador marcado.", "tp_error": "Erro ao teleportar.", "connecting_database_timeout": "Conexão com a base de dados esgotou o tempo limite. (O servidor SQL está ligado?)", "connecting_error": "Ocorreu um erro ao conectar ao servidor. (Verifique a consola do seu servidor)", "no_match_character_registration": "Nada além de letras é permitido, espaços em branco à direita também não são permitidos, e as palavras devem começar com uma letra maiúscula nos campos de entrada. No entanto, pode adicionar palavras com espaços entre elas.", "already_in_queue": "Já estás na fila.", "no_subqueue": "Não foste colocado em nenhuma sub-fila."}, "success": {"server_opened": "O servidor foi aberto.", "server_closed": "O servidor foi fechado.", "teleported_waypoint": "Teleportado para o marcador.", "character_deleted": "Personagem apagado!", "character_deleted_citizenid": "Personagem com o Citizen ID %s apagado com sucesso."}, "info": {"received_paycheck": "Recebeste o pagamento de %s€", "job_info": "Emprego: %s | Grau: %s | Serviço: %s", "gang_info": "Gangue: %s | Grau: %s", "on_duty": "Agora estás de serviço!", "off_duty": "Agora estás fora de serviço!", "checking_ban": "Olá, %s. Estamos a verificar se estás banido.", "join_server": "Bem-vindo %s a %s.", "checking_whitelisted": "Olá %s. Estamos a verificar a whitelist.", "exploit_banned": "Foste banido por uso de cheats. Discord: %s", "exploit_dropped": "Foste expulso por uso de exploits.", "multichar_title": "Personagens", "multichar_new_character": "Nova Personagem #%s", "char_male": "<PERSON><PERSON><PERSON><PERSON>", "char_female": "Feminino", "play": "<PERSON><PERSON>", "play_description": "Jogar como %s", "delete_character": "Apagar personagem", "delete_character_description": "Apagar %s", "logout_command_help": "Sair do personagem", "check_id": "Verificar ID", "deletechar_command_help": "Apagar o personagem de um jogador", "deletechar_command_arg_player_id": "ID do jogador", "character_registration_title": "Registo de Personagem", "first_name": "Nome", "last_name": "Apelido", "nationality": "Nacionalidade", "gender": "<PERSON><PERSON><PERSON>", "birth_date": "Data de nascimento", "select_gender": "Seleciona o teu género...", "confirm_delete": "Tens a certeza de queres apagar esta personagem?", "in_queue": "🐌 Estás na posição %s/%s na fila. (%s) %s"}, "command": {"tp": {"help": "TP para jogador ou coordenadas (Admin)", "params": {"x": {"name": "id/x", "help": "ID ou Posição X"}, "y": {"name": "y", "help": "Posição Y"}, "z": {"name": "z", "help": "Posição Z"}}}, "tpm": {"help": "TP para marcador (Admin)"}, "togglepvp": {"help": "Ativar/Desativar PVP no servidor (Apenas Admin)"}, "addpermission": {"help": "<PERSON> (<PERSON>)", "params": {"id": {"name": "id", "help": "ID"}, "permission": {"name": "permission", "help": "<PERSON><PERSON><PERSON><PERSON>"}}}, "removepermission": {"help": "<PERSON><PERSON><PERSON> (God)", "params": {"id": {"name": "id", "help": "ID"}, "permission": {"name": "permission", "help": "<PERSON><PERSON><PERSON><PERSON>"}}}, "openserver": {"help": "<PERSON><PERSON><PERSON> servid<PERSON> (Admin)"}, "closeserver": {"help": "<PERSON><PERSON><PERSON> (Admin)", "params": {"reason": {"name": "reason", "help": "Motivo"}}}, "car": {"help": "<PERSON><PERSON><PERSON> (Admin)", "params": {"model": {"name": "model", "help": "Modelo do veículo"}, "keepCurrentVehicle": {"name": "keepCurrentVehicle", "help": "<PERSON>ter o veículo atual? (deixar em branco para apagar)"}}}, "dv": {"help": "<PERSON><PERSON><PERSON> (Admin)", "params": {"radius": {"name": "radius", "help": "Apagar num raio (metros)"}}}, "givemoney": {"help": "<PERSON> (Admin)", "params": {"id": {"name": "id", "help": "ID"}, "moneytype": {"name": "moneytype", "help": "cash, bank, crypto"}, "amount": {"name": "amount", "help": "Quantidade"}}}, "setmoney": {"help": "<PERSON><PERSON><PERSON> (Admin)", "params": {"id": {"name": "id", "help": "ID"}, "moneytype": {"name": "moneytype", "help": "cash, bank, crypto"}, "amount": {"name": "amount", "help": "Quantidade"}}}, "job": {"help": "Verificar emprego"}, "setjob": {"help": "Definir emprego (Admin)", "params": {"id": {"name": "id", "help": "ID"}, "job": {"name": "job", "help": "Emprego"}, "grade": {"name": "grade", "help": "G<PERSON><PERSON>"}}}, "changejob": {"help": "Alterar emprego ativo (Admin)", "params": {"id": {"name": "id", "help": "Jogador"}, "job": {"name": "job", "help": "Emprego"}}}, "addjob": {"help": "Adicionar <PERSON> (Apenas Admin)", "params": {"id": {"name": "id", "help": "ID"}, "job": {"name": "job", "help": "Emprego"}, "grade": {"name": "grade", "help": "G<PERSON><PERSON>"}}}, "removejob": {"help": "Remover emprego (Admin)", "params": {"id": {"name": "id", "help": "ID"}, "job": {"name": "job", "help": "Emprego"}}}, "gang": {"help": "Verificar a tua gangue"}, "setgang": {"help": "<PERSON><PERSON><PERSON> (Admin)", "params": {"id": {"name": "id", "help": "ID"}, "gang": {"name": "gang", "help": "Nome da gangue"}, "grade": {"name": "grade", "help": "G<PERSON><PERSON>"}}}, "ooc": {"help": "Mensagem OOC"}, "me": {"help": "Falar fora de RP", "params": {"message": {"name": "message", "help": "Ação a simular"}}}}}