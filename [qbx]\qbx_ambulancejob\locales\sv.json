{"error": {"canceled": "Avbröts", "impossible": "Handling omöjligt...", "no_player": "Ingen spelare i närheten", "no_firstaid": "Du behöver ett första hjälpen kit", "no_bandage": "<PERSON> behöver ett bandage", "beds_taken": "Sängar är upptagna...", "possessions_taken": "Alla dina <PERSON>r har tagits...", "cant_help": "Du kan inte hjälpa den här personen...", "not_ems": "Du är inte Sjukvårdare"}, "success": {"revived": "<PERSON>de en person", "healthy_player": "Spelaren är frisk", "helped_player": "Du hjälpte personen", "being_helped": "<PERSON> får hjälp..."}, "info": {"civ_died": "Person Död", "civ_down": "Person <PERSON><PERSON>", "civ_call": "<PERSON><PERSON>", "ems_down": "Doctor %s Down", "respawn_txt": "RESPAWN OM: ~r~%s~s~ SEKUNDER", "respawn_revive": "HÅLL [~r~E~s~] I %s SEKUNDER FÖR ATT RESPAWNA (~r~%sKR~s~", "bleed_out": "DU KOMMER BLÖDA UT OM: ~r~%s~s~ SEKUNDER", "bleed_out_help": "DU KOMMER BLÖDA UT OM: ~r~%s~s~ SEKUNDER, DU KAN FÅ HJÄLP", "request_help": "TRYCK [~r~G~s~] FÖR ATT KALLA PÅ HJÄLP", "help_requested": "SJUKHUSPERSONAL HAR BLIVIT UNDERRÄTTADE", "amb_plate": "SJUK", "heli_plate": "AKUT", "status": "Statuscheck", "is_status": "Är %s", "healthy": "Du är helt frisk igen!", "safe": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ems_alert": "SJUKVÅRD LARM - %s", "mr": "Mr.", "mrs": "Mrs.", "dr_needed": "En läkare behövs till Pillbox Sjukhus", "dr_alert": "<PERSON><PERSON><PERSON><PERSON> har redan medd<PERSON>ts", "ems_report": "RING SJUKVÅRDARE", "message_sent": "Meddelande som ska skickas", "check_health": "Kontrollera en persons hälsa", "heal_player": "Behandla Skador", "revive_player": "<PERSON><PERSON><PERSON><PERSON><PERSON> en person"}, "mail": {"sender": "Pillbox Sjukhus", "subject": "Sjukkostnad", "message": "${firstname} ${lastname},<br /><br /><PERSON> har fått ett mail med kostnaderna för det senaste sjukhusbesöket.<br />De slutliga kostnaderna: <strong>$%skr</strong><br /><br />MVH Region Los Santos!"}, "menu": {"amb_vehicles": "<PERSON><PERSON>", "status": "Heath Status"}, "text": {"pstash_button": "~INPUT_CONTEXT~ - <PERSON><PERSON><PERSON>", "pstash": "<PERSON><PERSON><PERSON>", "onduty_button": "~INPUT_CONTEXT~ - Stämpla In", "offduty_button": "~INPUT_CONTEXT~ - Stämpla Ut", "duty": "On/Off Duty", "armory_button": "~INPUT_CONTEXT~ - <PERSON><PERSON><PERSON><PERSON><PERSON>", "armory": "Armory", "veh_button": "~INPUT_CONTEXT~ - <PERSON><PERSON>", "elevator_roof": "~INPUT_CONTEXT~ - Ta hissen till taket", "elevator_main": "~INPUT_CONTEXT~ - Ta hissen ner till lobbyn", "el_roof": "Ta hissen till taket", "el_main": "Ta hissen till bottenvåningen", "call_doc": "~INPUT_CONTEXT~ - <PERSON><PERSON> på l<PERSON>e", "call": "<PERSON><PERSON> på läkare", "check_in": "~INPUT_CONTEXT~ - <PERSON><PERSON> in", "check": "Checka In", "lie_bed": "~INPUT_CONTEXT~ - <PERSON>gg på sängen", "bed": "Ligg på sängen", "put_bed": "Placera medborgaren i sängen", "bed_out": "~INPUT_CONTEXT~ - <PERSON><PERSON> ur sängen..", "alert": "LARM!"}, "progress": {"ifaks": "Använder SJSK...", "bandage": "Anv<PERSON>nder Bandage...", "painkillers": "<PERSON><PERSON><PERSON><PERSON>...", "revive": "Gör HLR...", "healing": "Behandlar Skador...", "checking_in": "Checkar in..."}}