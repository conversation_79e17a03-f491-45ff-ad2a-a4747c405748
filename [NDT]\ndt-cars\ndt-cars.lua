-- NDT Custom Car Spawner
-- Spawn vehicles with full upgrades and godmode

local selectedVehicle = nil
local spawnedVehicle = nil
local isMenuOpen = false

-- Vehicle categories and their vehicles
local vehicleCategories = {
    {
        name = "Super Cars",
        vehicles = {
            {name = "Adder", model = "adder"},
            {name = "<PERSON><PERSON><PERSON>", model = "zentorno"},
            {name = "T20", model = "t20"},
            {name = "Osiris", model = "osiris"},
            {name = "Entity XF", model = "entityxf"},
            {name = "Cheetah", model = "cheetah"},
            {name = "Infernus", model = "infernus"},
            {name = "Vacca", model = "vacca"},
            {name = "Reaper", model = "reaper"},
            {name = "Turismo R", model = "turismor"}
        }
    },
    {
        name = "Sports Cars",
        vehicles = {
            {name = "Sultan", model = "sultan"},
            {name = "Elegy", model = "elegy"},
            {name = "<PERSON><PERSON><PERSON>", model = "feltzer2"},
            {name = "Banshee", model = "banshee"},
            {name = "Comet", model = "comet2"},
            {name = "Carbonizzare", model = "carbonizzare"},
            {name = "Rapid GT", model = "rapidgt"},
            {name = "9F", model = "ninef"},
            {name = "Buffalo", model = "buffalo"},
            {name = "Sultan RS", model = "sultanrs"}
        }
    },
    {
        name = "Muscle Cars",
        vehicles = {
            {name = "Dominator", model = "dominator"},
            {name = "Phoenix", model = "phoenix"},
            {name = "Gauntlet", model = "gauntlet"},
            {name = "Dukes", model = "dukes"},
            {name = "Vigero", model = "vigero"},
            {name = "Sabre Turbo", model = "sabregt"},
            {name = "Tampa", model = "tampa"},
            {name = "Phoenix", model = "phoenix"},
            {name = "Blade", model = "blade"},
            {name = "Buccaneer", model = "buccaneer"}
        }
    },
    {
        name = "Motorcycles",
        vehicles = {
            {name = "Akuma", model = "akuma"},
            {name = "Bati 801", model = "bati"},
            {name = "Double T", model = "double"},
            {name = "Faggio", model = "faggio"},
            {name = "PCJ 600", model = "pcj"},
            {name = "Ruffian", model = "ruffian"},
            {name = "Sanchez", model = "sanchez"},
            {name = "Vader", model = "vader"},
            {name = "Vortex", model = "vortex"},
            {name = "Nemesis", model = "nemesis"}
        }
    },
    {
        name = "SUVs",
        vehicles = {
            {name = "Baller", model = "baller"},
            {name = "Cavalcade", model = "cavalcade"},
            {name = "Dubsta", model = "dubsta"},
            {name = "FQ 2", model = "fq2"},
            {name = "Granger", model = "granger"},
            {name = "Gresley", model = "gresley"},
            {name = "Habanero", model = "habanero"},
            {name = "Huntley S", model = "huntley"},
            {name = "Mesa", model = "mesa"},
            {name = "Patriot", model = "patriot"}
        }
    },
    {
        name = "Trucks",
        vehicles = {
            {name = "Benson", model = "benson"},
            {name = "Biff", model = "biff"},
            {name = "Hauler", model = "hauler"},
            {name = "Mule", model = "mule"},
            {name = "Packer", model = "packer"},
            {name = "Phantom", model = "phantom"},
            {name = "Pounder", model = "pounder"},
            {name = "Stockade", model = "stockade"},
            {name = "Titan", model = "titan"},
            {name = "Flatbed", model = "flatbed"}
        }
    }
}

-- Function to fully upgrade a vehicle
local function fullyUpgradeVehicle(vehicle)
    if not DoesEntityExist(vehicle) then return end
    
    -- Engine upgrades
    SetVehicleEngineHealth(vehicle, 1000.0)
    
    -- Performance upgrades
    SetVehicleModKit(vehicle, 0)
    SetVehicleMod(vehicle, 11, 3, false) -- Engine
    SetVehicleMod(vehicle, 12, 2, false) -- Brakes
    SetVehicleMod(vehicle, 13, 2, false) -- Transmission
    SetVehicleMod(vehicle, 15, 3, false) -- Suspension
    SetVehicleMod(vehicle, 16, 4, false) -- Armor
    
    -- Visual upgrades
    SetVehicleMod(vehicle, 0, 1, false) -- Spoiler
    SetVehicleMod(vehicle, 1, 1, false) -- Front Bumper
    SetVehicleMod(vehicle, 2, 1, false) -- Rear Bumper
    SetVehicleMod(vehicle, 3, 1, false) -- Side Skirt
    SetVehicleMod(vehicle, 4, 1, false) -- Exhaust
    SetVehicleMod(vehicle, 5, 1, false) -- Roll Cage
    SetVehicleMod(vehicle, 6, 1, false) -- Grille
    SetVehicleMod(vehicle, 7, 1, false) -- Hood
    SetVehicleMod(vehicle, 8, 1, false) -- Fender
    SetVehicleMod(vehicle, 9, 1, false) -- Right Fender
    SetVehicleMod(vehicle, 10, 1, false) -- Roof
    
    -- Tires
    SetVehicleMod(vehicle, 23, 1, false) -- Wheels
    SetVehicleWheelType(vehicle, 0)
    
    -- Paint job
    SetVehicleColours(vehicle, 0, 0)
    
    -- Windows
    SetVehicleWindowTint(vehicle, 1)
    
    -- Plate
    SetVehicleNumberPlateText(vehicle, "NDT")
    
    -- Extras
    SetVehicleExtra(vehicle, 1, false)
    SetVehicleExtra(vehicle, 2, false)
    
    -- Godmode
    SetEntityInvincible(vehicle, true)
    SetVehicleCanBreak(vehicle, false)
    SetVehicleCanDeformWheels(vehicle, false)
    SetVehicleEngineCanDegrade(vehicle, false)
    
    -- Performance
    SetVehicleHandlingFloat(vehicle, "CHandlingData", "fDriveInertia", 1.0)
    SetVehicleHandlingFloat(vehicle, "CHandlingData", "fInitialDriveForce", 0.5)
    SetVehicleHandlingFloat(vehicle, "CHandlingData", "fDriveBiasFront", 0.5)
    
    print("NDT Cars: Vehicle fully upgraded and made invincible")
end

-- Function to spawn vehicle
local function spawnVehicle(vehicleModel)
    local playerPed = PlayerPedId()
    local playerCoords = GetEntityCoords(playerPed)
    local playerHeading = GetEntityHeading(playerPed)
    
    -- Delete existing spawned vehicle
    if spawnedVehicle and DoesEntityExist(spawnedVehicle) then
        DeleteEntity(spawnedVehicle)
    end
    
    -- Request and load the vehicle model
    local hash = GetHashKey(vehicleModel)
    RequestModel(hash)
    
    local timeout = 0
    while not HasModelLoaded(hash) and timeout < 100 do
        Wait(10)
        timeout = timeout + 1
    end
    
    if not HasModelLoaded(hash) then
        print("NDT Cars: Failed to load vehicle model: " .. vehicleModel)
        return
    end
    
    -- Spawn the vehicle
    spawnedVehicle = CreateVehicle(hash, playerCoords.x + 3.0, playerCoords.y, playerCoords.z, playerHeading, true, false)
    
    -- Set vehicle properties
    SetVehicleOnGroundProperly(spawnedVehicle)
    SetVehicleDoorsLocked(spawnedVehicle, 1) -- Unlocked
    SetVehicleEngineOn(spawnedVehicle, true, true, false)
    
    -- Fully upgrade the vehicle
    fullyUpgradeVehicle(spawnedVehicle)
    
    -- Wait a bit for vehicle to fully spawn
    Wait(1000)
    
    -- Method 1: Try TaskWarpPedIntoVehicle
    ClearPedTasks(playerPed)
    TaskWarpPedIntoVehicle(playerPed, spawnedVehicle, -1)
    Wait(1000)
    
    -- Check if player is in vehicle
    if not IsPedInVehicle(playerPed, spawnedVehicle, false) then
        print("NDT Cars: Method 1 failed, trying Method 2")
        
        -- Method 2: Try SetPedIntoVehicle
        SetPedIntoVehicle(playerPed, spawnedVehicle, -1)
        Wait(500)
        
        if not IsPedInVehicle(playerPed, spawnedVehicle, false) then
            print("NDT Cars: Method 2 failed, trying Method 3")
            
            -- Method 3: Try with different approach
            local vehicleCoords = GetEntityCoords(spawnedVehicle)
            SetEntityCoords(playerPed, vehicleCoords.x, vehicleCoords.y, vehicleCoords.z + 1.0, false, false, false, true)
            Wait(100)
            TaskWarpPedIntoVehicle(playerPed, spawnedVehicle, -1)
            Wait(500)
            
            if not IsPedInVehicle(playerPed, spawnedVehicle, false) then
                print("NDT Cars: All methods failed")
            else
                print("NDT Cars: Method 3 succeeded")
            end
        else
            print("NDT Cars: Method 2 succeeded")
        end
    else
        print("NDT Cars: Method 1 succeeded")
    end
    
    -- Ensure vehicle is unlocked and engine is on
    SetVehicleDoorsLocked(spawnedVehicle, 1)
    SetVehicleEngineOn(spawnedVehicle, true, true, false)
    
    -- Make sure player can exit vehicle
    SetPedCanBeDraggedOut(playerPed, true)
    SetPedCanBeKnockedOffVehicle(playerPed, 1)
    
    -- Release the model
    SetModelAsNoLongerNeeded(hash)
    
    print("NDT Cars: Vehicle spawned: " .. vehicleModel)
end

-- Function to show the UI menu
local function showVehicleMenu()
    if isMenuOpen then return end
    
    isMenuOpen = true
    SetNuiFocus(true, true)
    
    print("NDT Cars: Opening UI menu")
    SendNUIMessage({
        action = 'showMenu',
        categories = vehicleCategories
    })
end

-- Function to close the UI menu
local function closeVehicleMenu()
    if not isMenuOpen then return end
    
    isMenuOpen = false
    SetNuiFocus(false, false)
    
    print("NDT Cars: Closing UI menu")
    SendNUIMessage({
        action = 'hideMenu'
    })
end

-- Simple menu system without ox_lib (fallback)
local function showSimpleMenu()
    print("NDT Cars: Available categories:")
    for i, category in ipairs(vehicleCategories) do
        print(i .. ". " .. category.name)
    end
    print("Use /car [category_number] [vehicle_number] to spawn a vehicle")
    print("Example: /car 1 1 to spawn Adder (Super Cars)")
end

-- Function to spawn vehicle by category and vehicle index
local function spawnVehicleByIndex(categoryIndex, vehicleIndex)
    if not vehicleCategories[categoryIndex] then
        print("NDT Cars: Invalid category number")
        return
    end
    
    local category = vehicleCategories[categoryIndex]
    if not category.vehicles[vehicleIndex] then
        print("NDT Cars: Invalid vehicle number")
        return
    end
    
    local vehicle = category.vehicles[vehicleIndex]
    spawnVehicle(vehicle.model)
end

-- Register commands
RegisterCommand('cars', function()
    showVehicleMenu()
end, false)

RegisterCommand('car', function(source, args)
    if #args < 2 then
        print("NDT Cars: Usage: /car [category_number] [vehicle_number]")
        showSimpleMenu()
        return
    end
    
    local categoryIndex = tonumber(args[1])
    local vehicleIndex = tonumber(args[2])
    
    if not categoryIndex or not vehicleIndex then
        print("NDT Cars: Invalid numbers")
        return
    end
    
    spawnVehicleByIndex(categoryIndex, vehicleIndex)
end, false)

-- NUI Callbacks
RegisterNUICallback('spawnVehicle', function(data, cb)
    print("NDT Cars: NUI callback - spawnVehicle", data)
    if data.vehicle then
        spawnVehicle(data.vehicle)
        closeVehicleMenu()
    end
    cb('ok')
end)

RegisterNUICallback('closeMenu', function(data, cb)
    print("NDT Cars: NUI callback - closeMenu")
    closeVehicleMenu()
    cb('ok')
end)

-- Export functions for other resources
exports('spawnVehicle', spawnVehicle)
exports('fullyUpgradeVehicle', fullyUpgradeVehicle)

print("NDT Cars System loaded - Use /cars to open vehicle menu")
print("NDT Cars: Use /car [category] [vehicle] to spawn a vehicle") 