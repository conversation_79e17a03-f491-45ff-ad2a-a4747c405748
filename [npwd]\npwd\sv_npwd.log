{"level":"debug","message":"Marketplace service started","module":"marketplace","timestamp":"2025-08-02T23:19:18.331Z"}
{"level":"debug","message":"Call service started","module":"calls","timestamp":"2025-08-02T23:19:18.332Z"}
{"level":"debug","message":"Player Service started","module":"player","timestamp":"2025-08-02T23:19:18.332Z"}
{"level":"info","message":"Loading QBX bridge....","timestamp":"2025-08-02T23:19:18.332Z"}
{"level":"info","message":"QBX bridge initialized","timestamp":"2025-08-02T23:19:18.332Z"}
{"level":"debug","message":"Boot service started","module":"boot","timestamp":"2025-08-02T23:19:18.332Z"}
{"level":"debug","message":"Messages service started","module":"messages","timestamp":"2025-08-02T23:19:18.332Z"}
{"level":"debug","message":"Notes service started","module":"notes","timestamp":"2025-08-02T23:19:18.332Z"}
{"level":"debug","message":"Contacts service started","module":"contact","timestamp":"2025-08-02T23:19:18.332Z"}
{"level":"debug","message":"Photo service started","module":"photo","timestamp":"2025-08-02T23:19:18.337Z"}
{"level":"debug","message":"Twitter service started","module":"twitter","timestamp":"2025-08-02T23:19:18.337Z"}
{"level":"debug","message":"Match service started","module":"match","timestamp":"2025-08-02T23:19:18.338Z"}
{"level":"debug","message":"Beginning database schema validation","module":"boot","timestamp":"2025-08-02T23:19:18.836Z"}
{"level":"info","message":"Successfully started","timestamp":"2025-08-02T23:19:18.842Z"}
{"level":"info","message":"Executed query (SELECT COUNT(*) as count\n                       FROM information_schema.tables\n                       WHERE table_schema = ?\n                         AND table_name = ? [\"Qbox_8E9C28\",\"players\"]) in 36.1177ms'","module":"DBInterface","timestamp":"2025-08-02T23:19:18.872Z"}
{"level":"info","message":"Executed query (SHOW COLUMNS IN players []) in 2.9827ms'","module":"DBInterface","timestamp":"2025-08-02T23:19:18.875Z"}
{"level":"debug","message":"Database schema successfully validated","module":"boot","timestamp":"2025-08-02T23:19:18.875Z"}
{"level":"info","message":"New NPWD Player added through event (1) (WC9X328W)","module":"player","timestamp":"2025-08-02T23:32:15.004Z"}
{"level":"debug","message":{"_firstname":"Cacac","_identifier":"WC9X328W","_lastname":"Acaca","_phoneNumber":"3158469181","source":1,"username":"Bâu"},"module":"player","timestamp":"2025-08-02T23:32:15.004Z"}
{"level":"info","message":"New NPWD Player added through event (1) (WC9X328W)","module":"player","timestamp":"2025-08-02T23:32:15.007Z"}
{"level":"debug","message":{"_firstname":"Cacac","_identifier":"WC9X328W","_lastname":"Acaca","_phoneNumber":"3158469181","source":1,"username":"Bâu"},"module":"player","timestamp":"2025-08-02T23:32:15.007Z"}
{"level":"silly","message":"netPromise > npwd:fetchTweets > RequestObj","module":"events","timestamp":"2025-08-02T23:32:15.104Z"}
{"level":"silly","message":{"data":{"pageId":0},"source":1},"module":"events","timestamp":"2025-08-02T23:32:15.104Z"}
{"level":"info","message":"Executed query (\n        SELECT *\n        FROM npwd_twitter_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 1.8073ms'","module":"DBInterface","timestamp":"2025-08-02T23:32:15.106Z"}
{"level":"warn","message":"Aborted fetching tweets for user WC9X328W because they do not have a profile.","module":"twitter","timestamp":"2025-08-02T23:32:15.106Z"}
{"level":"silly","message":"Response Promise Event npwd:fetchTweets:6fb81c73-6f6a-4cc4-aab5-873b0b18821b (2.4388ms), Data >>","module":"events","timestamp":"2025-08-02T23:32:15.106Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:32:15.106Z"}
{"level":"silly","message":"netPromise > npwd:getOrCreateTwitterProfile > RequestObj","module":"events","timestamp":"2025-08-02T23:32:15.204Z"}
{"level":"silly","message":{"data":null,"source":1},"module":"events","timestamp":"2025-08-02T23:32:15.204Z"}
{"level":"silly","message":"netPromise > phone:getMyProfile > RequestObj","module":"events","timestamp":"2025-08-02T23:32:15.205Z"}
{"level":"silly","message":{"data":null,"source":1},"module":"events","timestamp":"2025-08-02T23:32:15.205Z"}
{"level":"silly","message":"netPromise > npwd:fetchMessageGroups > RequestObj","module":"events","timestamp":"2025-08-02T23:32:15.205Z"}
{"level":"silly","message":{"data":null,"source":1},"module":"events","timestamp":"2025-08-02T23:32:15.205Z"}
{"level":"silly","message":"netPromise > npwd-contact-getAll > RequestObj","module":"events","timestamp":"2025-08-02T23:32:15.206Z"}
{"level":"silly","message":{"data":null,"source":1},"module":"events","timestamp":"2025-08-02T23:32:15.206Z"}
{"level":"info","message":"Executed query (\n        SELECT *\n        FROM npwd_twitter_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 3.0313ms'","module":"DBInterface","timestamp":"2025-08-02T23:32:15.208Z"}
{"level":"info","message":"Creating default Twitter profile Cacac_Acaca for WC9X328W","module":"twitter","timestamp":"2025-08-02T23:32:15.208Z"}
{"level":"info","message":"Executed query (\n        SELECT *,\n               UNIX_TIMESTAMP(updatedAt) AS lastActive\n        FROM npwd_match_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 6.7716ms'","module":"DBInterface","timestamp":"2025-08-02T23:32:15.212Z"}
{"level":"info","message":"Creating default match profile Cacac Acaca for WC9X328W","module":"match","timestamp":"2025-08-02T23:32:15.212Z"}
{"level":"info","message":"Executed query (\n        SELECT *\n        FROM npwd_twitter_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 1.4046ms'","module":"DBInterface","timestamp":"2025-08-02T23:32:15.212Z"}
{"level":"info","message":"Executed query (SELECT npwd_messages_conversations.id,\n                          npwd_messages_conversations.conversation_list         as conversationList,\n                          npwd_messages_participants.unread_count               as unreadCount,\n                          npwd_messages_conversations.is_group_chat             as isGroupChat,\n                          npwd_messages_conversations.label,\n                          UNIX_TIMESTAMP(npwd_messages_conversations.updatedAt) as updatedAt,\n                          npwd_messages_participants.participant\n                   FROM npwd_messages_conversations\n                            INNER JOIN npwd_messages_participants\n                                       on npwd_messages_conversations.id = npwd_messages_participants.conversation_id\n                   WHERE npwd_messages_participants.participant = ? [\"3158469181\"]) in 7.3378ms'","module":"DBInterface","timestamp":"2025-08-02T23:32:15.213Z"}
{"level":"info","message":"Executed query (SELECT * FROM npwd_phone_contacts WHERE identifier = ? ORDER BY display ASC [\"WC9X328W\"]) in 7.2665ms'","module":"DBInterface","timestamp":"2025-08-02T23:32:15.213Z"}
{"level":"silly","message":"Response Promise Event npwd:getOrCreateTwitterProfile:116c1770-995e-426a-bb6f-762c27326a74 (8.0803ms), Data >>","module":"events","timestamp":"2025-08-02T23:32:15.212Z"}
{"level":"silly","message":{"data":{"avatar_url":"https://i.fivemanage.com/images/3ClWwmpwkFhL.png","createdAt":"2025-08-02T23:32:15.000Z","id":1,"identifier":"WC9X328W","profile_name":"Cacac_Acaca","updatedAt":"2025-08-02T23:32:15.000Z"},"status":"ok"},"module":"events","timestamp":"2025-08-02T23:32:15.212Z"}
{"level":"silly","message":"Response Promise Event npwd:fetchMessageGroups:bad8b719-9140-4691-b742-197b6570f077 (7.5593ms), Data >>","module":"events","timestamp":"2025-08-02T23:32:15.213Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:32:15.213Z"}
{"level":"silly","message":"Response Promise Event npwd-contact-getAll:168f27ea-2c73-4f23-b062-35b1c3243db6 (7.465ms), Data >>","module":"events","timestamp":"2025-08-02T23:32:15.213Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:32:15.213Z"}
{"level":"info","message":"Executed query (\n        SELECT *,\n               UNIX_TIMESTAMP(updatedAt) AS lastActive\n        FROM npwd_match_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 0.5057ms'","module":"DBInterface","timestamp":"2025-08-02T23:32:15.215Z"}
{"level":"silly","message":"Response Promise Event phone:getMyProfile:57d46d3c-719c-45fc-bfae-5d099d5547d1 (10.5522ms), Data >>","module":"events","timestamp":"2025-08-02T23:32:15.215Z"}
{"level":"silly","message":{"data":{"bio":"","createdAt":"2025-08-02T23:32:15.000Z","id":1,"identifier":"WC9X328W","image":"https://upload.wikimedia.org/wikipedia/commons/a/ac/No_image_available.svg","job":"","lastActive":1754177535,"lastActiveFormatted":"Sat, 02 Aug 2025 23:32:15 GMT","location":"","name":"Cacac Acaca","tagList":[],"tags":"","updatedAt":"2025-08-02T23:32:15.000Z","viewed":false,"voiceMessage":null},"status":"ok"},"module":"events","timestamp":"2025-08-02T23:32:15.215Z"}
{"level":"debug","message":"Received unloadPlayer event for 1","module":"player","timestamp":"2025-08-02T23:41:13.515Z"}
{"level":"info","message":"Executed query (SELECT id FROM npwd_marketplace_listings WHERE identifier = ? [\"WC9X328W\"]) in 3.4188ms'","module":"DBInterface","timestamp":"2025-08-02T23:41:13.518Z"}
{"level":"info","message":"Executed query (DELETE FROM npwd_marketplace_listings WHERE identifier = ? AND reported = 0 [\"WC9X328W\"]) in 0.3573ms'","module":"DBInterface","timestamp":"2025-08-02T23:41:13.519Z"}
{"level":"info","message":"Unloaded NPWD Player, source: (1)","module":"player","timestamp":"2025-08-02T23:41:13.519Z"}
{"level":"info","message":"New NPWD Player added through event (1) (WC9X328W)","module":"player","timestamp":"2025-08-02T23:41:19.392Z"}
{"level":"debug","message":{"_firstname":"Cacac","_identifier":"WC9X328W","_lastname":"Acaca","_phoneNumber":"3158469181","source":1,"username":"Bâu"},"module":"player","timestamp":"2025-08-02T23:41:19.392Z"}
{"level":"info","message":"New NPWD Player added through event (1) (WC9X328W)","module":"player","timestamp":"2025-08-02T23:41:19.394Z"}
{"level":"debug","message":{"_firstname":"Cacac","_identifier":"WC9X328W","_lastname":"Acaca","_phoneNumber":"3158469181","source":1,"username":"Bâu"},"module":"player","timestamp":"2025-08-02T23:41:19.394Z"}
{"level":"silly","message":"netPromise > npwd:fetchAllListings > RequestObj","module":"events","timestamp":"2025-08-02T23:41:19.492Z"}
{"level":"silly","message":{"data":null,"source":1},"module":"events","timestamp":"2025-08-02T23:41:19.492Z"}
{"level":"silly","message":"netPromise > npwd-contact-getAll > RequestObj","module":"events","timestamp":"2025-08-02T23:41:19.492Z"}
{"level":"silly","message":{"data":null,"source":1},"module":"events","timestamp":"2025-08-02T23:41:19.493Z"}
{"level":"silly","message":"netPromise > npwd:fetchMessageGroups > RequestObj","module":"events","timestamp":"2025-08-02T23:41:19.493Z"}
{"level":"silly","message":{"data":null,"source":1},"module":"events","timestamp":"2025-08-02T23:41:19.493Z"}
{"level":"silly","message":"netPromise > npwd:getOrCreateTwitterProfile > RequestObj","module":"events","timestamp":"2025-08-02T23:41:19.493Z"}
{"level":"silly","message":{"data":null,"source":1},"module":"events","timestamp":"2025-08-02T23:41:19.493Z"}
{"level":"silly","message":"netPromise > npwd:fetchTweets > RequestObj","module":"events","timestamp":"2025-08-02T23:41:19.493Z"}
{"level":"silly","message":{"data":{"pageId":0},"source":1},"module":"events","timestamp":"2025-08-02T23:41:19.493Z"}
{"level":"silly","message":"netPromise > phone:getMyProfile > RequestObj","module":"events","timestamp":"2025-08-02T23:41:19.493Z"}
{"level":"silly","message":{"data":null,"source":1},"module":"events","timestamp":"2025-08-02T23:41:19.493Z"}
{"level":"info","message":"Executed query (SELECT * FROM npwd_phone_contacts WHERE identifier = ? ORDER BY display ASC [\"WC9X328W\"]) in 4.0673ms'","module":"DBInterface","timestamp":"2025-08-02T23:41:19.497Z"}
{"level":"silly","message":"Response Promise Event npwd-contact-getAll:3584c3d9-0ead-4407-a42c-96309088ee10 (4.334ms), Data >>","module":"events","timestamp":"2025-08-02T23:41:19.497Z"}
{"level":"info","message":"Executed query (SELECT npwd_messages_conversations.id,\n                          npwd_messages_conversations.conversation_list         as conversationList,\n                          npwd_messages_participants.unread_count               as unreadCount,\n                          npwd_messages_conversations.is_group_chat             as isGroupChat,\n                          npwd_messages_conversations.label,\n                          UNIX_TIMESTAMP(npwd_messages_conversations.updatedAt) as updatedAt,\n                          npwd_messages_participants.participant\n                   FROM npwd_messages_conversations\n                            INNER JOIN npwd_messages_participants\n                                       on npwd_messages_conversations.id = npwd_messages_participants.conversation_id\n                   WHERE npwd_messages_participants.participant = ? [\"3158469181\"]) in 4.2879ms'","module":"DBInterface","timestamp":"2025-08-02T23:41:19.497Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:41:19.497Z"}
{"level":"info","message":"Executed query (\n        SELECT *\n        FROM npwd_twitter_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 4.407ms'","module":"DBInterface","timestamp":"2025-08-02T23:41:19.497Z"}
{"level":"silly","message":"Response Promise Event npwd:fetchMessageGroups:10139063-57d0-4dcb-b39f-4dba5fec16bd (4.4305ms), Data >>","module":"events","timestamp":"2025-08-02T23:41:19.497Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:41:19.497Z"}
{"level":"silly","message":"Response Promise Event npwd:getOrCreateTwitterProfile:b63ea9b9-bd9c-4209-b199-41e94e892e45 (4.5416ms), Data >>","module":"events","timestamp":"2025-08-02T23:41:19.497Z"}
{"level":"silly","message":{"data":{"avatar_url":"https://i.fivemanage.com/images/3ClWwmpwkFhL.png","createdAt":"2025-08-02T23:32:15.000Z","id":1,"identifier":"WC9X328W","profile_name":"Cacac_Acaca","updatedAt":"2025-08-02T23:32:15.000Z"},"status":"ok"},"module":"events","timestamp":"2025-08-02T23:41:19.497Z"}
{"level":"info","message":"Executed query (SELECT * FROM npwd_marketplace_listings WHERE reported = 0 ORDER BY id DESC []) in 5.5253ms'","module":"DBInterface","timestamp":"2025-08-02T23:41:19.498Z"}
{"level":"silly","message":"Response Promise Event npwd:fetchAllListings:072bfde6-29dc-4c42-9c17-87b6cd4fcd29 (5.969ms), Data >>","module":"events","timestamp":"2025-08-02T23:41:19.498Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:41:19.498Z"}
{"level":"info","message":"Executed query (\n        SELECT *\n        FROM npwd_twitter_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 5.7456ms'","module":"DBInterface","timestamp":"2025-08-02T23:41:19.499Z"}
{"level":"silly","message":"Response Promise Event phone:getMyProfile:706303cc-ca23-4d19-8c3e-ed2314d5af66 (5.8567ms), Data >>","module":"events","timestamp":"2025-08-02T23:41:19.499Z"}
{"level":"info","message":"Executed query (\n        SELECT *,\n               UNIX_TIMESTAMP(updatedAt) AS lastActive\n        FROM npwd_match_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 5.6432ms'","module":"DBInterface","timestamp":"2025-08-02T23:41:19.499Z"}
{"level":"silly","message":{"data":{"bio":"","createdAt":"2025-08-02T23:32:15.000Z","id":1,"identifier":"WC9X328W","image":"https://upload.wikimedia.org/wikipedia/commons/a/ac/No_image_available.svg","job":"","lastActive":1754177535,"lastActiveFormatted":"Sat, 02 Aug 2025 23:32:15 GMT","location":"","name":"Cacac Acaca","tagList":[],"tags":"","updatedAt":"2025-08-02T23:32:15.000Z","viewed":false,"voiceMessage":null},"status":"ok"},"module":"events","timestamp":"2025-08-02T23:41:19.499Z"}
{"level":"info","message":"Executed query (\n        SELECT \n  npwd_twitter_tweets.id,\n  npwd_twitter_tweets.identifier,\n  npwd_twitter_profiles.id AS profile_id,\n  npwd_twitter_profiles.profile_name,\n  npwd_twitter_profiles.avatar_url,\n  npwd_twitter_tweets.likes,\n  npwd_twitter_tweets.visible,\n  IFNULL(COALESCE(retweets.message, npwd_twitter_tweets.message), '') AS message,\n  IFNULL(COALESCE(retweets.images, npwd_twitter_tweets.images), '') AS images,\n  npwd_twitter_tweets.retweet IS NOT NULL AS isRetweet,\n  retweets.id AS retweetId,\n  retweets_profiles.profile_name AS retweetProfileName,\n  retweets_profiles.avatar_url AS retweetAvatarUrl,\n  npwd_twitter_likes.id IS NOT NULL AS isLiked,\n  npwd_twitter_reports.id IS NOT NULL AS isReported,\n  npwd_twitter_tweets.createdAt,\n  npwd_twitter_tweets.updatedAt,\n  TIME_TO_SEC(TIMEDIFF( NOW(), npwd_twitter_tweets.createdAt)) AS seconds_since_tweet\n\n        FROM npwd_twitter_tweets\n                 LEFT OUTER JOIN npwd_twitter_profiles\n                                 ON npwd_twitter_tweets.identifier = npwd_twitter_profiles.identifier\n                 LEFT OUTER JOIN npwd_twitter_likes ON npwd_twitter_tweets.id = npwd_twitter_likes.tweet_id AND\n                                                       npwd_twitter_likes.profile_id = ?\n                 LEFT OUTER JOIN npwd_twitter_reports ON npwd_twitter_tweets.id = npwd_twitter_reports.tweet_id AND\n                                                         npwd_twitter_reports.profile_id = ?\n                 LEFT OUTER JOIN npwd_twitter_tweets AS retweets ON retweets.id = npwd_twitter_tweets.retweet\n                 LEFT OUTER JOIN npwd_twitter_profiles AS retweets_profiles\n                                 ON retweets.identifier = retweets_profiles.identifier\n        WHERE npwd_twitter_tweets.visible = 1\n        ORDER BY id DESC\n        LIMIT ? OFFSET ?\n\t\t [1,1,\"25\",\"0\"]) in 3.8473ms'","module":"DBInterface","timestamp":"2025-08-02T23:41:19.503Z"}
{"level":"silly","message":"Response Promise Event npwd:fetchTweets:e6ac6e85-8d7b-4643-8077-c5017d474dbf (9.873ms), Data >>","module":"events","timestamp":"2025-08-02T23:41:19.503Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:41:19.503Z"}
{"level":"info","message":"Executed query (SELECT id FROM npwd_marketplace_listings WHERE identifier = ? [\"WC9X328W\"]) in 2.3519ms'","module":"DBInterface","timestamp":"2025-08-02T23:43:16.743Z"}
{"level":"info","message":"Executed query (DELETE FROM npwd_marketplace_listings WHERE identifier = ? AND reported = 0 [\"WC9X328W\"]) in 0.3721ms'","module":"DBInterface","timestamp":"2025-08-02T23:43:16.744Z"}
{"level":"info","message":"Unloaded NPWD Player, source: (1)","module":"player","timestamp":"2025-08-02T23:43:16.744Z"}
{"level":"info","message":"New NPWD Player added through event (2) (WC9X328W)","module":"player","timestamp":"2025-08-02T23:48:36.362Z"}
{"level":"debug","message":{"_firstname":"Cacac","_identifier":"WC9X328W","_lastname":"Acaca","_phoneNumber":"3158469181","source":2,"username":"Bâu"},"module":"player","timestamp":"2025-08-02T23:48:36.362Z"}
{"level":"info","message":"New NPWD Player added through event (2) (WC9X328W)","module":"player","timestamp":"2025-08-02T23:48:36.364Z"}
{"level":"debug","message":{"_firstname":"Cacac","_identifier":"WC9X328W","_lastname":"Acaca","_phoneNumber":"3158469181","source":2,"username":"Bâu"},"module":"player","timestamp":"2025-08-02T23:48:36.364Z"}
{"level":"silly","message":"netPromise > npwd:fetchTweets > RequestObj","module":"events","timestamp":"2025-08-02T23:48:36.461Z"}
{"level":"silly","message":{"data":{"pageId":0},"source":2},"module":"events","timestamp":"2025-08-02T23:48:36.462Z"}
{"level":"info","message":"Executed query (\n        SELECT *\n        FROM npwd_twitter_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 1.0144ms'","module":"DBInterface","timestamp":"2025-08-02T23:48:36.463Z"}
{"level":"info","message":"Executed query (\n        SELECT \n  npwd_twitter_tweets.id,\n  npwd_twitter_tweets.identifier,\n  npwd_twitter_profiles.id AS profile_id,\n  npwd_twitter_profiles.profile_name,\n  npwd_twitter_profiles.avatar_url,\n  npwd_twitter_tweets.likes,\n  npwd_twitter_tweets.visible,\n  IFNULL(COALESCE(retweets.message, npwd_twitter_tweets.message), '') AS message,\n  IFNULL(COALESCE(retweets.images, npwd_twitter_tweets.images), '') AS images,\n  npwd_twitter_tweets.retweet IS NOT NULL AS isRetweet,\n  retweets.id AS retweetId,\n  retweets_profiles.profile_name AS retweetProfileName,\n  retweets_profiles.avatar_url AS retweetAvatarUrl,\n  npwd_twitter_likes.id IS NOT NULL AS isLiked,\n  npwd_twitter_reports.id IS NOT NULL AS isReported,\n  npwd_twitter_tweets.createdAt,\n  npwd_twitter_tweets.updatedAt,\n  TIME_TO_SEC(TIMEDIFF( NOW(), npwd_twitter_tweets.createdAt)) AS seconds_since_tweet\n\n        FROM npwd_twitter_tweets\n                 LEFT OUTER JOIN npwd_twitter_profiles\n                                 ON npwd_twitter_tweets.identifier = npwd_twitter_profiles.identifier\n                 LEFT OUTER JOIN npwd_twitter_likes ON npwd_twitter_tweets.id = npwd_twitter_likes.tweet_id AND\n                                                       npwd_twitter_likes.profile_id = ?\n                 LEFT OUTER JOIN npwd_twitter_reports ON npwd_twitter_tweets.id = npwd_twitter_reports.tweet_id AND\n                                                         npwd_twitter_reports.profile_id = ?\n                 LEFT OUTER JOIN npwd_twitter_tweets AS retweets ON retweets.id = npwd_twitter_tweets.retweet\n                 LEFT OUTER JOIN npwd_twitter_profiles AS retweets_profiles\n                                 ON retweets.identifier = retweets_profiles.identifier\n        WHERE npwd_twitter_tweets.visible = 1\n        ORDER BY id DESC\n        LIMIT ? OFFSET ?\n\t\t [1,1,\"25\",\"0\"]) in 1.454ms'","module":"DBInterface","timestamp":"2025-08-02T23:48:36.464Z"}
{"level":"silly","message":"Response Promise Event npwd:fetchTweets:29b04066-9b1b-4028-9d8f-0692ee126a4b (2.8505ms), Data >>","module":"events","timestamp":"2025-08-02T23:48:36.464Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:48:36.464Z"}
{"level":"silly","message":"netPromise > npwd:getOrCreateTwitterProfile > RequestObj","module":"events","timestamp":"2025-08-02T23:48:36.561Z"}
{"level":"silly","message":{"data":null,"source":2},"module":"events","timestamp":"2025-08-02T23:48:36.561Z"}
{"level":"silly","message":"netPromise > phone:getMyProfile > RequestObj","module":"events","timestamp":"2025-08-02T23:48:36.561Z"}
{"level":"silly","message":{"data":null,"source":2},"module":"events","timestamp":"2025-08-02T23:48:36.561Z"}
{"level":"silly","message":"netPromise > npwd:fetchMessageGroups > RequestObj","module":"events","timestamp":"2025-08-02T23:48:36.562Z"}
{"level":"silly","message":{"data":null,"source":2},"module":"events","timestamp":"2025-08-02T23:48:36.562Z"}
{"level":"silly","message":"netPromise > npwd-contact-getAll > RequestObj","module":"events","timestamp":"2025-08-02T23:48:36.562Z"}
{"level":"silly","message":{"data":null,"source":2},"module":"events","timestamp":"2025-08-02T23:48:36.562Z"}
{"level":"info","message":"Executed query (\n        SELECT *\n        FROM npwd_twitter_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 1.04ms'","module":"DBInterface","timestamp":"2025-08-02T23:48:36.562Z"}
{"level":"silly","message":"Response Promise Event npwd:getOrCreateTwitterProfile:56c64a43-c728-4041-9cba-0b50071bc523 (1.4093ms), Data >>","module":"events","timestamp":"2025-08-02T23:48:36.562Z"}
{"level":"silly","message":{"data":{"avatar_url":"https://i.fivemanage.com/images/3ClWwmpwkFhL.png","createdAt":"2025-08-02T23:32:15.000Z","id":1,"identifier":"WC9X328W","profile_name":"Cacac_Acaca","updatedAt":"2025-08-02T23:32:15.000Z"},"status":"ok"},"module":"events","timestamp":"2025-08-02T23:48:36.563Z"}
{"level":"silly","message":"Response Promise Event phone:getMyProfile:2a283047-d721-4269-a754-31d24b3a9c25 (1.4214ms), Data >>","module":"events","timestamp":"2025-08-02T23:48:36.563Z"}
{"level":"info","message":"Executed query (\n        SELECT *,\n               UNIX_TIMESTAMP(updatedAt) AS lastActive\n        FROM npwd_match_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 1.2329ms'","module":"DBInterface","timestamp":"2025-08-02T23:48:36.563Z"}
{"level":"silly","message":{"data":{"bio":"","createdAt":"2025-08-02T23:32:15.000Z","id":1,"identifier":"WC9X328W","image":"https://upload.wikimedia.org/wikipedia/commons/a/ac/No_image_available.svg","job":"","lastActive":1754177535,"lastActiveFormatted":"Sat, 02 Aug 2025 23:32:15 GMT","location":"","name":"Cacac Acaca","tagList":[],"tags":"","updatedAt":"2025-08-02T23:32:15.000Z","viewed":false,"voiceMessage":null},"status":"ok"},"module":"events","timestamp":"2025-08-02T23:48:36.563Z"}
{"level":"info","message":"Executed query (SELECT * FROM npwd_phone_contacts WHERE identifier = ? ORDER BY display ASC [\"WC9X328W\"]) in 1.8461ms'","module":"DBInterface","timestamp":"2025-08-02T23:48:36.564Z"}
{"level":"info","message":"Executed query (SELECT npwd_messages_conversations.id,\n                          npwd_messages_conversations.conversation_list         as conversationList,\n                          npwd_messages_participants.unread_count               as unreadCount,\n                          npwd_messages_conversations.is_group_chat             as isGroupChat,\n                          npwd_messages_conversations.label,\n                          UNIX_TIMESTAMP(npwd_messages_conversations.updatedAt) as updatedAt,\n                          npwd_messages_participants.participant\n                   FROM npwd_messages_conversations\n                            INNER JOIN npwd_messages_participants\n                                       on npwd_messages_conversations.id = npwd_messages_participants.conversation_id\n                   WHERE npwd_messages_participants.participant = ? [\"3158469181\"]) in 2.3303ms'","module":"DBInterface","timestamp":"2025-08-02T23:48:36.564Z"}
{"level":"silly","message":"Response Promise Event npwd-contact-getAll:af37c30b-7b8d-4c48-8aaa-75c10193e541 (1.9819ms), Data >>","module":"events","timestamp":"2025-08-02T23:48:36.564Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:48:36.564Z"}
{"level":"silly","message":"Response Promise Event npwd:fetchMessageGroups:1ed2af9e-9925-4594-a371-b1631d5850bb (2.4872ms), Data >>","module":"events","timestamp":"2025-08-02T23:48:36.564Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:48:36.564Z"}
{"level":"info","message":"Executed query (SELECT id FROM npwd_marketplace_listings WHERE identifier = ? [\"WC9X328W\"]) in 1.8464ms'","module":"DBInterface","timestamp":"2025-08-02T23:49:37.364Z"}
{"level":"info","message":"Executed query (DELETE FROM npwd_marketplace_listings WHERE identifier = ? AND reported = 0 [\"WC9X328W\"]) in 1.3315ms'","module":"DBInterface","timestamp":"2025-08-02T23:49:37.366Z"}
{"level":"info","message":"Unloaded NPWD Player, source: (2)","module":"player","timestamp":"2025-08-02T23:49:37.366Z"}
{"level":"info","message":"New NPWD Player added through event (3) (WC9X328W)","module":"player","timestamp":"2025-08-02T23:52:23.239Z"}
{"level":"debug","message":{"_firstname":"Cacac","_identifier":"WC9X328W","_lastname":"Acaca","_phoneNumber":"3158469181","source":3,"username":"Bâu"},"module":"player","timestamp":"2025-08-02T23:52:23.239Z"}
{"level":"info","message":"New NPWD Player added through event (3) (WC9X328W)","module":"player","timestamp":"2025-08-02T23:52:23.241Z"}
{"level":"debug","message":{"_firstname":"Cacac","_identifier":"WC9X328W","_lastname":"Acaca","_phoneNumber":"3158469181","source":3,"username":"Bâu"},"module":"player","timestamp":"2025-08-02T23:52:23.241Z"}
{"level":"silly","message":"netPromise > npwd:fetchTweets > RequestObj","module":"events","timestamp":"2025-08-02T23:52:23.338Z"}
{"level":"silly","message":{"data":{"pageId":0},"source":3},"module":"events","timestamp":"2025-08-02T23:52:23.338Z"}
{"level":"info","message":"Executed query (\n        SELECT *\n        FROM npwd_twitter_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 1.0825ms'","module":"DBInterface","timestamp":"2025-08-02T23:52:23.339Z"}
{"level":"info","message":"Executed query (\n        SELECT \n  npwd_twitter_tweets.id,\n  npwd_twitter_tweets.identifier,\n  npwd_twitter_profiles.id AS profile_id,\n  npwd_twitter_profiles.profile_name,\n  npwd_twitter_profiles.avatar_url,\n  npwd_twitter_tweets.likes,\n  npwd_twitter_tweets.visible,\n  IFNULL(COALESCE(retweets.message, npwd_twitter_tweets.message), '') AS message,\n  IFNULL(COALESCE(retweets.images, npwd_twitter_tweets.images), '') AS images,\n  npwd_twitter_tweets.retweet IS NOT NULL AS isRetweet,\n  retweets.id AS retweetId,\n  retweets_profiles.profile_name AS retweetProfileName,\n  retweets_profiles.avatar_url AS retweetAvatarUrl,\n  npwd_twitter_likes.id IS NOT NULL AS isLiked,\n  npwd_twitter_reports.id IS NOT NULL AS isReported,\n  npwd_twitter_tweets.createdAt,\n  npwd_twitter_tweets.updatedAt,\n  TIME_TO_SEC(TIMEDIFF( NOW(), npwd_twitter_tweets.createdAt)) AS seconds_since_tweet\n\n        FROM npwd_twitter_tweets\n                 LEFT OUTER JOIN npwd_twitter_profiles\n                                 ON npwd_twitter_tweets.identifier = npwd_twitter_profiles.identifier\n                 LEFT OUTER JOIN npwd_twitter_likes ON npwd_twitter_tweets.id = npwd_twitter_likes.tweet_id AND\n                                                       npwd_twitter_likes.profile_id = ?\n                 LEFT OUTER JOIN npwd_twitter_reports ON npwd_twitter_tweets.id = npwd_twitter_reports.tweet_id AND\n                                                         npwd_twitter_reports.profile_id = ?\n                 LEFT OUTER JOIN npwd_twitter_tweets AS retweets ON retweets.id = npwd_twitter_tweets.retweet\n                 LEFT OUTER JOIN npwd_twitter_profiles AS retweets_profiles\n                                 ON retweets.identifier = retweets_profiles.identifier\n        WHERE npwd_twitter_tweets.visible = 1\n        ORDER BY id DESC\n        LIMIT ? OFFSET ?\n\t\t [1,1,\"25\",\"0\"]) in 2.0868ms'","module":"DBInterface","timestamp":"2025-08-02T23:52:23.341Z"}
{"level":"silly","message":"Response Promise Event npwd:fetchTweets:eeacbde0-f235-44af-8840-5c1a1b973e6d (3.623ms), Data >>","module":"events","timestamp":"2025-08-02T23:52:23.342Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:52:23.342Z"}
{"level":"silly","message":"netPromise > npwd:getOrCreateTwitterProfile > RequestObj","module":"events","timestamp":"2025-08-02T23:52:23.437Z"}
{"level":"silly","message":{"data":null,"source":3},"module":"events","timestamp":"2025-08-02T23:52:23.437Z"}
{"level":"silly","message":"netPromise > phone:getMyProfile > RequestObj","module":"events","timestamp":"2025-08-02T23:52:23.438Z"}
{"level":"silly","message":{"data":null,"source":3},"module":"events","timestamp":"2025-08-02T23:52:23.438Z"}
{"level":"silly","message":"netPromise > npwd:fetchMessageGroups > RequestObj","module":"events","timestamp":"2025-08-02T23:52:23.438Z"}
{"level":"silly","message":{"data":null,"source":3},"module":"events","timestamp":"2025-08-02T23:52:23.438Z"}
{"level":"silly","message":"netPromise > npwd-contact-getAll > RequestObj","module":"events","timestamp":"2025-08-02T23:52:23.438Z"}
{"level":"silly","message":{"data":null,"source":3},"module":"events","timestamp":"2025-08-02T23:52:23.438Z"}
{"level":"info","message":"Executed query (\n        SELECT *\n        FROM npwd_twitter_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 1.0204ms'","module":"DBInterface","timestamp":"2025-08-02T23:52:23.438Z"}
{"level":"silly","message":"Response Promise Event npwd:getOrCreateTwitterProfile:2b6ce9f5-5aa9-4c98-ab90-1ded5c92d0fe (1.3124ms), Data >>","module":"events","timestamp":"2025-08-02T23:52:23.439Z"}
{"level":"silly","message":{"data":{"avatar_url":"https://i.fivemanage.com/images/3ClWwmpwkFhL.png","createdAt":"2025-08-02T23:32:15.000Z","id":1,"identifier":"WC9X328W","profile_name":"Cacac_Acaca","updatedAt":"2025-08-02T23:32:15.000Z"},"status":"ok"},"module":"events","timestamp":"2025-08-02T23:52:23.439Z"}
{"level":"info","message":"Executed query (\n        SELECT *,\n               UNIX_TIMESTAMP(updatedAt) AS lastActive\n        FROM npwd_match_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 1.4921ms'","module":"DBInterface","timestamp":"2025-08-02T23:52:23.439Z"}
{"level":"silly","message":"Response Promise Event phone:getMyProfile:90c45df6-8e9b-4157-b660-162d18a1ae47 (1.681ms), Data >>","module":"events","timestamp":"2025-08-02T23:52:23.439Z"}
{"level":"silly","message":{"data":{"bio":"","createdAt":"2025-08-02T23:32:15.000Z","id":1,"identifier":"WC9X328W","image":"https://upload.wikimedia.org/wikipedia/commons/a/ac/No_image_available.svg","job":"","lastActive":1754177535,"lastActiveFormatted":"Sat, 02 Aug 2025 23:32:15 GMT","location":"","name":"Cacac Acaca","tagList":[],"tags":"","updatedAt":"2025-08-02T23:32:15.000Z","viewed":false,"voiceMessage":null},"status":"ok"},"module":"events","timestamp":"2025-08-02T23:52:23.439Z"}
{"level":"silly","message":"Response Promise Event npwd-contact-getAll:7f0b6955-1fb8-4ac4-8c0a-52365b0212b3 (1.7423ms), Data >>","module":"events","timestamp":"2025-08-02T23:52:23.439Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:52:23.439Z"}
{"level":"silly","message":"Response Promise Event npwd:fetchMessageGroups:89d99b4a-bbf0-4376-9860-52ead0f5ded5 (2.065ms), Data >>","module":"events","timestamp":"2025-08-02T23:52:23.440Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:52:23.440Z"}
{"level":"info","message":"Executed query (SELECT * FROM npwd_phone_contacts WHERE identifier = ? ORDER BY display ASC [\"WC9X328W\"]) in 1.6444ms'","module":"DBInterface","timestamp":"2025-08-02T23:52:23.439Z"}
{"level":"info","message":"Executed query (SELECT npwd_messages_conversations.id,\n                          npwd_messages_conversations.conversation_list         as conversationList,\n                          npwd_messages_participants.unread_count               as unreadCount,\n                          npwd_messages_conversations.is_group_chat             as isGroupChat,\n                          npwd_messages_conversations.label,\n                          UNIX_TIMESTAMP(npwd_messages_conversations.updatedAt) as updatedAt,\n                          npwd_messages_participants.participant\n                   FROM npwd_messages_conversations\n                            INNER JOIN npwd_messages_participants\n                                       on npwd_messages_conversations.id = npwd_messages_participants.conversation_id\n                   WHERE npwd_messages_participants.participant = ? [\"3158469181\"]) in 1.9299ms'","module":"DBInterface","timestamp":"2025-08-02T23:52:23.440Z"}
{"level":"debug","message":"Received unloadPlayer event for 3","module":"player","timestamp":"2025-08-02T23:57:46.200Z"}
{"level":"info","message":"Executed query (SELECT id FROM npwd_marketplace_listings WHERE identifier = ? [\"WC9X328W\"]) in 2.4778ms'","module":"DBInterface","timestamp":"2025-08-02T23:57:46.202Z"}
{"level":"info","message":"Executed query (DELETE FROM npwd_marketplace_listings WHERE identifier = ? AND reported = 0 [\"WC9X328W\"]) in 0.573ms'","module":"DBInterface","timestamp":"2025-08-02T23:57:46.203Z"}
{"level":"info","message":"Unloaded NPWD Player, source: (3)","module":"player","timestamp":"2025-08-02T23:57:46.203Z"}
{"level":"info","message":"New NPWD Player added through event (3) (WC9X328W)","module":"player","timestamp":"2025-08-02T23:57:50.466Z"}
{"level":"debug","message":{"_firstname":"Cacac","_identifier":"WC9X328W","_lastname":"Acaca","_phoneNumber":"3158469181","source":3,"username":"Bâu"},"module":"player","timestamp":"2025-08-02T23:57:50.466Z"}
{"level":"info","message":"New NPWD Player added through event (3) (WC9X328W)","module":"player","timestamp":"2025-08-02T23:57:50.467Z"}
{"level":"debug","message":{"_firstname":"Cacac","_identifier":"WC9X328W","_lastname":"Acaca","_phoneNumber":"3158469181","source":3,"username":"Bâu"},"module":"player","timestamp":"2025-08-02T23:57:50.467Z"}
{"level":"silly","message":"netPromise > npwd:fetchAllListings > RequestObj","module":"events","timestamp":"2025-08-02T23:57:50.566Z"}
{"level":"silly","message":{"data":null,"source":3},"module":"events","timestamp":"2025-08-02T23:57:50.566Z"}
{"level":"silly","message":"netPromise > npwd-contact-getAll > RequestObj","module":"events","timestamp":"2025-08-02T23:57:50.566Z"}
{"level":"silly","message":{"data":null,"source":3},"module":"events","timestamp":"2025-08-02T23:57:50.566Z"}
{"level":"silly","message":"netPromise > npwd:fetchMessageGroups > RequestObj","module":"events","timestamp":"2025-08-02T23:57:50.566Z"}
{"level":"silly","message":{"data":null,"source":3},"module":"events","timestamp":"2025-08-02T23:57:50.566Z"}
{"level":"silly","message":"netPromise > npwd:getOrCreateTwitterProfile > RequestObj","module":"events","timestamp":"2025-08-02T23:57:50.567Z"}
{"level":"silly","message":{"data":null,"source":3},"module":"events","timestamp":"2025-08-02T23:57:50.567Z"}
{"level":"silly","message":"netPromise > npwd:fetchTweets > RequestObj","module":"events","timestamp":"2025-08-02T23:57:50.567Z"}
{"level":"silly","message":{"data":{"pageId":0},"source":3},"module":"events","timestamp":"2025-08-02T23:57:50.567Z"}
{"level":"silly","message":"netPromise > phone:getMyProfile > RequestObj","module":"events","timestamp":"2025-08-02T23:57:50.567Z"}
{"level":"silly","message":{"data":null,"source":3},"module":"events","timestamp":"2025-08-02T23:57:50.567Z"}
{"level":"info","message":"Executed query (SELECT * FROM npwd_phone_contacts WHERE identifier = ? ORDER BY display ASC [\"WC9X328W\"]) in 2.4872ms'","module":"DBInterface","timestamp":"2025-08-02T23:57:50.569Z"}
{"level":"silly","message":"Response Promise Event npwd-contact-getAll:7085c1d2-841a-4c2f-9091-7c78c6da8d6d (2.6936ms), Data >>","module":"events","timestamp":"2025-08-02T23:57:50.569Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:57:50.569Z"}
{"level":"silly","message":"Response Promise Event npwd:getOrCreateTwitterProfile:c22ad65e-d808-49e8-96ae-c42f21e6334a (2.9382ms), Data >>","module":"events","timestamp":"2025-08-02T23:57:50.570Z"}
{"level":"silly","message":{"data":{"avatar_url":"https://i.fivemanage.com/images/3ClWwmpwkFhL.png","createdAt":"2025-08-02T23:32:15.000Z","id":1,"identifier":"WC9X328W","profile_name":"Cacac_Acaca","updatedAt":"2025-08-02T23:32:15.000Z"},"status":"ok"},"module":"events","timestamp":"2025-08-02T23:57:50.570Z"}
{"level":"info","message":"Executed query (\n        SELECT *\n        FROM npwd_twitter_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 2.7972ms'","module":"DBInterface","timestamp":"2025-08-02T23:57:50.569Z"}
{"level":"silly","message":"Response Promise Event npwd:fetchAllListings:9eaef111-5527-46fb-ad34-a6ea2200cb57 (4.2988ms), Data >>","module":"events","timestamp":"2025-08-02T23:57:50.570Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:57:50.570Z"}
{"level":"silly","message":"Response Promise Event npwd:fetchMessageGroups:237f1222-6a6b-4c8a-8618-c77d8cb134cc (4.1436ms), Data >>","module":"events","timestamp":"2025-08-02T23:57:50.571Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:57:50.571Z"}
{"level":"silly","message":"Response Promise Event phone:getMyProfile:b5578e12-35a1-4f94-a4cb-d176986fa64c (4.577ms), Data >>","module":"events","timestamp":"2025-08-02T23:57:50.571Z"}
{"level":"silly","message":{"data":{"bio":"","createdAt":"2025-08-02T23:32:15.000Z","id":1,"identifier":"WC9X328W","image":"https://upload.wikimedia.org/wikipedia/commons/a/ac/No_image_available.svg","job":"","lastActive":1754177535,"lastActiveFormatted":"Sat, 02 Aug 2025 23:32:15 GMT","location":"","name":"Cacac Acaca","tagList":[],"tags":"","updatedAt":"2025-08-02T23:32:15.000Z","viewed":false,"voiceMessage":null},"status":"ok"},"module":"events","timestamp":"2025-08-02T23:57:50.571Z"}
{"level":"info","message":"Executed query (SELECT * FROM npwd_marketplace_listings WHERE reported = 0 ORDER BY id DESC []) in 3.9704ms'","module":"DBInterface","timestamp":"2025-08-02T23:57:50.570Z"}
{"level":"info","message":"Executed query (SELECT npwd_messages_conversations.id,\n                          npwd_messages_conversations.conversation_list         as conversationList,\n                          npwd_messages_participants.unread_count               as unreadCount,\n                          npwd_messages_conversations.is_group_chat             as isGroupChat,\n                          npwd_messages_conversations.label,\n                          UNIX_TIMESTAMP(npwd_messages_conversations.updatedAt) as updatedAt,\n                          npwd_messages_participants.participant\n                   FROM npwd_messages_conversations\n                            INNER JOIN npwd_messages_participants\n                                       on npwd_messages_conversations.id = npwd_messages_participants.conversation_id\n                   WHERE npwd_messages_participants.participant = ? [\"3158469181\"]) in 3.995ms'","module":"DBInterface","timestamp":"2025-08-02T23:57:50.570Z"}
{"level":"info","message":"Executed query (\n        SELECT *\n        FROM npwd_twitter_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 4.103ms'","module":"DBInterface","timestamp":"2025-08-02T23:57:50.571Z"}
{"level":"info","message":"Executed query (\n        SELECT *,\n               UNIX_TIMESTAMP(updatedAt) AS lastActive\n        FROM npwd_match_profiles\n        WHERE identifier = ?\n        LIMIT 1\n\t\t [\"WC9X328W\"]) in 4.3544ms'","module":"DBInterface","timestamp":"2025-08-02T23:57:50.571Z"}
{"level":"info","message":"Executed query (\n        SELECT \n  npwd_twitter_tweets.id,\n  npwd_twitter_tweets.identifier,\n  npwd_twitter_profiles.id AS profile_id,\n  npwd_twitter_profiles.profile_name,\n  npwd_twitter_profiles.avatar_url,\n  npwd_twitter_tweets.likes,\n  npwd_twitter_tweets.visible,\n  IFNULL(COALESCE(retweets.message, npwd_twitter_tweets.message), '') AS message,\n  IFNULL(COALESCE(retweets.images, npwd_twitter_tweets.images), '') AS images,\n  npwd_twitter_tweets.retweet IS NOT NULL AS isRetweet,\n  retweets.id AS retweetId,\n  retweets_profiles.profile_name AS retweetProfileName,\n  retweets_profiles.avatar_url AS retweetAvatarUrl,\n  npwd_twitter_likes.id IS NOT NULL AS isLiked,\n  npwd_twitter_reports.id IS NOT NULL AS isReported,\n  npwd_twitter_tweets.createdAt,\n  npwd_twitter_tweets.updatedAt,\n  TIME_TO_SEC(TIMEDIFF( NOW(), npwd_twitter_tweets.createdAt)) AS seconds_since_tweet\n\n        FROM npwd_twitter_tweets\n                 LEFT OUTER JOIN npwd_twitter_profiles\n                                 ON npwd_twitter_tweets.identifier = npwd_twitter_profiles.identifier\n                 LEFT OUTER JOIN npwd_twitter_likes ON npwd_twitter_tweets.id = npwd_twitter_likes.tweet_id AND\n                                                       npwd_twitter_likes.profile_id = ?\n                 LEFT OUTER JOIN npwd_twitter_reports ON npwd_twitter_tweets.id = npwd_twitter_reports.tweet_id AND\n                                                         npwd_twitter_reports.profile_id = ?\n                 LEFT OUTER JOIN npwd_twitter_tweets AS retweets ON retweets.id = npwd_twitter_tweets.retweet\n                 LEFT OUTER JOIN npwd_twitter_profiles AS retweets_profiles\n                                 ON retweets.identifier = retweets_profiles.identifier\n        WHERE npwd_twitter_tweets.visible = 1\n        ORDER BY id DESC\n        LIMIT ? OFFSET ?\n\t\t [1,1,\"25\",\"0\"]) in 2.1944ms'","module":"DBInterface","timestamp":"2025-08-02T23:57:50.573Z"}
{"level":"silly","message":"Response Promise Event npwd:fetchTweets:1bfa8171-d40e-45cf-91fc-26e78a9a0b5c (6.5682ms), Data >>","module":"events","timestamp":"2025-08-02T23:57:50.573Z"}
{"level":"silly","message":{"data":[],"status":"ok"},"module":"events","timestamp":"2025-08-02T23:57:50.573Z"}
{"level":"info","message":"Executed query (SELECT id FROM npwd_marketplace_listings WHERE identifier = ? [\"WC9X328W\"]) in 2.4471ms'","module":"DBInterface","timestamp":"2025-08-03T00:27:32.192Z"}
{"level":"info","message":"Executed query (DELETE FROM npwd_marketplace_listings WHERE identifier = ? AND reported = 0 [\"WC9X328W\"]) in 0.3895ms'","module":"DBInterface","timestamp":"2025-08-03T00:27:32.192Z"}
{"level":"info","message":"Unloaded NPWD Player, source: (3)","module":"player","timestamp":"2025-08-03T00:27:32.192Z"}
{"level":"debug","message":"Marketplace service started","module":"marketplace","timestamp":"2025-08-03T22:44:17.886Z"}
{"level":"debug","message":"Call service started","module":"calls","timestamp":"2025-08-03T22:44:17.886Z"}
{"level":"debug","message":"Player Service started","module":"player","timestamp":"2025-08-03T22:44:17.887Z"}
{"level":"info","message":"Loading QBX bridge....","timestamp":"2025-08-03T22:44:17.887Z"}
{"level":"info","message":"QBX bridge initialized","timestamp":"2025-08-03T22:44:17.887Z"}
{"level":"debug","message":"Boot service started","module":"boot","timestamp":"2025-08-03T22:44:17.887Z"}
{"level":"debug","message":"Messages service started","module":"messages","timestamp":"2025-08-03T22:44:17.887Z"}
{"level":"debug","message":"Notes service started","module":"notes","timestamp":"2025-08-03T22:44:17.887Z"}
{"level":"debug","message":"Contacts service started","module":"contact","timestamp":"2025-08-03T22:44:17.887Z"}
{"level":"debug","message":"Photo service started","module":"photo","timestamp":"2025-08-03T22:44:17.894Z"}
{"level":"debug","message":"Twitter service started","module":"twitter","timestamp":"2025-08-03T22:44:17.894Z"}
{"level":"debug","message":"Match service started","module":"match","timestamp":"2025-08-03T22:44:17.894Z"}
{"level":"debug","message":"Beginning database schema validation","module":"boot","timestamp":"2025-08-03T22:44:18.406Z"}
{"level":"info","message":"Successfully started","timestamp":"2025-08-03T22:44:18.412Z"}
{"level":"error","message":"Error executing (SELECT COUNT(*) as count\n                       FROM information_schema.tables\n                       WHERE table_schema = ?\n                         AND table_name = ? [\"Qbox_8E9C28\",\"players\"]) with error message connect ECONNREFUSED 127.0.0.1:3306","module":"DBInterface","timestamp":"2025-08-03T22:44:18.432Z"}
