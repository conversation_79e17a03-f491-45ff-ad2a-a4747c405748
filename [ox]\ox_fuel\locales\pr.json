{"tank_full": "<PERSON><PERSON><PERSON> in vasile naghliye por ast", "petrolcan_cannot_afford": "<PERSON><PERSON>a pole kafi baraye kharid dabe benzin nadarid", "refuel_cannot_afford": "<PERSON><PERSON>a pole kafi baraye benzin zadan nadarid", "vehicle_far": "<PERSON><PERSON><PERSON> shoma dor ast", "pump_fuel_with_can": "Baraye estefade az pomo benzin bayad dabe ro kenar bezarid", "fuel_help": "Dokme ~INPUT_C2939D45~ jahat ben<PERSON><PERSON> zadan", "petrolcan_help": "Dokme ~INPUT_C2939D45~ jahat kharid ya por kardan dabe benzin", "leave_vehicle": "<PERSON><PERSON><PERSON> benzin zadan bayad az vasile naghliye kharej shid", "start_fueling": "<PERSON><PERSON><PERSON> zadan", "petrolcan_buy_or_refill": "<PERSON><PERSON>d ya por kardan dabe benzin", "fuel_station_blip": "Pomp Benzin", "not_enough_money": "Shoma pole kafi nadarid! $%s kam darid", "fuel_success": "Sokht giri anjam shod %s - $%s", "petrolcan_refill": "Shoma $%s baraye por kardan dabe benzin pardakht kardid", "petrolcan_buy": "Shoma $%s baraye dabe benzin pardakht kardid", "petrolcan_cannot_carry": "<PERSON><PERSON><PERSON> ne<PERSON>id dabe benzdin ra haml konid"}