import{j as l}from"./jsx-runtime-5fe4d0a7.js";import{S as s,__tla as e}from"./__federation_expose_Input-51304708.js";let a,i=Promise.all([(()=>{try{return e}catch{}})()]).then(async()=>{a=c=>l.jsx(s,{...c,children:l.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",fillRule:"evenodd",strokeLinejoin:"round",strokeMiterlimit:"2",clipRule:"evenodd",viewBox:"0 0 176 178",children:[l.jsx("metadata",{id:"author",children:"<EMAIL>"}),l.jsx("path",{fill:"none",d:"M0 0H176V178H0z"}),l.jsx("clipPath",{id:"_clip1",children:l.jsx("path",{d:"M0 0H176V178H0z"})}),l.jsxs("g",{clipPath:"url(#_clip1)",children:[l.jsx("path",{fill:"#444e87",d:"M87.594.286C152.799-3.983 176 40.142 176 89.135s-29.665 91.97-84.913 88.689C45.254 175.102-1.31 150.139.028 87.556.642 58.856 24.249 4.434 87.594.286z"}),l.jsxs("g",{children:[l.jsx("path",{fill:"#00dea7",d:"M134.136 50.66s-7.772-7.7-18.248-8.429c-9.398-.654-27.937 3.521-24.819 29.1-19.029-.356-36.082-8.062-50.195-26.707 0 0-9.207 15.317 4.01 34.204 0 0-11.132-7.019-10.39-.773.743 6.246 6.232 16.983 19.449 25.163 0 0-14.013-1.535-11.043 3.521 2.97 5.056 12.231 11.796 22.478 12.837 0 0-5.08 7.853-24.433 6.288-14.461-1.169 24.946 29.709 66.312 14.235 28.59-10.695 43.957-40.004 33.265-70.789 0 0 11.275-6.603 12.029-14.425 0 0-6.237 1.487-8.91 2.231 0 0 4.566-8.019 3.378-9.952-1.188-1.934-12.883 3.496-12.883 3.496z"}),l.jsx("path",{fill:"#fff",d:"M42.935 99.829c-.974.343-1.797.782-2.404 1.277-1.082.882-1.733 1.983-1.994 3.235-.254 1.222-.157 2.725.859 4.462 1.916 3.274 6.026 7.235 11.41 10.246a40.027 40.027 0 003.953 1.935c-.492.126-1.018.244-1.574.352-3.084.597-6.994.857-11.912.47-3.978-.314-5.957 1.348-6.76 2.748-.92 1.604-1.135 4.434 1.442 7.457 3.153 3.698 11.885 9.399 23.876 12.945 13.409 3.965 30.913 5.371 48.714-1.404 15.346-5.867 27.059-16.778 33.357-30.367 5.822-12.563 7.057-27.416 2.199-42.655 2.008-1.428 5.097-3.876 7.486-6.889 2.01-2.534 3.518-5.452 3.785-8.483a2.841 2.841 0 00-.96-2.413 2.83 2.83 0 00-2.517-.621l-3.241.842c.433-1.082.806-2.171 1.009-3.111.382-1.77.15-3.229-.386-4.083a3.357 3.357 0 00-1.837-1.432c-.778-.258-2.027-.317-3.528-.009-2.833.581-7.067 2.355-9.312 3.348a38.287 38.287 0 00-7.777-5.103c-3.051-1.498-6.715-2.733-10.747-3.051-6.34-.5-16.369 1.067-22.66 8.573-3.636 4.338-6.098 10.701-5.716 19.946-8.239-.641-16.051-2.817-23.342-6.874-7.462-4.15-14.36-10.28-20.637-18.707a3.571 3.571 0 00-3.037-1.412 3.573 3.573 0 00-2.87 1.728s-4.992 8.024-3.418 19.96c.364 2.76 1.083 5.736 2.311 8.872l-.154-.003c-2.194-.023-3.852.859-4.904 2.334-.715 1.003-1.241 2.455-1.001 4.571.409 3.605 2.125 8.547 5.718 13.743 1.728 2.497 3.899 5.065 6.569 7.573zm101.382-51.192l-3.068 7.113a2.759 2.759 0 00.22 3.06 2.751 2.751 0 002.909.962l3.516-.847c-.259.363-.534.719-.818 1.065-3.281 3.994-8.069 6.727-8.069 6.727a3.007 3.007 0 00-1.319 3.58c4.855 14.366 3.755 28.34-1.873 40.001-5.745 11.906-16.229 21.366-29.846 26.348-10.179 3.736-20.215 4.5-29.259 3.64-12.886-1.224-23.761-5.775-30.142-9.93l-.32-.205c4.696-.021 8.424-.617 11.356-1.436 8.651-2.418 11.219-6.904 11.219-6.904a4.115 4.115 0 00.255-4.013 4.104 4.104 0 00-3.286-2.311c-3.976-.398-7.779-1.79-11.013-3.587-2.64-1.467-4.914-3.185-6.537-4.876 2.52-.156 5.264.192 5.264.192a4.016 4.016 0 004.235-2.69 4.024 4.024 0 00-1.687-4.73c-4.889-3-8.643-6.373-11.425-9.718-3.004-3.612-4.872-7.147-5.755-10.057.159.058.309.117.442.175 1.897.835 3.541 1.855 3.541 1.855a3.8 3.8 0 004.782-.597 3.813 3.813 0 00.359-4.812c-3.833-5.409-5.642-10.488-6.284-14.918a27.57 27.57 0 01.37-10.027c5.947 6.694 12.324 11.74 19.072 15.355 9.296 4.98 19.292 7.277 29.855 7.389a3.11 3.11 0 003.141-3.487c-1.181-9.048.531-15.148 3.761-19.143 4.847-5.994 12.791-7.176 17.787-6.875 3.316.201 6.337 1.18 8.865 2.363 4.756 2.226 7.799 5.155 7.799 5.155a2.518 2.518 0 002.832.496s6.197-3.854 9.121-4.313zm.469-.043c.631-.013 1.023.215 1.026.796.004.606-.799-.438-1.026-.796z"})]})]})]})})});export{i as __tla,a as default};
