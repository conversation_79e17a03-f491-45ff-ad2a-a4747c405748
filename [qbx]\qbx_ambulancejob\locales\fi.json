{"error": {"canceled": "Peruutettu", "impossible": "To<PERSON><PERSON>o ma<PERSON>...", "no_player": "<PERSON>i pelaajia lä<PERSON>ttyvillä", "no_firstaid": "Tarvitset ensiapupakkauksen", "no_bandage": "Tarvitset sideharson", "beds_taken": "<PERSON><PERSON><PERSON> s<PERSON> ovat varattuja...", "possessions_taken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> on otettu talteen...", "cant_help": "Henkilöä ei voi enään auttaa...", "not_ems": "Et ole ensihoitaja"}, "success": {"revived": "Elvytit henkilön!", "healthy_player": "Henkilö on terve!", "helped_player": "Autoit henkilöä!", "being_helped": "Sinua ollaan auttamassa..."}, "info": {"civ_died": "Sivi<PERSON> on kuollut", "civ_down": "<PERSON><PERSON><PERSON> on tajuton", "civ_call": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ems_down": "Doctor %s Down", "respawn_txt": "<PERSON><PERSON><PERSON> pääsy: ~r~%s~s~ sekuntia", "respawn_revive": "Pidä [~r~E~s~] pohjassa  %s sekunnin ajan päästäksesi teholle hintaan $~r~%s~s~", "bleed_out": "Vuo<PERSON><PERSON> kuiviin ~r~%s~s~ sekunnin kuluttua", "bleed_out_help": "Vuo<PERSON><PERSON> kuiviin ~r~%s~s~ sekunnin kulutt<PERSON>, sinua voidaan vielä auttaa!", "request_help": "Paina [~r~G~s~] pyyt<PERSON>äksesi apua!", "help_requested": "Ensihoitoa on ilmoitettu!", "amb_plate": "HUSL", "heli_plate": "KOPU", "status": "<PERSON><PERSON><PERSON><PERSON> tarka<PERSON>us", "is_status": "On %s", "healthy": "<PERSON>t taas täysin terve!", "safe": "<PERSON><PERSON><PERSON> var<PERSON>", "ems_alert": "Ensihoidon hälytys %s", "mr": "<PERSON><PERSON>", "mrs": "<PERSON><PERSON><PERSON>.", "dr_needed": "Lääkäriä tarvitaan sa<PERSON>lla", "dr_alert": "Doctor has already been notified", "ems_report": "<PERSON>si<PERSON><PERSON><PERSON> ilmoitus", "message_sent": "Viesti lähetettäväksi", "check_health": "Tarkasta henkilön kunto", "heal_player": "Hoida henkilöä", "revive_player": "Elvytä henkilöä"}, "mail": {"sender": "<PERSON><PERSON><PERSON>", "subject": "<PERSON><PERSON><PERSON> lasku", "message": "Hyvä %s %s, <br /><br /> Viimeisimmän sa<PERSON>ynnin hoidon lasku on nyt annettu teille.<br /> Laskun summaksi tuli: <strong>$%s€</strong><br /><br />Toivomme Teille pikaista paranemista! <br /> Ystävällisin terveisin, <br /> Sairaalan henkilökunta "}, "menu": {"amb_vehicles": "Ajoneuvot", "status": "Health Status"}, "text": {"pstash_button": "[E] - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kaappi", "pstash": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kaappi", "onduty_button": "[E] - <PERSON><PERSON> vuoroon", "offduty_button": "~r~E~w~ - <PERSON><PERSON> pois vuorosta", "duty": "Vuoroon/pois vuorosta", "armory_button": "[E] - <PERSON><PERSON><PERSON>", "armory": "<PERSON><PERSON><PERSON>", "veh_button": "[E] - <PERSON><PERSON>eu<PERSON><PERSON>", "elevator_roof": "[E] - <PERSON><PERSON> hissi katolle", "elevator_main": "[E] - <PERSON><PERSON> hissi alas", "el_roof": "Take the elevator to the roof", "el_main": "Take the elevator to the main floor", "call_doc": "[E] - <PERSON><PERSON> paikalle henk<PERSON>", "call": "Kutsu", "check_in": "[E] - <PERSON><PERSON> ho<PERSON>on", "check": "<PERSON><PERSON>", "lie_bed": "[E] - <PERSON><PERSON><PERSON>", "bed": "Lay in bed", "put_bed": "<PERSON><PERSON> kans<PERSON>inen s<PERSON>", "bed_out": "[E] - <PERSON><PERSON> yl<PERSON> s<PERSON>ä..", "alert": "<PERSON><PERSON>!"}, "progress": {"ifaks": "Syödään lääkkeitä...", "bandage": "Käytetään sideharsoa...", "painkillers": "Syödään kipulääkkeitä...", "revive": "Elvytetään henkilöä...", "healing": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>...", "checking_in": "Pääset hoitoon..."}}