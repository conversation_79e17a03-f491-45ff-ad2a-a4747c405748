import{importShared as P,__tla as j}from"../__federation_fn_import.js";import{h as z,E as q,c as F,w as f,s as g,T as y,u as w,i as A,a as G,r as M,g as N,__tla as O}from"../emotion-element-6a883da9.browser.esm-71290749.js";import{C as R,b as D,_ as H,d as I,e as J,__tla as K}from"../emotion-element-6a883da9.browser.esm-71290749.js";import"../hoist-non-react-statics.cjs-e0c24d9b.js";let x,E,v,_,C,Q=Promise.all([(()=>{try{return j}catch{}})(),(()=>{try{return O}catch{}})(),(()=>{try{return K}catch{}})()]).then(async()=>{const{createElement:h,useContext:d,useRef:k,Fragment:S}=await P("react");v=function(s,e){var n=arguments;if(e==null||!z.call(e,"css"))return h.apply(void 0,n);var t=n.length,o=new Array(t);o[0]=q,o[1]=F(s,e);for(var a=2;a<t;a++)o[a]=n[a];return h.apply(null,o)},E=f(function(s,e){var n=s.styles,t=g([n],void 0,d(y)),o=k();return w(function(){var a=e.key+"-global",r=new e.sheet.constructor({key:a,nonce:e.sheet.nonce,container:e.sheet.container,speedy:e.sheet.isSpeedy}),u=!1,i=document.querySelector('style[data-emotion="'+a+" "+t.name+'"]');return e.sheet.tags.length&&(r.before=e.sheet.tags[0]),i!==null&&(u=!0,i.setAttribute("data-emotion",a),r.hydrate([i])),o.current=[r,u],function(){r.flush()}},[e]),w(function(){var a=o.current,r=a[0],u=a[1];if(u){a[1]=!1;return}if(t.next!==void 0&&A(e,t.next,!0),r.tags.length){var i=r.tags[r.tags.length-1].nextElementSibling;r.before=i,r.flush()}e.insert("",t,r,!1)},[e,t.name]),null}),_=function(){for(var s=arguments.length,e=new Array(s),n=0;n<s;n++)e[n]=arguments[n];return g(e)};let p;C=function(){var s=_.apply(void 0,arguments),e="animation-"+s.name;return{name:e,styles:"@keyframes "+e+"{"+s.styles+"}",anim:1,toString:function(){return"_EMO_"+this.name+"_"+this.styles+"_EMO_"}}},p=function s(e){for(var n=e.length,t=0,o="";t<n;t++){var a=e[t];if(a!=null){var r=void 0;switch(typeof a){case"boolean":break;case"object":{if(Array.isArray(a))r=s(a);else{r="";for(var u in a)a[u]&&u&&(r&&(r+=" "),r+=u)}break}default:r=a}r&&(o&&(o+=" "),o+=r)}}return o};function T(s,e,n){var t=[],o=N(s,t,n);return t.length<2?n:o+e(t)}let b;b=function(s){var e=s.cache,n=s.serializedArr;return G(function(){for(var t=0;t<n.length;t++)A(e,n[t],!1)}),null},x=f(function(s,e){var n=!1,t=[],o=function(){for(var i=arguments.length,c=new Array(i),l=0;l<i;l++)c[l]=arguments[l];var m=g(c,e.registered);return t.push(m),M(e,m,!1),e.key+"-"+m.name},a=function(){for(var i=arguments.length,c=new Array(i),l=0;l<i;l++)c[l]=arguments[l];return T(e.registered,o,p(c))},r={css:o,cx:a,theme:d(y)},u=s.children(r);return n=!0,h(S,null,h(b,{cache:e,serializedArr:t}),u)})});export{R as CacheProvider,x as ClassNames,E as Global,y as ThemeContext,D as ThemeProvider,Q as __tla,H as __unsafe_useEmotionCache,v as createElement,_ as css,v as jsx,C as keyframes,I as useTheme,f as withEmotionCache,J as withTheme};
