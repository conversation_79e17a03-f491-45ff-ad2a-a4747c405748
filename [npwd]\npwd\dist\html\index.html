<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + React + TS</title>
    <script type="module" crossorigin src="./assets/index-ebf41f23.js"></script>
    <link rel="modulepreload" crossorigin href="./assets/preload-helper-a4192956.js">
    <link rel="modulepreload" crossorigin href="./assets/__federation_fn_import.js">
    <link rel="modulepreload" crossorigin href="./assets/_commonjsHelpers-de833af9.js">
    <link rel="modulepreload" crossorigin href="./assets/__federation_shared_react-e93ad879.js">
    <link rel="modulepreload" crossorigin href="./assets/jsx-runtime-5fe4d0a7.js">
    <link rel="modulepreload" crossorigin href="./assets/hoist-non-react-statics.cjs-e0c24d9b.js">
    <link rel="modulepreload" crossorigin href="./assets/inheritsLoose-1db512d6.js">
    <link rel="modulepreload" crossorigin href="./assets/emotion-element-6a883da9.browser.esm-71290749.js">
    <link rel="modulepreload" crossorigin href="./assets/__federation_expose_Input-51304708.js">
    <link rel="modulepreload" crossorigin href="./assets/__federation_shared_@emotion/react-9ce1628c.js">
    <link rel="modulepreload" crossorigin href="./assets/setPrototypeOf-15ee5953.js">
    <link rel="stylesheet" href="./assets/index-b0f1ff4a.css">
  </head>
  <body>
    <div id="root"></div>
    
  </body>
</html>
