import{j as c}from"./jsx-runtime-5fe4d0a7.js";import{S as e,__tla as t}from"./__federation_expose_Input-51304708.js";let a,i=Promise.all([(()=>{try{return t}catch{}})()]).then(async()=>{a=l=>c.jsx(e,{...l,children:c.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",fillRule:"evenodd",strokeLinejoin:"round",strokeMiterlimit:"2",clipRule:"evenodd",viewBox:"0 0 132 132",children:[c.jsx("metadata",{id:"author",children:"<EMAIL>"}),c.jsx("path",{fill:"none",d:"M0 0H131.067V131.086H0z"}),c.jsxs("g",{fill:l.htmlColor||"#000",children:[c.jsx("path",{fillRule:"nonzero",d:"M58.446 100.843l-.002.001-.111-.814-.423.072a4.189 4.189 0 01-.628.059c-.215-.004-.373 0-.488.006-.457-.006-1.092-.107-1.901-.287l-.027-.006a9.083 9.083 0 00-1.957-.157 8.404 8.404 0 00-2.156.327c-.984.281-1.545.797-1.734 1.512-.175.65.03 1.248.659 1.785.655.559 1.911 1.072 3.819 1.456 4.335.947 8.77.769 13.305-.549.309 2.174.647 4.292 1.012 6.353l.087.326a.72.72 0 00.103.217c.624.898 1.978 1.497 4.182 1.594 1.511.08 2.51-.179 3.051-.589.482-.366.725-.866.629-1.524a1239.382 1239.382 0 01-1.545-9.553c.72-.411 1.412-.848 2.077-1.312 7.412-4.882 10.396-9.629 9.221-14.124-.847-3.503-4.878-6.508-12.293-8.84a37.323 37.323 0 00-2.198-.635c-.34-3.261-.616-6.387-.829-9.379a.643.643 0 00-.014-.098c-.001-.012-.052-.501-.112-1.217a46.405 46.405 0 017.372-1.382l.015-.002c1.086-.126 1.95-.436 2.606-.901.396-.28.616-.596.7-.929.112-.447-.019-.964-.535-1.518-.612-.657-1.577-1.186-2.932-1.546-1.293-.345-2.472-.442-3.533-.315a70.99 70.99 0 00-4.211.627 211.48 211.48 0 00-.383-5.395.658.658 0 00-.027-.163l-.082-.293a.71.71 0 00-.047-.111c-.476-.887-1.732-1.54-3.935-1.708l-.008-.001c-1.873-.131-3.01.255-3.52.838-.275.315-.42.707-.383 1.186.132 2.266.303 4.766.513 7.5-.402.11-.779.226-1.131.347-.887.294-1.603.547-2.15.759-.54.21-1.239.494-2.097.852-.901.376-1.648.74-2.243 1.088-.6.352-1.211.779-1.832 1.283-.646.525-1.156 1.055-1.537 1.586l-.007.01c-.917 1.325-1.276 2.575-1.122 3.743.156 1.185.668 2.201 1.519 3.052.794.793 1.877 1.553 3.257 2.271a29.99 29.99 0 003.778 1.671c1.179.42 2.454.8 3.823 1.139.373.106.86.234 1.448.383a604.656 604.656 0 002.224 17.775c-.711.422-1.436.786-2.178 1.093l-.038.017c-1.679.767-3.286 1.286-4.826 1.549l-.335.057.11.814zm13.367-18.787c.275.109.554.235.839.379l.56.129-.558-.128.329.163c.207.142.482.335.824.578l.249.139c.088.072.176.149.266.235l.382.506.21.259a.693.693 0 00.051.086c.406.585.605 1.225.601 1.918v.011c.019 1.744-.84 3.531-2.528 5.377a658.143 658.143 0 01-1.225-9.652zm-8.924-8.139c-1.188-.383-2.026-.773-2.479-1.216l-.021-.02c-.756-.69-.707-1.539-.028-2.542l.001-.002c.41-.61 1.089-1.176 2.024-1.711l.503 5.491z"}),c.jsx("path",{d:"M46.249 27.211a1.879 1.879 0 001.015 3.598s.527-.047 1.468-.123c.015.23.024.474.029.729-.558.043-.864.069-.864.069a1.36 1.36 0 00-.794 2.446 1.36 1.36 0 00.917 2.201l.575.098c-.036.468-.072.895-.106 1.258-.825.119-1.288.191-1.288.191a1.88 1.88 0 00-1.486 2.848c-.606.38-1.34.798-2.223 1.249-1.736.885-3.984 1.833-6.882 2.8-6.965 2.325-15.771 8.42-21.67 17.453-4.799 7.349-7.651 16.623-6.169 27.345 1.398 10.098 7.128 18.379 15.434 24.695 10.511 7.992 25.239 12.766 40.136 14.111 12.423 1.091 24.62-1.912 34.562-7.464 11.755-6.564 20.295-16.711 22.802-27.706 2.092-9.335.674-16.82-2.607-23.035-4.929-9.337-14.317-15.729-22.31-21.233-3.373-2.323-6.463-4.463-8.642-6.699-.662-.68-1.248-1.346-1.683-2.033a1.88 1.88 0 00.625-2.853c-.054-.079-.172-.185-.356-.301a1.36 1.36 0 00-1.306-1.582l-.14-1.035c.316.005.579.016.784.032a1.36 1.36 0 00.346-2.696 23.32 23.32 0 00-1.429-.186c-.042-.617-.097-1.209.016-1.704.426.087.838.186 1.234.297a1.879 1.879 0 001.24-3.546c-.223-.093-.45-.184-.682-.272 1.019-1.804 2.367-3.983 3.877-5.911 1.331-1.7 2.707-3.28 4.162-3.843 3.255-1.26 4.81-2.409 5.598-3.425 1.247-1.606 1.369-3.239.948-5.737a3.421 3.421 0 00-3.225-2.88l-33.093-.974c-2.56-.035-4.433.096-5.889.297a24.995 24.995 0 00-3.595.769c-1.129.319-1.903.621-3.564.373-1.588-.238-2.938-1.148-4.52-1.637-.946-.292-1.942-.495-3.033-.509a5.642 5.642 0 00-1.977.317 7.959 7.959 0 00-1.67.844c-.233.151-.462.31-.693.459-.107.069-.164.224-.297.225-1.511.014-2.736-.964-3.937-1.304-.731-.206-1.454-.311-2.174-.26-2.383.167-4.265.453-5.384.835-.984.335-1.651.824-2.084 1.323-.438.409-1.124 1.29-1.173 2.794-.031.92.16 2.309 1.5 4.126 1.152 1.563 3.305 3.682 6.393 6.136 2.708 2.152 6.155 4.601 10.255 7.218 1.083.69 2.12 1.239 3.029 1.812zm32.361 13.2c-1.061.089-2.208.188-3.417.289-2.669.221-5.614.512-8.575.666-2.615.135-5.233.165-7.656.18-2.86.018-4.243.131-6.235.101-.8.921-2.556 2.416-4.83 3.797-2.025 1.231-5.681 2.769-9.701 4.224-6.066 2.196-13.625 7.254-18.618 15.311-3.955 6.381-6.258 14.353-4.787 23.493 1.349 8.389 6.386 15.105 13.41 20.232 9.698 7.079 19.205 10.618 32.77 11.608 10.868.82 24.209-1.713 32.853-6.643 9.604-5.478 16.825-13.624 18.831-22.665 1.607-7.115.62-12.825-1.854-17.579-5.146-9.886-14.875-15.621-22.445-21.301-5.022-3.767-8.623-7.607-9.746-11.713zm-.712-5.983c-1.002.036-2.059.073-3.154.109-1.346.045-2.748.097-4.175.138l1.161.101c2.234.194 4.369.389 6.271.53l-.103-.878zm-.152-5.327a98.866 98.866 0 00-2.854.042c-1.797.051-3.633.17-5.473.272-1.854.104-3.706.216-5.506.316-2.344.131-4.596.237-6.65.359-1.422.085-2.746.172-3.936.254.016.243.026.494.032.751 1.743-.109 3.806-.224 6.04-.323 2.422-.108 5.044-.203 7.674-.209 2.619-.005 5.242.087 7.682.182.996.038 1.961.081 2.882.127l.002-.191c.008-.543.043-1.072.107-1.58zm1.763-4.717c1.23-2.241 3.264-5.652 5.61-8.551 1.665-2.058 3.511-3.84 5.341-4.988L64.909 9.613c-6.684-.236-8.083.956-10.283 1.21-.976.113-2.066.119-3.552-.135-1.876-.322-3.493-1.333-5.357-1.952-.434-.145-.891-.264-1.395-.282-.399-.014-.626.342-.924.531-.505.321-1.015.618-1.558.826a5.391 5.391 0 01-2.023.384 8.043 8.043 0 01-1.891-.248c-.981-.249-1.896-.665-2.764-1.05-.397-.175-.741-.428-1.121-.409a32.788 32.788 0 00-3.217.345l.065.099c.944 1.339 2.762 3.127 5.322 5.254 2.55 2.119 6.07 3.938 9.943 6.533 1.97 1.321 3.514 2.855 4.801 4.115.334.328.641.673.916 1.044 1.892-.394 4.193-.829 6.738-1.215 2.534-.384 5.306-.726 8.143-.87 2.753-.139 5.561-.079 8.259.11 1.546.109 3.054.268 4.498.481zm15.26-15.558l.074.231-.179.08c.016-.101.055-.215.105-.311zm.365-.049l-.177-.158c.037-.015.076-.007.111.028.044.045.066.088.066.13z"})]})]})})});export{i as __tla,a as default};
