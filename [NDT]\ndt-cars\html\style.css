/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: transparent;
    overflow: hidden;
}

/* Container */
#ndt-cars-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    display: flex;
    align-items: center;
    justify-content: center;
}

#ndt-cars-container.hidden {
    display: none !important;
}

/* Debug: Force show when not hidden */
#ndt-cars-container:not(.hidden) {
    display: flex !important;
}

/* Overlay - Completely Transparent */
.menu-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: transparent;
    backdrop-filter: none;
}

/* Menu Container - Cyberpunk Style */
.menu-container {
    position: relative;
    width: 90%;
    max-width: 1200px;
    max-height: 80vh;
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #0d0d0d 100%);
    border: 2px solid #00ffff;
    border-radius: 15px;
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.3),
        0 0 40px rgba(0, 255, 255, 0.1),
        inset 0 0 20px rgba(0, 255, 255, 0.05);
    overflow: hidden;
    animation: cyberSlideIn 0.4s ease-out;
}

@keyframes cyberSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
        border-color: transparent;
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        border-color: #00ffff;
    }
}

/* Header - Cyberpunk Style */
.menu-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 35px;
    background: linear-gradient(135deg, #00ffff 0%, #0080ff 50%, #00ffff 100%);
    color: #000;
    position: relative;
    border-bottom: 2px solid #00ffff;
}

.menu-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%),
        repeating-linear-gradient(90deg, transparent, transparent 2px, rgba(255, 255, 255, 0.1) 2px, rgba(255, 255, 255, 0.1) 4px);
    animation: scanline 2s linear infinite;
}

@keyframes scanline {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.header-content {
    position: relative;
    z-index: 1;
}

.header-content h1 {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 5px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
    color: #000;
}

.header-content p {
    font-size: 1rem;
    opacity: 0.8;
    font-weight: 500;
    color: #000;
}

.close-btn {
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid #000;
    color: #000;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
}

.close-btn:hover {
    background: rgba(0, 0, 0, 0.5);
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
}

/* Categories Section - Cyberpunk Style */
.categories-section {
    padding: 35px;
    max-height: calc(80vh - 100px);
    overflow-y: auto;
}

.categories-section h2 {
    color: #00ffff;
    font-size: 1.6rem;
    margin-bottom: 25px;
    text-align: center;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.category-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #0d0d0d 100%);
    border: 2px solid #00ffff;
    border-radius: 12px;
    padding: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
}

.category-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.category-card:hover::before {
    left: 100%;
}

.category-card:hover {
    transform: translateY(-5px);
    border-color: #ff00ff;
    box-shadow: 
        0 0 25px rgba(0, 255, 255, 0.4),
        0 0 50px rgba(255, 0, 255, 0.2);
}

.category-card h3 {
    color: #00ffff;
    font-size: 1.3rem;
    margin-bottom: 10px;
    font-weight: 600;
    text-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
}

.category-card p {
    color: #888;
    font-size: 0.9rem;
    margin-bottom: 15px;
}

.category-icon {
    font-size: 2.2rem;
    color: #00ffff;
    margin-bottom: 15px;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* Vehicles Section - Cyberpunk Style */
.vehicles-section {
    padding: 35px;
    max-height: calc(80vh - 100px);
    overflow-y: auto;
}

.vehicles-section.hidden {
    display: none;
}

.vehicles-header {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    gap: 20px;
}

.back-btn {
    background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);
    border: 2px solid #00ffff;
    color: #000;
    padding: 12px 20px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

.back-btn:hover {
    transform: translateX(-3px);
    box-shadow: 0 0 25px rgba(0, 255, 255, 0.5);
    border-color: #ff00ff;
}

#category-title {
    color: #00ffff;
    font-size: 1.6rem;
    font-weight: 600;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.vehicles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 15px;
}

.vehicle-card {
    background: linear-gradient(135deg, #1a1a1a 0%, #0d0d0d 100%);
    border: 2px solid #00ffff;
    border-radius: 10px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    overflow: hidden;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.2);
}

.vehicle-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
}

.vehicle-card:hover::before {
    left: 100%;
}

.vehicle-card:hover {
    transform: translateY(-3px);
    border-color: #ff00ff;
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.4),
        0 0 40px rgba(255, 0, 255, 0.2);
}

.vehicle-card h3 {
    color: #00ffff;
    font-size: 1.1rem;
    margin-bottom: 8px;
    font-weight: 500;
    text-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
}

.vehicle-card .vehicle-icon {
    font-size: 2.5rem;
    color: #00ffff;
    margin-bottom: 10px;
    text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.vehicle-card .spawn-btn {
    background: linear-gradient(135deg, #00ff00 0%, #00cc00 100%);
    border: 2px solid #00ff00;
    color: #000;
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    font-size: 0.9rem;
    font-weight: 600;
    transition: all 0.3s ease;
    margin-top: 10px;
    width: 100%;
    box-shadow: 0 0 10px rgba(0, 255, 0, 0.3);
}

.spawn-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
    border-color: #ffff00;
}

/* Loading - Cyberpunk Style */
.loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px;
    color: #00ffff;
}

.loading.hidden {
    display: none;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(0, 255, 255, 0.3);
    border-top: 4px solid #00ffff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.3);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading p {
    font-size: 1.1rem;
    font-weight: 500;
    text-shadow: 0 0 8px rgba(0, 255, 255, 0.5);
}

/* Scrollbar - Cyberpunk Style */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 255, 255, 0.1);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #00ffff 0%, #0080ff 100%);
    border-radius: 10px;
    border: 1px solid #00ffff;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #ff00ff 0%, #00ffff 100%);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* Responsive */
@media (max-width: 768px) {
    .menu-container {
        width: 95%;
        max-height: 90vh;
    }
    
    .menu-header {
        padding: 20px 25px;
    }
    
    .header-content h1 {
        font-size: 1.8rem;
    }
    
    .categories-section,
    .vehicles-section {
        padding: 25px;
    }
    
    .categories-grid {
        grid-template-columns: 1fr;
    }
    
    .vehicles-grid {
        grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.category-card,
.vehicle-card {
    animation: slideUp 0.3s ease-out;
} 