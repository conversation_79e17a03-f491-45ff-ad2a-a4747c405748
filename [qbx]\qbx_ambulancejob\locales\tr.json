{"error": {"canceled": "İptal edildi", "impossible": "İşlem imkansız...", "no_player": "Yakınlarda oyuncu yok", "no_firstaid": "İlk yardım çantasına ihtiyacın var", "no_bandage": "Bandaja ihtiyacın var", "beds_taken": "Yataklar dolu...", "possessions_taken": "Tüm eşyaların alındı...", "cant_help": "Bu kişiye yardım edem<PERSON>sin...", "not_ems": "E<PERSON>in veya mesaide de<PERSON>"}, "success": {"revived": "<PERSON><PERSON> kişiyi hayata dö<PERSON>", "healthy_player": "Oyuncu sağlıklı", "helped_player": "Kişiye yardım ettin", "being_helped": "<PERSON>ım ediliyorsun..."}, "info": {"civ_died": "<PERSON><PERSON>", "civ_down": "Sivil bayıldı", "civ_call": "Sivil Çağrısı", "ems_down": "Doktor %s bayıldı", "respawn_txt": "<PERSON><PERSON>den Doğuş: ~r~%s~s~ SANIYE içinde", "respawn_revive": "[~r~E~s~]'ye %s SANIYE basılı tutarak $~r~%s~s~ karşılığında yeniden doğ", "bleed_out": "Kan kaybı: ~r~%s~s~ SANIYE içinde", "bleed_out_help": "Kan kaybı: ~r~%s~s~ SANIYE içinde, yardım edilebilir", "request_help": "[~r~G~s~]'ye basarak yardım iste", "help_requested": "EMS ekibi bilgilendirildi", "amb_plate": "EMS", "heli_plate": "EMS", "status": "Durum Kontrolü", "is_status": "Durum: %s", "healthy": "Tamamen sağlıklısın!", "safe": "<PERSON><PERSON><PERSON>", "ems_alert": "EMS Uyarısı - %s", "mr": "Bay", "mrs": "Bayan", "dr_needed": "Pillbox Hastanesi'nde bir doktora ihtiyaç var", "dr_alert": "Doktor zaten bilgilendirildi", "ems_report": "EMS Raporu", "message_sent": "Gönderilecek mesaj", "check_health": "Oyuncunun sağlığını kontrol et", "heal_player": "Bir oyuncuyu iyileştir", "revive_player": "Bir oyuncuyu hayata dö<PERSON>ür"}, "mail": {"sender": "Pillbox Hastanesi", "subject": "<PERSON><PERSON><PERSON>", "message": "Sevgili %s %s, <br /><br />Son hastane ziyaretinin masraflarını içeren bir e-posta aldınız.<br />Son maliyet: <strong>$%s</strong><br /><br />Geçmiş olsun dileklerimizle!"}, "menu": {"amb_vehicles": "Ambulans Araçları", "status": "Sağlık Durumu"}, "text": {"pstash_button": "[E] - <PERSON><PERSON><PERSON><PERSON> depo", "pstash": "<PERSON><PERSON><PERSON><PERSON> depo", "onduty_button": "[E] - <PERSON><PERSON>", "offduty_button": "[E] - <PERSON><PERSON>", "duty": "Mesaidesin / Mesaiden Çıktın", "armory_button": "[E] - Ce<PERSON>nel<PERSON>", "armory": "Cephanelik", "veh_button": "[E] - Aracı Al / Depola", "elevator_roof": "[E] - Asansörle çatıya çık", "elevator_main": "[E] - <PERSON><PERSON><PERSON><PERSON> in", "el_roof": "Asansörle çatıya çık", "el_main": "Asans<PERSON><PERSON> ana kata in", "call_doc": "[E] - <PERSON><PERSON><PERSON>", "call": "Çağır", "check_in": "[E] - <PERSON><PERSON><PERSON>", "check": "<PERSON><PERSON><PERSON>", "lie_bed": "[E] - <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "bed": "Ya<PERSON><PERSON><PERSON>", "put_bed": "Vatandaşı yatağa yatır", "bed_out": "[E] - <PERSON><PERSON><PERSON><PERSON> kalk", "alert": "Uyarı!"}, "progress": {"ifaks": "İfaks alınıyor...", "bandage": "<PERSON><PERSON> kullanılıyor...", "painkillers": "Ağrı kesici alınıyor...", "revive": "<PERSON><PERSON><PERSON> hayata döndürülüyor...", "healing": "<PERSON><PERSON><PERSON> iyileştiriliyor...", "checking_in": "<PERSON><PERSON><PERSON> yapılıyor..."}}