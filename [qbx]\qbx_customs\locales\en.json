{"notifications": {"success": {"repaired": "Vehicle repaired!", "paid": "You paid $%s from your bank account"}, "error": {"money": "You don't have enough money!", "alreadyInstalled": "You already have this mod installed"}, "props": {"installTitle": "Customs"}}, "dragCam": {"zoomIn": "Increase zoom", "zoomOut": "Decrease zoom", "toggleDoors": "Toggle doors", "changeView": "Toggle first person view"}, "textUI": {"tune": "Press [E] to tune your car"}, "menus": {"main": {"title": "Popcorn Customs", "repair": "Repair", "performance": "Performance", "parts": "Cosmetics - Parts", "colors": "Cosmetics - Colors", "extras": "Extras"}, "colors": {"primary": "Paint primary", "secondary": "Paint secondary", "neon": "Neon", "cosmetics_colors": "Cosmetics - Colors"}, "neon": {"title": "Neon", "neon": "Neon %s%s", "color": "Neon color", "installed": "%s neon installed"}, "paint": {"title": "Cosmetics - Colors", "primary": "Primary paint", "secondary": "Secondary paint"}, "parts": {"title": "Cosmetics - Parts", "wheels": "Wheels"}, "performance": {"title": "Performance", "turbo": "Turbo"}, "wheels": {"title": "Wheels", "bikeRear": "Bike rear wheel", "installed": "%s %s installed"}, "options": {"interior": "Interior", "livery": "Livery", "pearlescent": "<PERSON><PERSON><PERSON>", "plateIndex": {"title": "Plate Index", "installed": "%s plate installed"}, "tyreSmoke": "Tyre smoke", "wheelColor": "Wheel color", "windowTint": {"title": "Window Tint", "installed": "%s windows installed"}, "xenon": {"title": "Xenon", "installed": "%s xenon installed"}}, "general": {"stock": "Stock", "enabled": "Enabled", "disabled": "Disabled", "installed": "%s installed", "applied": "%s applied"}}, "general": {"payReason": "Customs"}}