// NDT Cars UI Script
let currentCategory = null;
let categories = [];
let vehicles = {};

// Category icons mapping
const categoryIcons = {
    'Super Cars': 'fas fa-car-side',
    'Sports Cars': 'fas fa-car',
    'Muscle Cars': 'fas fa-car-rear',
    'Motorcycles': 'fas fa-motorcycle',
    'SUVs': 'fas fa-truck-monster',
    'Trucks': 'fas fa-truck'
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('NDT Cars UI loaded');
    
    // Listen for messages from FiveM
    window.addEventListener('message', function(event) {
        const data = event.data;
        console.log('Received message:', data);
        
        switch(data.action) {
            case 'showMenu':
                showMenu(data.categories);
                break;
            case 'hideMenu':
                hideMenu();
                break;
            case 'showVehicles':
                showVehicles(data.category, data.vehicles);
                break;
        }
    });
    
    // Close menu when clicking overlay
    document.querySelector('.menu-overlay').addEventListener('click', function(e) {
        if (e.target === this) {
            closeMenu();
        }
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeMenu();
        }
    });
});

// Show the main menu
function showMenu(categoriesData) {
    console.log('Showing menu with categories:', categoriesData);
    categories = categoriesData || [];
    currentCategory = null;
    
    // Show the container first
    const container = document.getElementById('ndt-cars-container');
    container.classList.remove('hidden');
    
    // Show loading
    showLoading();
    
    // Render categories
    renderCategories();
    
    // Hide loading and show categories
    setTimeout(() => {
        hideLoading();
        showCategories();
    }, 500);
}

// Render categories
function renderCategories() {
    const categoriesGrid = document.getElementById('categories-grid');
    categoriesGrid.innerHTML = '';
    
    console.log('Rendering categories:', categories);
    
    categories.forEach((category, index) => {
        console.log('Rendering category:', category);
        
        const categoryCard = document.createElement('div');
        categoryCard.className = 'category-card';
        categoryCard.onclick = () => selectCategory(category, index);
        
        const icon = categoryIcons[category.name] || 'fas fa-car';
        
        categoryCard.innerHTML = `
            <div class="category-icon">
                <i class="${icon}"></i>
            </div>
            <h3>${category.name}</h3>
            <p>${category.vehicles.length} vehicles available</p>
        `;
        
        categoriesGrid.appendChild(categoryCard);
    });
}

// Select a category
function selectCategory(category, index) {
    console.log('Selected category:', category);
    currentCategory = category;
    
    // Show loading
    showLoading();
    
    // Simulate loading time
    setTimeout(() => {
        hideLoading();
        showVehicles(category, category.vehicles);
    }, 300);
}

// Show vehicles in a category
function showVehicles(category, vehiclesList) {
    const vehiclesSection = document.getElementById('vehicles-section');
    const categoriesSection = document.querySelector('.categories-section');
    const categoryTitle = document.getElementById('category-title');
    const vehiclesGrid = document.getElementById('vehicles-grid');
    
    // Update title
    categoryTitle.textContent = category.name;
    
    // Render vehicles
    vehiclesGrid.innerHTML = '';
    
    vehiclesList.forEach((vehicle, index) => {
        const vehicleCard = document.createElement('div');
        vehicleCard.className = 'vehicle-card';
        
        vehicleCard.innerHTML = `
            <div class="vehicle-icon">
                <i class="fas fa-car"></i>
            </div>
            <h3>${vehicle.name}</h3>
            <button class="spawn-btn" onclick="spawnVehicle('${vehicle.model}')">
                <i class="fas fa-play"></i> Spawn Vehicle
            </button>
        `;
        
        vehiclesGrid.appendChild(vehicleCard);
    });
    
    // Show vehicles section, hide categories
    categoriesSection.classList.add('hidden');
    vehiclesSection.classList.remove('hidden');
}

// Show categories
function showCategories() {
    const vehiclesSection = document.getElementById('vehicles-section');
    const categoriesSection = document.querySelector('.categories-section');
    
    vehiclesSection.classList.add('hidden');
    categoriesSection.classList.remove('hidden');
}

// Spawn a vehicle
function spawnVehicle(vehicleModel) {
    console.log('Spawning vehicle:', vehicleModel);
    
    // Send message to FiveM
    fetch(`https://${GetParentResourceName()}/spawnVehicle`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            vehicle: vehicleModel
        })
    });
    
    // Close menu after spawning
    setTimeout(() => {
        closeMenu();
    }, 500);
}

// Close the menu
function closeMenu() {
    console.log('Closing menu');
    const container = document.getElementById('ndt-cars-container');
    container.classList.add('hidden');
    
    // Send message to FiveM
    fetch(`https://${GetParentResourceName()}/closeMenu`, {
        method: 'POST'
    });
}

// Hide the menu
function hideMenu() {
    const container = document.getElementById('ndt-cars-container');
    container.classList.add('hidden');
}

// Show loading
function showLoading() {
    const loading = document.getElementById('loading');
    loading.classList.remove('hidden');
}

// Hide loading
function hideLoading() {
    const loading = document.getElementById('loading');
    loading.classList.add('hidden');
}

// Get parent resource name (FiveM function)
function GetParentResourceName() {
    return 'ndt-cars';
}

// Export functions for FiveM
window.NDTCars = {
    showMenu: showMenu,
    hideMenu: hideMenu,
    closeMenu: closeMenu,
    spawnVehicle: spawnVehicle
}; 