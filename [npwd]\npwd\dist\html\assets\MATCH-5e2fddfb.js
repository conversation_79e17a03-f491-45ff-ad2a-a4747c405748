import{j as a}from"./jsx-runtime-5fe4d0a7.js";import{S as t,__tla as i}from"./__federation_expose_Input-51304708.js";let l,e=Promise.all([(()=>{try{return i}catch{}})()]).then(async()=>{l=s=>a.jsx(t,{...s,children:a.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",fillRule:"evenodd",strokeLinejoin:"round",strokeMiterlimit:"2",clipRule:"evenodd",viewBox:"0 0 176 178",children:[a.jsx("metadata",{id:"author",children:"<EMAIL>"}),a.jsx("path",{fill:"none",d:"M0 0H176V178H0z"}),a.jsx("clipPath",{id:"_clip1",children:a.jsx("path",{d:"M0 0H176V178H0z"})}),a.jsxs("g",{clipPath:"url(#_clip1)",children:[a.jsx("path",{fill:"#444e87",d:"M87.594.286C152.799-3.983 176 40.142 176 89.135s-29.665 91.97-84.913 88.689C45.254 175.102-1.31 150.139.028 87.556.642 58.856 24.249 4.434 87.594.286z"}),a.jsxs("g",{children:[a.jsx("path",{fill:"#c1365b",d:"M64.312 48.178s-3.065 23.758 6.131 24.371c9.197.613 33.261-19.129 21.919-46.903 0 0 33.721 14.838 42.151 40.128 8.43 25.291 1.839 83.382-48.742 80.01-50.581-3.372-47.822-42.61-47.056-50.274.767-7.664 5.543-22.915 11.189-30.196 5.825-7.51 14.408-17.136 14.408-17.136z"}),a.jsx("path",{fill:"#fff",d:"M66.817 48.501a2.525 2.525 0 00-4.39-2.004s-4.317 4.725-8.948 10.133a252.393 252.393 0 00-5.782 6.973c-2.936 3.702-5.696 9.381-7.841 15.181-2.251 6.083-3.814 12.294-4.271 16.413-.589 5.346-2.069 24.964 11.759 39.217 7.411 7.638 19.253 13.81 38.17 15.23 20.106 1.439 33.837-6.195 42.733-17.689 7.124-9.205 11.098-20.996 12.633-32.683 1.706-12.98.389-25.811-2.631-34.743-2.778-8.147-7.949-15.299-13.906-21.26-13.395-13.408-30.586-20.793-30.586-20.793a3.463 3.463 0 00-4.602 4.48c4.231 10.065 3.34 19.01.151 26.089-2.916 6.475-7.678 11.412-12.111 14.217-2.506 1.585-4.831 2.547-6.563 2.452-.944-.052-1.572-.685-2.07-1.522-.684-1.15-1.136-2.63-1.452-4.236-1.324-6.725-.293-15.455-.293-15.455zm-5.573 7.177c-.088 3.935.119 8.623 1.226 12.245 1.317 4.309 3.93 7.159 7.784 7.46 2.593.203 6.224-.818 10.033-3.112 5.339-3.214 11.134-8.95 14.757-16.57 3.087-6.489 4.6-14.343 2.925-23.183 5.706 3.31 13.944 8.765 20.994 16.044 5.057 5.222 9.514 11.397 11.814 18.458 4.212 12.771 4.26 34.332-3.735 51.208-6.747 14.241-19.475 25.037-41.014 23.697-16.539-.964-27.054-5.937-33.647-12.488-12.121-12.043-10.95-29.005-10.536-33.614.335-3.764 1.671-9.449 3.603-15.04 1.806-5.229 4.109-10.389 6.663-13.757a248.605 248.605 0 015.492-6.948 432.53 432.53 0 013.641-4.401v.001zm20.935 78.514s1.164 1.218 4.516 1.85c.845.16 1.842.277 2.997.22.881-.044 1.846-.205 2.896-.469 1.255-.316 2.579-.8 4.083-1.41a1.218 1.218 0 00-.59-2.362c-1.021.099-1.94.121-2.821.167-1.009.052-1.912.043-2.745.074-.642.024-1.232.061-1.776.09-.649.035-1.223.088-1.744.102-2.382.066-3.401-.243-3.401-.243a1.219 1.219 0 00-1.698.283 1.219 1.219 0 00.283 1.698zM49.844 87.483s-2.825 7.736-1.909 17.261c.354 3.681 1.265 7.633 3.136 11.432 1.649 3.346 4.047 6.575 7.344 9.413 3.816 3.285 8.865 6.037 15.467 7.9a1.62 1.62 0 001.024-3.073c-4.695-1.823-8.382-4.112-11.348-6.611a30.402 30.402 0 01-5.175-5.619c-1.016-1.422-1.851-2.883-2.581-4.341-2.09-4.175-3.092-8.448-3.469-12.307-.718-7.348.611-13.116.611-13.116a1.62 1.62 0 10-3.1-.939z"})]})]})]})})});export{e as __tla,l as default};
