import{r as u}from"./__federation_shared_react-e93ad879.js";var i={exports:{}},t={};/** @license React v17.0.2
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var a=u,l=60103;t.Fragment=60107;if(typeof Symbol=="function"&&Symbol.for){var _=Symbol.for;l=_("react.element"),t.Fragment=_("react.fragment")}var y=a.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,v=Object.prototype.hasOwnProperty,x={key:!0,ref:!0,__self:!0,__source:!0};function m(o,r,p){var e,n={},f=null,s=null;p!==void 0&&(f=""+p),r.key!==void 0&&(f=""+r.key),r.ref!==void 0&&(s=r.ref);for(e in r)v.call(r,e)&&!x.hasOwnProperty(e)&&(n[e]=r[e]);if(o&&o.defaultProps)for(e in r=o.defaultProps,r)n[e]===void 0&&(n[e]=r[e]);return{$$typeof:l,type:o,key:f,ref:s,props:n,_owner:y.current}}t.jsx=m;t.jsxs=m;i.exports=t;var d=i.exports;export{d as j};
