{"success": {"success_message": "Vellykket", "fuses_are_blown": "Sikringerne er blevet sprunget", "door_has_opened": "<PERSON><PERSON><PERSON>"}, "error": {"cancel_message": "<PERSON><PERSON><PERSON><PERSON>", "safe_too_strong": "Det ser ud til, at safelåsen er for stærk...", "missing_item": "Du mangler en genstand...", "bank_already_open": "Banken er allerede åben...", "minimum_police_required": "Mindst %s politi er påkrævet", "security_lock_active": "Sikkerhedslåsen er aktiv, åbning af døren er i øjeblikket ikke mulig", "wrong_type": "%s modtog ikke den rigtige type for argumentet '%s'\nmodtaget type: %s\nmodtaget værdi: %s\nforventet type: %s", "fuses_already_blown": "Sikringerne er allerede sprunget...", "event_trigger_wrong": "%s%s blev u<PERSON><PERSON><PERSON>, når nogle betingelser ikke blev opfyldt, kilde: %s", "missing_ignition_source": "Du mangler en tændingskilde"}, "general": {"breaking_open_safe": "<PERSON><PERSON>der sikkerhedsskabet op...", "connecting_hacking_device": "Forbinder hackingenheden...", "fleeca_robbery_alert": "Fleeca-bankrøveriforsøg", "paleto_robbery_alert": "Blaine County Savings-bankrøveriforsøg", "pacific_robbery_alert": "Pacific Standard Bank-røveriforsøg", "break_safe_open_option_target": "<PERSON><PERSON><PERSON> op", "break_safe_open_option_drawtext": "[E] <PERSON><PERSON><PERSON> op", "validating_bankcard": "Validerer kort...", "thermite_detonating_in_seconds": "Thermite går af om %s sekund(er)", "bank_robbery_police_call": "10-90: <PERSON><PERSON><PERSON><PERSON><PERSON>"}}