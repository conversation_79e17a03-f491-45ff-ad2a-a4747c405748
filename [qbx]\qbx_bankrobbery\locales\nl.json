{"success": {"success_message": "Succes", "fuses_are_blown": "De zekering zijn doorgebrand", "door_has_opened": "De deur is geopend"}, "error": {"cancel_message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "safe_too_strong": "Het <PERSON> van de <PERSON> is te sterk...", "missing_item": "Je mist een item...", "bank_already_open": "De bank is al geopend...", "minimum_police_required": "Minimaal %s politie nodig", "security_lock_active": "Het beveiligingsslot is actief, de deur kan momenteel niet geopend worden", "wrong_type": "%s ontving het verkeerde type voor argument '%s'\nontvangen type: %s\nontvangen value: %s\n verwacht type: %s", "fuses_already_blown": "De zekeringen zijn al doorgebrand...", "event_trigger_wrong": "%s%s was uitgevoerd terwijl een paar condities niet zijn vervuld, source: %s", "missing_ignition_source": "Je mist een ontstekingsbron"}, "general": {"breaking_open_safe": "Kluis open breken...", "connecting_hacking_device": "Hacking device aan het verbinden...", "fleeca_robbery_alert": "Fleeca bank overval poging", "paleto_robbery_alert": "Blaine County Savings bank overval poging", "pacific_robbery_alert": "Pacific Standard Bank overval poging", "break_safe_open_option_target": "<PERSON><PERSON>", "break_safe_open_option_drawtext": "[E] <PERSON><PERSON> kluis open", "validating_bankcard": "<PERSON><PERSON> valideren...", "thermite_detonating_in_seconds": "Thermite zal af gaan in %s seconde(s)", "bank_robbery_police_call": "Bank Overval"}}