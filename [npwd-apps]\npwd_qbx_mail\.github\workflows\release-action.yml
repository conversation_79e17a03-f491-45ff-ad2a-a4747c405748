name: "release-action"

on:
  push:
    tags:
      - "v*"

jobs:
    release-action:
        name: "Create Release"
        runs-on: "ubuntu-latest"
        steps:
            - name: Checkout Repository
              uses: actions/checkout@v4.1.1
              with:
                fetch-depth: 0
                ref: ${{ github.event.repository.default_branch }}

            - name: Install ZIP
              run: sudo apt install zip

            - name: Bundle files
              run: |
                rm -rf ./.github ./.vscode ./.git
                shopt -s extglob
                mkdir ./${{ github.event.repository.name }}
                cp -r !(${{ github.event.repository.name }}) ${{ github.event.repository.name }}
                zip -r ./${{ github.event.repository.name }}.zip ./${{ github.event.repository.name }}

            - name: Get App Token
              uses: actions/create-github-app-token@v1.6.1
              id: generate_token
              with:
                app-id: ${{ secrets.APP_ID }}
                private-key: ${{ secrets.PRIVATE_KEY }}

            - name: Create Release
              uses: 'marvinpinto/action-automatic-releases@latest'
              with:
                title: ${{ github.ref_name }}
                repo_token: '${{ steps.generate_token.outputs.token }}'
                prerelease: false
                files: ${{ github.event.repository.name }}.zip