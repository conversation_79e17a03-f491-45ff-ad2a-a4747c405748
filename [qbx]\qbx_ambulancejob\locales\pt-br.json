{"error": {"canceled": "Cancelado", "impossible": "Ação Impossível...", "no_player": "<PERSON><PERSON><PERSON>", "no_firstaid": "Você precisa de um Kit de Primeiros Socorros", "no_bandage": "Você precisa de uma Bandagem", "beds_taken": "Camas ocupadas...", "possessions_taken": "Todos os seus pertences foram levados...", "cant_help": "Você não pode ajudar essa pessoa...", "not_ems": "Você não é EMS ou não está em serviço"}, "success": {"revived": "Você reviveu uma pessoa", "healthy_player": "Jo<PERSON>r est<PERSON> saud<PERSON>", "helped_player": "<PERSON><PERSON><PERSON> ajudou a pessoa", "being_helped": "Você está sendo ajudado..."}, "info": {"civ_died": "Civil morreu", "civ_down": "Civil Abatido", "civ_call": "<PERSON><PERSON><PERSON>", "ems_down": "Doutor %s Abatido", "respawn_txt": "RESSURGIR EM: ~r~%s~s~ SEGUNDOS", "respawn_revive": "SEGURE [~r~E~s~] POR %s SEGUNDOS PARA RESSURGIR POR $~r~%s~s~", "bleed_out": "VOCÊ VAI SANGRAR ATÉ A MORTE EM: ~r~%s~s~ SEGUNDOS", "bleed_out_help": "VOCÊ VAI SANGRAR ATÉ A MORTE EM: ~r~%s~s~ SEGUNDOS, VOCÊ PODE SER AJUDADO", "request_help": "PRESSIONE [~r~G~s~] PARA PEDIR AJUDA", "help_requested": "O PESSOAL DO EMS FOI NOTIFICADO", "amb_plate": "AMBU", "heli_plate": "VIDA", "status": "Verificar Estado", "is_status": "Está %s", "healthy": "Você está completamente saudável novamente!", "safe": "Hospital Seguro", "ems_alert": "Alerta EMS - %s", "mr": "Sr.", "mrs": "<PERSON><PERSON>.", "dr_needed": "Um médico é necessário no Hospital Pillbox", "dr_alert": "Médico já foi notificado", "ems_report": "Relatório <PERSON>", "message_sent": "Mensagem a ser enviada", "check_health": "Verificar Saúde de um Jogador", "heal_player": "<PERSON><PERSON><PERSON> um Jogador", "revive_player": "Reviver um Jo<PERSON>r"}, "mail": {"sender": "Hospital Pillbox", "subject": "<PERSON>ustos <PERSON>", "message": "Caro %s %s, <br /><br />Você recebeu este e-mail com os custos da última visita ao hospital.<br />O custo final foi: <strong>$%s</strong><br /><br />Desejamos uma rápida recuperação!"}, "menu": {"amb_vehicles": "Veículos de Ambulância", "status": "Estado de Saúde"}, "text": {"pstash_button": "[E] - <PERSON><PERSON><PERSON><PERSON>", "pstash": "<PERSON><PERSON><PERSON><PERSON>", "onduty_button": "[E] - Entrar <PERSON> Servi<PERSON>", "offduty_button": "[E] - <PERSON><PERSON>", "duty": "Entrar/<PERSON><PERSON>", "armory_button": "[E] - Arsenal", "armory": "Arsenal", "veh_button": "[E] - <PERSON><PERSON>ar / Guardar Veículo", "elevator_roof": "[E] - <PERSON>egar o elevador para o telhado", "elevator_main": "[E] - <PERSON><PERSON><PERSON> o elevador para o andar principal", "el_roof": "Pegar o elevador para o telhado", "el_main": "Pegar o elevador para o andar principal", "call_doc": "[E] - <PERSON><PERSON>", "call": "<PERSON><PERSON>", "check_in": "[E] Fazer check-in", "check": "Check-In", "lie_bed": "[E] - <PERSON><PERSON><PERSON> na cama", "bed": "Deitar na cama", "put_bed": "Colocar cidadão na cama", "bed_out": "[E] - Levantar-se da cama..", "alert": "<PERSON><PERSON><PERSON>!"}, "progress": {"ifaks": "Pegando ifaks...", "bandage": "Usando Bandagem...", "painkillers": "Tomando Analgésicos...", "revive": "Revivendo P<PERSON>oa...", "healing": "Curando Ferimentos...", "checking_in": "Fazendo Check-In..."}}