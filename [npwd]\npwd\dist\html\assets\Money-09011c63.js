import{j as h}from"./jsx-runtime-5fe4d0a7.js";import{c,__tla as a}from"./__federation_expose_Input-51304708.js";let t,m=Promise.all([(()=>{try{return a}catch{}})()]).then(async()=>{t=c([h.jsx("path",{d:"M5 8h2v8H5zm7 0H9c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h3c.55 0 1-.45 1-1V9c0-.55-.45-1-1-1zm-1 6h-1v-4h1v4zm7-6h-3c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1h3c.55 0 1-.45 1-1V9c0-.55-.45-1-1-1zm-1 6h-1v-4h1v4z"},"0"),h.jsx("path",{d:"M2 4v16h20V4H2zm2 14V6h16v12H4z"},"1")],"Money")});export{t as M,m as __tla};
