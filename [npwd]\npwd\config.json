{"PhoneAsItem": {"enabled": true, "exportResource": "qbx_npwd", "exportFunction": "HasPhone"}, "customPhoneNumber": {"enabled": false, "exportResource": "number-generator-resource", "exportFunction": "generateNumber"}, "general": {"useResourceIntegration": true, "toggleKey": "m", "toggleCommand": "phone", "defaultLanguage": "en", "showId": false, "phoneNumberFormat": "/(\\d{3})(\\d{3})(\\d{4})/"}, "contacts": {"frameworkPay": false, "payResource": "my-core-resource", "payFunction": "myCheckerFunction"}, "database": {"useIdentifierPrefix": true, "playerTable": "players", "identifierColumn": "citizenid", "identifierType": "license", "profileQueries": true, "phoneNumberColumn": "phone_number"}, "images": {"url": "https://api.fivemanage.com/api/image", "type": "image", "imageEncoding": "webp", "contentType": "multipart/form-data", "useContentType": false, "useWebhook": false, "authorizationHeader": "Authorization", "authorizationPrefix": "", "useAuthorization": true, "returnedDataIndexes": ["url"]}, "imageSafety": {"filterUnsafeImageUrls": true, "embedUnsafeImages": false, "embedUrl": "https://i.example.com/embed", "safeImageUrls": ["i.imgur.com", "i.file.glass", "dropbox.com", "c.tenor.com", "discord.com", "cdn.discordapp.com", "media.discordapp.com", "media.discordapp.net", "upload.wikipedia.org", "i.projecterror.dev", "upcdn.io", "i.fivemanage.com", "api.fivemanage.com", "r2.fivemanage.com"]}, "profanityFilter": {"enabled": true, "badWords": ["esx", "qbus"]}, "twitter": {"showNotifications": true, "generateProfileNameFromUsers": true, "allowEditableProfileName": true, "allowDeleteTweets": true, "allowReportTweets": true, "allowRetweet": true, "characterLimit": 160, "newLineLimit": 10, "enableAvatars": true, "enableEmojis": true, "enableImages": true, "maxImages": 1, "resultsLimit": 25}, "match": {"generateProfileNameFromUsers": true, "allowEditableProfileName": true}, "marketplace": {"persistListings": false}, "browser": {"homepageUrl": "https://docs.fivemanage.com"}, "debug": {"level": "error", "enabled": true, "sentryEnabled": true}, "defaultContacts": [], "disabledApps": ["BROWSER"], "apps": ["npwd_qbx_mail", "npwd_qbx_garages"], "voiceMessage": {"enabled": true, "authorizationHeader": "Authorization", "url": "https://api.fivemanage.com/api/audio", "returnedDataIndexes": ["url"]}}