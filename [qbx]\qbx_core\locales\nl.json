{"error": {"not_online": "<PERSON><PERSON><PERSON> is op dit moment niet online", "wrong_format": "Incorrect format", "missing_args": "Not every argument has been entered (x, y, z)", "missing_args2": "All arguments must be filled out!", "no_access": "Je hebt geen toegang tot dit command", "company_too_poor": "Je werkgever is falliet", "item_not_exist": "Het opgegeven item bestaat niet, juist gespeld?", "too_heavy": "Inventaris zit te vol", "location_not_exist": "Deze locatie bestaat niet", "duplicate_license": "Duplicate Rockstar License Found", "no_valid_license": "No Valid Rockstar License Found", "not_whitelisted": "Je hebt geen whitelist voor de server", "server_already_open": "De server is reeds open", "server_already_closed": "De server is reeds gesloten", "no_permission": "Je hebt niet de juiste rechten hiervoor..", "no_waypoint": "GPS is niet ingesteld.", "tp_error": "Error tijdens het teleporteren.", "connecting_database_timeout": "Connection to database timed out. (Is the SQL server on?)", "connecting_error": "Er is een fout opgetreden tijdens het verbinden met de server. (Check de server console)", "no_match_character_registration": "Alles anders dan letters is niet toeges<PERSON>an, spaties aan het einde zijn ook niet toegestaan ​​en woorden moeten in invoervelden met een hoofdletter beginnen. Je kunt echter woorden toevoegen met spaties er<PERSON><PERSON>.", "already_in_queue": "You are already in queue.", "no_subqueue": "You were not let in any sub-queue."}, "success": {"server_opened": "De server is geopend!", "server_closed": "De server is gesloten!", "teleported_waypoint": "Geteleporteerd naar marker.", "character_deleted": "Karakter verwijderd!", "character_deleted_citizenid": "Je hebt je karakter succesvol verwij<PERSON>d met BSN %s."}, "info": {"received_paycheck": "Je hebt je loon ontvangen €%s", "job_info": "Baan: %s | Grade: %s | Dienst: %s", "gang_info": "Gang: %s | Grade: %s", "on_duty": "Je bent nu in dienst!", "off_duty": "Je bent nu uit dienst!", "checking_ban": "Hallo %s. We kijken even of je gebanned bent.", "join_server": "Welkom %s op %s.", "checking_whitelisted": "Hallo %s. We zijn je even aan het controleren.", "exploit_banned": "Je bent gebanned voor cheating. <PERSON><PERSON> op onze discord voor meer informatie: %s", "exploit_dropped": "<PERSON> <PERSON> gekickt voor exploiting", "multichar_title": "Qbox Multicharacter", "multichar_new_character": "Nieuw karakter #%s", "char_male": "Man", "char_female": "Vrouw", "play": "Spelen", "play_description": "Speel als %s", "delete_character": "Karakter verwijderen", "delete_character_description": "Verwijder %s", "logout_command_help": "Logt je uit van je huidige karakter", "check_id": "Bekijk je huidige server ID", "deletechar_command_help": "Ver<PERSON><PERSON>der een karakter van een speler", "deletechar_command_arg_player_id": "ID", "character_registration_title": "Karakter registeren", "first_name": "<PERSON><PERSON><PERSON><PERSON>", "last_name": "Achternaam", "nationality": "Nationaliteit", "gender": "Geslacht", "birth_date": "Geboortedatum", "select_gender": "Selecteer je geslacht...", "confirm_delete": "Weet je zeker dat je dit karakter wilt verwijderen? Er is hierna geen weg meer terug en je karakater zal voor altijd verdwijnen!", "in_queue": "🐌 You are %s/%s in queue. (%s) %s"}, "command": {"tp": {"help": "TP naar speler of coordinaten (Admin Only)", "params": {"x": {"name": "id/x", "help": "Speler ID of X positie"}, "y": {"name": "y", "help": "Y positie"}, "z": {"name": "z", "help": "Z positie"}}}, "tpm": {"help": "TP naar GPS marker (Admin Only)"}, "togglepvp": {"help": "Toggle PVP on the server (Admin Only)"}, "addpermission": {"help": "<PERSON><PERSON> speler permissies (God Only)", "params": {"id": {"name": "id", "help": "Speler ID"}, "permission": {"name": "permission", "help": "Permission level"}}}, "removepermission": {"help": "Permissies verwijderen iemand (God Only)", "params": {"id": {"name": "id", "help": "Speler ID"}, "permission": {"name": "permission", "help": "Permission level"}}}, "openserver": {"help": "Open de server voor iedereen (Admin Only)"}, "closeserver": {"help": "Sluit de server voor mensen zonder permissies (Admin Only)", "params": {"reason": {"name": "reason", "help": "Reden voor sluiten (optioneel)"}}}, "car": {"help": "Spawn voertuig (Admin Only)", "params": {"model": {"name": "model", "help": "<PERSON> van het voert<PERSON>g"}, "keepCurrentVehicle": {"name": "keepCurrentVehicle", "help": "Hou het huidige voertuig waar je in zit (laat leeg om huidige voertuig te laten verwijderen)"}}}, "dv": {"help": "<PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON> (Admin Only)", "params": {"radius": {"name": "radius", "help": "Radius om voertuigen in te verwijderen (meters)"}}}, "givemoney": {"help": "<PERSON><PERSON> geld aan een speler (Admin Only)", "params": {"id": {"name": "id", "help": "Speler ID"}, "moneytype": {"name": "moneytype", "help": "Type geld (contant, bank, crypto)"}, "amount": {"name": "amount", "help": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "setmoney": {"help": "<PERSON>er geld bij een speler (Admin Only)", "params": {"id": {"name": "id", "help": "Speler ID"}, "moneytype": {"name": "moneytype", "help": "Type geld (contant, bank, crypto)"}, "amount": {"name": "amount", "help": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}}, "job": {"help": "Bekijk je huidige baan"}, "setjob": {"help": "<PERSON><PERSON> een speler een baan (Admin Only)", "params": {"id": {"name": "id", "help": "Speler ID"}, "job": {"name": "job", "help": "<PERSON><PERSON>"}, "grade": {"name": "grade", "help": "Niveau"}}}, "changejob": {"help": "Change Active Job of Player (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}}}, "addjob": {"help": "Add Job to Player (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}, "grade": {"name": "grade", "help": "Job grade"}}}, "removejob": {"help": "Re<PERSON><PERSON> Job from Player (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}}}, "gang": {"help": "Bekijk je gang"}, "setgang": {"help": "Zet een speler in een bepaalde gang (Admin Only)", "params": {"id": {"name": "id", "help": "Speler ID"}, "gang": {"name": "gang", "help": "<PERSON> naam"}, "grade": {"name": "grade", "help": "Gang niveau"}}}, "ooc": {"help": "OOC chat bericht"}, "me": {"help": "Laat lokaal een bericht zien, aan spelers in de buurt", "params": {"message": {"name": "message", "help": "Be<PERSON>t om te versturen"}}}}}