import{j as a}from"./jsx-runtime-5fe4d0a7.js";import{c as r,__tla as e}from"./__federation_expose_Input-51304708.js";let l,o=Promise.all([(()=>{try{return e}catch{}})()]).then(async()=>{let t;t=r(a.jsx("path",{d:"m12 21.35-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"}),"Favorite"),l=()=>a.jsx(t,{fontSize:"small"})});export{o as __tla,l as default};
