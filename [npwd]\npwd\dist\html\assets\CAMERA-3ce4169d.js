import{j as t}from"./jsx-runtime-5fe4d0a7.js";import{c as l,__tla as e}from"./__federation_expose_Input-51304708.js";let c,r=Promise.all([(()=>{try{return e}catch{}})()]).then(async()=>{let a;a=l([t.jsx("circle",{cx:"12",cy:"12",r:"3.2"},"0"),t.jsx("path",{d:"M9 2 7.17 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3.17L15 2H9zm3 15c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5z"},"1")],"CameraAlt"),c=()=>t.jsx(a,{fontSize:"small"})});export{r as __tla,c as default};
