-- NDT Custom Spawn System
-- Forces all spawns to a fixed location: vec4(1147.42, -746.34, 57.2, 14.47)
-- Completely bypasses character selection and spawn selection menus

local NDT_SPAWN_POINT = {
    x = 1147.42,
    y = -746.34,
    z = 57.2,
    heading = 14.47
}

local spawnLock = false
local hasSpawned = false
local bypassCharacterSelection = true -- Enable auto character selection
local bypassSpawnSelection = true -- Enable auto spawn selection bypass

-- Utility function to teleport player to NDT spawn point
local function teleportToNDTSpawn()
    if spawnLock then return end
    spawnLock = true
    
    local ped = PlayerPedId()
    
    -- Set player position
    SetEntityCoordsNoOffset(ped, NDT_SPAWN_POINT.x, NDT_SPAWN_POINT.y, NDT_SPAWN_POINT.z, false, false, false, true)
    SetEntityHeading(ped, NDT_SPAWN_POINT.heading)
    
    -- Ensure player is alive and healthy
    if IsEntityDead(ped) then
        NetworkResurrectLocalPlayer(NDT_SPAWN_POINT.x, NDT_SPAWN_POINT.y, NDT_SPAWN_POINT.z, NDT_SPAWN_POINT.heading, true, true, false)
    end
    
    -- Wait for collision to load
    local timeout = GetGameTimer() + 5000
    while not HasCollisionLoadedAroundEntity(ped) and GetGameTimer() < timeout do
        Wait(0)
    end
    
    -- Basic cleanup
    ClearPedTasksImmediately(ped)
    SetEntityHealth(ped, 200)
    ClearPlayerWantedLevel(PlayerId())
    
    -- Fade in if screen is faded out
    if IsScreenFadedOut() then
        DoScreenFadeIn(500)
        while not IsScreenFadedIn() do
            Wait(0)
        end
    end
    
    spawnLock = false
    print("NDT Spawn: Player spawned at fixed location")
end

-- Create a default character automatically (without ox_lib)
local function createDefaultCharacter()
    print("NDT Spawn: Creating default character without ox_lib")
    Wait(2000)
    teleportToNDTSpawn()
    
    -- Trigger player loaded events
    TriggerServerEvent('QBCore:Server:OnPlayerLoaded')
    TriggerEvent('QBCore:Client:OnPlayerLoaded')
    TriggerServerEvent('qb-houses:server:SetInsideMeta', 0, false)
    TriggerServerEvent('qb-apartments:server:SetInsideMeta', 0, 0, false)
end

-- Auto-select first character and bypass character selection (without ox_lib)
local function autoSelectCharacter()
    if not bypassCharacterSelection then 
        teleportToNDTSpawn()
        return 
    end
    
    CreateThread(function()
        Wait(1000)
        
        -- Check if ox_lib is available
        if not lib then
            print("NDT Spawn: ox_lib not found, using direct spawn method")
            Wait(2000)
            teleportToNDTSpawn()
            
            -- Trigger player loaded events
            TriggerServerEvent('QBCore:Server:OnPlayerLoaded')
            TriggerEvent('QBCore:Client:OnPlayerLoaded')
            TriggerServerEvent('qb-houses:server:SetInsideMeta', 0, false)
            TriggerServerEvent('qb-apartments:server:SetInsideMeta', 0, 0, false)
            return
        end
        
        -- Try to get characters with ox_lib
        local success, characters = pcall(function()
            return lib.callback.await('qbx_core:server:getCharacters')
        end)
        
        if success and characters and #characters > 0 then
            -- Auto-select first character
            local firstCharacter = characters[1]
            print("NDT Spawn: Auto-selecting character: " .. firstCharacter.charinfo.firstname .. " " .. firstCharacter.charinfo.lastname)
            
            -- Load the character directly
            pcall(function()
                lib.callback.await('qbx_core:server:loadCharacter', false, firstCharacter.citizenid)
            end)
            
            Wait(1000)
            teleportToNDTSpawn()
            
            -- Trigger player loaded events
            TriggerServerEvent('QBCore:Server:OnPlayerLoaded')
            TriggerEvent('QBCore:Client:OnPlayerLoaded')
            TriggerServerEvent('qb-houses:server:SetInsideMeta', 0, false)
            TriggerServerEvent('qb-apartments:server:SetInsideMeta', 0, 0, false)
            
        else
            -- No characters exist, create one automatically or fallback
            print("NDT Spawn: No characters found, using direct spawn")
            createDefaultCharacter()
        end
    end)
end

-- Force spawn without any selection menus
local function forceDirectSpawn()
    CreateThread(function()
        Wait(1000)
        
        -- Disable all spawn selection UIs
        if bypassSpawnSelection then
            -- Close any open spawn UI
            TriggerEvent('qb-spawn:client:closeUI')
            TriggerEvent('apartments:client:closeUI')
            
            -- Disable spawn manager auto spawn
            if GetResourceState('spawnmanager') == 'started' then
                pcall(function()
                    exports.spawnmanager:setAutoSpawn(false)
                end)
            end
        end
        
        -- Auto-select character if enabled
        if bypassCharacterSelection then
            autoSelectCharacter()
        else
            -- Direct spawn without character selection
            Wait(2000)
            teleportToNDTSpawn()
        end
    end)
end

-- Override spawn manager behavior
CreateThread(function()
    -- Disable auto spawn from spawnmanager
    if GetResourceState('spawnmanager') == 'started' then
        pcall(function()
            exports.spawnmanager:setAutoSpawn(false)
        end)
    end
    
    -- Add our spawn point to spawnmanager if available
    if GetResourceState('spawnmanager') == 'started' then
        pcall(function()
            exports.spawnmanager:addSpawnPoint({
                x = NDT_SPAWN_POINT.x,
                y = NDT_SPAWN_POINT.y,
                z = NDT_SPAWN_POINT.z,
                heading = NDT_SPAWN_POINT.heading,
                model = 'player_zero'
            })
        end)
    end
end)

-- Handle initial player spawn and bypass ALL selection menus
CreateThread(function()
    while not NetworkIsPlayerActive(PlayerId()) do
        Wait(0)
    end
    
    Wait(2000) -- Give time for other resources to load
    
    if not hasSpawned then
        forceDirectSpawn()
        hasSpawned = true
    end
end)

-- Monitor for death and respawn
CreateThread(function()
    local isDead = false
    
    while true do
        Wait(100)
        
        local ped = PlayerPedId()
        
        if NetworkIsPlayerActive(PlayerId()) and ped and ped ~= -1 then
            if IsEntityDead(ped) or IsPedFatallyInjured(ped) then
                if not isDead then
                    isDead = true
                    -- Wait a bit before respawn
                    CreateThread(function()
                        Wait(2000)
                        teleportToNDTSpawn()
                        isDead = false
                    end)
                end
            else
                isDead = false
            end
        end
    end
end)

-- CONTINUOUS UI MONITOR - Force close any UI that appears
CreateThread(function()
    while true do
        Wait(50) -- Check every 50ms
        
        if bypassSpawnSelection then
            -- Force close any spawn-related UI
            TriggerEvent('qb-spawn:client:closeUI')
            TriggerEvent('apartments:client:closeUI')
            
            -- Disable any UI that might be trying to show
            if IsPauseMenuActive() then
                SetPauseMenuActive(false)
            end
            
            -- Force player to spawn if they're not spawned
            if not hasSpawned and NetworkIsPlayerActive(PlayerId()) then
                forceDirectSpawn()
                hasSpawned = true
            end
        end
    end
end)

-- STRONG UI BYPASS - Override ALL selection events

-- Override the character selection to auto-select
RegisterNetEvent('qbx_core:client:playerLoggedOut', function()
    if bypassCharacterSelection then
        CreateThread(function()
            Wait(100)
            autoSelectCharacter()
        end)
    end
end)

-- Override ALL spawn selection events with STRONG bypass
RegisterNetEvent('qb-spawn:client:setupSpawns', function()
    if bypassSpawnSelection then
        print("NDT Spawn: STRONG bypass - spawn selection UI")
        Wait(100)
        teleportToNDTSpawn()
    end
end)

RegisterNetEvent('qb-spawn:client:openUI', function()
    if bypassSpawnSelection then
        print("NDT Spawn: STRONG bypass - spawn UI open")
        Wait(100)
        teleportToNDTSpawn()
    end
end)

RegisterNetEvent('apartments:client:setupSpawnUI', function()
    if bypassSpawnSelection then
        print("NDT Spawn: STRONG bypass - apartment spawn UI")
        Wait(100)
        teleportToNDTSpawn()
    end
end)

RegisterNetEvent('apartments:client:openUI', function()
    if bypassSpawnSelection then
        print("NDT Spawn: STRONG bypass - apartment UI open")
        Wait(100)
        teleportToNDTSpawn()
    end
end)

RegisterNetEvent('qbx_core:client:spawnNoApartments', function()
    Wait(100)
    teleportToNDTSpawn()
end)

-- Override character selection events
RegisterNetEvent('qbx_core:client:chooseCharacter', function()
    if bypassCharacterSelection then
        print("NDT Spawn: STRONG bypass - character selection")
        Wait(100)
        autoSelectCharacter()
    end
end)

RegisterNetEvent('qbx_core:client:createCharacter', function()
    if bypassCharacterSelection then
        print("NDT Spawn: STRONG bypass - character creation")
        Wait(100)
        createDefaultCharacter()
    end
end)

-- Override any other spawn-related events
RegisterNetEvent('spawnmanager:spawnPlayer', function()
    print("NDT Spawn: STRONG bypass - spawnmanager spawn")
    Wait(100)
    teleportToNDTSpawn()
end)

RegisterNetEvent('spawnmanager:forceRespawn', function()
    print("NDT Spawn: STRONG bypass - force respawn")
    Wait(100)
    teleportToNDTSpawn()
end)

-- Handle QBX/QBCore spawn events
RegisterNetEvent('QBCore:Client:OnPlayerLoaded', function()
    Wait(1000)
    teleportToNDTSpawn()
end)

-- Handle basic spawn events
RegisterNetEvent('playerSpawned', function()
    Wait(500)
    teleportToNDTSpawn()
end)

-- Handle ESX spawn events if available
RegisterNetEvent('esx:onPlayerSpawn', function()
    Wait(500)
    teleportToNDTSpawn()
end)

-- Handle base events
RegisterNetEvent('baseevents:onPlayerDied', function()
    CreateThread(function()
        Wait(2000)
        teleportToNDTSpawn()
    end)
end)

-- Ensure spawn on resource start
AddEventHandler('onResourceStart', function(resourceName)
    if GetCurrentResourceName() == resourceName then
        Wait(2000)
        if NetworkIsPlayerActive(PlayerId()) then
            teleportToNDTSpawn()
        end
    end
end)

-- Fallback spawn command for admins
RegisterCommand('ndtspawn', function()
    teleportToNDTSpawn()
end, false)

-- Command to toggle character selection bypass
RegisterCommand('ndttoggle', function()
    bypassCharacterSelection = not bypassCharacterSelection
    print("NDT Spawn: Character selection bypass " .. (bypassCharacterSelection and "enabled" or "disabled"))
end, false)

-- Command to toggle spawn selection bypass
RegisterCommand('ndtspawntoggle', function()
    bypassSpawnSelection = not bypassSpawnSelection
    print("NDT Spawn: Spawn selection bypass " .. (bypassSpawnSelection and "enabled" or "disabled"))
end, false)

-- Export function for other resources
exports('forceNDTSpawn', teleportToNDTSpawn)
exports('toggleCharacterSelection', function(state)
    bypassCharacterSelection = state
end)
exports('toggleSpawnSelection', function(state)
    bypassSpawnSelection = state
end)

print("NDT Spawn System loaded - Fixed spawn point: " .. NDT_SPAWN_POINT.x .. ", " .. NDT_SPAWN_POINT.y .. ", " .. NDT_SPAWN_POINT.z)
print("NDT Spawn: Character selection bypass " .. (bypassCharacterSelection and "enabled" or "disabled"))
print("NDT Spawn: Spawn selection bypass " .. (bypassSpawnSelection and "enabled" or "disabled")) 