{"error": {"not_online": "Der Spieler ist nicht online", "wrong_format": "Falsches Format", "missing_args": "Nicht alle Argumente wurden ausgefüllt (x, y, z)", "missing_args2": "Alle Argumente müssen ausgefüllt sein!", "no_access": "<PERSON><PERSON> auf diesen Befehl", "company_too_poor": "Dein Arbeitgeber hat kein Geld mehr", "item_not_exist": "Das Item existiert nicht", "too_heavy": "<PERSON><PERSON><PERSON> zu voll", "location_not_exist": "Der Ort existiert nicht", "duplicate_license": "Doppelte Rockstar-Lizenz gefunden", "no_valid_license": "Keine verifizierte Rockstar-Lizenz gefunden", "not_whitelisted": "Du bist nicht gew<PERSON>elisted", "server_already_open": "Der Server ist schon geöffnet", "server_already_closed": "Der Server ist schon geschlossen", "no_permission": "Du hast keine Rechte da<PERSON>ür..", "no_waypoint": "<PERSON><PERSON>t gesetzt.", "tp_error": "Fehler beim Teleportieren.", "connecting_database_timeout": "Die Verbindung zur Datenbank nicht möglich. (Ist der SQL-Server eingeschaltet?)", "connecting_error": "Beim Herstellen der Verbindung zum Server ist ein Fehler aufgetreten. (Überprüfe die Serverkonsole)", "no_match_character_registration": "In den Eingabefeldern sind nur Buchstaben zulässig. Leerzeichen am Ende sind nicht zulässig und Wörter müssen mit einem Großbuchstaben beginnen. Du kannst jedoch Wörter mit Leerzeichen dazwischen hinzufügen.", "already_in_queue": "You are already in queue.", "no_subqueue": "You were not let in any sub-queue."}, "success": {"server_opened": "Der Server wurde geöffnet", "server_closed": "Der Server wurde geschlossen", "teleported_waypoint": "Zum Wegpunkt teleportiert.", "character_deleted": "Charakter gelöscht!", "character_deleted_citizenid": "Du hast den Charakter mit der Spieler-ID %s erfolgreich gelöscht."}, "info": {"received_paycheck": "Du hast dein Gehalt in Höhe von $%s erhalten", "job_info": "Beruf: %s | Dienstgrad: %s | im Dienst: %s", "gang_info": "Gang: %s | Rang: %s", "on_duty": "Du befindest dich nun im <PERSON>!", "off_duty": "Du befindest dich nun nicht mehr im <PERSON>!", "checking_ban": "Hallo %s. <PERSON> pr<PERSON>, ob du gebannt wurdest.", "join_server": "Willkommen %s bei %s.", "checking_whitelisted": "Hallo %s. Wir prüfen deine Erlaubnis.", "exploit_banned": "<PERSON> wurdest fürs Cheaten gebannt. Meld dich auf dem Discord: %s", "exploit_dropped": "<PERSON> wurdest gek<PERSON>t, für das Ausnutzen von Exploits", "multichar_title": "Qbox Multichar", "multichar_new_character": "Neuen Charakter #%s", "char_male": "<PERSON><PERSON><PERSON><PERSON>", "char_female": "<PERSON><PERSON><PERSON>", "play": "Aufwachen", "play_description": "Aufwachen als %s", "delete_character": "Charakter löschen", "delete_character_description": "Lösche %s", "logout_command_help": "Loggt dich aus deinem aktuellen Charakter aus.", "deletechar_command_help": "Löscht den Charakter eines Spielers.", "deletechar_command_arg_player_id": "<PERSON><PERSON><PERSON>-<PERSON>", "character_registration_title": "Charakterregistrierung", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Nachname", "nationality": "Nationalität", "gender": "Geschlecht", "birth_date": "Geburtsdatum", "select_gender": "Wähle dein Geschlecht", "confirm_delete": "Are you sure you wish to delete this character?", "in_queue": "🐌 You are %s/%s in queue. (%s) %s"}, "command": {"tp": {"help": "TP zu Spieler oder Coords (Nur Admins)", "params": {"x": {"name": "id/x", "help": "ID vom Spieler oder X position"}, "y": {"name": "y", "help": "Y position"}, "z": {"name": "z", "help": "Z position"}}}, "tpm": {"help": "TP zum <PERSON> (Nur Admins)"}, "togglepvp": {"help": "Schalte PVP ein oder aus (Nur Admins)"}, "addpermission": {"help": "<PERSON><PERSON><PERSON> einem Spieler Rechte (Nur God)", "params": {"id": {"name": "id", "help": "ID des Spielers"}, "permission": {"name": "permission", "help": "Zugriffsrechte"}}}, "removepermission": {"help": "<PERSON><PERSON> jemand die Rechte (Nur God)", "params": {"id": {"name": "id", "help": "ID des Spielers"}, "permission": {"name": "permission", "help": "Zugriffsrechte"}}}, "openserver": {"help": "<PERSON><PERSON><PERSON> den Server für jeden (Nur Admins)"}, "closeserver": {"help": "Schl<PERSON>ße den Server für Leute ohne Rechte (Nur Admins)", "params": {"reason": {"name": "reason", "help": "<PERSON><PERSON><PERSON> fürs schließen (optional)"}}}, "car": {"help": "Spawne ein Fahrzeug (Nur Admins)", "params": {"model": {"name": "model", "help": "Modell Name"}}}, "dv": {"help": "<PERSON>ah<PERSON><PERSON><PERSON> en<PERSON>nen (Nur Admins)"}, "givemoney": {"help": "<PERSON><PERSON> <PERSON><PERSON> (Nur Admins)", "params": {"id": {"name": "id", "help": "Spieler ID"}, "moneytype": {"name": "moneytype", "help": "Geldtyp (Bargeld, Bank, Crypto)"}, "amount": {"name": "amount", "help": "Geldmenge"}}}, "setmoney": {"help": "<PERSON>ze die Geldmenge für einen Spieler (Nur Admins)", "params": {"id": {"name": "id", "help": "Spieler ID"}, "moneytype": {"name": "moneytype", "help": "Geldtyp (Bargeld, Bank, Crypto)"}, "amount": {"name": "amount", "help": "Geldmenge"}}}, "job": {"help": "<PERSON> deinen <PERSON>"}, "setjob": {"help": "<PERSON><PERSON> den Job eines Spielers (Nur Admins)", "params": {"id": {"name": "id", "help": "Spieler ID"}, "job": {"name": "job", "help": "Job Name"}, "grade": {"name": "grade", "help": "Dienstgrad"}}}, "changejob": {"help": "Change Active Job of Player (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}}}, "addjob": {"help": "Add Job to Player (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}, "grade": {"name": "grade", "help": "Job grade"}}}, "removejob": {"help": "Re<PERSON><PERSON> Job from Player (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}}}, "gang": {"help": "Check deine Gang"}, "setgang": {"help": "Setze die Gang eines Spielers (Nur Admins)", "params": {"id": {"name": "id", "help": "Spieler ID"}, "gang": {"name": "gang", "help": "Gang Name"}, "grade": {"name": "grade", "help": "<PERSON>"}}}, "ooc": {"help": "OOC Chat Nachricht"}, "me": {"help": "Locale Chat Nachricht", "params": {"message": {"name": "message", "help": "<PERSON><PERSON><PERSON><PERSON> zu senden"}}}}}