{"error": {"not_online": "<PERSON><PERSON><PERSON> nie jest online", "wrong_format": "Nieprawidłowy format", "missing_args": "Nie wszystkie argumenty zostały podane (x, y, z)", "missing_args2": "Wszystkie argumenty muszą być wypełnione!", "no_access": "Brak dostępu do tej komendy", "company_too_poor": "Twój pracodawca jest bez środków", "item_not_exist": "Przedmiot nie istnieje", "too_heavy": "Ekwipunek jest zbyt pełny", "location_not_exist": "Lokalizacja nie istnieje", "duplicate_license": "Znaleziono zduplikowaną licencję Rockstar", "no_valid_license": "Nie znaleziono ważnej licencji Rockstar", "not_whitelisted": "Nie jesteś na liście dostępu do tego serwera", "server_already_open": "<PERSON><PERSON> jest już ot<PERSON>y", "server_already_closed": "Ser<PERSON> jest już zamk<PERSON>", "no_permission": "Nie masz uprawnień do tego...", "no_waypoint": "<PERSON><PERSON> usta<PERSON><PERSON> do<PERSON>.", "tp_error": "Błąd podczas teleportacji.", "connecting_database_timeout": "Przekroczono czas połączenia z bazą danych. (<PERSON><PERSON> serwer SQL jest włączony?)", "connecting_error": "Wystąpił błąd podczas łączenia z serwerem. (Sprawdź konsolę serwera)", "no_match_character_registration": "Dozwolone są tylko litery, spacje między słowami oraz słowa zaczynające się od wielkiej litery. Nie można używać znaków specjalnych ani białych znaków na końcu.", "already_in_queue": "Jesteś już w kolejce.", "no_subqueue": "Nie uzyskano dostępu do żadnej podkolejki."}, "success": {"server_opened": "<PERSON><PERSON> z<PERSON> o<PERSON>", "server_closed": "<PERSON>wer został zamknię<PERSON>", "teleported_waypoint": "Teleportowano do punktu docelowego.", "character_deleted": "Postać usunięta!", "character_deleted_citizenid": "Pomyślnie usunięto postać z Citizen ID %s."}, "info": {"received_paycheck": "Otrzymano wypłatę w wysokości $%s", "job_info": "Praca: %s | Stopień: %s | Służba: %s", "gang_info": "Gang: %s | Stopień: %s", "on_duty": "Jesteś teraz na służbie!", "off_duty": "Jesteś teraz poza służbą!", "checking_ban": "Witaj %s<PERSON>, czy j<PERSON>ś zbanowany.", "join_server": "Witaj %s na %s.", "checking_whitelisted": "Witaj %s. Sprawdzamy twoje uprawnienia.", "exploit_banned": "Zbanowano za oszustwo. Sprawdź nasz Discord, aby uzy<PERSON>ć więcej informacji: %s", "exploit_dropped": "Wyrzucono za wykorzystywanie błędów", "multichar_title": "Qbox Multichar", "multichar_new_character": "Nowa postać #%s", "char_male": "Mężczyzna", "char_female": "<PERSON><PERSON><PERSON>", "play": "<PERSON><PERSON>", "play_description": "Graj jako %s", "delete_character": "<PERSON><PERSON><PERSON>", "delete_character_description": "Usuń %s", "logout_command_help": "Wylogowuje cię z bieżącej postaci", "check_id": "Sprawdź swoje ID serwera", "deletechar_command_help": "<PERSON><PERSON><PERSON> postać gracza", "deletechar_command_arg_player_id": "ID gracza", "character_registration_title": "Rejestracja postaci", "first_name": "<PERSON><PERSON><PERSON>", "last_name": "Nazwisko", "nationality": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gender": "<PERSON><PERSON><PERSON><PERSON>", "birth_date": "Data urodzenia", "select_gender": "Wybierz płeć...", "confirm_delete": "<PERSON>zy na pewno chcesz usunąć tę postać?", "in_queue": "🐌 Jesteś %s/%s w kolejce. (%s) %s"}, "command": {"tp": {"help": "Teleportuj do gracza lub wsp<PERSON>łrzędnych (Tylko admin)", "params": {"x": {"name": "id/x", "help": "ID gracza lub pozycja X"}, "y": {"name": "y", "help": "Pozycja Y"}, "z": {"name": "z", "help": "Pozycja Z"}}}, "tpm": {"help": "Teleportuj do znacznika (Tylko admin)"}, "togglepvp": {"help": "Przełącz PVP na serwerze (Tylko admin)"}, "addpermission": {"help": "Nadaj <PERSON> (Tylko bogowie)", "params": {"id": {"name": "id", "help": "ID gracza"}, "permission": {"name": "permission", "help": "Poziom uprawnień"}}}, "removepermission": {"help": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>zowi uprawnienia (Tylko bogowie)", "params": {"id": {"name": "id", "help": "ID gracza"}, "permission": {"name": "permission", "help": "Poziom uprawnień"}}}, "openserver": {"help": "O<PERSON><PERSON><PERSON><PERSON> serwer dla w<PERSON> (Tylko admin)"}, "closeserver": {"help": "Zamk<PERSON>j serwer dla os<PERSON><PERSON> bez <PERSON>ń (Tylko admin)", "params": {"reason": {"name": "reason", "help": "Powó<PERSON> (opcjonalnie)"}}}, "car": {"help": "St<PERSON><PERSON><PERSON> pojaz<PERSON> (Tylko admin)", "params": {"model": {"name": "model", "help": "Nazwa modelu pojazdu"}, "keepCurrentVehicle": {"name": "keepCurrentVehicle", "help": "Zachowaj obecny pojazd (pozostaw puste, aby usun<PERSON> obecny pojazd)"}}}, "dv": {"help": "<PERSON><PERSON><PERSON> pojazd (Tylko admin)", "params": {"radius": {"name": "radius", "help": "Promień usuwania pojazdów (w metrach)"}}}, "givemoney": {"help": "<PERSON><PERSON> (Tylko admin)", "params": {"id": {"name": "id", "help": "ID gracza"}, "moneytype": {"name": "moneytype", "help": "Typ <PERSON>nię<PERSON>zy (gotówka, bank, krypto)"}, "amount": {"name": "amount", "help": "Kwota <PERSON>nię<PERSON>zy"}}}, "setmoney": {"help": "Ustaw kwotę pienię<PERSON> grac<PERSON> (Tylko admin)", "params": {"id": {"name": "id", "help": "ID gracza"}, "moneytype": {"name": "moneytype", "help": "Typ <PERSON>nię<PERSON>zy (gotówka, bank, krypto)"}, "amount": {"name": "amount", "help": "Kwota <PERSON>nię<PERSON>zy"}}}, "job": {"help": "Sprawdź swoją pracę"}, "setjob": {"help": "Ustaw pracę gracza (Tylko admin)", "params": {"id": {"name": "id", "help": "ID gracza"}, "job": {"name": "job", "help": "Nazwa pracy"}, "grade": {"name": "grade", "help": "Stopie<PERSON> pracy"}}}, "changejob": {"help": "Zmień aktywną pracę gracza (Tylko admin)", "params": {"id": {"name": "id", "help": "ID gracza"}, "job": {"name": "job", "help": "Nazwa pracy"}}}, "addjob": {"help": "Dodaj <PERSON> (Tylko admin)", "params": {"id": {"name": "id", "help": "ID gracza"}, "job": {"name": "job", "help": "Nazwa pracy"}, "grade": {"name": "grade", "help": "Stopie<PERSON> pracy"}}}, "removejob": {"help": "Usuń pracę g<PERSON> (Tylko admin)", "params": {"id": {"name": "id", "help": "ID gracza"}, "job": {"name": "job", "help": "Nazwa pracy"}}}, "gang": {"help": "Sprawdź swój gang"}, "setgang": {"help": "Ustaw gang gracza (Tylko admin)", "params": {"id": {"name": "id", "help": "ID gracza"}, "gang": {"name": "gang", "help": "<PERSON><PERSON><PERSON> gangu"}, "grade": {"name": "grade", "help": "<PERSON><PERSON><PERSON> gangu"}}}, "ooc": {"help": "Wiadomość na czacie OOC"}, "me": {"help": "Wyświetl lokalną wiadom<PERSON>", "params": {"message": {"name": "message", "help": "Wiadomość do wysłania"}}}}}