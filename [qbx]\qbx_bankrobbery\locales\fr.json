{"success": {"success_message": "Su<PERSON>ès", "fuses_are_blown": "Les fusibles ont sauté", "door_has_opened": "La porte a été ouverte"}, "error": {"cancel_message": "<PERSON><PERSON><PERSON>", "safe_too_strong": "Il semble que le verrou de la banque soit trop fort...", "missing_item": "Il vous manque un objet...", "bank_already_open": "La banque est déjà ouverte...", "minimum_police_required": "Il faut au moins %s policiers pour ouvrir la porte", "security_lock_active": "Le verrou de sécurité est actif, ouverture de la porte impossible", "wrong_type": "%s n'a pas reçu le bon type pour l'argument '%s'\nreçu type: %s\nreçu valeur: %s\n attendu type: %s", "fuses_already_blown": "Les fusibles sont déjà sautés...", "event_trigger_wrong": "%s%s a été déclenché quand certaines conditions n'ont pas été remplies, source: %s", "missing_ignition_source": "Il vous manque une source d'allumage"}, "general": {"breaking_open_safe": "Ouvre le coffre...", "connecting_hacking_device": "Connecte le matériel de hack...", "fleeca_robbery_alert": "Tentative de braquage de la Fleeca Bank", "paleto_robbery_alert": "Tentative de braquage de la Blaine County Savings Bank", "pacific_robbery_alert": "Tentative de braquage de la Pacific Standard", "break_safe_open_option_target": "<PERSON><PERSON><PERSON><PERSON><PERSON> le coffre", "break_safe_open_option_drawtext": "[E] <PERSON><PERSON><PERSON><PERSON><PERSON> le coffre", "validating_bankcard": "Validation de la carte...", "thermite_detonating_in_seconds": "Thermite va exploser dans %s seconde(s)", "bank_robbery_police_call": "10-90: Braquage de banque"}}