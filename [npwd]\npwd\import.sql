# comment this out if your server has a different table/location for phone_number
# or if it already exists in the users table
# ALTER TABLE `users` ADD COLUMN `phone_number` VARCHAR(20) DEFAULT NULL;

# if you already have the npwd_message table without `is_embed` and `embed`, run this query in your sql console
# ALTER TABLE npwd_messages ADD COLUMN `is_embed` tinyint(4) NOT NULL DEFAULT 0;
# ALTER TABLE npwd_messages ADD COLUMN `embed` varchar(512) NOT NULL DEFAULT '';

# ALTER TABLE npwd_calls ADD COLUMN `isAnonymous` tinyint(4) NOT NULL DEFAULT 0;

#match voice messages update
# ALTER TABLE npwd_match_profiles ADD COLUMN `voiceMessage` varchar(512) DEFAULT NULL;

CREATE TABLE IF NOT EXISTS `npwd_twitter_profiles`
(
    `id`           int         NOT NULL AUTO_INCREMENT,
    `profile_name` varchar(90) NOT NULL,
    `identifier`   varchar(48) NOT NULL COLLATE 'utf8mb4_general_ci',
#   Default Profile avatar can be set here
    `avatar_url`   varchar(255)         DEFAULT 'https://i.fivemanage.com/images/3ClWwmpwkFhL.png',
    `createdAt`    timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedAt`    timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `profile_name_UNIQUE` (`profile_name`),
    INDEX `identifier` (`identifier`)
);

CREATE TABLE IF NOT EXISTS `npwd_phone_contacts`
(
    `id`         int(11)      NOT NULL AUTO_INCREMENT,
    `identifier` varchar(48)           DEFAULT NULL COLLATE 'utf8mb4_general_ci',
    `avatar`     varchar(255)          DEFAULT NULL,
    `number`     varchar(20)           DEFAULT NULL,
    `display`    varchar(255) NOT NULL DEFAULT '',
    PRIMARY KEY (`id`),
    INDEX `identifier` (`identifier`)
);

CREATE TABLE `npwd_twitter_tweets`
(
    `id`         INT(11)       NOT NULL AUTO_INCREMENT,
    `message`    VARCHAR(1000) NOT NULL COLLATE 'utf8mb4_general_ci',
    `createdAt`  TIMESTAMP     NOT NULL DEFAULT current_timestamp(),
    `updatedAt`  TIMESTAMP     NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
    `likes`      INT(11)       NOT NULL DEFAULT '0',
    `identifier` VARCHAR(48)   NOT NULL COLLATE 'utf8mb4_general_ci',
    `visible`    TINYINT(4)    NOT NULL DEFAULT '1',
    `images`     VARCHAR(1000) NULL     DEFAULT '' COLLATE 'utf8mb4_general_ci',
    `retweet`    INT(11)       NULL     DEFAULT NULL,
    `profile_id` INT(11)       NOT NULL,
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `npwd_twitter_tweets_npwd_twitter_profiles_id_fk` (`profile_id`) USING BTREE,
    CONSTRAINT `npwd_twitter_tweets_npwd_twitter_profiles_id_fk` FOREIGN KEY (`profile_id`) REFERENCES `npwd_twitter_profiles` (`id`) ON UPDATE RESTRICT ON DELETE RESTRICT
);

CREATE TABLE IF NOT EXISTS `npwd_twitter_likes`
(
    `id`         int NOT NULL AUTO_INCREMENT,
    `profile_id` int NOT NULL,
    `tweet_id`   int NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_combination` (`profile_id`, `tweet_id`),
    KEY `profile_idx` (`profile_id`),
    KEY `tweet_idx` (`tweet_id`),
    CONSTRAINT `profile` FOREIGN KEY (`profile_id`) REFERENCES `npwd_twitter_profiles` (`id`),
    CONSTRAINT `tweet` FOREIGN KEY (`tweet_id`) REFERENCES `npwd_twitter_tweets` (`id`) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS `npwd_match_profiles`
(
    `id`            int          NOT NULL AUTO_INCREMENT,
    `identifier`    varchar(48)  NOT NULL COLLATE 'utf8mb4_general_ci',
    `name`          varchar(90)  NOT NULL,
    `image`         varchar(255) NOT NULL,
    `bio`           varchar(512)          DEFAULT NULL,
    `location`      varchar(45)           DEFAULT NULL,
    `job`           varchar(45)           DEFAULT NULL,
    `tags`          varchar(255) NOT NULL DEFAULT '',
    `voiceMessage`  varchar(512)         DEFAULT NULL,
    `createdAt`     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedAt`     timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `identifier_UNIQUE` (`identifier`)
);

CREATE TABLE IF NOT EXISTS `npwd_match_views`
(
    `id`         int         NOT NULL AUTO_INCREMENT,
    `identifier` varchar(48) NOT NULL COLLATE 'utf8mb4_general_ci',
    `profile`    int         NOT NULL,
    `liked`      tinyint              DEFAULT '0',
    `createdAt`  timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedAt`  timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `match_profile_idx` (`profile`),
    CONSTRAINT `match_profile` FOREIGN KEY (`profile`) REFERENCES `npwd_match_profiles` (`id`),
    INDEX `identifier` (`identifier`)
);

CREATE TABLE IF NOT EXISTS `npwd_notes`
(
    `id`         int(11)      NOT NULL AUTO_INCREMENT,
    `identifier` varchar(48)  NOT NULL COLLATE 'utf8mb4_general_ci',
    `title`      varchar(255) NOT NULL,
    `content`    varchar(255) NOT NULL,
    PRIMARY KEY (id),
    INDEX `identifier` (`identifier`)
);

CREATE TABLE IF NOT EXISTS `npwd_marketplace_listings`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `identifier`  varchar(48)           DEFAULT NULL COLLATE 'utf8mb4_general_ci',
    `username`    varchar(255)          DEFAULT NULL,
    `name`        varchar(50)           DEFAULT NULL,
    `number`      varchar(255) NOT NULL,
    `title`       varchar(255)          DEFAULT NULL,
    `url`         varchar(255)          DEFAULT NULL,
    `description` varchar(255) NOT NULL,
    `createdAt`   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedAt`   timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `reported`    tinyint      NOT NULL DEFAULT 0,
    PRIMARY KEY (id),
    INDEX `identifier` (`identifier`)
);

CREATE TABLE IF NOT EXISTS `npwd_twitter_reports`
(
    `id`         int NOT NULL AUTO_INCREMENT,
    `profile_id` int NOT NULL,
    `tweet_id`   int NOT NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_combination` (`profile_id`, `tweet_id`),
    KEY `profile_idx` (`profile_id`),
    KEY `tweet_idx` (`tweet_id`),
    CONSTRAINT `report_profile` FOREIGN KEY (`profile_id`) REFERENCES `npwd_twitter_profiles` (`id`),
    CONSTRAINT `report_tweet` FOREIGN KEY (`tweet_id`) REFERENCES `npwd_twitter_tweets` (`id`) ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS `npwd_messages`
(
    `id`              int(11)      NOT NULL AUTO_INCREMENT,
    `message`         varchar(512) NOT NULL COLLATE 'utf8mb4_general_ci',
    `user_identifier` varchar(48)  NOT NULL COLLATE 'utf8mb4_general_ci',
    `conversation_id` varchar(512) NOT NULL,
    `isRead`          tinyint(4)   NOT NULL DEFAULT 0,
    `createdAt`       timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP(),
    `updatedAt`       timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP,
    `visible`         tinyint(4)   NOT NULL DEFAULT 1,
    `author`          varchar(255) NOT NULL,
    `is_embed`        tinyint(4)   NOT NULL default 0,
    `embed`           varchar(512) NOT NULL DEFAULT '',
    PRIMARY KEY (id),
    INDEX `user_identifier` (`user_identifier`)
);

CREATE TABLE `npwd_messages_conversations`
(
    `id`                INT(11)      NOT NULL AUTO_INCREMENT,
    `conversation_list` VARCHAR(225) NOT NULL COLLATE 'utf8mb4_general_ci',
    `label`             VARCHAR(60)  NULL     DEFAULT '' COLLATE 'utf8mb4_general_ci',
    `createdAt`         TIMESTAMP    NOT NULL DEFAULT current_timestamp(),
    `updatedAt`         TIMESTAMP    NOT NULL DEFAULT current_timestamp(),
    `last_message_id`   INT(11)      NULL     DEFAULT NULL,
    `is_group_chat`     TINYINT(4)   NOT NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE
);

CREATE TABLE `npwd_messages_participants`
(
    `id`              INT(11)      NOT NULL AUTO_INCREMENT,
    `conversation_id` INT(11)      NOT NULL,
    `participant`     VARCHAR(225) NOT NULL COLLATE 'utf8mb4_general_ci',
    `unread_count`    INT(11)      NULL DEFAULT '0',
    PRIMARY KEY (`id`) USING BTREE,
    INDEX `message_participants_npwd_messages_conversations_id_fk` (`conversation_id`) USING BTREE,
    CONSTRAINT `message_participants_npwd_messages_conversations_id_fk` FOREIGN KEY (`conversation_id`) REFERENCES `npwd_messages_conversations` (`id`) ON UPDATE RESTRICT ON DELETE RESTRICT
);

CREATE TABLE IF NOT EXISTS `npwd_calls`
(
    `id`          int(11)      NOT NULL AUTO_INCREMENT,
    `identifier`  varchar(48)  DEFAULT NULL COLLATE 'utf8mb4_general_ci',
    `transmitter` varchar(255) NOT NULL,
    `receiver`    varchar(255) NOT NULL,
    `is_accepted` tinyint(4)   DEFAULT 0,
    `isAnonymous` tinyint(4)   NOT NULL DEFAULT 0,
    `start`       varchar(255) DEFAULT NULL,
    end           varchar(255) DEFAULT NULL,
    PRIMARY KEY (id),
    INDEX `identifier` (`identifier`)
);

CREATE TABLE IF NOT EXISTS `npwd_phone_gallery`
(
    `id`         int(11)      NOT NULL AUTO_INCREMENT,
    `identifier` varchar(48) DEFAULT NULL COLLATE 'utf8mb4_general_ci',
    `image`      varchar(255) NOT NULL,
    PRIMARY KEY (id),
    INDEX `identifier` (`identifier`)
);

CREATE TABLE `npwd_darkchat_channels` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`channel_identifier` VARCHAR(191) NOT NULL COLLATE 'utf8mb4_general_ci',
	`label` VARCHAR(255) NULL DEFAULT '' COLLATE 'utf8mb4_general_ci',
	PRIMARY KEY (`id`) USING BTREE,
	UNIQUE INDEX `darkchat_channels_channel_identifier_uindex` (`channel_identifier`) USING BTREE
)
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
AUTO_INCREMENT=20
;

CREATE TABLE `npwd_darkchat_channel_members` (
	`channel_id` INT(11) NOT NULL,
	`user_identifier` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_general_ci',
	`is_owner` TINYINT(4) NOT NULL DEFAULT '0',
	INDEX `npwd_darkchat_channel_members_npwd_darkchat_channels_id_fk` (`channel_id`) USING BTREE,
	CONSTRAINT `npwd_darkchat_channel_members_npwd_darkchat_channels_id_fk` FOREIGN KEY (`channel_id`) REFERENCES `npwd_darkchat_channels` (`id`) ON UPDATE RESTRICT ON DELETE RESTRICT
)
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
;

CREATE TABLE `npwd_darkchat_messages` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`channel_id` INT(11) NOT NULL,
	`message` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_general_ci',
	`user_identifier` VARCHAR(255) NOT NULL COLLATE 'utf8mb4_general_ci',
	`createdAt` TIMESTAMP NOT NULL DEFAULT current_timestamp(),
	`is_image` TINYINT(4) NOT NULL DEFAULT '0',
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `darkchat_messages_darkchat_channels_id_fk` (`channel_id`) USING BTREE,
	CONSTRAINT `darkchat_messages_darkchat_channels_id_fk` FOREIGN KEY (`channel_id`) REFERENCES `npwd_darkchat_channels` (`id`) ON UPDATE RESTRICT ON DELETE RESTRICT
)
COLLATE='utf8mb4_general_ci'
ENGINE=InnoDB
AUTO_INCREMENT=31
;
