# qbx_customs

![image](https://github.com/Popcorn-RP/qbx_customs/assets/85725579/43d91928-3e1d-4ef2-b73e-d1f47a831495)


A free-to-use and modify vehicle customization resource forked from [Popcornrp-customs](https://github.com/alberttheprince/popcornrp-customs)

[Preview](https://www.youtube.com/watch?v=8UcGmHJ3mUo)

# Dependencies

- [ox_lib](https://github.com/CommunityOx/ox_lib)
- [qbx_core](https://github.com/Qbox-project/qbx_core)

# Features

- Job Whitelist for free Customization
- Disable or Enable customization options
- Change pricing
- Drag your camera and zoom in and out
- Air wrench sound to confirm the customization
- Displayed pricing
- All tire and paint options (including Chameleon)
- Customize your car (duh)

# Info

The foundation for qbx_customs, Popcornrp-Customs, was created following the subsequent discovery of QB-Customs containing stolen/leaked code. Popcornrp-Customs is a total replacement for QB-Customs, meant to replace it and improve on its original functionality.

Popcornrp-customs was released as a thank-you to the FiveM open-source community and all the developers who have poured thousands of hours of their free time into creating helpful, free resources.

Thank you!

# Credits

Thank you, Jorn (Discord: jorn08), for your hard work on this resource. None of this would be possible without you!

Qbx_customs also includes functionality from the cool dragcam resource by Jorn, check it out!:  https://github.com/Jorn08/dragcam

the Popcorn Roleplay Community for testing and supporting the development of this resource, and the Qbox development team for choosing to use popcornrp-customs as a foundation for Qbox customs!

Popcorn Roleplay Discord: https://discord.gg/popcornroleplay

🍿 ❤️ 🦆
