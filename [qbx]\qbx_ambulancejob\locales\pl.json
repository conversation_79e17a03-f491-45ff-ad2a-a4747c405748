{"error": {"canceled": "<PERSON><PERSON><PERSON><PERSON>", "impossible": "Akcja niemożliwa...", "no_player": "Brak gracza w pobliżu", "no_firstaid": "<PERSON><PERSON><PERSON><PERSON> jest zestaw pierwszej pomocy", "no_bandage": "W<PERSON>agany jest bandaż", "beds_taken": "Łóżka są zajęte...", "possessions_taken": "Wszystkie przedmioty zostały zabrane...", "cant_help": "Nie można pomóc tej osobie...", "not_ems": "Nie jesteś ratownikiem medycznym lub nie jesteś na służbie"}, "success": {"revived": "Osoba została ożywiona", "healthy_player": "<PERSON><PERSON><PERSON> jest zdrowy", "helped_player": "Udzielono pomocy osobie", "being_helped": "Otr<PERSON><PERSON><PERSON> jest pomoc..."}, "info": {"civ_died": "<PERSON><PERSON><PERSON>", "civ_down": "<PERSON><PERSON><PERSON> ranny", "civ_call": "Wezwanie cywila", "ems_down": "Lekarz %s ranny", "respawn_txt": "ODRODZENIE ZA: ~r~%s~s~ SEKUND", "respawn_revive": "PRZYTRZYMAJ [~r~E~s~] PRZEZ %s SEKUND, ABY SIĘ ODRODZIĆ ZA $~r~%s~s~", "bleed_out": "WYKRWAWISZ SIĘ ZA: ~r~%s~s~ SEKUND", "bleed_out_help": "WYKRWAWISZ SIĘ ZA: ~r~%s~s~ SEKUND, MOŻESZ OTRZYMAĆ POMOC", "request_help": "NACIŚNIJ [~r~G~s~], ABY WEZWAĆ POMOC", "help_requested": "POWIADOMIONO RATOWNIKÓW MEDYCZNYCH", "amb_plate": "AMBU", "heli_plate": "LIFE", "status": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> stanu", "is_status": "Stan: %s", "healthy": "Je<PERSON>ś całkowicie zdrowy/a!", "safe": "Szpital bezpieczny", "ems_alert": "Alarm medyczny - %s", "mr": "Pan", "mrs": "<PERSON><PERSON>", "dr_needed": "Potrzebny lekarz w Szpitalu Pillbox", "dr_alert": "Lekarz został już powiadomiony", "ems_report": "Raport me<PERSON>ny", "message_sent": "Wiadomość do wysłania", "check_health": "Sprawdź stan zdrowia gracza", "heal_player": "Ulecz gracza", "revive_player": "Ożywij grac<PERSON>"}, "mail": {"sender": "Szpital Pillbox", "subject": "Koszty szpitalne", "message": "Szanowny/a %s %s, <br /><br />Otrzymano e-mail z kosztami ostatniej wizyty w szpitalu.<br />Końcowe koszty wyniosły: <strong>$%s</strong><br /><br />Życzymy szybkiego powrotu do zdrowia!"}, "menu": {"amb_vehicles": "<PERSON><PERSON><PERSON><PERSON>", "status": "<PERSON>"}, "text": {"pstash_button": "[E] - <PERSON><PERSON><PERSON><PERSON> s<PERSON>", "pstash": "<PERSON><PERSON><PERSON><PERSON> schowek", "onduty_button": "[E] - Rozpocznij służbę", "offduty_button": "[E] - Zakończ służbę", "duty": "Rozpocznij/Zakończ służbę", "armory_button": "[E] - Zbrojownia", "armory": "Zbrojownia", "veh_button": "[E] - <PERSON><PERSON>/Schowaj pojazd", "elevator_roof": "[E] - <PERSON><PERSON>ę na dach", "elevator_main": "[E] - <PERSON><PERSON> windę na parter", "el_roof": "<PERSON>ź windę na dach", "el_main": "<PERSON>ź windę na parter", "call_doc": "[E] - <PERSON><PERSON><PERSON><PERSON>", "call": "<PERSON><PERSON><PERSON><PERSON>", "check_in": "[E] - <PERSON><PERSON><PERSON><PERSON><PERSON>", "check": "<PERSON><PERSON><PERSON><PERSON>", "lie_bed": "[E] - Połóż się do łóżka", "bed": "Połóż się do łóżka", "put_bed": "Umieść cywila w łóżku", "bed_out": "[E] - Wstań z łóżka...", "alert": "Alarm!"}, "progress": {"ifaks": "Stosowanie ifaksów...", "bandage": "Używanie bandaża...", "painkillers": "Zażywanie leków przeciwbólowych...", "revive": "Ożywianie osoby...", "healing": "<PERSON><PERSON><PERSON><PERSON> ran...", "checking_in": "Zameldowywanie się..."}}