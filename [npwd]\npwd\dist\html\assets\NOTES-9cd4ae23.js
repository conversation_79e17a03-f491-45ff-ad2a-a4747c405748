import{j as t}from"./jsx-runtime-5fe4d0a7.js";import{S as r,__tla as a}from"./__federation_expose_Input-51304708.js";let s,h=Promise.all([(()=>{try{return a}catch{}})()]).then(async()=>{let l;l=e=>t.jsx(r,{...e,children:t.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[t.jsx("rect",{fill:"none"}),t.jsx("path",{d:"M19,3H4.99C3.89,3,3,3.9,3,5l0.01,14c0,1.1,0.89,2,1.99,2h10l6-6V5C21,3.9,20.1,3,19,3z M7,8h10v2H7V8z M12,14H7v-2h5V14z M14,19.5V14h5.5L14,19.5z"})]})}),s=()=>t.jsx(l,{fontSize:"small"})});export{h as __tla,s as default};
