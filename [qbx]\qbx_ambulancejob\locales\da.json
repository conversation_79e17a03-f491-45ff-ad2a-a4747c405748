{"error": {"canceled": "<PERSON><PERSON><PERSON><PERSON>", "impossible": "Handling umulig...", "no_player": "Ingen spiller i nærheden", "no_firstaid": "Du har brug for en førstehjælpskasse", "no_bandage": "Du har brug for et bandage", "beds_taken": "Senge er optaget...", "possessions_taken": "Alle dine ejendele er taget...", "cant_help": "Du kan ikke hjæ<PERSON><PERSON> denne person...", "not_ems": "Du er ikke EMS eller ikke logget ind"}, "success": {"revived": "Du genoplivede en person", "healthy_player": "<PERSON><PERSON>lleren er sund", "helped_player": "<PERSON>p personen", "being_helped": "Du bliver hjulpet..."}, "info": {"civ_died": "<PERSON><PERSON> død", "civ_down": "Borger nede", "civ_call": "Borger opkald", "ems_down": "Doctor %s Down", "respawn_txt": "GENOPSTÅ OM: ~r~%s~s~ SEKUNDER", "respawn_revive": "HOLD [~r~E~s~] I %s SEKUNDER FOR AT GENOPSTÅ FOR $~r~%s~s~", "bleed_out": "DU VIL BLØDE UD OM: ~r~%s~s~ SEKUNDER", "bleed_out_help": "DU VIL BLØDE UD OM: ~r~%s~s~ SEKUNDER, DU KAN BLIVE HJULPET", "request_help": "TRYK [~r~G~s~] FOR AT ANMODE OM HJÆLP", "help_requested": "EMS-PERSONALE ER UNDERRETTET", "amb_plate": "AMBU", "heli_plate": "LIFE", "status": "Status tjek", "is_status": "Er %s", "healthy": "Du er helt rask igen!", "safe": "Hospital Sikkert", "ems_alert": "EMS Alarm - %s", "mr": "Hr.", "mrs": "<PERSON><PERSON>", "dr_needed": "En læge er nødvendig på Pillbox Hospital", "dr_alert": "<PERSON>ægen er allerede underrettet", "ems_report": "EMS Rapport", "message_sent": "<PERSON>sked skal sendes", "check_health": "<PERSON><PERSON><PERSON> en spillers sundhed", "heal_player": "Helbred en spiller", "revive_player": "Genopliv en spiller"}, "mail": {"sender": "Pillbox Hospital", "subject": "Hospitalsomkostninger", "message": "Kære %s %s, <br /><br />Her<PERSON> modtager du en e-mail med omkostningerne ved det sidste hospitalsbesøg.<br />De endelige omkostninger er: <strong>$%s</strong><br /><br />Vi ønsker dig en hurtig bedring!"}, "menu": {"amb_vehicles": "Ambulance Køretøjer", "status": "<PERSON>dh<PERSON><PERSON><PERSON>"}, "text": {"pstash_button": "[E] - <PERSON><PERSON><PERSON> s<PERSON>", "pstash": "<PERSON><PERSON><PERSON> s<PERSON>", "onduty_button": "[E] - <PERSON><PERSON> på vagt", "offduty_button": "[E] - <PERSON><PERSON> ud af vagt", "duty": "På/Ud af Vagt", "armory_button": "[E] - Våbenlager", "armory": "Våbenlager", "veh_button": "[E] - <PERSON><PERSON> / Opbevar <PERSON>ø<PERSON>øj", "elevator_roof": "[E] - Tag elevatoren til taget", "elevator_main": "[E] - Tag elevatoren ned", "el_roof": "Tag elevatoren til taget", "el_main": "Tag elevatoren til hovedetagen", "call_doc": "[E] - <PERSON><PERSON> læge", "call": "Ring", "check_in": "[E] Check ind", "check": "Check Ind", "lie_bed": "[E] - Læg dig i sengen", "bed": "Lig i sengen", "put_bed": "Placer borgeren i sengen", "bed_out": "[E] - <PERSON><PERSON> ud af sengen..", "alert": "Alarm!"}, "progress": {"ifaks": "Tager ifaks...", "bandage": "B<PERSON>er bandage...", "painkillers": "Tager smertestillende...", "revive": "Genopliver person...", "healing": "<PERSON><PERSON> sår...", "checking_in": "Tjekker ind..."}}