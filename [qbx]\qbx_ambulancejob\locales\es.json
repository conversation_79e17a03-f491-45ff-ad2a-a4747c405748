{"error": {"canceled": "Cancelado", "impossible": "Acción imposible...", "no_player": "No hay ningún jugador cerca", "no_firstaid": "Necesitas un kit de primeros auxilios", "no_bandage": "Necesitas una benda", "beds_taken": "Las camas están ocupadas...", "possessions_taken": "<PERSON><PERSON> tus posesiones han sido confiscadas...", "cant_help": "No puedes ayudar a esta persona...", "not_ems": "No eres EMS"}, "success": {"revived": "<PERSON><PERSON> reanimada", "healthy_player": "El paciente ya está saludable", "helped_player": "Has ayudado a la persona", "being_helped": "Estás siendo tratado..."}, "info": {"civ_died": "Civil muerto", "civ_down": "Civil caído", "civ_call": "Llamada de civil", "ems_down": "Doctor %s Down", "respawn_txt": "REAPARECERAS EN %s SEGUNDOS", "respawn_revive": "MANTÉN [E] DURANTE %s SEGUNDOS PARA SER REVIVIDO POR %s", "bleed_out": "TE DESANGRARAS EN %s SEGUNDOS", "bleed_out_help": "TE DESANGRARAS EN %s SEGUNDOS, PUEDES SER AYUDADO", "request_help": "PULSA [G] PARA PEDIR AYUDA", "help_requested": "EMS EN CAMINO", "amb_plate": "LSMD", "heli_plate": "LSMD", "status": "Revisión de estado", "is_status": "es %s", "healthy": "¡Ya estás completamente saludable de nuevo!", "safe": "Caja fuerte de hopital", "ems_alert": "Alerta EMS - %s", "mr": "Dr.", "mrs": "<PERSON>a.", "dr_needed": "Se necesita un doctor en el hospital", "ems_report": "Reporte EMS", "message_sent": "Mensaje enviado", "check_health": "<PERSON><PERSON><PERSON> sa<PERSON> de juga<PERSON>", "heal_player": "<PERSON><PERSON><PERSON> juga<PERSON>", "revive_player": "<PERSON><PERSON><PERSON> j<PERSON>"}, "mail": {"sender": "Hospital Pillbox", "subject": "Costos de hospital", "message": "Querido %s %s, <br /><br />Le adjuntamos la factura con los costos de su última estancia en el hospital.<br />El costo total es de: <strong>$%s</strong><br /><br />¡Le deseamos una pronta recuperación!"}, "menu": {"amb_vehicles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "status": "Estado de salud"}, "text": {"pstash_button": "[E] - <PERSON><PERSON> personal", "pstash": "Stash personal", "onduty_button": "[E] - Entrar en servicio", "offduty_button": "[E] - <PERSON><PERSON> servicio", "duty": "En/fuera de servicio", "armory_button": "[E] - Armería", "armory": "Armería", "veh_button": "[E] - <PERSON><PERSON> / guardar ve<PERSON>lo", "elevator_roof": "[E] - <PERSON><PERSON> el elevador al último piso", "elevator_main": "[E] - <PERSON>ar el elevador hacía abajo", "el_roof": "Tomar el elevador al último piso", "el_main": "Take the elevator to the main floor", "call_doc": "[E] - <PERSON><PERSON><PERSON> doctor", "call": "<PERSON><PERSON><PERSON>", "check_in": "[E] Hacer check-in", "check": "Check-in", "lie_bed": "[E] - Para acostarse en la cama", "bed": "Acostarse en la cama", "put_bed": "Acostar al ciudadano en la cama", "bed_out": "[E] - Para salir de la cama..", "alert": "<PERSON><PERSON>!"}, "progress": {"ifaks": "Tomando ifaks...", "bandage": "<PERSON>ando vendas...", "painkillers": "Tomando pastillas para el dolor...", "revive": "<PERSON><PERSON><PERSON><PERSON> persona..", "healing": "<PERSON><PERSON><PERSON>...", "checking_in": "Realizando revisión..."}}