var Bl=Object.defineProperty;var jl=(e,t,n)=>t in e?Bl(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var A=(e,t,n)=>(jl(e,typeof t!="symbol"?t+"":t,n),n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))i(s);new MutationObserver(s=>{for(const o of s)if(o.type==="childList")for(const r of o.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&i(r)}).observe(document,{childList:!0,subtree:!0});function n(s){const o={};return s.integrity&&(o.integrity=s.integrity),s.referrerPolicy&&(o.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?o.credentials="include":s.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function i(s){if(s.ep)return;s.ep=!0;const o=n(s);fetch(s.href,o)}})();function gt(){}const ha=e=>e;function H(e,t){for(const n in t)e[n]=t[n];return e}function ga(e){return e()}function gs(){return Object.create(null)}function Jt(e){e.forEach(ga)}function ue(e){return typeof e=="function"}function U(e,t){return e!=e?t==t:e!==t||e&&typeof e=="object"||typeof e=="function"}function Wl(e){return Object.keys(e).length===0}function Ns(e,...t){if(e==null)return gt;const n=e.subscribe(...t);return n.unsubscribe?()=>n.unsubscribe():n}function Gl(e){let t;return Ns(e,n=>t=n)(),t}function pt(e,t,n){e.$$.on_destroy.push(Ns(t,n))}function Pt(e,t,n,i){if(e){const s=pa(e,t,n,i);return e[0](s)}}function pa(e,t,n,i){return e[1]&&i?H(n.ctx.slice(),e[1](i(t))):n.ctx}function Ot(e,t,n,i){if(e[2]&&i){const s=e[2](i(n));if(t.dirty===void 0)return s;if(typeof s=="object"){const o=[],r=Math.max(t.dirty.length,s.length);for(let a=0;a<r;a+=1)o[a]=t.dirty[a]|s[a];return o}return t.dirty|s}return t.dirty}function Dt(e,t,n,i,s,o){if(s){const r=pa(t,n,i,o);e.p(r,s)}}function Ft(e){if(e.ctx.length>32){const t=[],n=e.ctx.length/32;for(let i=0;i<n;i++)t[i]=-1;return t}return-1}function et(e){const t={};for(const n in e)n[0]!=="$"&&(t[n]=e[n]);return t}function _o(e,t){const n={};t=new Set(t);for(const i in e)!t.has(i)&&i[0]!=="$"&&(n[i]=e[i]);return n}function _t(e,t,n){return e.set(n),t}function Bs(e){return e&&ue(e.destroy)?e.destroy:gt}const ma=typeof window<"u";let ql=ma?()=>window.performance.now():()=>Date.now(),js=ma?e=>requestAnimationFrame(e):gt;const Ue=new Set;function _a(e){Ue.forEach(t=>{t.c(e)||(Ue.delete(t),t.f())}),Ue.size!==0&&js(_a)}function Yl(e){let t;return Ue.size===0&&js(_a),{promise:new Promise(n=>{Ue.add(t={c:e,f:n})}),abort(){Ue.delete(t)}}}let Ei=!1;function Ul(){Ei=!0}function Xl(){Ei=!1}function Kl(e,t,n,i){for(;e<t;){const s=e+(t-e>>1);n(s)<=i?e=s+1:t=s}return e}function Ql(e){if(e.hydrate_init)return;e.hydrate_init=!0;let t=e.childNodes;if(e.nodeName==="HEAD"){const l=[];for(let c=0;c<t.length;c++){const u=t[c];u.claim_order!==void 0&&l.push(u)}t=l}const n=new Int32Array(t.length+1),i=new Int32Array(t.length);n[0]=-1;let s=0;for(let l=0;l<t.length;l++){const c=t[l].claim_order,u=(s>0&&t[n[s]].claim_order<=c?s+1:Kl(1,s,d=>t[n[d]].claim_order,c))-1;i[l]=n[u]+1;const f=u+1;n[f]=l,s=Math.max(f,s)}const o=[],r=[];let a=t.length-1;for(let l=n[s]+1;l!=0;l=i[l-1]){for(o.push(t[l-1]);a>=l;a--)r.push(t[a]);a--}for(;a>=0;a--)r.push(t[a]);o.reverse(),r.sort((l,c)=>l.claim_order-c.claim_order);for(let l=0,c=0;l<r.length;l++){for(;c<o.length&&r[l].claim_order>=o[c].claim_order;)c++;const u=c<o.length?o[c]:null;e.insertBefore(r[l],u)}}function w(e,t){e.appendChild(t)}function ba(e){if(!e)return document;const t=e.getRootNode?e.getRootNode():e.ownerDocument;return t&&t.host?t:e.ownerDocument}function Zl(e){const t=D("style");return Jl(ba(e),t),t.sheet}function Jl(e,t){return w(e.head||e,t),t.sheet}function tc(e,t){if(Ei){for(Ql(e),(e.actual_end_child===void 0||e.actual_end_child!==null&&e.actual_end_child.parentNode!==e)&&(e.actual_end_child=e.firstChild);e.actual_end_child!==null&&e.actual_end_child.claim_order===void 0;)e.actual_end_child=e.actual_end_child.nextSibling;t!==e.actual_end_child?(t.claim_order!==void 0||t.parentNode!==e)&&e.insertBefore(t,e.actual_end_child):e.actual_end_child=t.nextSibling}else(t.parentNode!==e||t.nextSibling!==null)&&e.appendChild(t)}function nt(e,t,n){e.insertBefore(t,n||null)}function ec(e,t,n){Ei&&!n?tc(e,t):(t.parentNode!==e||t.nextSibling!=n)&&e.insertBefore(t,n||null)}function X(e){e.parentNode&&e.parentNode.removeChild(e)}function Ze(e,t){for(let n=0;n<e.length;n+=1)e[n]&&e[n].d(t)}function D(e){return document.createElement(e)}function ya(e){return document.createElementNS("http://www.w3.org/2000/svg",e)}function J(e){return document.createTextNode(e)}function Y(){return J(" ")}function Pn(){return J("")}function It(e,t,n,i){return e.addEventListener(t,n,i),()=>e.removeEventListener(t,n,i)}function F(e,t,n){n==null?e.removeAttribute(t):e.getAttribute(t)!==n&&e.setAttribute(t,n)}const nc=["width","height"];function ic(e,t){const n=Object.getOwnPropertyDescriptors(e.__proto__);for(const i in t)t[i]==null?e.removeAttribute(i):i==="style"?e.style.cssText=t[i]:i==="__value"?e.value=e[i]=t[i]:n[i]&&n[i].set&&nc.indexOf(i)===-1?e[i]=t[i]:F(e,i,t[i])}function li(e,t){for(const n in t)F(e,n,t[n])}function sc(e){return Array.from(e.childNodes)}function oc(e){e.claim_info===void 0&&(e.claim_info={last_index:0,total_claimed:0})}function rc(e,t,n,i,s=!1){oc(e);const o=(()=>{for(let r=e.claim_info.last_index;r<e.length;r++){const a=e[r];if(t(a)){const l=n(a);return l===void 0?e.splice(r,1):e[r]=l,s||(e.claim_info.last_index=r),a}}for(let r=e.claim_info.last_index-1;r>=0;r--){const a=e[r];if(t(a)){const l=n(a);return l===void 0?e.splice(r,1):e[r]=l,s?l===void 0&&e.claim_info.last_index--:e.claim_info.last_index=r,a}}return i()})();return o.claim_order=e.claim_info.total_claimed,e.claim_info.total_claimed+=1,o}function ac(e,t){return rc(e,n=>n.nodeType===3,n=>{const i=""+t;if(n.data.startsWith(i)){if(n.data.length!==i.length)return n.splitText(i.length)}else n.data=i},()=>J(t),!0)}function Wt(e,t){t=""+t,e.data!==t&&(e.data=t)}function ci(e,t){e.value=t??""}function Tn(e,t,n){e.classList[n?"add":"remove"](t)}function lc(e,t,{bubbles:n=!1,cancelable:i=!1}={}){const s=document.createEvent("CustomEvent");return s.initCustomEvent(e,n,i,t),s}function xe(e,t){return new e(t)}const ui=new Map;let fi=0;function cc(e){let t=5381,n=e.length;for(;n--;)t=(t<<5)-t^e.charCodeAt(n);return t>>>0}function uc(e,t){const n={stylesheet:Zl(t),rules:{}};return ui.set(e,n),n}function bo(e,t,n,i,s,o,r,a=0){const l=16.666/i;let c=`{
`;for(let m=0;m<=1;m+=l){const _=t+(n-t)*o(m);c+=m*100+`%{${r(_,1-_)}}
`}const u=c+`100% {${r(n,1-n)}}
}`,f=`__svelte_${cc(u)}_${a}`,d=ba(e),{stylesheet:h,rules:g}=ui.get(d)||uc(d,e);g[f]||(g[f]=!0,h.insertRule(`@keyframes ${f} ${u}`,h.cssRules.length));const p=e.style.animation||"";return e.style.animation=`${p?`${p}, `:""}${f} ${i}ms linear ${s}ms 1 both`,fi+=1,f}function fc(e,t){const n=(e.style.animation||"").split(", "),i=n.filter(t?o=>o.indexOf(t)<0:o=>o.indexOf("__svelte")===-1),s=n.length-i.length;s&&(e.style.animation=i.join(", "),fi-=s,fi||dc())}function dc(){js(()=>{fi||(ui.forEach(e=>{const{ownerNode:t}=e.stylesheet;t&&X(t)}),ui.clear())})}let Ee;function Pe(e){Ee=e}function on(){if(!Ee)throw new Error("Function called outside component initialization");return Ee}function Li(e){on().$$.on_mount.push(e)}function hc(e){on().$$.after_update.push(e)}function rn(e){on().$$.on_destroy.push(e)}function gc(e,t){return on().$$.context.set(e,t),t}function Ws(e){return on().$$.context.get(e)}function pc(e){return on().$$.context.has(e)}function mc(e,t){const n=e.$$.callbacks[t.type];n&&n.slice().forEach(i=>i.call(this,t))}const Ge=[],Je=[];let Xe=[];const ps=[],xa=Promise.resolve();let ms=!1;function va(){ms||(ms=!0,xa.then(Sa))}function Gs(){return va(),xa}function tn(e){Xe.push(e)}function wa(e){ps.push(e)}const Gi=new Set;let Ne=0;function Sa(){if(Ne!==0)return;const e=Ee;do{try{for(;Ne<Ge.length;){const t=Ge[Ne];Ne++,Pe(t),_c(t.$$)}}catch(t){throw Ge.length=0,Ne=0,t}for(Pe(null),Ge.length=0,Ne=0;Je.length;)Je.pop()();for(let t=0;t<Xe.length;t+=1){const n=Xe[t];Gi.has(n)||(Gi.add(n),n())}Xe.length=0}while(Ge.length);for(;ps.length;)ps.pop()();ms=!1,Gi.clear(),Pe(e)}function _c(e){if(e.fragment!==null){e.update(),Jt(e.before_update);const t=e.dirty;e.dirty=[-1],e.fragment&&e.fragment.p(e.ctx,t),e.after_update.forEach(tn)}}function bc(e){const t=[],n=[];Xe.forEach(i=>e.indexOf(i)===-1?t.push(i):n.push(i)),n.forEach(i=>i()),Xe=t}let ln;function yc(){return ln||(ln=Promise.resolve(),ln.then(()=>{ln=null})),ln}function qi(e,t,n){e.dispatchEvent(lc(`${t?"intro":"outro"}${n}`))}const ni=new Set;let ce;function Rt(){ce={r:0,c:[],p:ce}}function $t(){ce.r||Jt(ce.c),ce=ce.p}function S(e,t){e&&e.i&&(ni.delete(e),e.i(t))}function C(e,t,n,i){if(e&&e.o){if(ni.has(e))return;ni.add(e),ce.c.push(()=>{ni.delete(e),i&&(n&&e.d(1),i())}),e.o(t)}else i&&i()}const xc={duration:0};function di(e,t,n,i){const s={direction:"both"};let o=t(e,n,s),r=i?0:1,a=null,l=null,c=null;function u(){c&&fc(e,c)}function f(h,g){const p=h.b-r;return g*=Math.abs(p),{a:r,b:h.b,d:p,duration:g,start:h.start,end:h.start+g,group:h.group}}function d(h){const{delay:g=0,duration:p=300,easing:m=ha,tick:_=gt,css:b}=o||xc,x={start:ql()+g,b:h};h||(x.group=ce,ce.r+=1),a||l?l=x:(b&&(u(),c=bo(e,r,h,p,g,m,b)),h&&_(0,1),a=f(x,p),tn(()=>qi(e,h,"start")),Yl(v=>{if(l&&v>l.start&&(a=f(l,p),l=null,qi(e,a.b,"start"),b&&(u(),c=bo(e,r,a.b,a.duration,0,m,o.css))),a){if(v>=a.end)_(r=a.b,1-r),qi(e,a.b,"end"),l||(a.b?u():--a.group.r||Jt(a.group.c)),a=null;else if(v>=a.start){const y=v-a.start;r=a.a+a.d*m(y/a.duration),_(r,1-r)}}return!!(a||l)}))}return{run(h){ue(o)?yc().then(()=>{o=o(s),d(h)}):d(h)},end(){u(),a=l=null}}}function zt(e,t){const n={},i={},s={$$scope:1};let o=e.length;for(;o--;){const r=e[o],a=t[o];if(a){for(const l in r)l in a||(i[l]=1);for(const l in a)s[l]||(n[l]=a[l],s[l]=1);e[o]=a}else for(const l in r)s[l]=1}for(const r in i)r in n||(n[r]=void 0);return n}function te(e){return typeof e=="object"&&e!==null?e:{}}const vc=/[&"]/g,wc=/[&<]/g;function Sc(e,t=!1){const n=String(e),i=t?vc:wc;i.lastIndex=0;let s="",o=0;for(;i.test(n);){const r=i.lastIndex-1,a=n[r];s+=n.substring(o,r)+(a==="&"?"&amp;":a==='"'?"&quot;":"&lt;"),o=r+1}return s+n.substring(o)}function Cc(e,t){if(!e||!e.$$render)throw t==="svelte:component"&&(t+=" this={...}"),new Error(`<${t}> is not a valid SSR component. You may need to review your build config to ensure that dependencies are compiled, rather than imported as pre-compiled modules. Otherwise you may need to fix a <${t}>.`);return e}let Yi;function Ca(e){function t(n,i,s,o,r){const a=Ee,l={on_destroy:Yi,context:new Map(r||(a?a.$$.context:[])),on_mount:[],before_update:[],after_update:[],callbacks:gs()};Pe({$$:l});const c=e(n,i,s,o);return Pe(a),c}return{render:(n={},{$$slots:i={},context:s=new Map}={})=>{Yi=[];const o={title:"",head:"",css:new Set},r=t(o,n,{},i,s);return Jt(Yi),{html:r,css:{code:Array.from(o.css).map(a=>a.code).join(`
`),map:null},head:o.title+o.head}},$$render:t}}function Ma(e,t,n){const i=e.$$.props[t];i!==void 0&&(e.$$.bound[i]=n,n(e.$$.ctx[i]))}function I(e){e&&e.c()}function Mc(e,t){e&&e.l(t)}function E(e,t,n,i){const{fragment:s,after_update:o}=e.$$;s&&s.m(t,n),i||tn(()=>{const r=e.$$.on_mount.map(ga).filter(ue);e.$$.on_destroy?e.$$.on_destroy.push(...r):Jt(r),e.$$.on_mount=[]}),o.forEach(tn)}function L(e,t){const n=e.$$;n.fragment!==null&&(bc(n.after_update),Jt(n.on_destroy),n.fragment&&n.fragment.d(t),n.on_destroy=n.fragment=null,n.ctx=[])}function kc(e,t){e.$$.dirty[0]===-1&&(Ge.push(e),va(),e.$$.dirty.fill(0)),e.$$.dirty[t/31|0]|=1<<t%31}function Z(e,t,n,i,s,o,r,a=[-1]){const l=Ee;Pe(e);const c=e.$$={fragment:null,ctx:[],props:o,update:gt,not_equal:s,bound:gs(),on_mount:[],on_destroy:[],on_disconnect:[],before_update:[],after_update:[],context:new Map(t.context||(l?l.$$.context:[])),callbacks:gs(),dirty:a,skip_bound:!1,root:t.target||l.$$.root};r&&r(c.root);let u=!1;if(c.ctx=n?n(e,t.props||{},(f,d,...h)=>{const g=h.length?h[0]:d;return c.ctx&&s(c.ctx[f],c.ctx[f]=g)&&(!c.skip_bound&&c.bound[f]&&c.bound[f](g),u&&kc(e,f)),d}):[],c.update(),u=!0,Jt(c.before_update),c.fragment=i?i(c.ctx):!1,t.target){if(t.hydrate){Ul();const f=sc(t.target);c.fragment&&c.fragment.l(f),f.forEach(X)}else c.fragment&&c.fragment.c();t.intro&&S(e.$$.fragment),E(e,t.target,t.anchor,t.customElement),Xl(),Sa()}Pe(l)}class K{$destroy(){L(this,1),this.$destroy=gt}$on(t,n){if(!ue(n))return gt;const i=this.$$.callbacks[t]||(this.$$.callbacks[t]=[]);return i.push(n),()=>{const s=i.indexOf(n);s!==-1&&i.splice(s,1)}}$set(t){this.$$set&&!Wl(t)&&(this.$$.skip_bound=!0,this.$$set(t),this.$$.skip_bound=!1)}}const Be=[];function _s(e,t){return{subscribe:Et(e,t).subscribe}}function Et(e,t=gt){let n;const i=new Set;function s(a){if(U(e,a)&&(e=a,n)){const l=!Be.length;for(const c of i)c[1](),Be.push(c,e);if(l){for(let c=0;c<Be.length;c+=2)Be[c][0](Be[c+1]);Be.length=0}}}function o(a){s(a(e))}function r(a,l=gt){const c=[a,l];return i.add(c),i.size===1&&(n=t(s)||gt),a(e),()=>{i.delete(c),i.size===0&&n&&(n(),n=null)}}return{set:s,update:o,subscribe:r}}function qs(e,t,n){const i=!Array.isArray(e),s=i?[e]:e,o=t.length<2;return _s(n,r=>{let a=!1;const l=[];let c=0,u=gt;const f=()=>{if(c)return;u();const h=t(i?l[0]:l,r);o?r(h):u=ue(h)?h:gt},d=s.map((h,g)=>Ns(h,p=>{l[g]=p,c&=~(1<<g),a&&f()},()=>{c|=1<<g}));return a=!0,f(),function(){Jt(d),u(),a=!1}})}function bs(e,t=!1){return e=e.slice(e.startsWith("/#")?2:0,e.endsWith("/*")?-2:void 0),e.startsWith("/")||(e="/"+e),e==="/"&&(e=""),t&&!e.endsWith("/")&&(e+="/"),e}function Rc(e,t){e=bs(e,!0),t=bs(t,!0);let n=[],i={},s=!0,o=e.split("/").map(a=>a.startsWith(":")?(n.push(a.slice(1)),"([^\\/]+)"):a).join("\\/"),r=t.match(new RegExp(`^${o}$`));return r||(s=!1,r=t.match(new RegExp(`^${o}`))),r?(n.forEach((a,l)=>i[a]=r[l+1]),{exact:s,params:i,part:r[0].slice(0,-1)}):null}function ka(e,t,n){if(n==="")return e;if(n[0]==="/")return n;let i=r=>r.split("/").filter(a=>a!==""),s=i(e);return"/"+(t?i(t):[]).map((r,a)=>s[a]).join("/")+"/"+n}function yo(e,t,n,i){let s=[t,"data-"+t].reduce((o,r)=>{let a=e.getAttribute(r);return n&&e.removeAttribute(r),a===null?o:a},!1);return!i&&s===""?!0:s||i||!1}function $c(e){let t=e.split("&").map(n=>n.split("=")).reduce((n,i)=>{let s=i[0];if(!s)return n;let o=i.length>1?i[i.length-1]:!0;return typeof o=="string"&&o.includes(",")&&(o=o.split(",")),n[s]===void 0?n[s]=[o]:n[s].push(o),n},{});return Object.entries(t).reduce((n,i)=>(n[i[0]]=i[1].length>1?i[1]:i[1][0],n),{})}function Ac(e){return Object.entries(e).map(([t,n])=>n?n===!0?t:`${t}=${Array.isArray(n)?n.join(","):n}`:null).filter(t=>t).join("&")}function xo(e,t){return e?t+e:""}function Ra(e){throw new Error("[Tinro] "+e)}var Nt={HISTORY:1,HASH:2,MEMORY:3,OFF:4,run(e,t,n,i){return e===this.HISTORY?t&&t():e===this.HASH?n&&n():i&&i()},getDefault(){return!window||window.location.pathname==="srcdoc"?this.MEMORY:this.HISTORY}},Ys,$a,Aa,hi="",Ht=Pc();function Pc(){let e=Nt.getDefault(),t,n=r=>window.onhashchange=window.onpopstate=Ys=null,i=r=>t&&t(Ui(e)),s=r=>{r&&(e=r),n(),e!==Nt.OFF&&Nt.run(e,a=>window.onpopstate=i,a=>window.onhashchange=i)&&i()},o=r=>{let a=Object.assign(Ui(e),r);return a.path+xo(Ac(a.query),"?")+xo(a.hash,"#")};return{mode:s,get:r=>Ui(e),go(r,a){Oc(e,r,a),i()},start(r){t=r,s()},stop(){t=null,s(Nt.OFF)},set(r){this.go(o(r),!r.path)},methods(){return Dc(this)},base:r=>hi=r}}function Oc(e,t,n){!n&&($a=Aa);let i=s=>history[`${n?"replace":"push"}State`]({},"",s);Nt.run(e,s=>i(hi+t),s=>i(`#${t}`),s=>Ys=t)}function Ui(e){let t=window.location,n=Nt.run(e,s=>(hi?t.pathname.replace(hi,""):t.pathname)+t.search+t.hash,s=>String(t.hash.slice(1)||"/"),s=>Ys||"/"),i=n.match(/^([^?#]+)(?:\?([^#]+))?(?:\#(.+))?$/);return Aa=n,{url:n,from:$a,path:i[1]||"",query:$c(i[2]||""),hash:i[3]||""}}function Dc(e){let t=()=>e.get().query,n=r=>e.set({query:r}),i=r=>n(r(t())),s=()=>e.get().hash,o=r=>e.set({hash:r});return{hash:{get:s,set:o,clear:()=>o("")},query:{replace:n,clear:()=>n(""),get(r){return r?t()[r]:t()},set(r,a){i(l=>(l[r]=a,l))},delete(r){i(a=>(a[r]&&delete a[r],a))}}}}var Le=Fc();function Fc(){let{subscribe:e}=Et(Ht.get(),t=>{Ht.start(t);let n=Ec(Ht.go);return()=>{Ht.stop(),n()}});return{subscribe:e,goto:Ht.go,params:Lc,meta:Us,useHashNavigation:t=>Ht.mode(t?Nt.HASH:Nt.HISTORY),mode:{hash:()=>Ht.mode(Nt.HASH),history:()=>Ht.mode(Nt.HISTORY),memory:()=>Ht.mode(Nt.MEMORY)},base:Ht.base,location:Ht.methods()}}function Ec(e){let t=n=>{let i=n.target.closest("a[href]"),s=i&&yo(i,"target",!1,"_self"),o=i&&yo(i,"tinro-ignore"),r=n.ctrlKey||n.metaKey||n.altKey||n.shiftKey;if(s=="_self"&&!o&&!r&&i){let a=i.getAttribute("href").replace(/^\/#/,"");/^\/\/|^#|^[a-zA-Z]+:/.test(a)||(n.preventDefault(),e(a.startsWith("/")?a:i.href.replace(window.location.origin,"")))}};return addEventListener("click",t),()=>removeEventListener("click",t)}function Lc(){return Ws("tinro").meta.params}var gi="tinro",Tc=Pa({pattern:"",matched:!0});function Ic(e){let t=Ws(gi)||Tc;(t.exact||t.fallback)&&Ra(`${e.fallback?"<Route fallback>":`<Route path="${e.path}">`}  can't be inside ${t.fallback?"<Route fallback>":`<Route path="${t.path||"/"}"> with exact path`}`);let n=e.fallback?"fallbacks":"childs",i=Et({}),s=Pa({fallback:e.fallback,parent:t,update(o){s.exact=!o.path.endsWith("/*"),s.pattern=bs(`${s.parent.pattern||""}${o.path}`),s.redirect=o.redirect,s.firstmatch=o.firstmatch,s.breadcrumb=o.breadcrumb,s.match()},register:()=>(s.parent[n].add(s),async()=>{s.parent[n].delete(s),s.parent.activeChilds.delete(s),s.router.un&&s.router.un(),s.parent.match()}),show:()=>{e.onShow(),!s.fallback&&s.parent.activeChilds.add(s)},hide:()=>{e.onHide(),s.parent.activeChilds.delete(s)},match:async()=>{s.matched=!1;let{path:o,url:r,from:a,query:l}=s.router.location,c=Rc(s.pattern,o);if(!s.fallback&&c&&s.redirect&&(!s.exact||s.exact&&c.exact)){let u=ka(o,s.parent.pattern,s.redirect);return Le.goto(u,!0)}s.meta=c&&{from:a,url:r,query:l,match:c.part,pattern:s.pattern,breadcrumbs:s.parent.meta&&s.parent.meta.breadcrumbs.slice()||[],params:c.params,subscribe:i.subscribe},s.breadcrumb&&s.meta&&s.meta.breadcrumbs.push({name:s.breadcrumb,path:c.part}),i.set(s.meta),c&&!s.fallback&&(!s.exact||s.exact&&c.exact)&&(!s.parent.firstmatch||!s.parent.matched)?(e.onMeta(s.meta),s.parent.matched=!0,s.show()):s.hide(),c&&s.showFallbacks()}});return gc(gi,s),Li(()=>s.register()),s}function Us(){return pc(gi)?Ws(gi).meta:Ra("meta() function must be run inside any `<Route>` child component only")}function Pa(e){let t={router:{},exact:!1,pattern:null,meta:null,parent:null,fallback:!1,redirect:!1,firstmatch:!1,breadcrumb:null,matched:!1,childs:new Set,activeChilds:new Set,fallbacks:new Set,async showFallbacks(){if(!this.fallback&&(await Gs(),this.childs.size>0&&this.activeChilds.size==0||this.childs.size==0&&this.fallbacks.size>0)){let n=this;for(;n.fallbacks.size==0;)if(n=n.parent,!n)return;n&&n.fallbacks.forEach(i=>{if(i.redirect){let s=ka("/",i.parent.pattern,i.redirect);Le.goto(s,!0)}else i.show()})}},start(){this.router.un||(this.router.un=Le.subscribe(n=>{this.router.location=n,this.pattern!==null&&this.match()}))},match(){this.showFallbacks()}};return Object.assign(t,e),t.start(),t}const Vc=e=>({params:e&2,meta:e&4}),vo=e=>({params:e[1],meta:e[2]});function wo(e){let t;const n=e[9].default,i=Pt(n,e,e[8],vo);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&262)&&Dt(i,n,s,s[8],t?Ot(n,s[8],o,Vc):Ft(s[8]),vo)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function zc(e){let t,n,i=e[0]&&wo(e);return{c(){i&&i.c(),t=Pn()},m(s,o){i&&i.m(s,o),nt(s,t,o),n=!0},p(s,[o]){s[0]?i?(i.p(s,o),o&1&&S(i,1)):(i=wo(s),i.c(),S(i,1),i.m(t.parentNode,t)):i&&(Rt(),C(i,1,1,()=>{i=null}),$t())},i(s){n||(S(i),n=!0)},o(s){C(i),n=!1},d(s){i&&i.d(s),s&&X(t)}}}function Hc(e,t,n){let{$$slots:i={},$$scope:s}=t,{path:o="/*"}=t,{fallback:r=!1}=t,{redirect:a=!1}=t,{firstmatch:l=!1}=t,{breadcrumb:c=null}=t,u=!1,f={},d={};const h=Ic({fallback:r,onShow(){n(0,u=!0)},onHide(){n(0,u=!1)},onMeta(g){n(2,d=g),n(1,f=d.params)}});return e.$$set=g=>{"path"in g&&n(3,o=g.path),"fallback"in g&&n(4,r=g.fallback),"redirect"in g&&n(5,a=g.redirect),"firstmatch"in g&&n(6,l=g.firstmatch),"breadcrumb"in g&&n(7,c=g.breadcrumb),"$$scope"in g&&n(8,s=g.$$scope)},e.$$.update=()=>{e.$$.dirty&232&&h.update({path:o,redirect:a,firstmatch:l,breadcrumb:c})},[u,f,d,o,r,a,l,c,s,i]}class So extends K{constructor(t){super(),Z(this,t,Hc,zc,U,{path:3,fallback:4,redirect:5,firstmatch:6,breadcrumb:7})}}async function Oa(e,t={}){const n={method:"post",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify(t)},i=window.GetParentResourceName?window.GetParentResourceName():"nui-frame-app";return await(await fetch(`https://${i}/${e}`,n)).json()}var Co={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor","stroke-width":2,"stroke-linecap":"round","stroke-linejoin":"round"};function Mo(e,t,n){const i=e.slice();return i[9]=t[n][0],i[10]=t[n][1],i}function Xi(e){let t,n=[e[10]],i={};for(let s=0;s<n.length;s+=1)i=H(i,n[s]);return{c(){t=ya(e[9]),li(t,i)},m(s,o){nt(s,t,o)},p(s,o){li(t,i=zt(n,[o&16&&s[10]]))},d(s){s&&X(t)}}}function ko(e){let t=e[9],n,i=e[9]&&Xi(e);return{c(){i&&i.c(),n=Pn()},m(s,o){i&&i.m(s,o),nt(s,n,o)},p(s,o){s[9]?t?U(t,s[9])?(i.d(1),i=Xi(s),t=s[9],i.c(),i.m(n.parentNode,n)):i.p(s,o):(i=Xi(s),t=s[9],i.c(),i.m(n.parentNode,n)):t&&(i.d(1),i=null,t=s[9])},d(s){s&&X(n),i&&i.d(s)}}}function Nc(e){let t,n,i,s,o=e[4],r=[];for(let f=0;f<o.length;f+=1)r[f]=ko(Mo(e,o,f));const a=e[8].default,l=Pt(a,e,e[7],null);let c=[Co,e[5],{width:e[2]},{height:e[2]},{stroke:e[1]},{"stroke-width":e[3]},{class:i=`tabler-icon tabler-icon-${e[0]} ${e[6].class??""}`}],u={};for(let f=0;f<c.length;f+=1)u=H(u,c[f]);return{c(){t=ya("svg");for(let f=0;f<r.length;f+=1)r[f].c();n=Pn(),l&&l.c(),li(t,u)},m(f,d){nt(f,t,d);for(let h=0;h<r.length;h+=1)r[h]&&r[h].m(t,null);w(t,n),l&&l.m(t,null),s=!0},p(f,[d]){if(d&16){o=f[4];let h;for(h=0;h<o.length;h+=1){const g=Mo(f,o,h);r[h]?r[h].p(g,d):(r[h]=ko(g),r[h].c(),r[h].m(t,n))}for(;h<r.length;h+=1)r[h].d(1);r.length=o.length}l&&l.p&&(!s||d&128)&&Dt(l,a,f,f[7],s?Ot(a,f[7],d,null):Ft(f[7]),null),li(t,u=zt(c,[Co,d&32&&f[5],(!s||d&4)&&{width:f[2]},(!s||d&4)&&{height:f[2]},(!s||d&2)&&{stroke:f[1]},(!s||d&8)&&{"stroke-width":f[3]},(!s||d&65&&i!==(i=`tabler-icon tabler-icon-${f[0]} ${f[6].class??""}`))&&{class:i}]))},i(f){s||(S(l,f),s=!0)},o(f){C(l,f),s=!1},d(f){f&&X(t),Ze(r,f),l&&l.d(f)}}}function Bc(e,t,n){const i=["name","color","size","stroke","iconNode"];let s=_o(t,i),{$$slots:o={},$$scope:r}=t,{name:a}=t,{color:l="currentColor"}=t,{size:c=24}=t,{stroke:u=2}=t,{iconNode:f}=t;return e.$$set=d=>{n(6,t=H(H({},t),et(d))),n(5,s=_o(t,i)),"name"in d&&n(0,a=d.name),"color"in d&&n(1,l=d.color),"size"in d&&n(2,c=d.size),"stroke"in d&&n(3,u=d.stroke),"iconNode"in d&&n(4,f=d.iconNode),"$$scope"in d&&n(7,r=d.$$scope)},t=et(t),[a,l,c,u,f,s,t,r,o]}class jc extends K{constructor(t){super(),Z(this,t,Bc,Nc,U,{name:0,color:1,size:2,stroke:3,iconNode:4})}}const fe=jc;function Wc(e){let t;const n=e[2].default,i=Pt(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Dt(i,n,s,s[3],t?Ot(n,s[3],o,null):Ft(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function Gc(e){let t,n;const i=[{name:"chevron-left"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[Wc]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=H(s,i[o]);return t=new fe({props:s}),{c(){I(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&te(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function qc(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M15 6l-6 6l6 6"}]];return e.$$set=r=>{n(1,t=H(H({},t),et(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=et(t),[o,t,i,s]}class Yc extends K{constructor(t){super(),Z(this,t,qc,Gc,U,{})}}const Da=Yc;function Uc(e){let t;const n=e[2].default,i=Pt(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Dt(i,n,s,s[3],t?Ot(n,s[3],o,null):Ft(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function Xc(e){let t,n;const i=[{name:"chevron-right"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[Uc]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=H(s,i[o]);return t=new fe({props:s}),{c(){I(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&te(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function Kc(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M9 6l6 6l-6 6"}]];return e.$$set=r=>{n(1,t=H(H({},t),et(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=et(t),[o,t,i,s]}class Qc extends K{constructor(t){super(),Z(this,t,Kc,Xc,U,{})}}const Zc=Qc;function Jc(e){let t;const n=e[2].default,i=Pt(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Dt(i,n,s,s[3],t?Ot(n,s[3],o,null):Ft(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function tu(e){let t,n;const i=[{name:"chevrons-left"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[Jc]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=H(s,i[o]);return t=new fe({props:s}),{c(){I(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&te(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function eu(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M11 7l-5 5l5 5"}],["path",{d:"M17 7l-5 5l5 5"}]];return e.$$set=r=>{n(1,t=H(H({},t),et(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=et(t),[o,t,i,s]}class nu extends K{constructor(t){super(),Z(this,t,eu,tu,U,{})}}const iu=nu;function su(e){let t;const n=e[2].default,i=Pt(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Dt(i,n,s,s[3],t?Ot(n,s[3],o,null):Ft(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function ou(e){let t,n;const i=[{name:"chevrons-right"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[su]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=H(s,i[o]);return t=new fe({props:s}),{c(){I(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&te(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function ru(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M7 7l5 5l-5 5"}],["path",{d:"M13 7l5 5l-5 5"}]];return e.$$set=r=>{n(1,t=H(H({},t),et(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=et(t),[o,t,i,s]}class au extends K{constructor(t){super(),Z(this,t,ru,ou,U,{})}}const lu=au,Ki=Et(!1),ys=Et("");let Ro;const cu=qs(ys,(e,t)=>(Ro=setTimeout(()=>t(e),500),()=>clearTimeout(Ro))),xs=Et([]),uu=qs([xs,cu],([e,t],n)=>{if(t===""||!t)return n(e);const i=t.toLowerCase();return n(e.filter(s=>s.toLowerCase().includes(i)))}),vs=Et({queries:0,timeQuerying:0,slowQueries:0}),ws=Et({labels:[],data:[]}),ii=Et([]),Ss=Et({resourceQueriesCount:0,resourceSlowQueries:0,resourceTime:0}),Bt=Et({search:"",page:0});function fu(e){let t,n,i,s,o,r,a,l,c,u,f,d=e[1].page+1+"",h,g,p,m,_,b,x,v,y,k,M,$,P,O;return i=new iu({}),a=new Da({}),b=new Zc({}),k=new lu({}),{c(){t=D("div"),n=D("button"),I(i.$$.fragment),o=Y(),r=D("button"),I(a.$$.fragment),c=Y(),u=D("p"),f=J("Page "),h=J(d),g=J(" of "),p=J(e[0]),m=Y(),_=D("button"),I(b.$$.fragment),v=Y(),y=D("button"),I(k.$$.fragment),n.disabled=s=e[1].page===0,F(n,"class","bg-dark-600 disabled:bg-dark-300 disabled:text-dark-400 text-dark-100 hover:bg-dark-500 rounded-md border-[1px] border-transparent p-2 outline-none hover:text-white focus-visible:border-cyan-600 focus-visible:text-white active:translate-y-[3px] disabled:cursor-not-allowed"),r.disabled=l=e[1].page===0,F(r,"class","bg-dark-600 disabled:bg-dark-300 disabled:text-dark-400 text-dark-100 hover:bg-dark-500 rounded-md border-[1px] border-transparent p-2 outline-none hover:text-white focus-visible:border-cyan-600 focus-visible:text-white active:translate-y-[3px] disabled:cursor-not-allowed"),_.disabled=x=e[1].page>=e[0]-1,F(_,"class","bg-dark-600 disabled:bg-dark-300 disabled:text-dark-400 text-dark-100 hover:bg-dark-500 rounded-md border-[1px] border-transparent p-2 outline-none hover:text-white focus-visible:border-cyan-600 focus-visible:text-white active:translate-y-[3px] disabled:cursor-not-allowed"),y.disabled=M=e[1].page===e[0]-1,F(y,"class","bg-dark-600 disabled:bg-dark-300 disabled:text-dark-400 text-dark-100 hover:bg-dark-500 rounded-md border-[1px] border-transparent p-2 outline-none hover:text-white focus-visible:border-cyan-600 focus-visible:text-white active:translate-y-[3px] disabled:cursor-not-allowed"),F(t,"class","flex items-center justify-center gap-6 pb-5")},m(R,z){nt(R,t,z),w(t,n),E(i,n,null),w(t,o),w(t,r),E(a,r,null),w(t,c),w(t,u),w(u,f),w(u,h),w(u,g),w(u,p),w(t,m),w(t,_),E(b,_,null),w(t,v),w(t,y),E(k,y,null),$=!0,P||(O=[It(n,"click",e[2]),It(r,"click",e[3]),It(_,"click",e[4]),It(y,"click",e[5])],P=!0)},p(R,[z]){(!$||z&2&&s!==(s=R[1].page===0))&&(n.disabled=s),(!$||z&2&&l!==(l=R[1].page===0))&&(r.disabled=l),(!$||z&2)&&d!==(d=R[1].page+1+"")&&Wt(h,d),(!$||z&1)&&Wt(p,R[0]),(!$||z&3&&x!==(x=R[1].page>=R[0]-1))&&(_.disabled=x),(!$||z&3&&M!==(M=R[1].page===R[0]-1))&&(y.disabled=M)},i(R){$||(S(i.$$.fragment,R),S(a.$$.fragment,R),S(b.$$.fragment,R),S(k.$$.fragment,R),$=!0)},o(R){C(i.$$.fragment,R),C(a.$$.fragment,R),C(b.$$.fragment,R),C(k.$$.fragment,R),$=!1},d(R){R&&X(t),L(i),L(a),L(b),L(k),P=!1,Jt(O)}}}function du(e,t,n){let i;pt(e,Bt,c=>n(1,i=c));let{maxPage:s}=t;rn(()=>n(0,s=0));const o=()=>_t(Bt,i.page=0,i),r=()=>_t(Bt,i.page--,i),a=()=>_t(Bt,i.page++,i),l=()=>_t(Bt,i.page=s-1,i);return e.$$set=c=>{"maxPage"in c&&n(0,s=c.maxPage)},[s,i,o,r,a,l]}let hu=class extends K{constructor(t){super(),Z(this,t,du,fu,U,{maxPage:0})}};function gu(e){let t;const n=e[2].default,i=Pt(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Dt(i,n,s,s[3],t?Ot(n,s[3],o,null):Ft(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function pu(e){let t,n;const i=[{name:"chevron-down"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[gu]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=H(s,i[o]);return t=new fe({props:s}),{c(){I(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&te(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function mu(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M6 9l6 6l6 -6"}]];return e.$$set=r=>{n(1,t=H(H({},t),et(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=et(t),[o,t,i,s]}class _u extends K{constructor(t){super(),Z(this,t,mu,pu,U,{})}}const bu=_u;function yu(e){let t;const n=e[2].default,i=Pt(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Dt(i,n,s,s[3],t?Ot(n,s[3],o,null):Ft(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function xu(e){let t,n;const i=[{name:"chevron-up"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[yu]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=H(s,i[o]);return t=new fe({props:s}),{c(){I(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&te(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function vu(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M6 15l6 -6l6 6"}]];return e.$$set=r=>{n(1,t=H(H({},t),et(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=et(t),[o,t,i,s]}class wu extends K{constructor(t){super(),Z(this,t,vu,xu,U,{})}}const Su=wu;/**
 * table-core
 *
 * Copyright (c) TanStack
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function pe(e,t){return typeof e=="function"?e(t):e}function Vt(e,t){return n=>{t.setState(i=>({...i,[e]:pe(n,i[e])}))}}function pi(e){return e instanceof Function}function Cu(e,t){const n=[],i=s=>{s.forEach(o=>{n.push(o);const r=t(o);r!=null&&r.length&&i(r)})};return i(e),n}function T(e,t,n){let i=[],s;return()=>{let o;n.key&&n.debug&&(o=Date.now());const r=e();if(!(r.length!==i.length||r.some((c,u)=>i[u]!==c)))return s;i=r;let l;if(n.key&&n.debug&&(l=Date.now()),s=t(...r),n==null||n.onChange==null||n.onChange(s),n.key&&n.debug&&n!=null&&n.debug()){const c=Math.round((Date.now()-o)*100)/100,u=Math.round((Date.now()-l)*100)/100,f=u/16,d=(h,g)=>{for(h=String(h);h.length<g;)h=" "+h;return h};console.info(`%c⏱ ${d(u,5)} /${d(c,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*f,120))}deg 100% 31%);`,n==null?void 0:n.key)}return s}}function Mu(e,t,n,i){var s,o;const a={...e._getDefaultColumnDef(),...t},l=a.accessorKey;let c=(s=(o=a.id)!=null?o:l?l.replace(".","_"):void 0)!=null?s:typeof a.header=="string"?a.header:void 0,u;if(a.accessorFn?u=a.accessorFn:l&&(l.includes(".")?u=d=>{let h=d;for(const p of l.split(".")){var g;h=(g=h)==null?void 0:g[p]}return h}:u=d=>d[a.accessorKey]),!c)throw new Error;let f={id:`${String(c)}`,accessorFn:u,parent:i,depth:n,columnDef:a,columns:[],getFlatColumns:T(()=>[!0],()=>{var d;return[f,...(d=f.columns)==null?void 0:d.flatMap(h=>h.getFlatColumns())]},{key:"column.getFlatColumns",debug:()=>{var d;return(d=e.options.debugAll)!=null?d:e.options.debugColumns}}),getLeafColumns:T(()=>[e._getOrderColumnsFn()],d=>{var h;if((h=f.columns)!=null&&h.length){let g=f.columns.flatMap(p=>p.getLeafColumns());return d(g)}return[f]},{key:"column.getLeafColumns",debug:()=>{var d;return(d=e.options.debugAll)!=null?d:e.options.debugColumns}})};return f=e._features.reduce((d,h)=>Object.assign(d,h.createColumn==null?void 0:h.createColumn(f,e)),f),f}function $o(e,t,n){var i;let o={id:(i=n.id)!=null?i:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{const r=[],a=l=>{l.subHeaders&&l.subHeaders.length&&l.subHeaders.map(a),r.push(l)};return a(o),r},getContext:()=>({table:e,header:o,column:t})};return e._features.forEach(r=>{Object.assign(o,r.createHeader==null?void 0:r.createHeader(o,e))}),o}const ku={createTable:e=>({getHeaderGroups:T(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,i,s)=>{var o,r;const a=(o=i==null?void 0:i.map(f=>n.find(d=>d.id===f)).filter(Boolean))!=null?o:[],l=(r=s==null?void 0:s.map(f=>n.find(d=>d.id===f)).filter(Boolean))!=null?r:[],c=n.filter(f=>!(i!=null&&i.includes(f.id))&&!(s!=null&&s.includes(f.id)));return In(t,[...a,...c,...l],e)},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getCenterHeaderGroups:T(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,i,s)=>(n=n.filter(o=>!(i!=null&&i.includes(o.id))&&!(s!=null&&s.includes(o.id))),In(t,n,e,"center")),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getLeftHeaderGroups:T(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,i)=>{var s;const o=(s=i==null?void 0:i.map(r=>n.find(a=>a.id===r)).filter(Boolean))!=null?s:[];return In(t,o,e,"left")},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getRightHeaderGroups:T(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,i)=>{var s;const o=(s=i==null?void 0:i.map(r=>n.find(a=>a.id===r)).filter(Boolean))!=null?s:[];return In(t,o,e,"right")},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getFooterGroups:T(()=>[e.getHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getLeftFooterGroups:T(()=>[e.getLeftHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getCenterFooterGroups:T(()=>[e.getCenterHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getRightFooterGroups:T(()=>[e.getRightHeaderGroups()],t=>[...t].reverse(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getFlatHeaders:T(()=>[e.getHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getLeftFlatHeaders:T(()=>[e.getLeftHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getCenterFlatHeaders:T(()=>[e.getCenterHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getRightFlatHeaders:T(()=>[e.getRightHeaderGroups()],t=>t.map(n=>n.headers).flat(),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getCenterLeafHeaders:T(()=>[e.getCenterFlatHeaders()],t=>t.filter(n=>{var i;return!((i=n.subHeaders)!=null&&i.length)}),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getLeftLeafHeaders:T(()=>[e.getLeftFlatHeaders()],t=>t.filter(n=>{var i;return!((i=n.subHeaders)!=null&&i.length)}),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getRightLeafHeaders:T(()=>[e.getRightFlatHeaders()],t=>t.filter(n=>{var i;return!((i=n.subHeaders)!=null&&i.length)}),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}}),getLeafHeaders:T(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(t,n,i)=>{var s,o,r,a,l,c;return[...(s=(o=t[0])==null?void 0:o.headers)!=null?s:[],...(r=(a=n[0])==null?void 0:a.headers)!=null?r:[],...(l=(c=i[0])==null?void 0:c.headers)!=null?l:[]].map(u=>u.getLeafHeaders()).flat()},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugHeaders}})})};function In(e,t,n,i){var s,o;let r=0;const a=function(d,h){h===void 0&&(h=1),r=Math.max(r,h),d.filter(g=>g.getIsVisible()).forEach(g=>{var p;(p=g.columns)!=null&&p.length&&a(g.columns,h+1)},0)};a(e);let l=[];const c=(d,h)=>{const g={depth:h,id:[i,`${h}`].filter(Boolean).join("_"),headers:[]},p=[];d.forEach(m=>{const _=[...p].reverse()[0],b=m.column.depth===g.depth;let x,v=!1;if(b&&m.column.parent?x=m.column.parent:(x=m.column,v=!0),_&&(_==null?void 0:_.column)===x)_.subHeaders.push(m);else{const y=$o(n,x,{id:[i,h,x.id,m==null?void 0:m.id].filter(Boolean).join("_"),isPlaceholder:v,placeholderId:v?`${p.filter(k=>k.column===x).length}`:void 0,depth:h,index:p.length});y.subHeaders.push(m),p.push(y)}g.headers.push(m),m.headerGroup=g}),l.push(g),h>0&&c(p,h-1)},u=t.map((d,h)=>$o(n,d,{depth:r,index:h}));c(u,r-1),l.reverse();const f=d=>d.filter(g=>g.column.getIsVisible()).map(g=>{let p=0,m=0,_=[0];g.subHeaders&&g.subHeaders.length?(_=[],f(g.subHeaders).forEach(x=>{let{colSpan:v,rowSpan:y}=x;p+=v,_.push(y)})):p=1;const b=Math.min(..._);return m=m+b,g.colSpan=p,g.rowSpan=m,{colSpan:p,rowSpan:m}});return f((s=(o=l[0])==null?void 0:o.headers)!=null?s:[]),l}const Vn={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},Qi=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),Ru={getDefaultColumnDef:()=>Vn,getInitialState:e=>({columnSizing:{},columnSizingInfo:Qi(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",onColumnSizingChange:Vt("columnSizing",e),onColumnSizingInfoChange:Vt("columnSizingInfo",e)}),createColumn:(e,t)=>({getSize:()=>{var n,i,s;const o=t.getState().columnSizing[e.id];return Math.min(Math.max((n=e.columnDef.minSize)!=null?n:Vn.minSize,(i=o??e.columnDef.size)!=null?i:Vn.size),(s=e.columnDef.maxSize)!=null?s:Vn.maxSize)},getStart:n=>{const i=n?n==="left"?t.getLeftVisibleLeafColumns():t.getRightVisibleLeafColumns():t.getVisibleLeafColumns(),s=i.findIndex(o=>o.id===e.id);if(s>0){const o=i[s-1];return o.getStart(n)+o.getSize()}return 0},resetSize:()=>{t.setColumnSizing(n=>{let{[e.id]:i,...s}=n;return s})},getCanResize:()=>{var n,i;return((n=e.columnDef.enableResizing)!=null?n:!0)&&((i=t.options.enableColumnResizing)!=null?i:!0)},getIsResizing:()=>t.getState().columnSizingInfo.isResizingColumn===e.id}),createHeader:(e,t)=>({getSize:()=>{let n=0;const i=s=>{if(s.subHeaders.length)s.subHeaders.forEach(i);else{var o;n+=(o=s.column.getSize())!=null?o:0}};return i(e),n},getStart:()=>{if(e.index>0){const n=e.headerGroup.headers[e.index-1];return n.getStart()+n.getSize()}return 0},getResizeHandler:()=>{const n=t.getColumn(e.column.id),i=n==null?void 0:n.getCanResize();return s=>{if(!n||!i||(s.persist==null||s.persist(),Zi(s)&&s.touches&&s.touches.length>1))return;const o=e.getSize(),r=e?e.getLeafHeaders().map(p=>[p.column.id,p.column.getSize()]):[[n.id,n.getSize()]],a=Zi(s)?Math.round(s.touches[0].clientX):s.clientX,l={},c=(p,m)=>{typeof m=="number"&&(t.setColumnSizingInfo(_=>{var b,x;const v=m-((b=_==null?void 0:_.startOffset)!=null?b:0),y=Math.max(v/((x=_==null?void 0:_.startSize)!=null?x:0),-.999999);return _.columnSizingStart.forEach(k=>{let[M,$]=k;l[M]=Math.round(Math.max($+$*y,0)*100)/100}),{..._,deltaOffset:v,deltaPercentage:y}}),(t.options.columnResizeMode==="onChange"||p==="end")&&t.setColumnSizing(_=>({..._,...l})))},u=p=>c("move",p),f=p=>{c("end",p),t.setColumnSizingInfo(m=>({...m,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},d={moveHandler:p=>u(p.clientX),upHandler:p=>{document.removeEventListener("mousemove",d.moveHandler),document.removeEventListener("mouseup",d.upHandler),f(p.clientX)}},h={moveHandler:p=>(p.cancelable&&(p.preventDefault(),p.stopPropagation()),u(p.touches[0].clientX),!1),upHandler:p=>{var m;document.removeEventListener("touchmove",h.moveHandler),document.removeEventListener("touchend",h.upHandler),p.cancelable&&(p.preventDefault(),p.stopPropagation()),f((m=p.touches[0])==null?void 0:m.clientX)}},g=$u()?{passive:!1}:!1;Zi(s)?(document.addEventListener("touchmove",h.moveHandler,g),document.addEventListener("touchend",h.upHandler,g)):(document.addEventListener("mousemove",d.moveHandler,g),document.addEventListener("mouseup",d.upHandler,g)),t.setColumnSizingInfo(p=>({...p,startOffset:a,startSize:o,deltaOffset:0,deltaPercentage:0,columnSizingStart:r,isResizingColumn:n.id}))}}}),createTable:e=>({setColumnSizing:t=>e.options.onColumnSizingChange==null?void 0:e.options.onColumnSizingChange(t),setColumnSizingInfo:t=>e.options.onColumnSizingInfoChange==null?void 0:e.options.onColumnSizingInfoChange(t),resetColumnSizing:t=>{var n;e.setColumnSizing(t?{}:(n=e.initialState.columnSizing)!=null?n:{})},resetHeaderSizeInfo:t=>{var n;e.setColumnSizingInfo(t?Qi():(n=e.initialState.columnSizingInfo)!=null?n:Qi())},getTotalSize:()=>{var t,n;return(t=(n=e.getHeaderGroups()[0])==null?void 0:n.headers.reduce((i,s)=>i+s.getSize(),0))!=null?t:0},getLeftTotalSize:()=>{var t,n;return(t=(n=e.getLeftHeaderGroups()[0])==null?void 0:n.headers.reduce((i,s)=>i+s.getSize(),0))!=null?t:0},getCenterTotalSize:()=>{var t,n;return(t=(n=e.getCenterHeaderGroups()[0])==null?void 0:n.headers.reduce((i,s)=>i+s.getSize(),0))!=null?t:0},getRightTotalSize:()=>{var t,n;return(t=(n=e.getRightHeaderGroups()[0])==null?void 0:n.headers.reduce((i,s)=>i+s.getSize(),0))!=null?t:0}})};let zn=null;function $u(){if(typeof zn=="boolean")return zn;let e=!1;try{const t={get passive(){return e=!0,!1}},n=()=>{};window.addEventListener("test",n,t),window.removeEventListener("test",n)}catch{e=!1}return zn=e,zn}function Zi(e){return e.type==="touchstart"}const Au={getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:Vt("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;return{_autoResetExpanded:()=>{var i,s;if(!t){e._queue(()=>{t=!0});return}if((i=(s=e.options.autoResetAll)!=null?s:e.options.autoResetExpanded)!=null?i:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},setExpanded:i=>e.options.onExpandedChange==null?void 0:e.options.onExpandedChange(i),toggleAllRowsExpanded:i=>{i??!e.getIsAllRowsExpanded()?e.setExpanded(!0):e.setExpanded({})},resetExpanded:i=>{var s,o;e.setExpanded(i?{}:(s=(o=e.initialState)==null?void 0:o.expanded)!=null?s:{})},getCanSomeRowsExpand:()=>e.getRowModel().flatRows.some(i=>i.getCanExpand()),getToggleAllRowsExpandedHandler:()=>i=>{i.persist==null||i.persist(),e.toggleAllRowsExpanded()},getIsSomeRowsExpanded:()=>{const i=e.getState().expanded;return i===!0||Object.values(i).some(Boolean)},getIsAllRowsExpanded:()=>{const i=e.getState().expanded;return typeof i=="boolean"?i===!0:!(!Object.keys(i).length||e.getRowModel().flatRows.some(s=>!s.getIsExpanded()))},getExpandedDepth:()=>{let i=0;return(e.getState().expanded===!0?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(o=>{const r=o.split(".");i=Math.max(i,r.length)}),i},getPreExpandedRowModel:()=>e.getSortedRowModel(),getExpandedRowModel:()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel?e.getPreExpandedRowModel():e._getExpandedRowModel())}},createRow:(e,t)=>({toggleExpanded:n=>{t.setExpanded(i=>{var s;const o=i===!0?!0:!!(i!=null&&i[e.id]);let r={};if(i===!0?Object.keys(t.getRowModel().rowsById).forEach(a=>{r[a]=!0}):r=i,n=(s=n)!=null?s:!o,!o&&n)return{...r,[e.id]:!0};if(o&&!n){const{[e.id]:a,...l}=r;return l}return i})},getIsExpanded:()=>{var n;const i=t.getState().expanded;return!!((n=t.options.getIsRowExpanded==null?void 0:t.options.getIsRowExpanded(e))!=null?n:i===!0||i!=null&&i[e.id])},getCanExpand:()=>{var n,i,s;return(n=t.options.getRowCanExpand==null?void 0:t.options.getRowCanExpand(e))!=null?n:((i=t.options.enableExpanding)!=null?i:!0)&&!!((s=e.subRows)!=null&&s.length)},getToggleExpandedHandler:()=>{const n=e.getCanExpand();return()=>{n&&e.toggleExpanded()}}})},Fa=(e,t,n)=>{var i;const s=n.toLowerCase();return!!((i=e.getValue(t))!=null&&i.toLowerCase().includes(s))};Fa.autoRemove=e=>Gt(e);const Ea=(e,t,n)=>{var i;return!!((i=e.getValue(t))!=null&&i.includes(n))};Ea.autoRemove=e=>Gt(e);const La=(e,t,n)=>{var i;return((i=e.getValue(t))==null?void 0:i.toLowerCase())===n.toLowerCase()};La.autoRemove=e=>Gt(e);const Ta=(e,t,n)=>{var i;return(i=e.getValue(t))==null?void 0:i.includes(n)};Ta.autoRemove=e=>Gt(e)||!(e!=null&&e.length);const Ia=(e,t,n)=>!n.some(i=>{var s;return!((s=e.getValue(t))!=null&&s.includes(i))});Ia.autoRemove=e=>Gt(e)||!(e!=null&&e.length);const Va=(e,t,n)=>n.some(i=>{var s;return(s=e.getValue(t))==null?void 0:s.includes(i)});Va.autoRemove=e=>Gt(e)||!(e!=null&&e.length);const za=(e,t,n)=>e.getValue(t)===n;za.autoRemove=e=>Gt(e);const Ha=(e,t,n)=>e.getValue(t)==n;Ha.autoRemove=e=>Gt(e);const Xs=(e,t,n)=>{let[i,s]=n;const o=e.getValue(t);return o>=i&&o<=s};Xs.resolveFilterValue=e=>{let[t,n]=e,i=typeof t!="number"?parseFloat(t):t,s=typeof n!="number"?parseFloat(n):n,o=t===null||Number.isNaN(i)?-1/0:i,r=n===null||Number.isNaN(s)?1/0:s;if(o>r){const a=o;o=r,r=a}return[o,r]};Xs.autoRemove=e=>Gt(e)||Gt(e[0])&&Gt(e[1]);const se={includesString:Fa,includesStringSensitive:Ea,equalsString:La,arrIncludes:Ta,arrIncludesAll:Ia,arrIncludesSome:Va,equals:za,weakEquals:Ha,inNumberRange:Xs};function Gt(e){return e==null||e===""}const Pu={getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],globalFilter:void 0,...e}),getDefaultOptions:e=>({onColumnFiltersChange:Vt("columnFilters",e),onGlobalFilterChange:Vt("globalFilter",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100,globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n,i;const s=(n=e.getCoreRowModel().flatRows[0])==null||(i=n._getAllCellsByColumnId()[t.id])==null?void 0:i.getValue();return typeof s=="string"||typeof s=="number"}}),createColumn:(e,t)=>({getAutoFilterFn:()=>{const n=t.getCoreRowModel().flatRows[0],i=n==null?void 0:n.getValue(e.id);return typeof i=="string"?se.includesString:typeof i=="number"?se.inNumberRange:typeof i=="boolean"||i!==null&&typeof i=="object"?se.equals:Array.isArray(i)?se.arrIncludes:se.weakEquals},getFilterFn:()=>{var n,i;return pi(e.columnDef.filterFn)?e.columnDef.filterFn:e.columnDef.filterFn==="auto"?e.getAutoFilterFn():(n=(i=t.options.filterFns)==null?void 0:i[e.columnDef.filterFn])!=null?n:se[e.columnDef.filterFn]},getCanFilter:()=>{var n,i,s;return((n=e.columnDef.enableColumnFilter)!=null?n:!0)&&((i=t.options.enableColumnFilters)!=null?i:!0)&&((s=t.options.enableFilters)!=null?s:!0)&&!!e.accessorFn},getCanGlobalFilter:()=>{var n,i,s,o;return((n=e.columnDef.enableGlobalFilter)!=null?n:!0)&&((i=t.options.enableGlobalFilter)!=null?i:!0)&&((s=t.options.enableFilters)!=null?s:!0)&&((o=t.options.getColumnCanGlobalFilter==null?void 0:t.options.getColumnCanGlobalFilter(e))!=null?o:!0)&&!!e.accessorFn},getIsFiltered:()=>e.getFilterIndex()>-1,getFilterValue:()=>{var n,i;return(n=t.getState().columnFilters)==null||(i=n.find(s=>s.id===e.id))==null?void 0:i.value},getFilterIndex:()=>{var n,i;return(n=(i=t.getState().columnFilters)==null?void 0:i.findIndex(s=>s.id===e.id))!=null?n:-1},setFilterValue:n=>{t.setColumnFilters(i=>{const s=e.getFilterFn(),o=i==null?void 0:i.find(u=>u.id===e.id),r=pe(n,o?o.value:void 0);if(Ao(s,r,e)){var a;return(a=i==null?void 0:i.filter(u=>u.id!==e.id))!=null?a:[]}const l={id:e.id,value:r};if(o){var c;return(c=i==null?void 0:i.map(u=>u.id===e.id?l:u))!=null?c:[]}return i!=null&&i.length?[...i,l]:[l]})},_getFacetedRowModel:t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),getFacetedRowModel:()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),_getFacetedUniqueValues:t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),getFacetedUniqueValues:()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,_getFacetedMinMaxValues:t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),getFacetedMinMaxValues:()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}),createRow:(e,t)=>({columnFilters:{},columnFiltersMeta:{}}),createTable:e=>({getGlobalAutoFilterFn:()=>se.includesString,getGlobalFilterFn:()=>{var t,n;const{globalFilterFn:i}=e.options;return pi(i)?i:i==="auto"?e.getGlobalAutoFilterFn():(t=(n=e.options.filterFns)==null?void 0:n[i])!=null?t:se[i]},setColumnFilters:t=>{const n=e.getAllLeafColumns(),i=s=>{var o;return(o=pe(t,s))==null?void 0:o.filter(r=>{const a=n.find(l=>l.id===r.id);if(a){const l=a.getFilterFn();if(Ao(l,r.value,a))return!1}return!0})};e.options.onColumnFiltersChange==null||e.options.onColumnFiltersChange(i)},setGlobalFilter:t=>{e.options.onGlobalFilterChange==null||e.options.onGlobalFilterChange(t)},resetGlobalFilter:t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)},resetColumnFilters:t=>{var n,i;e.setColumnFilters(t?[]:(n=(i=e.initialState)==null?void 0:i.columnFilters)!=null?n:[])},getPreFilteredRowModel:()=>e.getCoreRowModel(),getFilteredRowModel:()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel?e.getPreFilteredRowModel():e._getFilteredRowModel()),_getGlobalFacetedRowModel:e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),getGlobalFacetedRowModel:()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),_getGlobalFacetedUniqueValues:e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),getGlobalFacetedUniqueValues:()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,_getGlobalFacetedMinMaxValues:e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),getGlobalFacetedMinMaxValues:()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}})};function Ao(e,t,n){return(e&&e.autoRemove?e.autoRemove(t,n):!1)||typeof t>"u"||typeof t=="string"&&!t}const Ou=(e,t,n)=>n.reduce((i,s)=>{const o=s.getValue(e);return i+(typeof o=="number"?o:0)},0),Du=(e,t,n)=>{let i;return n.forEach(s=>{const o=s.getValue(e);o!=null&&(i>o||i===void 0&&o>=o)&&(i=o)}),i},Fu=(e,t,n)=>{let i;return n.forEach(s=>{const o=s.getValue(e);o!=null&&(i<o||i===void 0&&o>=o)&&(i=o)}),i},Eu=(e,t,n)=>{let i,s;return n.forEach(o=>{const r=o.getValue(e);r!=null&&(i===void 0?r>=r&&(i=s=r):(i>r&&(i=r),s<r&&(s=r)))}),[i,s]},Lu=(e,t)=>{let n=0,i=0;if(t.forEach(s=>{let o=s.getValue(e);o!=null&&(o=+o)>=o&&(++n,i+=o)}),n)return i/n},Tu=(e,t)=>{if(!t.length)return;let n=0,i=0;return t.forEach(s=>{let o=s.getValue(e);typeof o=="number"&&(n=Math.min(n,o),i=Math.max(i,o))}),(n+i)/2},Iu=(e,t)=>Array.from(new Set(t.map(n=>n.getValue(e))).values()),Vu=(e,t)=>new Set(t.map(n=>n.getValue(e))).size,zu=(e,t)=>t.length,Ji={sum:Ou,min:Du,max:Fu,extent:Eu,mean:Lu,median:Tu,unique:Iu,uniqueCount:Vu,count:zu},Hu={getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return(t=(n=e.getValue())==null||n.toString==null?void 0:n.toString())!=null?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:Vt("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>({toggleGrouping:()=>{t.setGrouping(n=>n!=null&&n.includes(e.id)?n.filter(i=>i!==e.id):[...n??[],e.id])},getCanGroup:()=>{var n,i,s,o;return(n=(i=(s=(o=e.columnDef.enableGrouping)!=null?o:!0)!=null?s:t.options.enableGrouping)!=null?i:!0)!=null?n:!!e.accessorFn},getIsGrouped:()=>{var n;return(n=t.getState().grouping)==null?void 0:n.includes(e.id)},getGroupedIndex:()=>{var n;return(n=t.getState().grouping)==null?void 0:n.indexOf(e.id)},getToggleGroupingHandler:()=>{const n=e.getCanGroup();return()=>{n&&e.toggleGrouping()}},getAutoAggregationFn:()=>{const n=t.getCoreRowModel().flatRows[0],i=n==null?void 0:n.getValue(e.id);if(typeof i=="number")return Ji.sum;if(Object.prototype.toString.call(i)==="[object Date]")return Ji.extent},getAggregationFn:()=>{var n,i;if(!e)throw new Error;return pi(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:e.columnDef.aggregationFn==="auto"?e.getAutoAggregationFn():(n=(i=t.options.aggregationFns)==null?void 0:i[e.columnDef.aggregationFn])!=null?n:Ji[e.columnDef.aggregationFn]}}),createTable:e=>({setGrouping:t=>e.options.onGroupingChange==null?void 0:e.options.onGroupingChange(t),resetGrouping:t=>{var n,i;e.setGrouping(t?[]:(n=(i=e.initialState)==null?void 0:i.grouping)!=null?n:[])},getPreGroupedRowModel:()=>e.getFilteredRowModel(),getGroupedRowModel:()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel?e.getPreGroupedRowModel():e._getGroupedRowModel())}),createRow:e=>({getIsGrouped:()=>!!e.groupingColumnId,_groupingValuesCache:{}}),createCell:(e,t,n,i)=>({getIsGrouped:()=>t.getIsGrouped()&&t.id===n.groupingColumnId,getIsPlaceholder:()=>!e.getIsGrouped()&&t.getIsGrouped(),getIsAggregated:()=>{var s;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!((s=n.subRows)!=null&&s.length)}})};function Nu(e,t,n){if(!(t!=null&&t.length)||!n)return e;const i=e.filter(o=>!t.includes(o.id));return n==="remove"?i:[...t.map(o=>e.find(r=>r.id===o)).filter(Boolean),...i]}const Bu={getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:Vt("columnOrder",e)}),createTable:e=>({setColumnOrder:t=>e.options.onColumnOrderChange==null?void 0:e.options.onColumnOrderChange(t),resetColumnOrder:t=>{var n;e.setColumnOrder(t?[]:(n=e.initialState.columnOrder)!=null?n:[])},_getOrderColumnsFn:T(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(t,n,i)=>s=>{let o=[];if(!(t!=null&&t.length))o=s;else{const r=[...t],a=[...s];for(;a.length&&r.length;){const l=r.shift(),c=a.findIndex(u=>u.id===l);c>-1&&o.push(a.splice(c,1)[0])}o=[...o,...a]}return Nu(o,n,i)},{key:!1})})},Cs=0,Ms=10,ts=()=>({pageIndex:Cs,pageSize:Ms}),ju={getInitialState:e=>({...e,pagination:{...ts(),...e==null?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:Vt("pagination",e)}),createTable:e=>{let t=!1,n=!1;return{_autoResetPageIndex:()=>{var i,s;if(!t){e._queue(()=>{t=!0});return}if((i=(s=e.options.autoResetAll)!=null?s:e.options.autoResetPageIndex)!=null?i:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},setPagination:i=>{const s=o=>pe(i,o);return e.options.onPaginationChange==null?void 0:e.options.onPaginationChange(s)},resetPagination:i=>{var s;e.setPagination(i?ts():(s=e.initialState.pagination)!=null?s:ts())},setPageIndex:i=>{e.setPagination(s=>{let o=pe(i,s.pageIndex);const r=typeof e.options.pageCount>"u"||e.options.pageCount===-1?Number.MAX_SAFE_INTEGER:e.options.pageCount-1;return o=Math.max(0,Math.min(o,r)),{...s,pageIndex:o}})},resetPageIndex:i=>{var s,o,r;e.setPageIndex(i?Cs:(s=(o=e.initialState)==null||(r=o.pagination)==null?void 0:r.pageIndex)!=null?s:Cs)},resetPageSize:i=>{var s,o,r;e.setPageSize(i?Ms:(s=(o=e.initialState)==null||(r=o.pagination)==null?void 0:r.pageSize)!=null?s:Ms)},setPageSize:i=>{e.setPagination(s=>{const o=Math.max(1,pe(i,s.pageSize)),r=s.pageSize*s.pageIndex,a=Math.floor(r/o);return{...s,pageIndex:a,pageSize:o}})},setPageCount:i=>e.setPagination(s=>{var o;let r=pe(i,(o=e.options.pageCount)!=null?o:-1);return typeof r=="number"&&(r=Math.max(-1,r)),{...s,pageCount:r}}),getPageOptions:T(()=>[e.getPageCount()],i=>{let s=[];return i&&i>0&&(s=[...new Array(i)].fill(null).map((o,r)=>r)),s},{key:!1,debug:()=>{var i;return(i=e.options.debugAll)!=null?i:e.options.debugTable}}),getCanPreviousPage:()=>e.getState().pagination.pageIndex>0,getCanNextPage:()=>{const{pageIndex:i}=e.getState().pagination,s=e.getPageCount();return s===-1?!0:s===0?!1:i<s-1},previousPage:()=>e.setPageIndex(i=>i-1),nextPage:()=>e.setPageIndex(i=>i+1),getPrePaginationRowModel:()=>e.getExpandedRowModel(),getPaginationRowModel:()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel?e.getPrePaginationRowModel():e._getPaginationRowModel()),getPageCount:()=>{var i;return(i=e.options.pageCount)!=null?i:Math.ceil(e.getPrePaginationRowModel().rows.length/e.getState().pagination.pageSize)}}}},es=()=>({left:[],right:[]}),Wu={getInitialState:e=>({columnPinning:es(),...e}),getDefaultOptions:e=>({onColumnPinningChange:Vt("columnPinning",e)}),createColumn:(e,t)=>({pin:n=>{const i=e.getLeafColumns().map(s=>s.id).filter(Boolean);t.setColumnPinning(s=>{var o,r;if(n==="right"){var a,l;return{left:((a=s==null?void 0:s.left)!=null?a:[]).filter(f=>!(i!=null&&i.includes(f))),right:[...((l=s==null?void 0:s.right)!=null?l:[]).filter(f=>!(i!=null&&i.includes(f))),...i]}}if(n==="left"){var c,u;return{left:[...((c=s==null?void 0:s.left)!=null?c:[]).filter(f=>!(i!=null&&i.includes(f))),...i],right:((u=s==null?void 0:s.right)!=null?u:[]).filter(f=>!(i!=null&&i.includes(f)))}}return{left:((o=s==null?void 0:s.left)!=null?o:[]).filter(f=>!(i!=null&&i.includes(f))),right:((r=s==null?void 0:s.right)!=null?r:[]).filter(f=>!(i!=null&&i.includes(f)))}})},getCanPin:()=>e.getLeafColumns().some(i=>{var s,o;return((s=i.columnDef.enablePinning)!=null?s:!0)&&((o=t.options.enablePinning)!=null?o:!0)}),getIsPinned:()=>{const n=e.getLeafColumns().map(a=>a.id),{left:i,right:s}=t.getState().columnPinning,o=n.some(a=>i==null?void 0:i.includes(a)),r=n.some(a=>s==null?void 0:s.includes(a));return o?"left":r?"right":!1},getPinnedIndex:()=>{var n,i,s;const o=e.getIsPinned();return o?(n=(i=t.getState().columnPinning)==null||(s=i[o])==null?void 0:s.indexOf(e.id))!=null?n:-1:0}}),createRow:(e,t)=>({getCenterVisibleCells:T(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(n,i,s)=>{const o=[...i??[],...s??[]];return n.filter(r=>!o.includes(r.column.id))},{key:"row.getCenterVisibleCells",debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}}),getLeftVisibleCells:T(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,,],(n,i)=>(i??[]).map(o=>n.find(r=>r.column.id===o)).filter(Boolean).map(o=>({...o,position:"left"})),{key:"row.getLeftVisibleCells",debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}}),getRightVisibleCells:T(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(n,i)=>(i??[]).map(o=>n.find(r=>r.column.id===o)).filter(Boolean).map(o=>({...o,position:"right"})),{key:"row.getRightVisibleCells",debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}})}),createTable:e=>({setColumnPinning:t=>e.options.onColumnPinningChange==null?void 0:e.options.onColumnPinningChange(t),resetColumnPinning:t=>{var n,i;return e.setColumnPinning(t?es():(n=(i=e.initialState)==null?void 0:i.columnPinning)!=null?n:es())},getIsSomeColumnsPinned:t=>{var n;const i=e.getState().columnPinning;if(!t){var s,o;return!!((s=i.left)!=null&&s.length||(o=i.right)!=null&&o.length)}return!!((n=i[t])!=null&&n.length)},getLeftLeafColumns:T(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(t,n)=>(n??[]).map(i=>t.find(s=>s.id===i)).filter(Boolean),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugColumns}}),getRightLeafColumns:T(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(t,n)=>(n??[]).map(i=>t.find(s=>s.id===i)).filter(Boolean),{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugColumns}}),getCenterLeafColumns:T(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,i)=>{const s=[...n??[],...i??[]];return t.filter(o=>!s.includes(o.id))},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugColumns}})})},Gu={getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:Vt("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>({setRowSelection:t=>e.options.onRowSelectionChange==null?void 0:e.options.onRowSelectionChange(t),resetRowSelection:t=>{var n;return e.setRowSelection(t?{}:(n=e.initialState.rowSelection)!=null?n:{})},toggleAllRowsSelected:t=>{e.setRowSelection(n=>{t=typeof t<"u"?t:!e.getIsAllRowsSelected();const i={...n},s=e.getPreGroupedRowModel().flatRows;return t?s.forEach(o=>{o.getCanSelect()&&(i[o.id]=!0)}):s.forEach(o=>{delete i[o.id]}),i})},toggleAllPageRowsSelected:t=>e.setRowSelection(n=>{const i=typeof t<"u"?t:!e.getIsAllPageRowsSelected(),s={...n};return e.getRowModel().rows.forEach(o=>{ks(s,o.id,i,e)}),s}),getPreSelectedRowModel:()=>e.getCoreRowModel(),getSelectedRowModel:T(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?ns(e,n):{rows:[],flatRows:[],rowsById:{}},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable}}),getFilteredSelectedRowModel:T(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?ns(e,n):{rows:[],flatRows:[],rowsById:{}},{key:"getFilteredSelectedRowModel",debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable}}),getGroupedSelectedRowModel:T(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?ns(e,n):{rows:[],flatRows:[],rowsById:{}},{key:"getGroupedSelectedRowModel",debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable}}),getIsAllRowsSelected:()=>{const t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState();let i=!!(t.length&&Object.keys(n).length);return i&&t.some(s=>s.getCanSelect()&&!n[s.id])&&(i=!1),i},getIsAllPageRowsSelected:()=>{const t=e.getPaginationRowModel().flatRows,{rowSelection:n}=e.getState();let i=!!t.length;return i&&t.some(s=>s.getCanSelect()&&!n[s.id])&&(i=!1),i},getIsSomeRowsSelected:()=>{var t;const n=Object.keys((t=e.getState().rowSelection)!=null?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},getIsSomePageRowsSelected:()=>{const t=e.getPaginationRowModel().flatRows;return e.getIsAllPageRowsSelected()?!1:t.some(n=>n.getIsSelected()||n.getIsSomeSelected())},getToggleAllRowsSelectedHandler:()=>t=>{e.toggleAllRowsSelected(t.target.checked)},getToggleAllPageRowsSelectedHandler:()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}}),createRow:(e,t)=>({toggleSelected:n=>{const i=e.getIsSelected();t.setRowSelection(s=>{if(n=typeof n<"u"?n:!i,i===n)return s;const o={...s};return ks(o,e.id,n,t),o})},getIsSelected:()=>{const{rowSelection:n}=t.getState();return Ks(e,n)},getIsSomeSelected:()=>{const{rowSelection:n}=t.getState();return Po(e,n)==="some"},getIsAllSubRowsSelected:()=>{const{rowSelection:n}=t.getState();return Po(e,n)==="all"},getCanSelect:()=>{var n;return typeof t.options.enableRowSelection=="function"?t.options.enableRowSelection(e):(n=t.options.enableRowSelection)!=null?n:!0},getCanSelectSubRows:()=>{var n;return typeof t.options.enableSubRowSelection=="function"?t.options.enableSubRowSelection(e):(n=t.options.enableSubRowSelection)!=null?n:!0},getCanMultiSelect:()=>{var n;return typeof t.options.enableMultiRowSelection=="function"?t.options.enableMultiRowSelection(e):(n=t.options.enableMultiRowSelection)!=null?n:!0},getToggleSelectedHandler:()=>{const n=e.getCanSelect();return i=>{var s;n&&e.toggleSelected((s=i.target)==null?void 0:s.checked)}}})},ks=(e,t,n,i)=>{var s;const o=i.getRow(t);n?(o.getCanMultiSelect()||Object.keys(e).forEach(r=>delete e[r]),o.getCanSelect()&&(e[t]=!0)):delete e[t],(s=o.subRows)!=null&&s.length&&o.getCanSelectSubRows()&&o.subRows.forEach(r=>ks(e,r.id,n,i))};function ns(e,t){const n=e.getState().rowSelection,i=[],s={},o=function(r,a){return r.map(l=>{var c;const u=Ks(l,n);if(u&&(i.push(l),s[l.id]=l),(c=l.subRows)!=null&&c.length&&(l={...l,subRows:o(l.subRows)}),u)return l}).filter(Boolean)};return{rows:o(t.rows),flatRows:i,rowsById:s}}function Ks(e,t){var n;return(n=t[e.id])!=null?n:!1}function Po(e,t,n){if(e.subRows&&e.subRows.length){let i=!0,s=!1;return e.subRows.forEach(o=>{s&&!i||(Ks(o,t)?s=!0:i=!1)}),i?"all":s?"some":!1}return!1}const Rs=/([0-9]+)/gm,qu=(e,t,n)=>Na(ve(e.getValue(n)).toLowerCase(),ve(t.getValue(n)).toLowerCase()),Yu=(e,t,n)=>Na(ve(e.getValue(n)),ve(t.getValue(n))),Uu=(e,t,n)=>Qs(ve(e.getValue(n)).toLowerCase(),ve(t.getValue(n)).toLowerCase()),Xu=(e,t,n)=>Qs(ve(e.getValue(n)),ve(t.getValue(n))),Ku=(e,t,n)=>{const i=e.getValue(n),s=t.getValue(n);return i>s?1:i<s?-1:0},Qu=(e,t,n)=>Qs(e.getValue(n),t.getValue(n));function Qs(e,t){return e===t?0:e>t?1:-1}function ve(e){return typeof e=="number"?isNaN(e)||e===1/0||e===-1/0?"":String(e):typeof e=="string"?e:""}function Na(e,t){const n=e.split(Rs).filter(Boolean),i=t.split(Rs).filter(Boolean);for(;n.length&&i.length;){const s=n.shift(),o=i.shift(),r=parseInt(s,10),a=parseInt(o,10),l=[r,a].sort();if(isNaN(l[0])){if(s>o)return 1;if(o>s)return-1;continue}if(isNaN(l[1]))return isNaN(r)?-1:1;if(r>a)return 1;if(a>r)return-1}return n.length-i.length}const cn={alphanumeric:qu,alphanumericCaseSensitive:Yu,text:Uu,textCaseSensitive:Xu,datetime:Ku,basic:Qu},Zu={getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto"}),getDefaultOptions:e=>({onSortingChange:Vt("sorting",e),isMultiSortEvent:t=>t.shiftKey}),createColumn:(e,t)=>({getAutoSortingFn:()=>{const n=t.getFilteredRowModel().flatRows.slice(10);let i=!1;for(const s of n){const o=s==null?void 0:s.getValue(e.id);if(Object.prototype.toString.call(o)==="[object Date]")return cn.datetime;if(typeof o=="string"&&(i=!0,o.split(Rs).length>1))return cn.alphanumeric}return i?cn.text:cn.basic},getAutoSortDir:()=>{const n=t.getFilteredRowModel().flatRows[0];return typeof(n==null?void 0:n.getValue(e.id))=="string"?"asc":"desc"},getSortingFn:()=>{var n,i;if(!e)throw new Error;return pi(e.columnDef.sortingFn)?e.columnDef.sortingFn:e.columnDef.sortingFn==="auto"?e.getAutoSortingFn():(n=(i=t.options.sortingFns)==null?void 0:i[e.columnDef.sortingFn])!=null?n:cn[e.columnDef.sortingFn]},toggleSorting:(n,i)=>{const s=e.getNextSortingOrder(),o=typeof n<"u"&&n!==null;t.setSorting(r=>{const a=r==null?void 0:r.find(h=>h.id===e.id),l=r==null?void 0:r.findIndex(h=>h.id===e.id);let c=[],u,f=o?n:s==="desc";if(r!=null&&r.length&&e.getCanMultiSort()&&i?a?u="toggle":u="add":r!=null&&r.length&&l!==r.length-1?u="replace":a?u="toggle":u="replace",u==="toggle"&&(o||s||(u="remove")),u==="add"){var d;c=[...r,{id:e.id,desc:f}],c.splice(0,c.length-((d=t.options.maxMultiSortColCount)!=null?d:Number.MAX_SAFE_INTEGER))}else u==="toggle"?c=r.map(h=>h.id===e.id?{...h,desc:f}:h):u==="remove"?c=r.filter(h=>h.id!==e.id):c=[{id:e.id,desc:f}];return c})},getFirstSortDir:()=>{var n,i;return((n=(i=e.columnDef.sortDescFirst)!=null?i:t.options.sortDescFirst)!=null?n:e.getAutoSortDir()==="desc")?"desc":"asc"},getNextSortingOrder:n=>{var i,s;const o=e.getFirstSortDir(),r=e.getIsSorted();return r?r!==o&&((i=t.options.enableSortingRemoval)==null||i)&&(!(n&&(s=t.options.enableMultiRemove)!=null)||s)?!1:r==="desc"?"asc":"desc":o},getCanSort:()=>{var n,i;return((n=e.columnDef.enableSorting)!=null?n:!0)&&((i=t.options.enableSorting)!=null?i:!0)&&!!e.accessorFn},getCanMultiSort:()=>{var n,i;return(n=(i=e.columnDef.enableMultiSort)!=null?i:t.options.enableMultiSort)!=null?n:!!e.accessorFn},getIsSorted:()=>{var n;const i=(n=t.getState().sorting)==null?void 0:n.find(s=>s.id===e.id);return i?i.desc?"desc":"asc":!1},getSortIndex:()=>{var n,i;return(n=(i=t.getState().sorting)==null?void 0:i.findIndex(s=>s.id===e.id))!=null?n:-1},clearSorting:()=>{t.setSorting(n=>n!=null&&n.length?n.filter(i=>i.id!==e.id):[])},getToggleSortingHandler:()=>{const n=e.getCanSort();return i=>{n&&(i.persist==null||i.persist(),e.toggleSorting==null||e.toggleSorting(void 0,e.getCanMultiSort()?t.options.isMultiSortEvent==null?void 0:t.options.isMultiSortEvent(i):!1))}}}),createTable:e=>({setSorting:t=>e.options.onSortingChange==null?void 0:e.options.onSortingChange(t),resetSorting:t=>{var n,i;e.setSorting(t?[]:(n=(i=e.initialState)==null?void 0:i.sorting)!=null?n:[])},getPreSortedRowModel:()=>e.getGroupedRowModel(),getSortedRowModel:()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel?e.getPreSortedRowModel():e._getSortedRowModel())})},Ju={getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:Vt("columnVisibility",e)}),createColumn:(e,t)=>({toggleVisibility:n=>{e.getCanHide()&&t.setColumnVisibility(i=>({...i,[e.id]:n??!e.getIsVisible()}))},getIsVisible:()=>{var n,i;return(n=(i=t.getState().columnVisibility)==null?void 0:i[e.id])!=null?n:!0},getCanHide:()=>{var n,i;return((n=e.columnDef.enableHiding)!=null?n:!0)&&((i=t.options.enableHiding)!=null?i:!0)},getToggleVisibilityHandler:()=>n=>{e.toggleVisibility==null||e.toggleVisibility(n.target.checked)}}),createRow:(e,t)=>({_getAllVisibleCells:T(()=>[e.getAllCells(),t.getState().columnVisibility],n=>n.filter(i=>i.column.getIsVisible()),{key:"row._getAllVisibleCells",debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}}),getVisibleCells:T(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(n,i,s)=>[...n,...i,...s],{key:!1,debug:()=>{var n;return(n=t.options.debugAll)!=null?n:t.options.debugRows}})}),createTable:e=>{const t=(n,i)=>T(()=>[i(),i().filter(s=>s.getIsVisible()).map(s=>s.id).join("_")],s=>s.filter(o=>o.getIsVisible==null?void 0:o.getIsVisible()),{key:n,debug:()=>{var s;return(s=e.options.debugAll)!=null?s:e.options.debugColumns}});return{getVisibleFlatColumns:t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),getVisibleLeafColumns:t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),getLeftVisibleLeafColumns:t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),getRightVisibleLeafColumns:t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),getCenterVisibleLeafColumns:t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),setColumnVisibility:n=>e.options.onColumnVisibilityChange==null?void 0:e.options.onColumnVisibilityChange(n),resetColumnVisibility:n=>{var i;e.setColumnVisibility(n?{}:(i=e.initialState.columnVisibility)!=null?i:{})},toggleAllColumnsVisible:n=>{var i;n=(i=n)!=null?i:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((s,o)=>({...s,[o.id]:n||!(o.getCanHide!=null&&o.getCanHide())}),{}))},getIsAllColumnsVisible:()=>!e.getAllLeafColumns().some(n=>!(n.getIsVisible!=null&&n.getIsVisible())),getIsSomeColumnsVisible:()=>e.getAllLeafColumns().some(n=>n.getIsVisible==null?void 0:n.getIsVisible()),getToggleAllColumnsVisibilityHandler:()=>n=>{var i;e.toggleAllColumnsVisible((i=n.target)==null?void 0:i.checked)}}}},Oo=[ku,Ju,Bu,Wu,Pu,Zu,Hu,Au,ju,Gu,Ru];function tf(e){var t;(e.debugAll||e.debugTable)&&console.info("Creating Table Instance...");let n={_features:Oo};const i=n._features.reduce((u,f)=>Object.assign(u,f.getDefaultOptions==null?void 0:f.getDefaultOptions(n)),{}),s=u=>n.options.mergeOptions?n.options.mergeOptions(i,u):{...i,...u};let r={...{},...(t=e.initialState)!=null?t:{}};n._features.forEach(u=>{var f;r=(f=u.getInitialState==null?void 0:u.getInitialState(r))!=null?f:r});const a=[];let l=!1;const c={_features:Oo,options:{...i,...e},initialState:r,_queue:u=>{a.push(u),l||(l=!0,Promise.resolve().then(()=>{for(;a.length;)a.shift()();l=!1}).catch(f=>setTimeout(()=>{throw f})))},reset:()=>{n.setState(n.initialState)},setOptions:u=>{const f=pe(u,n.options);n.options=s(f)},getState:()=>n.options.state,setState:u=>{n.options.onStateChange==null||n.options.onStateChange(u)},_getRowId:(u,f,d)=>{var h;return(h=n.options.getRowId==null?void 0:n.options.getRowId(u,f,d))!=null?h:`${d?[d.id,f].join("."):f}`},getCoreRowModel:()=>(n._getCoreRowModel||(n._getCoreRowModel=n.options.getCoreRowModel(n)),n._getCoreRowModel()),getRowModel:()=>n.getPaginationRowModel(),getRow:u=>{const f=n.getRowModel().rowsById[u];if(!f)throw new Error;return f},_getDefaultColumnDef:T(()=>[n.options.defaultColumn],u=>{var f;return u=(f=u)!=null?f:{},{header:d=>{const h=d.header.column.columnDef;return h.accessorKey?h.accessorKey:h.accessorFn?h.id:null},cell:d=>{var h,g;return(h=(g=d.renderValue())==null||g.toString==null?void 0:g.toString())!=null?h:null},...n._features.reduce((d,h)=>Object.assign(d,h.getDefaultColumnDef==null?void 0:h.getDefaultColumnDef()),{}),...u}},{debug:()=>{var u;return(u=n.options.debugAll)!=null?u:n.options.debugColumns},key:!1}),_getColumnDefs:()=>n.options.columns,getAllColumns:T(()=>[n._getColumnDefs()],u=>{const f=function(d,h,g){return g===void 0&&(g=0),d.map(p=>{const m=Mu(n,p,g,h),_=p;return m.columns=_.columns?f(_.columns,m,g+1):[],m})};return f(u)},{key:!1,debug:()=>{var u;return(u=n.options.debugAll)!=null?u:n.options.debugColumns}}),getAllFlatColumns:T(()=>[n.getAllColumns()],u=>u.flatMap(f=>f.getFlatColumns()),{key:!1,debug:()=>{var u;return(u=n.options.debugAll)!=null?u:n.options.debugColumns}}),_getAllFlatColumnsById:T(()=>[n.getAllFlatColumns()],u=>u.reduce((f,d)=>(f[d.id]=d,f),{}),{key:!1,debug:()=>{var u;return(u=n.options.debugAll)!=null?u:n.options.debugColumns}}),getAllLeafColumns:T(()=>[n.getAllColumns(),n._getOrderColumnsFn()],(u,f)=>{let d=u.flatMap(h=>h.getLeafColumns());return f(d)},{key:!1,debug:()=>{var u;return(u=n.options.debugAll)!=null?u:n.options.debugColumns}}),getColumn:u=>n._getAllFlatColumnsById()[u]};return Object.assign(n,c),n._features.forEach(u=>Object.assign(n,u.createTable==null?void 0:u.createTable(n))),n}function ef(e,t,n,i){const s=()=>{var r;return(r=o.getValue())!=null?r:e.options.renderFallbackValue},o={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(i),renderValue:s,getContext:T(()=>[e,n,t,o],(r,a,l,c)=>({table:r,column:a,row:l,cell:c,getValue:c.getValue,renderValue:c.renderValue}),{key:!1,debug:()=>e.options.debugAll})};return e._features.forEach(r=>{Object.assign(o,r.createCell==null?void 0:r.createCell(o,n,t,e))},{}),o}const nf=(e,t,n,i,s,o,r)=>{let a={id:t,index:i,original:n,depth:s,parentRow:r,_valuesCache:{},_uniqueValuesCache:{},getValue:l=>{if(a._valuesCache.hasOwnProperty(l))return a._valuesCache[l];const c=e.getColumn(l);if(c!=null&&c.accessorFn)return a._valuesCache[l]=c.accessorFn(a.original,i),a._valuesCache[l]},getUniqueValues:l=>{if(a._uniqueValuesCache.hasOwnProperty(l))return a._uniqueValuesCache[l];const c=e.getColumn(l);if(c!=null&&c.accessorFn)return c.columnDef.getUniqueValues?(a._uniqueValuesCache[l]=c.columnDef.getUniqueValues(a.original,i),a._uniqueValuesCache[l]):(a._uniqueValuesCache[l]=[a.getValue(l)],a._uniqueValuesCache[l])},renderValue:l=>{var c;return(c=a.getValue(l))!=null?c:e.options.renderFallbackValue},subRows:o??[],getLeafRows:()=>Cu(a.subRows,l=>l.subRows),getAllCells:T(()=>[e.getAllLeafColumns()],l=>l.map(c=>ef(e,a,c,c.id)),{key:!1,debug:()=>{var l;return(l=e.options.debugAll)!=null?l:e.options.debugRows}}),_getAllCellsByColumnId:T(()=>[a.getAllCells()],l=>l.reduce((c,u)=>(c[u.column.id]=u,c),{}),{key:"row.getAllCellsByColumnId",debug:()=>{var l;return(l=e.options.debugAll)!=null?l:e.options.debugRows}})};for(let l=0;l<e._features.length;l++){const c=e._features[l];Object.assign(a,c==null||c.createRow==null?void 0:c.createRow(a,e))}return a};function sf(){return e=>T(()=>[e.options.data],t=>{const n={rows:[],flatRows:[],rowsById:{}},i=function(s,o,r){o===void 0&&(o=0);const a=[];for(let c=0;c<s.length;c++){const u=nf(e,e._getRowId(s[c],c,r),s[c],c,o,void 0,r);if(n.flatRows.push(u),n.rowsById[u.id]=u,a.push(u),e.options.getSubRows){var l;u.originalSubRows=e.options.getSubRows(s[c],c),(l=u.originalSubRows)!=null&&l.length&&(u.subRows=i(u.originalSubRows,o+1,u))}}return a};return n.rows=i(t),n},{key:!1,debug:()=>{var t;return(t=e.options.debugAll)!=null?t:e.options.debugTable},onChange:()=>{e._autoResetPageIndex()}})}/**
 * svelte-table
 *
 * Copyright (c) TanStack
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function of(e){let t;return{c(){t=J(e[0])},l(n){t=ac(n,e[0])},m(n,i){ec(n,t,i)},p(n,[i]){i&1&&Wt(t,n[0])},i:gt,o:gt,d(n){n&&X(t)}}}function rf(e,t,n){let{content:i}=t;return e.$$set=s=>{"content"in s&&n(0,i=s.content)},[i]}class af extends K{constructor(t){super(),Z(this,t,rf,of,U,{content:0})}}const lf=Ca((e,t,n,i)=>`${Sc(t.content)}`);var cf=typeof document>"u"?lf:af;function uf(e,t,n){let i,s;return i=new t({props:n,$$inline:!0}),{c(){I(i.$$.fragment)},l(o){Mc(i.$$.fragment,o)},m(o,r){E(i,o,r),s=!0},p:gt,i(o){s||(S(i.$$.fragment,o),s=!0)},o(o){C(i.$$.fragment,o),s=!1},d(o){L(i,o)}}}function ff(e,t){return class extends K{constructor(i){super(),Z(this,i,null,s=>uf(s,e,t),U,{},void 0)}}}function df(e,t){return Ca((i,s,o,r)=>`${Cc(e,"TableComponent").$$render(i,t,{},{})}`)}const Ba=typeof window>"u"?df:ff;function hf(e){return typeof e=="object"&&typeof e.$$render=="function"&&typeof e.render=="function"}function gf(e){var t,n;let i="__SVELTE_HMR"in window;return e.prototype instanceof K||i&&((t=e.name)==null?void 0:t.startsWith("Proxy<"))&&((n=e.name)==null?void 0:n.endsWith(">"))}function Do(e){return typeof document>"u"?hf(e):gf(e)}function Fo(e){return Ba(cf,{content:e})}function mi(e,t){if(!e)return null;if(Do(e))return Ba(e,t);if(typeof e=="function"){const n=e(t);return Do(n)?n:Fo(n)}return Fo(e)}function pf(e){let t;"subscribe"in e?t=e:t=_s(e);let n={state:{},onStateChange:()=>{},renderFallbackValue:null,...Gl(t)},i=tf(n),s=Et(i.initialState),o=qs([s,t],a=>a);return _s(i,function(l){const c=o.subscribe(u=>{let[f,d]=u;i.setOptions(h=>({...h,...d,state:{...f,...d.state},onStateChange:g=>{g instanceof Function?s.update(g):s.set(g),n.onStateChange==null||n.onStateChange(g)}})),l(i)});return function(){c()}})}const _i=Math.min,Oe=Math.max,bi=Math.round,Hn=Math.floor,Qt=e=>({x:e,y:e}),mf={left:"right",right:"left",bottom:"top",top:"bottom"},_f={start:"end",end:"start"};function Eo(e,t,n){return Oe(e,_i(t,n))}function Ti(e,t){return typeof e=="function"?e(t):e}function Te(e){return e.split("-")[0]}function Ii(e){return e.split("-")[1]}function ja(e){return e==="x"?"y":"x"}function Wa(e){return e==="y"?"height":"width"}function De(e){return["top","bottom"].includes(Te(e))?"y":"x"}function Ga(e){return ja(De(e))}function bf(e,t,n){n===void 0&&(n=!1);const i=Ii(e),s=Ga(e),o=Wa(s);let r=s==="x"?i===(n?"end":"start")?"right":"left":i==="start"?"bottom":"top";return t.reference[o]>t.floating[o]&&(r=yi(r)),[r,yi(r)]}function yf(e){const t=yi(e);return[$s(e),t,$s(t)]}function $s(e){return e.replace(/start|end/g,t=>_f[t])}function xf(e,t,n){const i=["left","right"],s=["right","left"],o=["top","bottom"],r=["bottom","top"];switch(e){case"top":case"bottom":return n?t?s:i:t?i:s;case"left":case"right":return t?o:r;default:return[]}}function vf(e,t,n,i){const s=Ii(e);let o=xf(Te(e),n==="start",i);return s&&(o=o.map(r=>r+"-"+s),t&&(o=o.concat(o.map($s)))),o}function yi(e){return e.replace(/left|right|bottom|top/g,t=>mf[t])}function wf(e){return{top:0,right:0,bottom:0,left:0,...e}}function Sf(e){return typeof e!="number"?wf(e):{top:e,right:e,bottom:e,left:e}}function xi(e){const{x:t,y:n,width:i,height:s}=e;return{width:i,height:s,top:n,left:t,right:t+i,bottom:n+s,x:t,y:n}}function Lo(e,t,n){let{reference:i,floating:s}=e;const o=De(t),r=Ga(t),a=Wa(r),l=Te(t),c=o==="y",u=i.x+i.width/2-s.width/2,f=i.y+i.height/2-s.height/2,d=i[a]/2-s[a]/2;let h;switch(l){case"top":h={x:u,y:i.y-s.height};break;case"bottom":h={x:u,y:i.y+i.height};break;case"right":h={x:i.x+i.width,y:f};break;case"left":h={x:i.x-s.width,y:f};break;default:h={x:i.x,y:i.y}}switch(Ii(t)){case"start":h[r]-=d*(n&&c?-1:1);break;case"end":h[r]+=d*(n&&c?-1:1);break}return h}const Cf=async(e,t,n)=>{const{placement:i="bottom",strategy:s="absolute",middleware:o=[],platform:r}=n,a=o.filter(Boolean),l=await(r.isRTL==null?void 0:r.isRTL(t));let c=await r.getElementRects({reference:e,floating:t,strategy:s}),{x:u,y:f}=Lo(c,i,l),d=i,h={},g=0;for(let p=0;p<a.length;p++){const{name:m,fn:_}=a[p],{x:b,y:x,data:v,reset:y}=await _({x:u,y:f,initialPlacement:i,placement:d,strategy:s,middlewareData:h,rects:c,platform:r,elements:{reference:e,floating:t}});u=b??u,f=x??f,h={...h,[m]:{...h[m],...v}},y&&g<=50&&(g++,typeof y=="object"&&(y.placement&&(d=y.placement),y.rects&&(c=y.rects===!0?await r.getElementRects({reference:e,floating:t,strategy:s}):y.rects),{x:u,y:f}=Lo(c,d,l)),p=-1)}return{x:u,y:f,placement:d,strategy:s,middlewareData:h}};async function qa(e,t){var n;t===void 0&&(t={});const{x:i,y:s,platform:o,rects:r,elements:a,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:f="floating",altBoundary:d=!1,padding:h=0}=Ti(t,e),g=Sf(h),m=a[d?f==="floating"?"reference":"floating":f],_=xi(await o.getClippingRect({element:(n=await(o.isElement==null?void 0:o.isElement(m)))==null||n?m:m.contextElement||await(o.getDocumentElement==null?void 0:o.getDocumentElement(a.floating)),boundary:c,rootBoundary:u,strategy:l})),b=f==="floating"?{x:i,y:s,width:r.floating.width,height:r.floating.height}:r.reference,x=await(o.getOffsetParent==null?void 0:o.getOffsetParent(a.floating)),v=await(o.isElement==null?void 0:o.isElement(x))?await(o.getScale==null?void 0:o.getScale(x))||{x:1,y:1}:{x:1,y:1},y=xi(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:b,offsetParent:x,strategy:l}):b);return{top:(_.top-y.top+g.top)/v.y,bottom:(y.bottom-_.bottom+g.bottom)/v.y,left:(_.left-y.left+g.left)/v.x,right:(y.right-_.right+g.right)/v.x}}const Mf=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,i;const{placement:s,middlewareData:o,rects:r,initialPlacement:a,platform:l,elements:c}=t,{mainAxis:u=!0,crossAxis:f=!0,fallbackPlacements:d,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:p=!0,...m}=Ti(e,t);if((n=o.arrow)!=null&&n.alignmentOffset)return{};const _=Te(s),b=De(a),x=Te(a)===a,v=await(l.isRTL==null?void 0:l.isRTL(c.floating)),y=d||(x||!p?[yi(a)]:yf(a)),k=g!=="none";!d&&k&&y.push(...vf(a,p,g,v));const M=[a,...y],$=await qa(t,m),P=[];let O=((i=o.flip)==null?void 0:i.overflows)||[];if(u&&P.push($[_]),f){const V=bf(s,r,v);P.push($[V[0]],$[V[1]])}if(O=[...O,{placement:s,overflows:P}],!P.every(V=>V<=0)){var R,z;const V=(((R=o.flip)==null?void 0:R.index)||0)+1,G=M[V];if(G){var ct;const it=f==="alignment"?b!==De(G):!1,B=((ct=O[0])==null?void 0:ct.overflows[0])>0;if(!it||B)return{data:{index:V,overflows:O},reset:{placement:G}}}let N=(z=O.filter(it=>it.overflows[0]<=0).sort((it,B)=>it.overflows[1]-B.overflows[1])[0])==null?void 0:z.placement;if(!N)switch(h){case"bestFit":{var dt;const it=(dt=O.filter(B=>{if(k){const ft=De(B.placement);return ft===b||ft==="y"}return!0}).map(B=>[B.placement,B.overflows.filter(ft=>ft>0).reduce((ft,ne)=>ft+ne,0)]).sort((B,ft)=>B[1]-ft[1])[0])==null?void 0:dt[0];it&&(N=it);break}case"initialPlacement":N=a;break}if(s!==N)return{reset:{placement:N}}}return{}}}};async function kf(e,t){const{placement:n,platform:i,elements:s}=e,o=await(i.isRTL==null?void 0:i.isRTL(s.floating)),r=Te(n),a=Ii(n),l=De(n)==="y",c=["left","top"].includes(r)?-1:1,u=o&&l?-1:1,f=Ti(t,e);let{mainAxis:d,crossAxis:h,alignmentAxis:g}=typeof f=="number"?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return a&&typeof g=="number"&&(h=a==="end"?g*-1:g),l?{x:h*u,y:d*c}:{x:d*c,y:h*u}}const Rf=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,i;const{x:s,y:o,placement:r,middlewareData:a}=t,l=await kf(t,e);return r===((n=a.offset)==null?void 0:n.placement)&&(i=a.arrow)!=null&&i.alignmentOffset?{}:{x:s+l.x,y:o+l.y,data:{...l,placement:r}}}}},$f=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:i,placement:s}=t,{mainAxis:o=!0,crossAxis:r=!1,limiter:a={fn:m=>{let{x:_,y:b}=m;return{x:_,y:b}}},...l}=Ti(e,t),c={x:n,y:i},u=await qa(t,l),f=De(Te(s)),d=ja(f);let h=c[d],g=c[f];if(o){const m=d==="y"?"top":"left",_=d==="y"?"bottom":"right",b=h+u[m],x=h-u[_];h=Eo(b,h,x)}if(r){const m=f==="y"?"top":"left",_=f==="y"?"bottom":"right",b=g+u[m],x=g-u[_];g=Eo(b,g,x)}const p=a.fn({...t,[d]:h,[f]:g});return{...p,data:{x:p.x-n,y:p.y-i,enabled:{[d]:o,[f]:r}}}}}};function Vi(){return typeof window<"u"}function an(e){return Ya(e)?(e.nodeName||"").toLowerCase():"#document"}function kt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function ee(e){var t;return(t=(Ya(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Ya(e){return Vi()?e instanceof Node||e instanceof kt(e).Node:!1}function qt(e){return Vi()?e instanceof Element||e instanceof kt(e).Element:!1}function Zt(e){return Vi()?e instanceof HTMLElement||e instanceof kt(e).HTMLElement:!1}function To(e){return!Vi()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof kt(e).ShadowRoot}function On(e){const{overflow:t,overflowX:n,overflowY:i,display:s}=Yt(e);return/auto|scroll|overlay|hidden|clip/.test(t+i+n)&&!["inline","contents"].includes(s)}function Af(e){return["table","td","th"].includes(an(e))}function zi(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function Zs(e){const t=Js(),n=qt(e)?Yt(e):e;return["transform","translate","scale","rotate","perspective"].some(i=>n[i]?n[i]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(i=>(n.willChange||"").includes(i))||["paint","layout","strict","content"].some(i=>(n.contain||"").includes(i))}function Pf(e){let t=we(e);for(;Zt(t)&&!en(t);){if(Zs(t))return t;if(zi(t))return null;t=we(t)}return null}function Js(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function en(e){return["html","body","#document"].includes(an(e))}function Yt(e){return kt(e).getComputedStyle(e)}function Hi(e){return qt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function we(e){if(an(e)==="html")return e;const t=e.assignedSlot||e.parentNode||To(e)&&e.host||ee(e);return To(t)?t.host:t}function Ua(e){const t=we(e);return en(t)?e.ownerDocument?e.ownerDocument.body:e.body:Zt(t)&&On(t)?t:Ua(t)}function Cn(e,t,n){var i;t===void 0&&(t=[]),n===void 0&&(n=!0);const s=Ua(e),o=s===((i=e.ownerDocument)==null?void 0:i.body),r=kt(s);if(o){const a=As(r);return t.concat(r,r.visualViewport||[],On(s)?s:[],a&&n?Cn(a):[])}return t.concat(s,Cn(s,[],n))}function As(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Xa(e){const t=Yt(e);let n=parseFloat(t.width)||0,i=parseFloat(t.height)||0;const s=Zt(e),o=s?e.offsetWidth:n,r=s?e.offsetHeight:i,a=bi(n)!==o||bi(i)!==r;return a&&(n=o,i=r),{width:n,height:i,$:a}}function to(e){return qt(e)?e:e.contextElement}function Ke(e){const t=to(e);if(!Zt(t))return Qt(1);const n=t.getBoundingClientRect(),{width:i,height:s,$:o}=Xa(t);let r=(o?bi(n.width):n.width)/i,a=(o?bi(n.height):n.height)/s;return(!r||!Number.isFinite(r))&&(r=1),(!a||!Number.isFinite(a))&&(a=1),{x:r,y:a}}const Of=Qt(0);function Ka(e){const t=kt(e);return!Js()||!t.visualViewport?Of:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function Df(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==kt(e)?!1:t}function Ie(e,t,n,i){t===void 0&&(t=!1),n===void 0&&(n=!1);const s=e.getBoundingClientRect(),o=to(e);let r=Qt(1);t&&(i?qt(i)&&(r=Ke(i)):r=Ke(e));const a=Df(o,n,i)?Ka(o):Qt(0);let l=(s.left+a.x)/r.x,c=(s.top+a.y)/r.y,u=s.width/r.x,f=s.height/r.y;if(o){const d=kt(o),h=i&&qt(i)?kt(i):i;let g=d,p=As(g);for(;p&&i&&h!==g;){const m=Ke(p),_=p.getBoundingClientRect(),b=Yt(p),x=_.left+(p.clientLeft+parseFloat(b.paddingLeft))*m.x,v=_.top+(p.clientTop+parseFloat(b.paddingTop))*m.y;l*=m.x,c*=m.y,u*=m.x,f*=m.y,l+=x,c+=v,g=kt(p),p=As(g)}}return xi({width:u,height:f,x:l,y:c})}function eo(e,t){const n=Hi(e).scrollLeft;return t?t.left+n:Ie(ee(e)).left+n}function Qa(e,t,n){n===void 0&&(n=!1);const i=e.getBoundingClientRect(),s=i.left+t.scrollLeft-(n?0:eo(e,i)),o=i.top+t.scrollTop;return{x:s,y:o}}function Ff(e){let{elements:t,rect:n,offsetParent:i,strategy:s}=e;const o=s==="fixed",r=ee(i),a=t?zi(t.floating):!1;if(i===r||a&&o)return n;let l={scrollLeft:0,scrollTop:0},c=Qt(1);const u=Qt(0),f=Zt(i);if((f||!f&&!o)&&((an(i)!=="body"||On(r))&&(l=Hi(i)),Zt(i))){const h=Ie(i);c=Ke(i),u.x=h.x+i.clientLeft,u.y=h.y+i.clientTop}const d=r&&!f&&!o?Qa(r,l,!0):Qt(0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-l.scrollLeft*c.x+u.x+d.x,y:n.y*c.y-l.scrollTop*c.y+u.y+d.y}}function Ef(e){return Array.from(e.getClientRects())}function Lf(e){const t=ee(e),n=Hi(e),i=e.ownerDocument.body,s=Oe(t.scrollWidth,t.clientWidth,i.scrollWidth,i.clientWidth),o=Oe(t.scrollHeight,t.clientHeight,i.scrollHeight,i.clientHeight);let r=-n.scrollLeft+eo(e);const a=-n.scrollTop;return Yt(i).direction==="rtl"&&(r+=Oe(t.clientWidth,i.clientWidth)-s),{width:s,height:o,x:r,y:a}}function Tf(e,t){const n=kt(e),i=ee(e),s=n.visualViewport;let o=i.clientWidth,r=i.clientHeight,a=0,l=0;if(s){o=s.width,r=s.height;const c=Js();(!c||c&&t==="fixed")&&(a=s.offsetLeft,l=s.offsetTop)}return{width:o,height:r,x:a,y:l}}function If(e,t){const n=Ie(e,!0,t==="fixed"),i=n.top+e.clientTop,s=n.left+e.clientLeft,o=Zt(e)?Ke(e):Qt(1),r=e.clientWidth*o.x,a=e.clientHeight*o.y,l=s*o.x,c=i*o.y;return{width:r,height:a,x:l,y:c}}function Io(e,t,n){let i;if(t==="viewport")i=Tf(e,n);else if(t==="document")i=Lf(ee(e));else if(qt(t))i=If(t,n);else{const s=Ka(e);i={x:t.x-s.x,y:t.y-s.y,width:t.width,height:t.height}}return xi(i)}function Za(e,t){const n=we(e);return n===t||!qt(n)||en(n)?!1:Yt(n).position==="fixed"||Za(n,t)}function Vf(e,t){const n=t.get(e);if(n)return n;let i=Cn(e,[],!1).filter(a=>qt(a)&&an(a)!=="body"),s=null;const o=Yt(e).position==="fixed";let r=o?we(e):e;for(;qt(r)&&!en(r);){const a=Yt(r),l=Zs(r);!l&&a.position==="fixed"&&(s=null),(o?!l&&!s:!l&&a.position==="static"&&!!s&&["absolute","fixed"].includes(s.position)||On(r)&&!l&&Za(e,r))?i=i.filter(u=>u!==r):s=a,r=we(r)}return t.set(e,i),i}function zf(e){let{element:t,boundary:n,rootBoundary:i,strategy:s}=e;const r=[...n==="clippingAncestors"?zi(t)?[]:Vf(t,this._c):[].concat(n),i],a=r[0],l=r.reduce((c,u)=>{const f=Io(t,u,s);return c.top=Oe(f.top,c.top),c.right=_i(f.right,c.right),c.bottom=_i(f.bottom,c.bottom),c.left=Oe(f.left,c.left),c},Io(t,a,s));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}}function Hf(e){const{width:t,height:n}=Xa(e);return{width:t,height:n}}function Nf(e,t,n){const i=Zt(t),s=ee(t),o=n==="fixed",r=Ie(e,!0,o,t);let a={scrollLeft:0,scrollTop:0};const l=Qt(0);function c(){l.x=eo(s)}if(i||!i&&!o)if((an(t)!=="body"||On(s))&&(a=Hi(t)),i){const h=Ie(t,!0,o,t);l.x=h.x+t.clientLeft,l.y=h.y+t.clientTop}else s&&c();o&&!i&&s&&c();const u=s&&!i&&!o?Qa(s,a):Qt(0),f=r.left+a.scrollLeft-l.x-u.x,d=r.top+a.scrollTop-l.y-u.y;return{x:f,y:d,width:r.width,height:r.height}}function is(e){return Yt(e).position==="static"}function Vo(e,t){if(!Zt(e)||Yt(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return ee(e)===n&&(n=n.ownerDocument.body),n}function Ja(e,t){const n=kt(e);if(zi(e))return n;if(!Zt(e)){let s=we(e);for(;s&&!en(s);){if(qt(s)&&!is(s))return s;s=we(s)}return n}let i=Vo(e,t);for(;i&&Af(i)&&is(i);)i=Vo(i,t);return i&&en(i)&&is(i)&&!Zs(i)?n:i||Pf(e)||n}const Bf=async function(e){const t=this.getOffsetParent||Ja,n=this.getDimensions,i=await n(e.floating);return{reference:Nf(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:i.width,height:i.height}}};function jf(e){return Yt(e).direction==="rtl"}const Wf={convertOffsetParentRelativeRectToViewportRelativeRect:Ff,getDocumentElement:ee,getClippingRect:zf,getOffsetParent:Ja,getElementRects:Bf,getClientRects:Ef,getDimensions:Hf,getScale:Ke,isElement:qt,isRTL:jf};function tl(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function Gf(e,t){let n=null,i;const s=ee(e);function o(){var a;clearTimeout(i),(a=n)==null||a.disconnect(),n=null}function r(a,l){a===void 0&&(a=!1),l===void 0&&(l=1),o();const c=e.getBoundingClientRect(),{left:u,top:f,width:d,height:h}=c;if(a||t(),!d||!h)return;const g=Hn(f),p=Hn(s.clientWidth-(u+d)),m=Hn(s.clientHeight-(f+h)),_=Hn(u),x={rootMargin:-g+"px "+-p+"px "+-m+"px "+-_+"px",threshold:Oe(0,_i(1,l))||1};let v=!0;function y(k){const M=k[0].intersectionRatio;if(M!==l){if(!v)return r();M?r(!1,M):i=setTimeout(()=>{r(!1,1e-7)},1e3)}M===1&&!tl(c,e.getBoundingClientRect())&&r(),v=!1}try{n=new IntersectionObserver(y,{...x,root:s.ownerDocument})}catch{n=new IntersectionObserver(y,x)}n.observe(e)}return r(!0),o}function qf(e,t,n,i){i===void 0&&(i={});const{ancestorScroll:s=!0,ancestorResize:o=!0,elementResize:r=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:l=!1}=i,c=to(e),u=s||o?[...c?Cn(c):[],...Cn(t)]:[];u.forEach(_=>{s&&_.addEventListener("scroll",n,{passive:!0}),o&&_.addEventListener("resize",n)});const f=c&&a?Gf(c,n):null;let d=-1,h=null;r&&(h=new ResizeObserver(_=>{let[b]=_;b&&b.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(d),d=requestAnimationFrame(()=>{var x;(x=h)==null||x.observe(t)})),n()}),c&&!l&&h.observe(c),h.observe(t));let g,p=l?Ie(e):null;l&&m();function m(){const _=Ie(e);p&&!tl(p,_)&&n(),p=_,g=requestAnimationFrame(m)}return n(),()=>{var _;u.forEach(b=>{s&&b.removeEventListener("scroll",n),o&&b.removeEventListener("resize",n)}),f==null||f(),(_=h)==null||_.disconnect(),h=null,l&&cancelAnimationFrame(g)}}const Yf=Rf,Uf=$f,Xf=Mf,Kf=(e,t,n)=>{const i=new Map,s={platform:Wf,...n},o={...s.platform,_c:i};return Cf(e,t,{...s,platform:o})},Qf=e=>{let t,n;const i={autoUpdate:!0};let s=e??{};const o=u=>({...i,...e||{},...u||{}}),r=u=>{t&&n&&(s=o(u),Kf(t,n,s).then(f=>{Object.assign(n.style,{position:f.strategy,left:`${f.x}px`,top:`${f.y}px`}),s!=null&&s.onComputed&&s.onComputed(f)}))},a=u=>{if("subscribe"in u)return c(u),{};t=u,r()},l=(u,f)=>{let d;n=u,s=o(f),setTimeout(()=>r(f),0),r(f);const h=()=>{d&&(d(),d=void 0)},g=(p=s)=>new Promise(m=>{const{autoUpdate:_}=p||{};h(),_!==!1&&Gs().then(()=>{m(qf(t,n,()=>r(p),_===!0?{}:_))})});return g().then(p=>d=p),{update(p){r(p),g().then(m=>d=m)},destroy(){h()}}},c=u=>{const f=u.subscribe(d=>{t===void 0?(t=d,r()):(Object.assign(t,d),r())});rn(f)};return[a,l,r]};function Zf(e){const t=e-1;return t*t*t+1}function zo(e,{delay:t=0,duration:n=400,easing:i=ha}={}){const s=+getComputedStyle(e).opacity;return{delay:t,duration:n,easing:i,css:o=>`opacity: ${o*s}`}}function Ho(e,{delay:t=0,duration:n=400,easing:i=Zf,start:s=0,opacity:o=0}={}){const r=getComputedStyle(e),a=+r.opacity,l=r.transform==="none"?"":r.transform,c=1-s,u=a*(1-o);return{delay:t,duration:n,easing:i,css:(f,d)=>`
			transform: ${l} scale(${1-c*d});
			opacity: ${a-u*d}
		`}}function Jf(e){let t,n,i,s,o;const r=e[2].default,a=Pt(r,e,e[1],null);return{c(){t=D("div"),a&&a.c(),t.hidden=!0},m(l,c){nt(l,t,c),a&&a.m(t,null),i=!0,s||(o=Bs(n=td.call(null,t,e[0])),s=!0)},p(l,[c]){a&&a.p&&(!i||c&2)&&Dt(a,r,l,l[1],i?Ot(r,l[1],c,null):Ft(l[1]),null),n&&ue(n.update)&&c&1&&n.update.call(null,l[0])},i(l){i||(S(a,l),i=!0)},o(l){C(a,l),i=!1},d(l){l&&X(t),a&&a.d(l),s=!1,o()}}}function td(e,t="body"){let n;async function i(o){if(t=o,typeof t=="string"){if(n=document.querySelector(t),n===null&&(await Gs(),n=document.querySelector(t)),n===null)throw new Error(`No element found matching css selector: "${t}"`)}else if(t instanceof HTMLElement)n=t;else throw new TypeError(`Unknown portal target type: ${t===null?"null":typeof t}. Allowed types: string (CSS selector) or HTMLElement.`);n.appendChild(e),e.hidden=!1}function s(){e.parentNode&&e.parentNode.removeChild(e)}return i(t),{update:i,destroy:s}}function ed(e,t,n){let{$$slots:i={},$$scope:s}=t,{target:o="body"}=t;return e.$$set=r=>{"target"in r&&n(0,o=r.target),"$$scope"in r&&n(1,s=r.$$scope)},[o,s,i]}class nd extends K{constructor(t){super(),Z(this,t,ed,Jf,U,{target:0})}}const id=e=>({}),No=e=>({floatingRef:e[3],displayTooltip:e[5],hideTooltip:e[6]});function Bo(e){let t,n;return t=new nd({props:{target:"body",$$slots:{default:[sd]},$$scope:{ctx:e}}}),{c(){I(t.$$.fragment)},m(i,s){E(t,i,s),n=!0},p(i,s){const o={};s&257&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(S(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){L(t,i)}}}function sd(e){let t,n,i,s,o,r;return{c(){t=D("div"),n=J(e[0]),F(t,"class","absolute p-2 text-sm bg-dark-50 text-dark-400 rounded-md max-w-xl font-main")},m(a,l){nt(a,t,l),w(t,n),s=!0,o||(r=Bs(e[4].call(null,t)),o=!0)},p(a,l){(!s||l&1)&&Wt(n,a[0])},i(a){s||(tn(()=>{s&&(i||(i=di(t,zo,{duration:150},!0)),i.run(1))}),s=!0)},o(a){i||(i=di(t,zo,{duration:150},!1)),i.run(0),s=!1},d(a){a&&X(t),a&&i&&i.end(),o=!1,r()}}}function od(e){let t,n,i;const s=e[7].default,o=Pt(s,e,e[8],No);let r=e[2]&&!e[1]&&Bo(e);return{c(){o&&o.c(),t=Y(),r&&r.c(),n=Pn()},m(a,l){o&&o.m(a,l),nt(a,t,l),r&&r.m(a,l),nt(a,n,l),i=!0},p(a,[l]){o&&o.p&&(!i||l&256)&&Dt(o,s,a,a[8],i?Ot(s,a[8],l,id):Ft(a[8]),No),a[2]&&!a[1]?r?(r.p(a,l),l&6&&S(r,1)):(r=Bo(a),r.c(),S(r,1),r.m(n.parentNode,n)):r&&(Rt(),C(r,1,1,()=>{r=null}),$t())},i(a){i||(S(o,a),S(r),i=!0)},o(a){C(o,a),C(r),i=!1},d(a){o&&o.d(a),a&&X(t),r&&r.d(a),a&&X(n)}}}function rd(e,t,n){let{$$slots:i={},$$scope:s}=t,{content:o}=t,{disabled:r}=t;const[a,l]=Qf({strategy:"absolute",placement:"top",middleware:[Yf(6),Xf(),Uf()]});let c=!1,u;const f=()=>{r||(clearTimeout(u),u=setTimeout(()=>{n(2,c=!0)},300))},d=()=>{r||(clearTimeout(u),n(2,c=!1))};return e.$$set=h=>{"content"in h&&n(0,o=h.content),"disabled"in h&&n(1,r=h.disabled),"$$scope"in h&&n(8,s=h.$$scope)},[o,r,c,a,l,f,d,i,s]}class ad extends K{constructor(t){super(),Z(this,t,rd,od,U,{content:0,disabled:1})}}function jo(e,t,n){const i=e.slice();return i[10]=t[n],i}function Wo(e,t,n){const i=e.slice();return i[13]=t[n],i}function Go(e,t,n){const i=e.slice();return i[19]=t[n],i}function qo(e,t,n){const i=e.slice();return i[22]=t[n],i}function Yo(e){let t,n,i,s,o,r,a,l,c,u;var f=mi(e[22].column.columnDef.header,e[22].getContext());function d(m){return{}}f&&(n=xe(f,d()));const h=[cd,ld],g=[];function p(m,_){return _&1&&(s=null),_&1&&(o=null),s==null&&(s=m[22].column.getIsSorted()==="asc"),s?0:(o==null&&(o=m[22].column.getIsSorted()==="desc"),o?1:-1)}return~(r=p(e,-1))&&(a=g[r]=h[r](e)),{c(){t=D("button"),n&&I(n.$$.fragment),i=Y(),a&&a.c(),F(t,"class","flex w-full items-center justify-center gap-1"),Tn(t,"cursor-pointer",e[22].column.getCanSort()),Tn(t,"select-none",e[22].column.getCanSort())},m(m,_){nt(m,t,_),n&&E(n,t,null),w(t,i),~r&&g[r].m(t,null),l=!0,c||(u=It(t,"click",function(){ue(e[22].column.getToggleSortingHandler())&&e[22].column.getToggleSortingHandler().apply(this,arguments)}),c=!0)},p(m,_){if(e=m,_&1&&f!==(f=mi(e[22].column.columnDef.header,e[22].getContext()))){if(n){Rt();const x=n;C(x.$$.fragment,1,0,()=>{L(x,1)}),$t()}f?(n=xe(f,d()),I(n.$$.fragment),S(n.$$.fragment,1),E(n,t,i)):n=null}let b=r;r=p(e,_),r!==b&&(a&&(Rt(),C(g[b],1,1,()=>{g[b]=null}),$t()),~r?(a=g[r],a||(a=g[r]=h[r](e),a.c()),S(a,1),a.m(t,null)):a=null),(!l||_&1)&&Tn(t,"cursor-pointer",e[22].column.getCanSort()),(!l||_&1)&&Tn(t,"select-none",e[22].column.getCanSort())},i(m){l||(n&&S(n.$$.fragment,m),S(a),l=!0)},o(m){n&&C(n.$$.fragment,m),C(a),l=!1},d(m){m&&X(t),n&&L(n),~r&&g[r].d(),c=!1,u()}}}function ld(e){let t,n;return t=new bu({}),{c(){I(t.$$.fragment)},m(i,s){E(t,i,s),n=!0},i(i){n||(S(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){L(t,i)}}}function cd(e){let t,n;return t=new Su({}),{c(){I(t.$$.fragment)},m(i,s){E(t,i,s),n=!0},i(i){n||(S(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){L(t,i)}}}function Uo(e){let t,n,i,s=!e[22].isPlaceholder&&Yo(e);return{c(){t=D("th"),s&&s.c(),F(t,"class",n=`bg-dark-600 select-none p-1 ${e[22].id==="executionTime"?"w-1/4":"w-3/4"}`)},m(o,r){nt(o,t,r),s&&s.m(t,null),i=!0},p(o,r){o[22].isPlaceholder?s&&(Rt(),C(s,1,1,()=>{s=null}),$t()):s?(s.p(o,r),r&1&&S(s,1)):(s=Yo(o),s.c(),S(s,1),s.m(t,null)),(!i||r&1&&n!==(n=`bg-dark-600 select-none p-1 ${o[22].id==="executionTime"?"w-1/4":"w-3/4"}`))&&F(t,"class",n)},i(o){i||(S(s),i=!0)},o(o){C(s),i=!1},d(o){o&&X(t),s&&s.d()}}}function Xo(e){let t,n,i,s=e[19].headers,o=[];for(let a=0;a<s.length;a+=1)o[a]=Uo(qo(e,s,a));const r=a=>C(o[a],1,1,()=>{o[a]=null});return{c(){t=D("tr");for(let a=0;a<o.length;a+=1)o[a].c();n=Y()},m(a,l){nt(a,t,l);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(t,null);w(t,n),i=!0},p(a,l){if(l&1){s=a[19].headers;let c;for(c=0;c<s.length;c+=1){const u=qo(a,s,c);o[c]?(o[c].p(u,l),S(o[c],1)):(o[c]=Uo(u),o[c].c(),S(o[c],1),o[c].m(t,n))}for(Rt(),c=s.length;c<o.length;c+=1)r(c);$t()}},i(a){if(!i){for(let l=0;l<s.length;l+=1)S(o[l]);i=!0}},o(a){o=o.filter(Boolean);for(let l=0;l<o.length;l+=1)C(o[l]);i=!1},d(a){a&&X(t),Ze(o,a)}}}function ud(e){let t,n,i,s,o,r;var a=mi(e[13].column.columnDef.cell,e[13].getContext());function l(c){return{}}return a&&(n=xe(a,l())),{c(){t=D("td"),n&&I(n.$$.fragment),F(t,"class",i=`${e[13].column.id==="executionTime"&&"text-center"} bg-dark-700 p-2 ${e[10].original.slow&&"text-yellow-500"} max-w-[200px] truncate`)},m(c,u){nt(c,t,u),n&&E(n,t,null),s=!0,o||(r=[Bs(e[16].call(null,t)),It(t,"mouseenter",function(){ue(e[17])&&e[17].apply(this,arguments)}),It(t,"mouseleave",function(){ue(e[18])&&e[18].apply(this,arguments)})],o=!0)},p(c,u){if(e=c,u&1&&a!==(a=mi(e[13].column.columnDef.cell,e[13].getContext()))){if(n){Rt();const f=n;C(f.$$.fragment,1,0,()=>{L(f,1)}),$t()}a?(n=xe(a,l()),I(n.$$.fragment),S(n.$$.fragment,1),E(n,t,null)):n=null}(!s||u&1&&i!==(i=`${e[13].column.id==="executionTime"&&"text-center"} bg-dark-700 p-2 ${e[10].original.slow&&"text-yellow-500"} max-w-[200px] truncate`))&&F(t,"class",i)},i(c){s||(n&&S(n.$$.fragment,c),s=!0)},o(c){n&&C(n.$$.fragment,c),s=!1},d(c){c&&X(t),n&&L(n),o=!1,Jt(r)}}}function Ko(e){let t,n;return t=new ad({props:{content:e[13].getValue(),disabled:e[13].column.id!=="query",$$slots:{default:[ud,({floatingRef:i,displayTooltip:s,hideTooltip:o})=>({16:i,17:s,18:o}),({floatingRef:i,displayTooltip:s,hideTooltip:o})=>(i?65536:0)|(s?131072:0)|(o?262144:0)]},$$scope:{ctx:e}}}),{c(){I(t.$$.fragment)},m(i,s){E(t,i,s),n=!0},p(i,s){const o={};s&1&&(o.content=i[13].getValue()),s&1&&(o.disabled=i[13].column.id!=="query"),s&33947649&&(o.$$scope={dirty:s,ctx:i}),t.$set(o)},i(i){n||(S(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){L(t,i)}}}function Qo(e){let t,n,i,s=e[10].getVisibleCells(),o=[];for(let a=0;a<s.length;a+=1)o[a]=Ko(Wo(e,s,a));const r=a=>C(o[a],1,1,()=>{o[a]=null});return{c(){t=D("tr");for(let a=0;a<o.length;a+=1)o[a].c();n=Y()},m(a,l){nt(a,t,l);for(let c=0;c<o.length;c+=1)o[c]&&o[c].m(t,null);w(t,n),i=!0},p(a,l){if(l&393217){s=a[10].getVisibleCells();let c;for(c=0;c<s.length;c+=1){const u=Wo(a,s,c);o[c]?(o[c].p(u,l),S(o[c],1)):(o[c]=Ko(u),o[c].c(),S(o[c],1),o[c].m(t,n))}for(Rt(),c=s.length;c<o.length;c+=1)r(c);$t()}},i(a){if(!i){for(let l=0;l<s.length;l+=1)S(o[l]);i=!0}},o(a){o=o.filter(Boolean);for(let l=0;l<o.length;l+=1)C(o[l]);i=!1},d(a){a&&X(t),Ze(o,a)}}}function fd(e){let t,n,i,s,o,r,a=e[0].getHeaderGroups(),l=[];for(let h=0;h<a.length;h+=1)l[h]=Xo(Go(e,a,h));const c=h=>C(l[h],1,1,()=>{l[h]=null});let u=e[0].getRowModel().rows,f=[];for(let h=0;h<u.length;h+=1)f[h]=Qo(jo(e,u,h));const d=h=>C(f[h],1,1,()=>{f[h]=null});return{c(){t=D("div"),n=D("table"),i=D("thead");for(let h=0;h<l.length;h+=1)l[h].c();s=Y(),o=D("tbody");for(let h=0;h<f.length;h+=1)f[h].c();F(i,"class","bg-dark-600"),F(n,"class","w-full"),F(t,"class","px-4")},m(h,g){nt(h,t,g),w(t,n),w(n,i);for(let p=0;p<l.length;p+=1)l[p]&&l[p].m(i,null);w(n,s),w(n,o);for(let p=0;p<f.length;p+=1)f[p]&&f[p].m(o,null);r=!0},p(h,[g]){if(g&1){a=h[0].getHeaderGroups();let p;for(p=0;p<a.length;p+=1){const m=Go(h,a,p);l[p]?(l[p].p(m,g),S(l[p],1)):(l[p]=Xo(m),l[p].c(),S(l[p],1),l[p].m(i,null))}for(Rt(),p=a.length;p<l.length;p+=1)c(p);$t()}if(g&393217){u=h[0].getRowModel().rows;let p;for(p=0;p<u.length;p+=1){const m=jo(h,u,p);f[p]?(f[p].p(m,g),S(f[p],1)):(f[p]=Qo(m),f[p].c(),S(f[p],1),f[p].m(o,null))}for(Rt(),p=u.length;p<f.length;p+=1)d(p);$t()}},i(h){if(!r){for(let g=0;g<a.length;g+=1)S(l[g]);for(let g=0;g<u.length;g+=1)S(f[g]);r=!0}},o(h){l=l.filter(Boolean);for(let g=0;g<l.length;g+=1)C(l[g]);f=f.filter(Boolean);for(let g=0;g<f.length;g+=1)C(f[g]);r=!1},d(h){h&&X(t),Ze(l,h),Ze(f,h)}}}function dd(e,t,n){let i,s,o;pt(e,Bt,h=>n(4,i=h)),pt(e,ii,h=>n(5,s=h));const r=Us(),a=[{accessorKey:"query",header:"Query",cell:h=>h.getValue(),enableSorting:!0},{accessorKey:"executionTime",header:"Time (ms)",cell:h=>h.getValue().toFixed(4),enableSorting:!0}];let l=[];const c=h=>{h instanceof Function?n(2,l=h(l)):n(2,l=h),u.update(g=>({...g,state:{...g.state,sorting:l}}))},u=Et({data:s,columns:a,manualPagination:!0,manualSorting:!0,pageCount:-1,getCoreRowModel:sf(),onSortingChange:c,state:{sorting:l}}),f=pf(u);pt(e,f,h=>n(0,o=h));let d;return e.$$.update=()=>{e.$$.dirty&32&&u.update(h=>({...h,data:s})),e.$$.dirty&28&&(clearTimeout(d),n(3,d=setTimeout(()=>{Oa("fetchResource",{resource:r.params.resource,pageIndex:i.page,search:i.search,sortBy:l})},300)))},[o,f,l,d,i,s]}class hd extends K{constructor(t){super(),Z(this,t,dd,fd,U,{})}}function gd(e){let t,n,i,s,o,r,a,l,c,u=e[0].resourceQueriesCount+"",f,d,h,g,p=e[0].resourceTime.toFixed(4)+"",m,_,b,x,v,y=e[0].resourceSlowQueries+"",k,M,$,P;return i=new Da({}),{c(){t=D("div"),n=D("button"),I(i.$$.fragment),s=Y(),o=D("p"),o.textContent=`${e[1].params.resource}`,r=Y(),a=D("div"),l=D("p"),c=J("Queries: "),f=J(u),d=Y(),h=D("p"),g=J("Time: "),m=J(p),_=J(" ms"),b=Y(),x=D("p"),v=J("Slow queries: "),k=J(y),F(n,"class","flex p-2 w-12 bg-dark-600 text-dark-100 hover:text-white rounded-md justify-center items-center hover:bg-dark-500 outline-none border-[1px] border-transparent focus-visible:border-cyan-600"),F(o,"class","text-center text-lg"),F(x,"class","text-yellow-500"),F(a,"class","text-end text-dark-100 flex flex-col text-xs"),F(t,"class","p-4 grid grid-flow-col grid-cols-3 items-center ")},m(O,R){nt(O,t,R),w(t,n),E(i,n,null),w(t,s),w(t,o),w(t,r),w(t,a),w(a,l),w(l,c),w(l,f),w(a,d),w(a,h),w(h,g),w(h,m),w(h,_),w(a,b),w(a,x),w(x,v),w(x,k),M=!0,$||(P=It(n,"click",e[2]),$=!0)},p(O,[R]){(!M||R&1)&&u!==(u=O[0].resourceQueriesCount+"")&&Wt(f,u),(!M||R&1)&&p!==(p=O[0].resourceTime.toFixed(4)+"")&&Wt(m,p),(!M||R&1)&&y!==(y=O[0].resourceSlowQueries+"")&&Wt(k,y)},i(O){M||(S(i.$$.fragment,O),M=!0)},o(O){C(i.$$.fragment,O),M=!1},d(O){O&&X(t),L(i),$=!1,P()}}}function pd(e,t,n){let i;pt(e,Ss,r=>n(0,i=r));const s=Us();return[i,s,()=>Le.goto("/")]}class md extends K{constructor(t){super(),Z(this,t,pd,gd,U,{})}}function el(e,t){const n=i=>{const{action:s,data:o}=i.data;s===e&&t(o)};Li(()=>window.addEventListener("message",n)),rn(()=>window.removeEventListener("message",n))}const nl=()=>!window.invokeNative,il=(e,t=1e3)=>{if(nl())for(const n of e)setTimeout(()=>{window.dispatchEvent(new MessageEvent("message",{data:{action:n.action,data:n.data}}))},t)};function _d(e){let t,n,i,s,o,r,a,l;var c=e[0];function u(f){return{props:{class:"text-dark-300"}}}return c&&(i=xe(c,u())),{c(){t=D("div"),n=D("div"),i&&I(i.$$.fragment),s=Y(),o=D("input"),F(n,"class","pr-2"),F(o,"type","text"),F(o,"class","w-full bg-transparent outline-none"),F(o,"placeholder","Search queries..."),F(t,"class","bg-dark-600 m-4 mt-0 flex items-center rounded-md border-[1px] border-transparent p-2 outline-none transition-all duration-100 focus-within:border-cyan-600")},m(f,d){nt(f,t,d),w(t,n),i&&E(i,n,null),w(t,s),w(t,o),ci(o,e[1]),r=!0,a||(l=It(o,"input",e[2]),a=!0)},p(f,[d]){if(d&1&&c!==(c=f[0])){if(i){Rt();const h=i;C(h.$$.fragment,1,0,()=>{L(h,1)}),$t()}c?(i=xe(c,u()),I(i.$$.fragment),S(i.$$.fragment,1),E(i,n,null)):i=null}d&2&&o.value!==f[1]&&ci(o,f[1])},i(f){r||(i&&S(i.$$.fragment,f),r=!0)},o(f){i&&C(i.$$.fragment,f),r=!1},d(f){f&&X(t),i&&L(i),a=!1,l()}}}function bd(e,t,n){let i;pt(e,Bt,a=>n(3,i=a));let s="",{icon:o}=t;function r(){s=this.value,n(1,s)}return e.$$set=a=>{"icon"in a&&n(0,o=a.icon)},e.$$.update=()=>{e.$$.dirty&2&&(_t(Bt,i.search=s,i),_t(Bt,i.page=0,i))},[o,s,r]}class yd extends K{constructor(t){super(),Z(this,t,bd,_d,U,{icon:0})}}function xd(e){let t;const n=e[2].default,i=Pt(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Dt(i,n,s,s[3],t?Ot(n,s[3],o,null):Ft(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function vd(e){let t,n;const i=[{name:"search"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[xd]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=H(s,i[o]);return t=new fe({props:s}),{c(){I(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&te(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function wd(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M10 10m-7 0a7 7 0 1 0 14 0a7 7 0 1 0 -14 0"}],["path",{d:"M21 21l-6 -6"}]];return e.$$set=r=>{n(1,t=H(H({},t),et(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=et(t),[o,t,i,s]}class Sd extends K{constructor(t){super(),Z(this,t,wd,vd,U,{})}}const sl=Sd;function Cd(e){let t,n,i,s,o,r,a,l,c,u;return i=new md({}),o=new yd({props:{icon:sl}}),a=new hd({}),c=new hu({props:{maxPage:e[0]}}),{c(){t=D("div"),n=D("div"),I(i.$$.fragment),s=Y(),I(o.$$.fragment),r=Y(),I(a.$$.fragment),l=Y(),I(c.$$.fragment),F(t,"class","flex w-full flex-col justify-between")},m(f,d){nt(f,t,d),w(t,n),E(i,n,null),w(n,s),E(o,n,null),w(n,r),E(a,n,null),w(t,l),E(c,t,null),u=!0},p(f,[d]){const h={};d&1&&(h.maxPage=f[0]),c.$set(h)},i(f){u||(S(i.$$.fragment,f),S(o.$$.fragment,f),S(a.$$.fragment,f),S(c.$$.fragment,f),u=!0)},o(f){C(i.$$.fragment,f),C(o.$$.fragment,f),C(a.$$.fragment,f),C(c.$$.fragment,f),u=!1},d(f){f&&X(t),L(i),L(o),L(a),L(c)}}}function Md(e,t,n){let i,s,o;pt(e,Ss,a=>n(1,i=a)),pt(e,ii,a=>n(2,s=a)),pt(e,Bt,a=>n(3,o=a));let r=0;return rn(()=>{_t(ii,s=[],s),_t(Bt,o.page=0,o)}),il([{action:"loadResource",data:{queries:[{query:"SELECT * FROM users WHERE ID = 1",executionTime:3,slow:!1,date:Date.now()},{query:"SELECT * FROM users WHERE ID = 1",executionTime:23,slow:!0,date:Date.now()},{query:"SELECT * FROM users WHERE ID = 1",executionTime:15,slow:!1,date:Date.now()},{query:"SELECT * FROM users WHERE ID = 1",executionTime:122,slow:!0,date:Date.now()}],resourceQueriesCount:3,resourceSlowQueries:2,resourceTime:1342,pageCount:3}}]),el("loadResource",a=>{n(0,r=a.pageCount),_t(ii,s=a.queries,s),_t(Ss,i={resourceQueriesCount:a.resourceQueriesCount,resourceSlowQueries:a.resourceSlowQueries,resourceTime:a.resourceTime},i)}),[r]}class kd extends K{constructor(t){super(),Z(this,t,Md,Cd,U,{})}}function Rd(e){let t,n,i,s,o,r,a,l;var c=e[1];function u(f){return{props:{class:"text-dark-300"}}}return c&&(i=xe(c,u())),{c(){t=D("div"),n=D("div"),i&&I(i.$$.fragment),s=Y(),o=D("input"),F(n,"class","pr-2"),F(o,"type","text"),F(o,"class","bg-transparent outline-none w-full"),F(o,"placeholder","Search resources..."),F(t,"class","p-2 flex items-center outline-none border-[1px] border-transparent transition-all duration-100 focus-within:border-cyan-600 rounded-md bg-dark-600")},m(f,d){nt(f,t,d),w(t,n),i&&E(i,n,null),w(t,s),w(t,o),ci(o,e[0]),r=!0,a||(l=It(o,"input",e[2]),a=!0)},p(f,[d]){if(d&2&&c!==(c=f[1])){if(i){Rt();const h=i;C(h.$$.fragment,1,0,()=>{L(h,1)}),$t()}c?(i=xe(c,u()),I(i.$$.fragment),S(i.$$.fragment,1),E(i,n,null)):i=null}d&1&&o.value!==f[0]&&ci(o,f[0])},i(f){r||(i&&S(i.$$.fragment,f),r=!0)},o(f){i&&C(i.$$.fragment,f),r=!1},d(f){f&&X(t),i&&L(i),a=!1,l()}}}function $d(e,t,n){let{icon:i}=t,{value:s}=t;function o(){s=this.value,n(0,s)}return e.$$set=r=>{"icon"in r&&n(1,i=r.icon),"value"in r&&n(0,s=r.value)},[s,i,o]}class Ad extends K{constructor(t){super(),Z(this,t,$d,Rd,U,{icon:1,value:0})}}function Pd(e){let t;const n=e[2].default,i=Pt(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Dt(i,n,s,s[3],t?Ot(n,s[3],o,null):Ft(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function Od(e){let t,n;const i=[{name:"file-analytics"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[Pd]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=H(s,i[o]);return t=new fe({props:s}),{c(){I(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&te(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function Dd(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M14 3v4a1 1 0 0 0 1 1h4"}],["path",{d:"M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"}],["path",{d:"M9 17l0 -5"}],["path",{d:"M12 17l0 -1"}],["path",{d:"M15 17l0 -3"}]];return e.$$set=r=>{n(1,t=H(H({},t),et(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=et(t),[o,t,i,s]}class Fd extends K{constructor(t){super(),Z(this,t,Dd,Od,U,{})}}const Ed=Fd;function Ld(e){let t;const n=e[2].default,i=Pt(n,e,e[3],null);return{c(){i&&i.c()},m(s,o){i&&i.m(s,o),t=!0},p(s,o){i&&i.p&&(!t||o&8)&&Dt(i,n,s,s[3],t?Ot(n,s[3],o,null):Ft(s[3]),null)},i(s){t||(S(i,s),t=!0)},o(s){C(i,s),t=!1},d(s){i&&i.d(s)}}}function Td(e){let t,n;const i=[{name:"source-code"},e[1],{iconNode:e[0]}];let s={$$slots:{default:[Ld]},$$scope:{ctx:e}};for(let o=0;o<i.length;o+=1)s=H(s,i[o]);return t=new fe({props:s}),{c(){I(t.$$.fragment)},m(o,r){E(t,o,r),n=!0},p(o,[r]){const a=r&3?zt(i,[i[0],r&2&&te(o[1]),r&1&&{iconNode:o[0]}]):{};r&8&&(a.$$scope={dirty:r,ctx:o}),t.$set(a)},i(o){n||(S(t.$$.fragment,o),n=!0)},o(o){C(t.$$.fragment,o),n=!1},d(o){L(t,o)}}}function Id(e,t,n){let{$$slots:i={},$$scope:s}=t;const o=[["path",{d:"M14.5 4h2.5a3 3 0 0 1 3 3v10a3 3 0 0 1 -3 3h-10a3 3 0 0 1 -3 -3v-5"}],["path",{d:"M6 5l-2 2l2 2"}],["path",{d:"M10 9l2 -2l-2 -2"}]];return e.$$set=r=>{n(1,t=H(H({},t),et(r))),"$$scope"in r&&n(3,s=r.$$scope)},t=et(t),[o,t,i,s]}class Vd extends K{constructor(t){super(),Z(this,t,Id,Td,U,{})}}const zd=Vd;/*!
 * @kurkle/color v0.3.4
 * https://github.com/kurkle/color#readme
 * (c) 2024 Jukka Kurkela
 * Released under the MIT License
 */function Dn(e){return e+.5|0}const me=(e,t,n)=>Math.max(Math.min(e,n),t);function pn(e){return me(Dn(e*2.55),0,255)}function ye(e){return me(Dn(e*255),0,255)}function le(e){return me(Dn(e/2.55)/100,0,1)}function Zo(e){return me(Dn(e*100),0,100)}const Tt={0:0,1:1,2:2,3:3,4:4,5:5,6:6,7:7,8:8,9:9,A:10,B:11,C:12,D:13,E:14,F:15,a:10,b:11,c:12,d:13,e:14,f:15},Ps=[..."0123456789ABCDEF"],Hd=e=>Ps[e&15],Nd=e=>Ps[(e&240)>>4]+Ps[e&15],Nn=e=>(e&240)>>4===(e&15),Bd=e=>Nn(e.r)&&Nn(e.g)&&Nn(e.b)&&Nn(e.a);function jd(e){var t=e.length,n;return e[0]==="#"&&(t===4||t===5?n={r:255&Tt[e[1]]*17,g:255&Tt[e[2]]*17,b:255&Tt[e[3]]*17,a:t===5?Tt[e[4]]*17:255}:(t===7||t===9)&&(n={r:Tt[e[1]]<<4|Tt[e[2]],g:Tt[e[3]]<<4|Tt[e[4]],b:Tt[e[5]]<<4|Tt[e[6]],a:t===9?Tt[e[7]]<<4|Tt[e[8]]:255})),n}const Wd=(e,t)=>e<255?t(e):"";function Gd(e){var t=Bd(e)?Hd:Nd;return e?"#"+t(e.r)+t(e.g)+t(e.b)+Wd(e.a,t):void 0}const qd=/^(hsla?|hwb|hsv)\(\s*([-+.e\d]+)(?:deg)?[\s,]+([-+.e\d]+)%[\s,]+([-+.e\d]+)%(?:[\s,]+([-+.e\d]+)(%)?)?\s*\)$/;function ol(e,t,n){const i=t*Math.min(n,1-n),s=(o,r=(o+e/30)%12)=>n-i*Math.max(Math.min(r-3,9-r,1),-1);return[s(0),s(8),s(4)]}function Yd(e,t,n){const i=(s,o=(s+e/60)%6)=>n-n*t*Math.max(Math.min(o,4-o,1),0);return[i(5),i(3),i(1)]}function Ud(e,t,n){const i=ol(e,1,.5);let s;for(t+n>1&&(s=1/(t+n),t*=s,n*=s),s=0;s<3;s++)i[s]*=1-t-n,i[s]+=t;return i}function Xd(e,t,n,i,s){return e===s?(t-n)/i+(t<n?6:0):t===s?(n-e)/i+2:(e-t)/i+4}function no(e){const n=e.r/255,i=e.g/255,s=e.b/255,o=Math.max(n,i,s),r=Math.min(n,i,s),a=(o+r)/2;let l,c,u;return o!==r&&(u=o-r,c=a>.5?u/(2-o-r):u/(o+r),l=Xd(n,i,s,u,o),l=l*60+.5),[l|0,c||0,a]}function io(e,t,n,i){return(Array.isArray(t)?e(t[0],t[1],t[2]):e(t,n,i)).map(ye)}function so(e,t,n){return io(ol,e,t,n)}function Kd(e,t,n){return io(Ud,e,t,n)}function Qd(e,t,n){return io(Yd,e,t,n)}function rl(e){return(e%360+360)%360}function Zd(e){const t=qd.exec(e);let n=255,i;if(!t)return;t[5]!==i&&(n=t[6]?pn(+t[5]):ye(+t[5]));const s=rl(+t[2]),o=+t[3]/100,r=+t[4]/100;return t[1]==="hwb"?i=Kd(s,o,r):t[1]==="hsv"?i=Qd(s,o,r):i=so(s,o,r),{r:i[0],g:i[1],b:i[2],a:n}}function Jd(e,t){var n=no(e);n[0]=rl(n[0]+t),n=so(n),e.r=n[0],e.g=n[1],e.b=n[2]}function th(e){if(!e)return;const t=no(e),n=t[0],i=Zo(t[1]),s=Zo(t[2]);return e.a<255?`hsla(${n}, ${i}%, ${s}%, ${le(e.a)})`:`hsl(${n}, ${i}%, ${s}%)`}const Jo={x:"dark",Z:"light",Y:"re",X:"blu",W:"gr",V:"medium",U:"slate",A:"ee",T:"ol",S:"or",B:"ra",C:"lateg",D:"ights",R:"in",Q:"turquois",E:"hi",P:"ro",O:"al",N:"le",M:"de",L:"yello",F:"en",K:"ch",G:"arks",H:"ea",I:"ightg",J:"wh"},tr={OiceXe:"f0f8ff",antiquewEte:"faebd7",aqua:"ffff",aquamarRe:"7fffd4",azuY:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"0",blanKedOmond:"ffebcd",Xe:"ff",XeviTet:"8a2be2",bPwn:"a52a2a",burlywood:"deb887",caMtXe:"5f9ea0",KartYuse:"7fff00",KocTate:"d2691e",cSO:"ff7f50",cSnflowerXe:"6495ed",cSnsilk:"fff8dc",crimson:"dc143c",cyan:"ffff",xXe:"8b",xcyan:"8b8b",xgTMnPd:"b8860b",xWay:"a9a9a9",xgYF:"6400",xgYy:"a9a9a9",xkhaki:"bdb76b",xmagFta:"8b008b",xTivegYF:"556b2f",xSange:"ff8c00",xScEd:"9932cc",xYd:"8b0000",xsOmon:"e9967a",xsHgYF:"8fbc8f",xUXe:"483d8b",xUWay:"2f4f4f",xUgYy:"2f4f4f",xQe:"ced1",xviTet:"9400d3",dAppRk:"ff1493",dApskyXe:"bfff",dimWay:"696969",dimgYy:"696969",dodgerXe:"1e90ff",fiYbrick:"b22222",flSOwEte:"fffaf0",foYstWAn:"228b22",fuKsia:"ff00ff",gaRsbSo:"dcdcdc",ghostwEte:"f8f8ff",gTd:"ffd700",gTMnPd:"daa520",Way:"808080",gYF:"8000",gYFLw:"adff2f",gYy:"808080",honeyMw:"f0fff0",hotpRk:"ff69b4",RdianYd:"cd5c5c",Rdigo:"4b0082",ivSy:"fffff0",khaki:"f0e68c",lavFMr:"e6e6fa",lavFMrXsh:"fff0f5",lawngYF:"7cfc00",NmoncEffon:"fffacd",ZXe:"add8e6",ZcSO:"f08080",Zcyan:"e0ffff",ZgTMnPdLw:"fafad2",ZWay:"d3d3d3",ZgYF:"90ee90",ZgYy:"d3d3d3",ZpRk:"ffb6c1",ZsOmon:"ffa07a",ZsHgYF:"20b2aa",ZskyXe:"87cefa",ZUWay:"778899",ZUgYy:"778899",ZstAlXe:"b0c4de",ZLw:"ffffe0",lime:"ff00",limegYF:"32cd32",lRF:"faf0e6",magFta:"ff00ff",maPon:"800000",VaquamarRe:"66cdaa",VXe:"cd",VScEd:"ba55d3",VpurpN:"9370db",VsHgYF:"3cb371",VUXe:"7b68ee",VsprRggYF:"fa9a",VQe:"48d1cc",VviTetYd:"c71585",midnightXe:"191970",mRtcYam:"f5fffa",mistyPse:"ffe4e1",moccasR:"ffe4b5",navajowEte:"ffdead",navy:"80",Tdlace:"fdf5e6",Tive:"808000",TivedBb:"6b8e23",Sange:"ffa500",SangeYd:"ff4500",ScEd:"da70d6",pOegTMnPd:"eee8aa",pOegYF:"98fb98",pOeQe:"afeeee",pOeviTetYd:"db7093",papayawEp:"ffefd5",pHKpuff:"ffdab9",peru:"cd853f",pRk:"ffc0cb",plum:"dda0dd",powMrXe:"b0e0e6",purpN:"800080",YbeccapurpN:"663399",Yd:"ff0000",Psybrown:"bc8f8f",PyOXe:"4169e1",saddNbPwn:"8b4513",sOmon:"fa8072",sandybPwn:"f4a460",sHgYF:"2e8b57",sHshell:"fff5ee",siFna:"a0522d",silver:"c0c0c0",skyXe:"87ceeb",UXe:"6a5acd",UWay:"708090",UgYy:"708090",snow:"fffafa",sprRggYF:"ff7f",stAlXe:"4682b4",tan:"d2b48c",teO:"8080",tEstN:"d8bfd8",tomato:"ff6347",Qe:"40e0d0",viTet:"ee82ee",JHt:"f5deb3",wEte:"ffffff",wEtesmoke:"f5f5f5",Lw:"ffff00",LwgYF:"9acd32"};function eh(){const e={},t=Object.keys(tr),n=Object.keys(Jo);let i,s,o,r,a;for(i=0;i<t.length;i++){for(r=a=t[i],s=0;s<n.length;s++)o=n[s],a=a.replace(o,Jo[o]);o=parseInt(tr[r],16),e[a]=[o>>16&255,o>>8&255,o&255]}return e}let Bn;function nh(e){Bn||(Bn=eh(),Bn.transparent=[0,0,0,0]);const t=Bn[e.toLowerCase()];return t&&{r:t[0],g:t[1],b:t[2],a:t.length===4?t[3]:255}}const ih=/^rgba?\(\s*([-+.\d]+)(%)?[\s,]+([-+.e\d]+)(%)?[\s,]+([-+.e\d]+)(%)?(?:[\s,/]+([-+.e\d]+)(%)?)?\s*\)$/;function sh(e){const t=ih.exec(e);let n=255,i,s,o;if(t){if(t[7]!==i){const r=+t[7];n=t[8]?pn(r):me(r*255,0,255)}return i=+t[1],s=+t[3],o=+t[5],i=255&(t[2]?pn(i):me(i,0,255)),s=255&(t[4]?pn(s):me(s,0,255)),o=255&(t[6]?pn(o):me(o,0,255)),{r:i,g:s,b:o,a:n}}}function oh(e){return e&&(e.a<255?`rgba(${e.r}, ${e.g}, ${e.b}, ${le(e.a)})`:`rgb(${e.r}, ${e.g}, ${e.b})`)}const ss=e=>e<=.0031308?e*12.92:Math.pow(e,1/2.4)*1.055-.055,je=e=>e<=.04045?e/12.92:Math.pow((e+.055)/1.055,2.4);function rh(e,t,n){const i=je(le(e.r)),s=je(le(e.g)),o=je(le(e.b));return{r:ye(ss(i+n*(je(le(t.r))-i))),g:ye(ss(s+n*(je(le(t.g))-s))),b:ye(ss(o+n*(je(le(t.b))-o))),a:e.a+n*(t.a-e.a)}}function jn(e,t,n){if(e){let i=no(e);i[t]=Math.max(0,Math.min(i[t]+i[t]*n,t===0?360:1)),i=so(i),e.r=i[0],e.g=i[1],e.b=i[2]}}function al(e,t){return e&&Object.assign(t||{},e)}function er(e){var t={r:0,g:0,b:0,a:255};return Array.isArray(e)?e.length>=3&&(t={r:e[0],g:e[1],b:e[2],a:255},e.length>3&&(t.a=ye(e[3]))):(t=al(e,{r:0,g:0,b:0,a:1}),t.a=ye(t.a)),t}function ah(e){return e.charAt(0)==="r"?sh(e):Zd(e)}class Mn{constructor(t){if(t instanceof Mn)return t;const n=typeof t;let i;n==="object"?i=er(t):n==="string"&&(i=jd(t)||nh(t)||ah(t)),this._rgb=i,this._valid=!!i}get valid(){return this._valid}get rgb(){var t=al(this._rgb);return t&&(t.a=le(t.a)),t}set rgb(t){this._rgb=er(t)}rgbString(){return this._valid?oh(this._rgb):void 0}hexString(){return this._valid?Gd(this._rgb):void 0}hslString(){return this._valid?th(this._rgb):void 0}mix(t,n){if(t){const i=this.rgb,s=t.rgb;let o;const r=n===o?.5:n,a=2*r-1,l=i.a-s.a,c=((a*l===-1?a:(a+l)/(1+a*l))+1)/2;o=1-c,i.r=255&c*i.r+o*s.r+.5,i.g=255&c*i.g+o*s.g+.5,i.b=255&c*i.b+o*s.b+.5,i.a=r*i.a+(1-r)*s.a,this.rgb=i}return this}interpolate(t,n){return t&&(this._rgb=rh(this._rgb,t._rgb,n)),this}clone(){return new Mn(this.rgb)}alpha(t){return this._rgb.a=ye(t),this}clearer(t){const n=this._rgb;return n.a*=1-t,this}greyscale(){const t=this._rgb,n=Dn(t.r*.3+t.g*.59+t.b*.11);return t.r=t.g=t.b=n,this}opaquer(t){const n=this._rgb;return n.a*=1+t,this}negate(){const t=this._rgb;return t.r=255-t.r,t.g=255-t.g,t.b=255-t.b,this}lighten(t){return jn(this._rgb,2,t),this}darken(t){return jn(this._rgb,2,-t),this}saturate(t){return jn(this._rgb,1,t),this}desaturate(t){return jn(this._rgb,1,-t),this}rotate(t){return Jd(this._rgb,t),this}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */function oe(){}const lh=(()=>{let e=0;return()=>e++})();function Q(e){return e==null}function st(e){if(Array.isArray&&Array.isArray(e))return!0;const t=Object.prototype.toString.call(e);return t.slice(0,7)==="[object"&&t.slice(-6)==="Array]"}function W(e){return e!==null&&Object.prototype.toString.call(e)==="[object Object]"}function ht(e){return(typeof e=="number"||e instanceof Number)&&isFinite(+e)}function Ct(e,t){return ht(e)?e:t}function tt(e,t){return typeof e>"u"?t:e}const ch=(e,t)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100:+e/t,ll=(e,t)=>typeof e=="string"&&e.endsWith("%")?parseFloat(e)/100*t:+e;function at(e,t,n){if(e&&typeof e.call=="function")return e.apply(n,t)}function q(e,t,n,i){let s,o,r;if(st(e))if(o=e.length,i)for(s=o-1;s>=0;s--)t.call(n,e[s],s);else for(s=0;s<o;s++)t.call(n,e[s],s);else if(W(e))for(r=Object.keys(e),o=r.length,s=0;s<o;s++)t.call(n,e[r[s]],r[s])}function vi(e,t){let n,i,s,o;if(!e||!t||e.length!==t.length)return!1;for(n=0,i=e.length;n<i;++n)if(s=e[n],o=t[n],s.datasetIndex!==o.datasetIndex||s.index!==o.index)return!1;return!0}function wi(e){if(st(e))return e.map(wi);if(W(e)){const t=Object.create(null),n=Object.keys(e),i=n.length;let s=0;for(;s<i;++s)t[n[s]]=wi(e[n[s]]);return t}return e}function cl(e){return["__proto__","prototype","constructor"].indexOf(e)===-1}function uh(e,t,n,i){if(!cl(e))return;const s=t[e],o=n[e];W(s)&&W(o)?kn(s,o,i):t[e]=wi(o)}function kn(e,t,n){const i=st(t)?t:[t],s=i.length;if(!W(e))return e;n=n||{};const o=n.merger||uh;let r;for(let a=0;a<s;++a){if(r=i[a],!W(r))continue;const l=Object.keys(r);for(let c=0,u=l.length;c<u;++c)o(l[c],e,r,n)}return e}function yn(e,t){return kn(e,t,{merger:fh})}function fh(e,t,n){if(!cl(e))return;const i=t[e],s=n[e];W(i)&&W(s)?yn(i,s):Object.prototype.hasOwnProperty.call(t,e)||(t[e]=wi(s))}const nr={"":e=>e,x:e=>e.x,y:e=>e.y};function dh(e){const t=e.split("."),n=[];let i="";for(const s of t)i+=s,i.endsWith("\\")?i=i.slice(0,-1)+".":(n.push(i),i="");return n}function hh(e){const t=dh(e);return n=>{for(const i of t){if(i==="")break;n=n&&n[i]}return n}}function nn(e,t){return(nr[t]||(nr[t]=hh(t)))(e)}function oo(e){return e.charAt(0).toUpperCase()+e.slice(1)}const Si=e=>typeof e<"u",Se=e=>typeof e=="function",ir=(e,t)=>{if(e.size!==t.size)return!1;for(const n of e)if(!t.has(n))return!1;return!0};function gh(e){return e.type==="mouseup"||e.type==="click"||e.type==="contextmenu"}const rt=Math.PI,lt=2*rt,Ci=Number.POSITIVE_INFINITY,ph=rt/180,ut=rt/2,Me=rt/4,sr=rt*2/3,_e=Math.log10,Mi=Math.sign;function si(e,t,n){return Math.abs(e-t)<n}function or(e){const t=Math.round(e);e=si(e,t,e/1e3)?t:e;const n=Math.pow(10,Math.floor(_e(e))),i=e/n;return(i<=1?1:i<=2?2:i<=5?5:10)*n}function mh(e){const t=[],n=Math.sqrt(e);let i;for(i=1;i<n;i++)e%i===0&&(t.push(i),t.push(e/i));return n===(n|0)&&t.push(n),t.sort((s,o)=>s-o).pop(),t}function _h(e){return typeof e=="symbol"||typeof e=="object"&&e!==null&&!(Symbol.toPrimitive in e||"toString"in e||"valueOf"in e)}function ki(e){return!_h(e)&&!isNaN(parseFloat(e))&&isFinite(e)}function bh(e,t){const n=Math.round(e);return n-t<=e&&n+t>=e}function ul(e,t,n){let i,s,o;for(i=0,s=e.length;i<s;i++)o=e[i][n],isNaN(o)||(t.min=Math.min(t.min,o),t.max=Math.max(t.max,o))}function jt(e){return e*(rt/180)}function ro(e){return e*(180/rt)}function rr(e){if(!ht(e))return;let t=1,n=0;for(;Math.round(e*t)/t!==e;)t*=10,n++;return n}function fl(e,t){const n=t.x-e.x,i=t.y-e.y,s=Math.sqrt(n*n+i*i);let o=Math.atan2(i,n);return o<-.5*rt&&(o+=lt),{angle:o,distance:s}}function yh(e,t){return Math.sqrt(Math.pow(t.x-e.x,2)+Math.pow(t.y-e.y,2))}function Kt(e){return(e%lt+lt)%lt}function Ri(e,t,n,i){const s=Kt(e),o=Kt(t),r=Kt(n),a=Kt(o-s),l=Kt(r-s),c=Kt(s-o),u=Kt(s-r);return s===o||s===r||i&&o===r||a>l&&c<u}function Mt(e,t,n){return Math.max(t,Math.min(n,e))}function xh(e){return Mt(e,-32768,32767)}function vh(e,t,n,i=1e-6){return e>=Math.min(t,n)-i&&e<=Math.max(t,n)+i}function ao(e,t,n){n=n||(r=>e[r]<t);let i=e.length-1,s=0,o;for(;i-s>1;)o=s+i>>1,n(o)?s=o:i=o;return{lo:s,hi:i}}const Os=(e,t,n,i)=>ao(e,n,i?s=>{const o=e[s][t];return o<n||o===n&&e[s+1][t]===n}:s=>e[s][t]<n),wh=(e,t,n)=>ao(e,n,i=>e[i][t]>=n);function Sh(e,t,n){let i=0,s=e.length;for(;i<s&&e[i]<t;)i++;for(;s>i&&e[s-1]>n;)s--;return i>0||s<e.length?e.slice(i,s):e}const dl=["push","pop","shift","splice","unshift"];function Ch(e,t){if(e._chartjs){e._chartjs.listeners.push(t);return}Object.defineProperty(e,"_chartjs",{configurable:!0,enumerable:!1,value:{listeners:[t]}}),dl.forEach(n=>{const i="_onData"+oo(n),s=e[n];Object.defineProperty(e,n,{configurable:!0,enumerable:!1,value(...o){const r=s.apply(this,o);return e._chartjs.listeners.forEach(a=>{typeof a[i]=="function"&&a[i](...o)}),r}})})}function ar(e,t){const n=e._chartjs;if(!n)return;const i=n.listeners,s=i.indexOf(t);s!==-1&&i.splice(s,1),!(i.length>0)&&(dl.forEach(o=>{delete e[o]}),delete e._chartjs)}function Mh(e){const t=new Set(e);return t.size===e.length?e:Array.from(t)}const hl=function(){return typeof window>"u"?function(e){return e()}:window.requestAnimationFrame}();function gl(e,t){let n=[],i=!1;return function(...s){n=s,i||(i=!0,hl.call(window,()=>{i=!1,e.apply(t,n)}))}}function kh(e,t){let n;return function(...i){return t?(clearTimeout(n),n=setTimeout(e,t,i)):e.apply(this,i),t}}const pl=e=>e==="start"?"left":e==="end"?"right":"center",xn=(e,t,n)=>e==="start"?t:e==="end"?n:(t+n)/2,Wn=e=>e===0||e===1,lr=(e,t,n)=>-(Math.pow(2,10*(e-=1))*Math.sin((e-t)*lt/n)),cr=(e,t,n)=>Math.pow(2,-10*e)*Math.sin((e-t)*lt/n)+1,vn={linear:e=>e,easeInQuad:e=>e*e,easeOutQuad:e=>-e*(e-2),easeInOutQuad:e=>(e/=.5)<1?.5*e*e:-.5*(--e*(e-2)-1),easeInCubic:e=>e*e*e,easeOutCubic:e=>(e-=1)*e*e+1,easeInOutCubic:e=>(e/=.5)<1?.5*e*e*e:.5*((e-=2)*e*e+2),easeInQuart:e=>e*e*e*e,easeOutQuart:e=>-((e-=1)*e*e*e-1),easeInOutQuart:e=>(e/=.5)<1?.5*e*e*e*e:-.5*((e-=2)*e*e*e-2),easeInQuint:e=>e*e*e*e*e,easeOutQuint:e=>(e-=1)*e*e*e*e+1,easeInOutQuint:e=>(e/=.5)<1?.5*e*e*e*e*e:.5*((e-=2)*e*e*e*e+2),easeInSine:e=>-Math.cos(e*ut)+1,easeOutSine:e=>Math.sin(e*ut),easeInOutSine:e=>-.5*(Math.cos(rt*e)-1),easeInExpo:e=>e===0?0:Math.pow(2,10*(e-1)),easeOutExpo:e=>e===1?1:-Math.pow(2,-10*e)+1,easeInOutExpo:e=>Wn(e)?e:e<.5?.5*Math.pow(2,10*(e*2-1)):.5*(-Math.pow(2,-10*(e*2-1))+2),easeInCirc:e=>e>=1?e:-(Math.sqrt(1-e*e)-1),easeOutCirc:e=>Math.sqrt(1-(e-=1)*e),easeInOutCirc:e=>(e/=.5)<1?-.5*(Math.sqrt(1-e*e)-1):.5*(Math.sqrt(1-(e-=2)*e)+1),easeInElastic:e=>Wn(e)?e:lr(e,.075,.3),easeOutElastic:e=>Wn(e)?e:cr(e,.075,.3),easeInOutElastic(e){return Wn(e)?e:e<.5?.5*lr(e*2,.1125,.45):.5+.5*cr(e*2-1,.1125,.45)},easeInBack(e){return e*e*((1.70158+1)*e-1.70158)},easeOutBack(e){return(e-=1)*e*((1.70158+1)*e********)+1},easeInOutBack(e){let t=1.70158;return(e/=.5)<1?.5*(e*e*(((t*=1.525)+1)*e-t)):.5*((e-=2)*e*(((t*=1.525)+1)*e+t)+2)},easeInBounce:e=>1-vn.easeOutBounce(1-e),easeOutBounce(e){return e<1/2.75?7.5625*e*e:e<2/2.75?7.5625*(e-=1.5/2.75)*e+.75:e<2.5/2.75?7.5625*(e-=2.25/2.75)*e+.9375:7.5625*(e-=2.625/2.75)*e+.984375},easeInOutBounce:e=>e<.5?vn.easeInBounce(e*2)*.5:vn.easeOutBounce(e*2-1)*.5+.5};function ml(e){if(e&&typeof e=="object"){const t=e.toString();return t==="[object CanvasPattern]"||t==="[object CanvasGradient]"}return!1}function ur(e){return ml(e)?e:new Mn(e)}function os(e){return ml(e)?e:new Mn(e).saturate(.5).darken(.1).hexString()}const Rh=["x","y","borderWidth","radius","tension"],$h=["color","borderColor","backgroundColor"];function Ah(e){e.set("animation",{delay:void 0,duration:1e3,easing:"easeOutQuart",fn:void 0,from:void 0,loop:void 0,to:void 0,type:void 0}),e.describe("animation",{_fallback:!1,_indexable:!1,_scriptable:t=>t!=="onProgress"&&t!=="onComplete"&&t!=="fn"}),e.set("animations",{colors:{type:"color",properties:$h},numbers:{type:"number",properties:Rh}}),e.describe("animations",{_fallback:"animation"}),e.set("transitions",{active:{animation:{duration:400}},resize:{animation:{duration:0}},show:{animations:{colors:{from:"transparent"},visible:{type:"boolean",duration:0}}},hide:{animations:{colors:{to:"transparent"},visible:{type:"boolean",easing:"linear",fn:t=>t|0}}}})}function Ph(e){e.set("layout",{autoPadding:!0,padding:{top:0,right:0,bottom:0,left:0}})}const fr=new Map;function Oh(e,t){t=t||{};const n=e+JSON.stringify(t);let i=fr.get(n);return i||(i=new Intl.NumberFormat(e,t),fr.set(n,i)),i}function Fn(e,t,n){return Oh(t,n).format(e)}const _l={values(e){return st(e)?e:""+e},numeric(e,t,n){if(e===0)return"0";const i=this.chart.options.locale;let s,o=e;if(n.length>1){const c=Math.max(Math.abs(n[0].value),Math.abs(n[n.length-1].value));(c<1e-4||c>1e15)&&(s="scientific"),o=Dh(e,n)}const r=_e(Math.abs(o)),a=isNaN(r)?1:Math.max(Math.min(-1*Math.floor(r),20),0),l={notation:s,minimumFractionDigits:a,maximumFractionDigits:a};return Object.assign(l,this.options.ticks.format),Fn(e,i,l)},logarithmic(e,t,n){if(e===0)return"0";const i=n[t].significand||e/Math.pow(10,Math.floor(_e(e)));return[1,2,3,5,10,15].includes(i)||t>.8*n.length?_l.numeric.call(this,e,t,n):""}};function Dh(e,t){let n=t.length>3?t[2].value-t[1].value:t[1].value-t[0].value;return Math.abs(n)>=1&&e!==Math.floor(e)&&(n=e-Math.floor(e)),n}var Ni={formatters:_l};function Fh(e){e.set("scale",{display:!0,offset:!1,reverse:!1,beginAtZero:!1,bounds:"ticks",clip:!0,grace:0,grid:{display:!0,lineWidth:1,drawOnChartArea:!0,drawTicks:!0,tickLength:8,tickWidth:(t,n)=>n.lineWidth,tickColor:(t,n)=>n.color,offset:!1},border:{display:!0,dash:[],dashOffset:0,width:1},title:{display:!1,text:"",padding:{top:4,bottom:4}},ticks:{minRotation:0,maxRotation:50,mirror:!1,textStrokeWidth:0,textStrokeColor:"",padding:3,display:!0,autoSkip:!0,autoSkipPadding:3,labelOffset:0,callback:Ni.formatters.values,minor:{},major:{},align:"center",crossAlign:"near",showLabelBackdrop:!1,backdropColor:"rgba(255, 255, 255, 0.75)",backdropPadding:2}}),e.route("scale.ticks","color","","color"),e.route("scale.grid","color","","borderColor"),e.route("scale.border","color","","borderColor"),e.route("scale.title","color","","color"),e.describe("scale",{_fallback:!1,_scriptable:t=>!t.startsWith("before")&&!t.startsWith("after")&&t!=="callback"&&t!=="parser",_indexable:t=>t!=="borderDash"&&t!=="tickBorderDash"&&t!=="dash"}),e.describe("scales",{_fallback:"scale"}),e.describe("scale.ticks",{_scriptable:t=>t!=="backdropPadding"&&t!=="callback",_indexable:t=>t!=="backdropPadding"})}const Ve=Object.create(null),Ds=Object.create(null);function wn(e,t){if(!t)return e;const n=t.split(".");for(let i=0,s=n.length;i<s;++i){const o=n[i];e=e[o]||(e[o]=Object.create(null))}return e}function rs(e,t,n){return typeof t=="string"?kn(wn(e,t),n):kn(wn(e,""),t)}class Eh{constructor(t,n){this.animation=void 0,this.backgroundColor="rgba(0,0,0,0.1)",this.borderColor="rgba(0,0,0,0.1)",this.color="#666",this.datasets={},this.devicePixelRatio=i=>i.chart.platform.getDevicePixelRatio(),this.elements={},this.events=["mousemove","mouseout","click","touchstart","touchmove"],this.font={family:"'Helvetica Neue', 'Helvetica', 'Arial', sans-serif",size:12,style:"normal",lineHeight:1.2,weight:null},this.hover={},this.hoverBackgroundColor=(i,s)=>os(s.backgroundColor),this.hoverBorderColor=(i,s)=>os(s.borderColor),this.hoverColor=(i,s)=>os(s.color),this.indexAxis="x",this.interaction={mode:"nearest",intersect:!0,includeInvisible:!1},this.maintainAspectRatio=!0,this.onHover=null,this.onClick=null,this.parsing=!0,this.plugins={},this.responsive=!0,this.scale=void 0,this.scales={},this.showLine=!0,this.drawActiveElementsOnTop=!0,this.describe(t),this.apply(n)}set(t,n){return rs(this,t,n)}get(t){return wn(this,t)}describe(t,n){return rs(Ds,t,n)}override(t,n){return rs(Ve,t,n)}route(t,n,i,s){const o=wn(this,t),r=wn(this,i),a="_"+n;Object.defineProperties(o,{[a]:{value:o[n],writable:!0},[n]:{enumerable:!0,get(){const l=this[a],c=r[s];return W(l)?Object.assign({},c,l):tt(l,c)},set(l){this[a]=l}}})}apply(t){t.forEach(n=>n(this))}}var ot=new Eh({_scriptable:e=>!e.startsWith("on"),_indexable:e=>e!=="events",hover:{_fallback:"interaction"},interaction:{_scriptable:!1,_indexable:!1}},[Ah,Ph,Fh]);function Lh(e){return!e||Q(e.size)||Q(e.family)?null:(e.style?e.style+" ":"")+(e.weight?e.weight+" ":"")+e.size+"px "+e.family}function $i(e,t,n,i,s){let o=t[s];return o||(o=t[s]=e.measureText(s).width,n.push(s)),o>i&&(i=o),i}function Th(e,t,n,i){i=i||{};let s=i.data=i.data||{},o=i.garbageCollect=i.garbageCollect||[];i.font!==t&&(s=i.data={},o=i.garbageCollect=[],i.font=t),e.save(),e.font=t;let r=0;const a=n.length;let l,c,u,f,d;for(l=0;l<a;l++)if(f=n[l],f!=null&&!st(f))r=$i(e,s,o,r,f);else if(st(f))for(c=0,u=f.length;c<u;c++)d=f[c],d!=null&&!st(d)&&(r=$i(e,s,o,r,d));e.restore();const h=o.length/2;if(h>n.length){for(l=0;l<h;l++)delete s[o[l]];o.splice(0,h)}return r}function ke(e,t,n){const i=e.currentDevicePixelRatio,s=n!==0?Math.max(n/2,.5):0;return Math.round((t-s)*i)/i+s}function dr(e,t){!t&&!e||(t=t||e.getContext("2d"),t.save(),t.resetTransform(),t.clearRect(0,0,e.width,e.height),t.restore())}function hr(e,t,n,i){Ih(e,t,n,i,null)}function Ih(e,t,n,i,s){let o,r,a,l,c,u,f,d;const h=t.pointStyle,g=t.rotation,p=t.radius;let m=(g||0)*ph;if(h&&typeof h=="object"&&(o=h.toString(),o==="[object HTMLImageElement]"||o==="[object HTMLCanvasElement]")){e.save(),e.translate(n,i),e.rotate(m),e.drawImage(h,-h.width/2,-h.height/2,h.width,h.height),e.restore();return}if(!(isNaN(p)||p<=0)){switch(e.beginPath(),h){default:s?e.ellipse(n,i,s/2,p,0,0,lt):e.arc(n,i,p,0,lt),e.closePath();break;case"triangle":u=s?s/2:p,e.moveTo(n+Math.sin(m)*u,i-Math.cos(m)*p),m+=sr,e.lineTo(n+Math.sin(m)*u,i-Math.cos(m)*p),m+=sr,e.lineTo(n+Math.sin(m)*u,i-Math.cos(m)*p),e.closePath();break;case"rectRounded":c=p*.516,l=p-c,r=Math.cos(m+Me)*l,f=Math.cos(m+Me)*(s?s/2-c:l),a=Math.sin(m+Me)*l,d=Math.sin(m+Me)*(s?s/2-c:l),e.arc(n-f,i-a,c,m-rt,m-ut),e.arc(n+d,i-r,c,m-ut,m),e.arc(n+f,i+a,c,m,m+ut),e.arc(n-d,i+r,c,m+ut,m+rt),e.closePath();break;case"rect":if(!g){l=Math.SQRT1_2*p,u=s?s/2:l,e.rect(n-u,i-l,2*u,2*l);break}m+=Me;case"rectRot":f=Math.cos(m)*(s?s/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,d=Math.sin(m)*(s?s/2:p),e.moveTo(n-f,i-a),e.lineTo(n+d,i-r),e.lineTo(n+f,i+a),e.lineTo(n-d,i+r),e.closePath();break;case"crossRot":m+=Me;case"cross":f=Math.cos(m)*(s?s/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,d=Math.sin(m)*(s?s/2:p),e.moveTo(n-f,i-a),e.lineTo(n+f,i+a),e.moveTo(n+d,i-r),e.lineTo(n-d,i+r);break;case"star":f=Math.cos(m)*(s?s/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,d=Math.sin(m)*(s?s/2:p),e.moveTo(n-f,i-a),e.lineTo(n+f,i+a),e.moveTo(n+d,i-r),e.lineTo(n-d,i+r),m+=Me,f=Math.cos(m)*(s?s/2:p),r=Math.cos(m)*p,a=Math.sin(m)*p,d=Math.sin(m)*(s?s/2:p),e.moveTo(n-f,i-a),e.lineTo(n+f,i+a),e.moveTo(n+d,i-r),e.lineTo(n-d,i+r);break;case"line":r=s?s/2:Math.cos(m)*p,a=Math.sin(m)*p,e.moveTo(n-r,i-a),e.lineTo(n+r,i+a);break;case"dash":e.moveTo(n,i),e.lineTo(n+Math.cos(m)*(s?s/2:p),i+Math.sin(m)*p);break;case!1:e.closePath();break}e.fill(),t.borderWidth>0&&e.stroke()}}function qe(e,t,n){return n=n||.5,!t||e&&e.x>t.left-n&&e.x<t.right+n&&e.y>t.top-n&&e.y<t.bottom+n}function bl(e,t){e.save(),e.beginPath(),e.rect(t.left,t.top,t.right-t.left,t.bottom-t.top),e.clip()}function yl(e){e.restore()}function Vh(e,t){t.translation&&e.translate(t.translation[0],t.translation[1]),Q(t.rotation)||e.rotate(t.rotation),t.color&&(e.fillStyle=t.color),t.textAlign&&(e.textAlign=t.textAlign),t.textBaseline&&(e.textBaseline=t.textBaseline)}function zh(e,t,n,i,s){if(s.strikethrough||s.underline){const o=e.measureText(i),r=t-o.actualBoundingBoxLeft,a=t+o.actualBoundingBoxRight,l=n-o.actualBoundingBoxAscent,c=n+o.actualBoundingBoxDescent,u=s.strikethrough?(l+c)/2:c;e.strokeStyle=e.fillStyle,e.beginPath(),e.lineWidth=s.decorationWidth||2,e.moveTo(r,u),e.lineTo(a,u),e.stroke()}}function Hh(e,t){const n=e.fillStyle;e.fillStyle=t.color,e.fillRect(t.left,t.top,t.width,t.height),e.fillStyle=n}function Rn(e,t,n,i,s,o={}){const r=st(t)?t:[t],a=o.strokeWidth>0&&o.strokeColor!=="";let l,c;for(e.save(),e.font=s.string,Vh(e,o),l=0;l<r.length;++l)c=r[l],o.backdrop&&Hh(e,o.backdrop),a&&(o.strokeColor&&(e.strokeStyle=o.strokeColor),Q(o.strokeWidth)||(e.lineWidth=o.strokeWidth),e.strokeText(c,n,i,o.maxWidth)),e.fillText(c,n,i,o.maxWidth),zh(e,n,i,c,o),i+=Number(s.lineHeight);e.restore()}function Fs(e,t){const{x:n,y:i,w:s,h:o,radius:r}=t;e.arc(n+r.topLeft,i+r.topLeft,r.topLeft,1.5*rt,rt,!0),e.lineTo(n,i+o-r.bottomLeft),e.arc(n+r.bottomLeft,i+o-r.bottomLeft,r.bottomLeft,rt,ut,!0),e.lineTo(n+s-r.bottomRight,i+o),e.arc(n+s-r.bottomRight,i+o-r.bottomRight,r.bottomRight,ut,0,!0),e.lineTo(n+s,i+r.topRight),e.arc(n+s-r.topRight,i+r.topRight,r.topRight,0,-ut,!0),e.lineTo(n+r.topLeft,i)}const Nh=/^(normal|(\d+(?:\.\d+)?)(px|em|%)?)$/,Bh=/^(normal|italic|initial|inherit|unset|(oblique( -?[0-9]?[0-9]deg)?))$/;function jh(e,t){const n=(""+e).match(Nh);if(!n||n[1]==="normal")return t*1.2;switch(e=+n[2],n[3]){case"px":return e;case"%":e/=100;break}return t*e}const Wh=e=>+e||0;function lo(e,t){const n={},i=W(t),s=i?Object.keys(t):t,o=W(e)?i?r=>tt(e[r],e[t[r]]):r=>e[r]:()=>e;for(const r of s)n[r]=Wh(o(r));return n}function Gh(e){return lo(e,{top:"y",right:"x",bottom:"y",left:"x"})}function Sn(e){return lo(e,["topLeft","topRight","bottomLeft","bottomRight"])}function At(e){const t=Gh(e);return t.width=t.left+t.right,t.height=t.top+t.bottom,t}function bt(e,t){e=e||{},t=t||ot.font;let n=tt(e.size,t.size);typeof n=="string"&&(n=parseInt(n,10));let i=tt(e.style,t.style);i&&!(""+i).match(Bh)&&(console.warn('Invalid font style specified: "'+i+'"'),i=void 0);const s={family:tt(e.family,t.family),lineHeight:jh(tt(e.lineHeight,t.lineHeight),n),size:n,style:i,weight:tt(e.weight,t.weight),string:""};return s.string=Lh(s),s}function Gn(e,t,n,i){let s=!0,o,r,a;for(o=0,r=e.length;o<r;++o)if(a=e[o],a!==void 0&&(t!==void 0&&typeof a=="function"&&(a=a(t),s=!1),n!==void 0&&st(a)&&(a=a[n%a.length],s=!1),a!==void 0))return i&&!s&&(i.cacheable=!1),a}function qh(e,t,n){const{min:i,max:s}=e,o=ll(t,(s-i)/2),r=(a,l)=>n&&a===0?0:a+l;return{min:r(i,-Math.abs(o)),max:r(s,o)}}function ze(e,t){return Object.assign(Object.create(e),t)}function co(e,t=[""],n,i,s=()=>e[0]){const o=n||e;typeof i>"u"&&(i=Sl("_fallback",e));const r={[Symbol.toStringTag]:"Object",_cacheable:!0,_scopes:e,_rootScopes:o,_fallback:i,_getTarget:s,override:a=>co([a,...e],t,o,i)};return new Proxy(r,{deleteProperty(a,l){return delete a[l],delete a._keys,delete e[0][l],!0},get(a,l){return vl(a,l,()=>tg(l,t,e,a))},getOwnPropertyDescriptor(a,l){return Reflect.getOwnPropertyDescriptor(a._scopes[0],l)},getPrototypeOf(){return Reflect.getPrototypeOf(e[0])},has(a,l){return pr(a).includes(l)},ownKeys(a){return pr(a)},set(a,l,c){const u=a._storage||(a._storage=s());return a[l]=u[l]=c,delete a._keys,!0}})}function sn(e,t,n,i){const s={_cacheable:!1,_proxy:e,_context:t,_subProxy:n,_stack:new Set,_descriptors:xl(e,i),setContext:o=>sn(e,o,n,i),override:o=>sn(e.override(o),t,n,i)};return new Proxy(s,{deleteProperty(o,r){return delete o[r],delete e[r],!0},get(o,r,a){return vl(o,r,()=>Uh(o,r,a))},getOwnPropertyDescriptor(o,r){return o._descriptors.allKeys?Reflect.has(e,r)?{enumerable:!0,configurable:!0}:void 0:Reflect.getOwnPropertyDescriptor(e,r)},getPrototypeOf(){return Reflect.getPrototypeOf(e)},has(o,r){return Reflect.has(e,r)},ownKeys(){return Reflect.ownKeys(e)},set(o,r,a){return e[r]=a,delete o[r],!0}})}function xl(e,t={scriptable:!0,indexable:!0}){const{_scriptable:n=t.scriptable,_indexable:i=t.indexable,_allKeys:s=t.allKeys}=e;return{allKeys:s,scriptable:n,indexable:i,isScriptable:Se(n)?n:()=>n,isIndexable:Se(i)?i:()=>i}}const Yh=(e,t)=>e?e+oo(t):t,uo=(e,t)=>W(t)&&e!=="adapters"&&(Object.getPrototypeOf(t)===null||t.constructor===Object);function vl(e,t,n){if(Object.prototype.hasOwnProperty.call(e,t)||t==="constructor")return e[t];const i=n();return e[t]=i,i}function Uh(e,t,n){const{_proxy:i,_context:s,_subProxy:o,_descriptors:r}=e;let a=i[t];return Se(a)&&r.isScriptable(t)&&(a=Xh(t,a,e,n)),st(a)&&a.length&&(a=Kh(t,a,e,r.isIndexable)),uo(t,a)&&(a=sn(a,s,o&&o[t],r)),a}function Xh(e,t,n,i){const{_proxy:s,_context:o,_subProxy:r,_stack:a}=n;if(a.has(e))throw new Error("Recursion detected: "+Array.from(a).join("->")+"->"+e);a.add(e);let l=t(o,r||i);return a.delete(e),uo(e,l)&&(l=fo(s._scopes,s,e,l)),l}function Kh(e,t,n,i){const{_proxy:s,_context:o,_subProxy:r,_descriptors:a}=n;if(typeof o.index<"u"&&i(e))return t[o.index%t.length];if(W(t[0])){const l=t,c=s._scopes.filter(u=>u!==l);t=[];for(const u of l){const f=fo(c,s,e,u);t.push(sn(f,o,r&&r[e],a))}}return t}function wl(e,t,n){return Se(e)?e(t,n):e}const Qh=(e,t)=>e===!0?t:typeof e=="string"?nn(t,e):void 0;function Zh(e,t,n,i,s){for(const o of t){const r=Qh(n,o);if(r){e.add(r);const a=wl(r._fallback,n,s);if(typeof a<"u"&&a!==n&&a!==i)return a}else if(r===!1&&typeof i<"u"&&n!==i)return null}return!1}function fo(e,t,n,i){const s=t._rootScopes,o=wl(t._fallback,n,i),r=[...e,...s],a=new Set;a.add(i);let l=gr(a,r,n,o||n,i);return l===null||typeof o<"u"&&o!==n&&(l=gr(a,r,o,l,i),l===null)?!1:co(Array.from(a),[""],s,o,()=>Jh(t,n,i))}function gr(e,t,n,i,s){for(;n;)n=Zh(e,t,n,i,s);return n}function Jh(e,t,n){const i=e._getTarget();t in i||(i[t]={});const s=i[t];return st(s)&&W(n)?n:s||{}}function tg(e,t,n,i){let s;for(const o of t)if(s=Sl(Yh(o,e),n),typeof s<"u")return uo(e,s)?fo(n,i,e,s):s}function Sl(e,t){for(const n of t){if(!n)continue;const i=n[e];if(typeof i<"u")return i}}function pr(e){let t=e._keys;return t||(t=e._keys=eg(e._scopes)),t}function eg(e){const t=new Set;for(const n of e)for(const i of Object.keys(n).filter(s=>!s.startsWith("_")))t.add(i);return Array.from(t)}function ng(e,t,n,i){const{iScale:s}=e,{key:o="r"}=this._parsing,r=new Array(i);let a,l,c,u;for(a=0,l=i;a<l;++a)c=a+n,u=t[c],r[a]={r:s.parse(nn(u,o),c)};return r}function ho(){return typeof window<"u"&&typeof document<"u"}function go(e){let t=e.parentNode;return t&&t.toString()==="[object ShadowRoot]"&&(t=t.host),t}function Ai(e,t,n){let i;return typeof e=="string"?(i=parseInt(e,10),e.indexOf("%")!==-1&&(i=i/100*t.parentNode[n])):i=e,i}const Bi=e=>e.ownerDocument.defaultView.getComputedStyle(e,null);function ig(e,t){return Bi(e).getPropertyValue(t)}const sg=["top","right","bottom","left"];function Fe(e,t,n){const i={};n=n?"-"+n:"";for(let s=0;s<4;s++){const o=sg[s];i[o]=parseFloat(e[t+"-"+o+n])||0}return i.width=i.left+i.right,i.height=i.top+i.bottom,i}const og=(e,t,n)=>(e>0||t>0)&&(!n||!n.shadowRoot);function rg(e,t){const n=e.touches,i=n&&n.length?n[0]:e,{offsetX:s,offsetY:o}=i;let r=!1,a,l;if(og(s,o,e.target))a=s,l=o;else{const c=t.getBoundingClientRect();a=i.clientX-c.left,l=i.clientY-c.top,r=!0}return{x:a,y:l,box:r}}function Ae(e,t){if("native"in e)return e;const{canvas:n,currentDevicePixelRatio:i}=t,s=Bi(n),o=s.boxSizing==="border-box",r=Fe(s,"padding"),a=Fe(s,"border","width"),{x:l,y:c,box:u}=rg(e,n),f=r.left+(u&&a.left),d=r.top+(u&&a.top);let{width:h,height:g}=t;return o&&(h-=r.width+a.width,g-=r.height+a.height),{x:Math.round((l-f)/h*n.width/i),y:Math.round((c-d)/g*n.height/i)}}function ag(e,t,n){let i,s;if(t===void 0||n===void 0){const o=e&&go(e);if(!o)t=e.clientWidth,n=e.clientHeight;else{const r=o.getBoundingClientRect(),a=Bi(o),l=Fe(a,"border","width"),c=Fe(a,"padding");t=r.width-c.width-l.width,n=r.height-c.height-l.height,i=Ai(a.maxWidth,o,"clientWidth"),s=Ai(a.maxHeight,o,"clientHeight")}}return{width:t,height:n,maxWidth:i||Ci,maxHeight:s||Ci}}const qn=e=>Math.round(e*10)/10;function lg(e,t,n,i){const s=Bi(e),o=Fe(s,"margin"),r=Ai(s.maxWidth,e,"clientWidth")||Ci,a=Ai(s.maxHeight,e,"clientHeight")||Ci,l=ag(e,t,n);let{width:c,height:u}=l;if(s.boxSizing==="content-box"){const d=Fe(s,"border","width"),h=Fe(s,"padding");c-=h.width+d.width,u-=h.height+d.height}return c=Math.max(0,c-o.width),u=Math.max(0,i?c/i:u-o.height),c=qn(Math.min(c,r,l.maxWidth)),u=qn(Math.min(u,a,l.maxHeight)),c&&!u&&(u=qn(c/2)),(t!==void 0||n!==void 0)&&i&&l.height&&u>l.height&&(u=l.height,c=qn(Math.floor(u*i))),{width:c,height:u}}function mr(e,t,n){const i=t||1,s=Math.floor(e.height*i),o=Math.floor(e.width*i);e.height=Math.floor(e.height),e.width=Math.floor(e.width);const r=e.canvas;return r.style&&(n||!r.style.height&&!r.style.width)&&(r.style.height=`${e.height}px`,r.style.width=`${e.width}px`),e.currentDevicePixelRatio!==i||r.height!==s||r.width!==o?(e.currentDevicePixelRatio=i,r.height=s,r.width=o,e.ctx.setTransform(i,0,0,i,0,0),!0):!1}const cg=function(){let e=!1;try{const t={get passive(){return e=!0,!1}};ho()&&(window.addEventListener("test",null,t),window.removeEventListener("test",null,t))}catch{}return e}();function _r(e,t){const n=ig(e,t),i=n&&n.match(/^(\d+)(\.\d+)?px$/);return i?+i[1]:void 0}const ug=function(e,t){return{x(n){return e+e+t-n},setWidth(n){t=n},textAlign(n){return n==="center"?n:n==="right"?"left":"right"},xPlus(n,i){return n-i},leftForLtr(n,i){return n-i}}},fg=function(){return{x(e){return e},setWidth(e){},textAlign(e){return e},xPlus(e,t){return e+t},leftForLtr(e,t){return e}}};function as(e,t,n){return e?ug(t,n):fg()}function dg(e,t){let n,i;(t==="ltr"||t==="rtl")&&(n=e.canvas.style,i=[n.getPropertyValue("direction"),n.getPropertyPriority("direction")],n.setProperty("direction",t,"important"),e.prevTextDirection=i)}function hg(e,t){t!==void 0&&(delete e.prevTextDirection,e.canvas.style.setProperty("direction",t[0],t[1]))}function Yn(e,t,n){return e.options.clip?e[n]:t[n]}function gg(e,t){const{xScale:n,yScale:i}=e;return n&&i?{left:Yn(n,t,"left"),right:Yn(n,t,"right"),top:Yn(i,t,"top"),bottom:Yn(i,t,"bottom")}:t}function pg(e,t){const n=t._clip;if(n.disabled)return!1;const i=gg(t,e.chartArea);return{left:n.left===!1?0:i.left-(n.left===!0?0:n.left),right:n.right===!1?e.width:i.right+(n.right===!0?0:n.right),top:n.top===!1?0:i.top-(n.top===!0?0:n.top),bottom:n.bottom===!1?e.height:i.bottom+(n.bottom===!0?0:n.bottom)}}/*!
 * Chart.js v4.4.9
 * https://www.chartjs.org
 * (c) 2025 Chart.js Contributors
 * Released under the MIT License
 */class mg{constructor(){this._request=null,this._charts=new Map,this._running=!1,this._lastDate=void 0}_notify(t,n,i,s){const o=n.listeners[s],r=n.duration;o.forEach(a=>a({chart:t,initial:n.initial,numSteps:r,currentStep:Math.min(i-n.start,r)}))}_refresh(){this._request||(this._running=!0,this._request=hl.call(window,()=>{this._update(),this._request=null,this._running&&this._refresh()}))}_update(t=Date.now()){let n=0;this._charts.forEach((i,s)=>{if(!i.running||!i.items.length)return;const o=i.items;let r=o.length-1,a=!1,l;for(;r>=0;--r)l=o[r],l._active?(l._total>i.duration&&(i.duration=l._total),l.tick(t),a=!0):(o[r]=o[o.length-1],o.pop());a&&(s.draw(),this._notify(s,i,t,"progress")),o.length||(i.running=!1,this._notify(s,i,t,"complete"),i.initial=!1),n+=o.length}),this._lastDate=t,n===0&&(this._running=!1)}_getAnims(t){const n=this._charts;let i=n.get(t);return i||(i={running:!1,initial:!0,items:[],listeners:{complete:[],progress:[]}},n.set(t,i)),i}listen(t,n,i){this._getAnims(t).listeners[n].push(i)}add(t,n){!n||!n.length||this._getAnims(t).items.push(...n)}has(t){return this._getAnims(t).items.length>0}start(t){const n=this._charts.get(t);n&&(n.running=!0,n.start=Date.now(),n.duration=n.items.reduce((i,s)=>Math.max(i,s._duration),0),this._refresh())}running(t){if(!this._running)return!1;const n=this._charts.get(t);return!(!n||!n.running||!n.items.length)}stop(t){const n=this._charts.get(t);if(!n||!n.items.length)return;const i=n.items;let s=i.length-1;for(;s>=0;--s)i[s].cancel();n.items=[],this._notify(t,n,Date.now(),"complete")}remove(t){return this._charts.delete(t)}}var re=new mg;const br="transparent",_g={boolean(e,t,n){return n>.5?t:e},color(e,t,n){const i=ur(e||br),s=i.valid&&ur(t||br);return s&&s.valid?s.mix(i,n).hexString():t},number(e,t,n){return e+(t-e)*n}};class bg{constructor(t,n,i,s){const o=n[i];s=Gn([t.to,s,o,t.from]);const r=Gn([t.from,o,s]);this._active=!0,this._fn=t.fn||_g[t.type||typeof r],this._easing=vn[t.easing]||vn.linear,this._start=Math.floor(Date.now()+(t.delay||0)),this._duration=this._total=Math.floor(t.duration),this._loop=!!t.loop,this._target=n,this._prop=i,this._from=r,this._to=s,this._promises=void 0}active(){return this._active}update(t,n,i){if(this._active){this._notify(!1);const s=this._target[this._prop],o=i-this._start,r=this._duration-o;this._start=i,this._duration=Math.floor(Math.max(r,t.duration)),this._total+=o,this._loop=!!t.loop,this._to=Gn([t.to,n,s,t.from]),this._from=Gn([t.from,s,n])}}cancel(){this._active&&(this.tick(Date.now()),this._active=!1,this._notify(!1))}tick(t){const n=t-this._start,i=this._duration,s=this._prop,o=this._from,r=this._loop,a=this._to;let l;if(this._active=o!==a&&(r||n<i),!this._active){this._target[s]=a,this._notify(!0);return}if(n<0){this._target[s]=o;return}l=n/i%2,l=r&&l>1?2-l:l,l=this._easing(Math.min(1,Math.max(0,l))),this._target[s]=this._fn(o,a,l)}wait(){const t=this._promises||(this._promises=[]);return new Promise((n,i)=>{t.push({res:n,rej:i})})}_notify(t){const n=t?"res":"rej",i=this._promises||[];for(let s=0;s<i.length;s++)i[s][n]()}}class Cl{constructor(t,n){this._chart=t,this._properties=new Map,this.configure(n)}configure(t){if(!W(t))return;const n=Object.keys(ot.animation),i=this._properties;Object.getOwnPropertyNames(t).forEach(s=>{const o=t[s];if(!W(o))return;const r={};for(const a of n)r[a]=o[a];(st(o.properties)&&o.properties||[s]).forEach(a=>{(a===s||!i.has(a))&&i.set(a,r)})})}_animateOptions(t,n){const i=n.options,s=xg(t,i);if(!s)return[];const o=this._createAnimations(s,i);return i.$shared&&yg(t.options.$animations,i).then(()=>{t.options=i},()=>{}),o}_createAnimations(t,n){const i=this._properties,s=[],o=t.$animations||(t.$animations={}),r=Object.keys(n),a=Date.now();let l;for(l=r.length-1;l>=0;--l){const c=r[l];if(c.charAt(0)==="$")continue;if(c==="options"){s.push(...this._animateOptions(t,n));continue}const u=n[c];let f=o[c];const d=i.get(c);if(f)if(d&&f.active()){f.update(d,u,a);continue}else f.cancel();if(!d||!d.duration){t[c]=u;continue}o[c]=f=new bg(d,t,c,u),s.push(f)}return s}update(t,n){if(this._properties.size===0){Object.assign(t,n);return}const i=this._createAnimations(t,n);if(i.length)return re.add(this._chart,i),!0}}function yg(e,t){const n=[],i=Object.keys(t);for(let s=0;s<i.length;s++){const o=e[i[s]];o&&o.active()&&n.push(o.wait())}return Promise.all(n)}function xg(e,t){if(!t)return;let n=e.options;if(!n){e.options=t;return}return n.$shared&&(e.options=n=Object.assign({},n,{$shared:!1,$animations:{}})),n}function yr(e,t){const n=e&&e.options||{},i=n.reverse,s=n.min===void 0?t:0,o=n.max===void 0?t:0;return{start:i?o:s,end:i?s:o}}function vg(e,t,n){if(n===!1)return!1;const i=yr(e,n),s=yr(t,n);return{top:s.end,right:i.end,bottom:s.start,left:i.start}}function wg(e){let t,n,i,s;return W(e)?(t=e.top,n=e.right,i=e.bottom,s=e.left):t=n=i=s=e,{top:t,right:n,bottom:i,left:s,disabled:e===!1}}function Ml(e,t){const n=[],i=e._getSortedDatasetMetas(t);let s,o;for(s=0,o=i.length;s<o;++s)n.push(i[s].index);return n}function xr(e,t,n,i={}){const s=e.keys,o=i.mode==="single";let r,a,l,c;if(t===null)return;let u=!1;for(r=0,a=s.length;r<a;++r){if(l=+s[r],l===n){if(u=!0,i.all)continue;break}c=e.values[l],ht(c)&&(o||t===0||Mi(t)===Mi(c))&&(t+=c)}return!u&&!i.all?0:t}function Sg(e,t){const{iScale:n,vScale:i}=t,s=n.axis==="x"?"x":"y",o=i.axis==="x"?"x":"y",r=Object.keys(e),a=new Array(r.length);let l,c,u;for(l=0,c=r.length;l<c;++l)u=r[l],a[l]={[s]:u,[o]:e[u]};return a}function ls(e,t){const n=e&&e.options.stacked;return n||n===void 0&&t.stack!==void 0}function Cg(e,t,n){return`${e.id}.${t.id}.${n.stack||n.type}`}function Mg(e){const{min:t,max:n,minDefined:i,maxDefined:s}=e.getUserBounds();return{min:i?t:Number.NEGATIVE_INFINITY,max:s?n:Number.POSITIVE_INFINITY}}function kg(e,t,n){const i=e[t]||(e[t]={});return i[n]||(i[n]={})}function vr(e,t,n,i){for(const s of t.getMatchingVisibleMetas(i).reverse()){const o=e[s.index];if(n&&o>0||!n&&o<0)return s.index}return null}function wr(e,t){const{chart:n,_cachedMeta:i}=e,s=n._stacks||(n._stacks={}),{iScale:o,vScale:r,index:a}=i,l=o.axis,c=r.axis,u=Cg(o,r,i),f=t.length;let d;for(let h=0;h<f;++h){const g=t[h],{[l]:p,[c]:m}=g,_=g._stacks||(g._stacks={});d=_[c]=kg(s,u,p),d[a]=m,d._top=vr(d,r,!0,i.type),d._bottom=vr(d,r,!1,i.type);const b=d._visualValues||(d._visualValues={});b[a]=m}}function cs(e,t){const n=e.scales;return Object.keys(n).filter(i=>n[i].axis===t).shift()}function Rg(e,t){return ze(e,{active:!1,dataset:void 0,datasetIndex:t,index:t,mode:"default",type:"dataset"})}function $g(e,t,n){return ze(e,{active:!1,dataIndex:t,parsed:void 0,raw:void 0,element:n,index:t,mode:"default",type:"data"})}function un(e,t){const n=e.controller.index,i=e.vScale&&e.vScale.axis;if(i){t=t||e._parsed;for(const s of t){const o=s._stacks;if(!o||o[i]===void 0||o[i][n]===void 0)return;delete o[i][n],o[i]._visualValues!==void 0&&o[i]._visualValues[n]!==void 0&&delete o[i]._visualValues[n]}}}const us=e=>e==="reset"||e==="none",Sr=(e,t)=>t?e:Object.assign({},e),Ag=(e,t,n)=>e&&!t.hidden&&t._stacked&&{keys:Ml(n,!0),values:null};class Qe{constructor(t,n){this.chart=t,this._ctx=t.ctx,this.index=n,this._cachedDataOpts={},this._cachedMeta=this.getMeta(),this._type=this._cachedMeta.type,this.options=void 0,this._parsing=!1,this._data=void 0,this._objectData=void 0,this._sharedOptions=void 0,this._drawStart=void 0,this._drawCount=void 0,this.enableOptionSharing=!1,this.supportsDecimation=!1,this.$context=void 0,this._syncList=[],this.datasetElementType=new.target.datasetElementType,this.dataElementType=new.target.dataElementType,this.initialize()}initialize(){const t=this._cachedMeta;this.configure(),this.linkScales(),t._stacked=ls(t.vScale,t),this.addElements(),this.options.fill&&!this.chart.isPluginEnabled("filler")&&console.warn("Tried to use the 'fill' option without the 'Filler' plugin enabled. Please import and register the 'Filler' plugin and make sure it is not disabled in the options")}updateIndex(t){this.index!==t&&un(this._cachedMeta),this.index=t}linkScales(){const t=this.chart,n=this._cachedMeta,i=this.getDataset(),s=(f,d,h,g)=>f==="x"?d:f==="r"?g:h,o=n.xAxisID=tt(i.xAxisID,cs(t,"x")),r=n.yAxisID=tt(i.yAxisID,cs(t,"y")),a=n.rAxisID=tt(i.rAxisID,cs(t,"r")),l=n.indexAxis,c=n.iAxisID=s(l,o,r,a),u=n.vAxisID=s(l,r,o,a);n.xScale=this.getScaleForId(o),n.yScale=this.getScaleForId(r),n.rScale=this.getScaleForId(a),n.iScale=this.getScaleForId(c),n.vScale=this.getScaleForId(u)}getDataset(){return this.chart.data.datasets[this.index]}getMeta(){return this.chart.getDatasetMeta(this.index)}getScaleForId(t){return this.chart.scales[t]}_getOtherScale(t){const n=this._cachedMeta;return t===n.iScale?n.vScale:n.iScale}reset(){this._update("reset")}_destroy(){const t=this._cachedMeta;this._data&&ar(this._data,this),t._stacked&&un(t)}_dataCheck(){const t=this.getDataset(),n=t.data||(t.data=[]),i=this._data;if(W(n)){const s=this._cachedMeta;this._data=Sg(n,s)}else if(i!==n){if(i){ar(i,this);const s=this._cachedMeta;un(s),s._parsed=[]}n&&Object.isExtensible(n)&&Ch(n,this),this._syncList=[],this._data=n}}addElements(){const t=this._cachedMeta;this._dataCheck(),this.datasetElementType&&(t.dataset=new this.datasetElementType)}buildOrUpdateElements(t){const n=this._cachedMeta,i=this.getDataset();let s=!1;this._dataCheck();const o=n._stacked;n._stacked=ls(n.vScale,n),n.stack!==i.stack&&(s=!0,un(n),n.stack=i.stack),this._resyncElements(t),(s||o!==n._stacked)&&(wr(this,n._parsed),n._stacked=ls(n.vScale,n))}configure(){const t=this.chart.config,n=t.datasetScopeKeys(this._type),i=t.getOptionScopes(this.getDataset(),n,!0);this.options=t.createResolver(i,this.getContext()),this._parsing=this.options.parsing,this._cachedDataOpts={}}parse(t,n){const{_cachedMeta:i,_data:s}=this,{iScale:o,_stacked:r}=i,a=o.axis;let l=t===0&&n===s.length?!0:i._sorted,c=t>0&&i._parsed[t-1],u,f,d;if(this._parsing===!1)i._parsed=s,i._sorted=!0,d=s;else{st(s[t])?d=this.parseArrayData(i,s,t,n):W(s[t])?d=this.parseObjectData(i,s,t,n):d=this.parsePrimitiveData(i,s,t,n);const h=()=>f[a]===null||c&&f[a]<c[a];for(u=0;u<n;++u)i._parsed[u+t]=f=d[u],l&&(h()&&(l=!1),c=f);i._sorted=l}r&&wr(this,d)}parsePrimitiveData(t,n,i,s){const{iScale:o,vScale:r}=t,a=o.axis,l=r.axis,c=o.getLabels(),u=o===r,f=new Array(s);let d,h,g;for(d=0,h=s;d<h;++d)g=d+i,f[d]={[a]:u||o.parse(c[g],g),[l]:r.parse(n[g],g)};return f}parseArrayData(t,n,i,s){const{xScale:o,yScale:r}=t,a=new Array(s);let l,c,u,f;for(l=0,c=s;l<c;++l)u=l+i,f=n[u],a[l]={x:o.parse(f[0],u),y:r.parse(f[1],u)};return a}parseObjectData(t,n,i,s){const{xScale:o,yScale:r}=t,{xAxisKey:a="x",yAxisKey:l="y"}=this._parsing,c=new Array(s);let u,f,d,h;for(u=0,f=s;u<f;++u)d=u+i,h=n[d],c[u]={x:o.parse(nn(h,a),d),y:r.parse(nn(h,l),d)};return c}getParsed(t){return this._cachedMeta._parsed[t]}getDataElement(t){return this._cachedMeta.data[t]}applyStack(t,n,i){const s=this.chart,o=this._cachedMeta,r=n[t.axis],a={keys:Ml(s,!0),values:n._stacks[t.axis]._visualValues};return xr(a,r,o.index,{mode:i})}updateRangeFromParsed(t,n,i,s){const o=i[n.axis];let r=o===null?NaN:o;const a=s&&i._stacks[n.axis];s&&a&&(s.values=a,r=xr(s,o,this._cachedMeta.index)),t.min=Math.min(t.min,r),t.max=Math.max(t.max,r)}getMinMax(t,n){const i=this._cachedMeta,s=i._parsed,o=i._sorted&&t===i.iScale,r=s.length,a=this._getOtherScale(t),l=Ag(n,i,this.chart),c={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY},{min:u,max:f}=Mg(a);let d,h;function g(){h=s[d];const p=h[a.axis];return!ht(h[t.axis])||u>p||f<p}for(d=0;d<r&&!(!g()&&(this.updateRangeFromParsed(c,t,h,l),o));++d);if(o){for(d=r-1;d>=0;--d)if(!g()){this.updateRangeFromParsed(c,t,h,l);break}}return c}getAllParsedValues(t){const n=this._cachedMeta._parsed,i=[];let s,o,r;for(s=0,o=n.length;s<o;++s)r=n[s][t.axis],ht(r)&&i.push(r);return i}getMaxOverflow(){return!1}getLabelAndValue(t){const n=this._cachedMeta,i=n.iScale,s=n.vScale,o=this.getParsed(t);return{label:i?""+i.getLabelForValue(o[i.axis]):"",value:s?""+s.getLabelForValue(o[s.axis]):""}}_update(t){const n=this._cachedMeta;this.update(t||"default"),n._clip=wg(tt(this.options.clip,vg(n.xScale,n.yScale,this.getMaxOverflow())))}update(t){}draw(){const t=this._ctx,n=this.chart,i=this._cachedMeta,s=i.data||[],o=n.chartArea,r=[],a=this._drawStart||0,l=this._drawCount||s.length-a,c=this.options.drawActiveElementsOnTop;let u;for(i.dataset&&i.dataset.draw(t,o,a,l),u=a;u<a+l;++u){const f=s[u];f.hidden||(f.active&&c?r.push(f):f.draw(t,o))}for(u=0;u<r.length;++u)r[u].draw(t,o)}getStyle(t,n){const i=n?"active":"default";return t===void 0&&this._cachedMeta.dataset?this.resolveDatasetElementOptions(i):this.resolveDataElementOptions(t||0,i)}getContext(t,n,i){const s=this.getDataset();let o;if(t>=0&&t<this._cachedMeta.data.length){const r=this._cachedMeta.data[t];o=r.$context||(r.$context=$g(this.getContext(),t,r)),o.parsed=this.getParsed(t),o.raw=s.data[t],o.index=o.dataIndex=t}else o=this.$context||(this.$context=Rg(this.chart.getContext(),this.index)),o.dataset=s,o.index=o.datasetIndex=this.index;return o.active=!!n,o.mode=i,o}resolveDatasetElementOptions(t){return this._resolveElementOptions(this.datasetElementType.id,t)}resolveDataElementOptions(t,n){return this._resolveElementOptions(this.dataElementType.id,n,t)}_resolveElementOptions(t,n="default",i){const s=n==="active",o=this._cachedDataOpts,r=t+"-"+n,a=o[r],l=this.enableOptionSharing&&Si(i);if(a)return Sr(a,l);const c=this.chart.config,u=c.datasetElementScopeKeys(this._type,t),f=s?[`${t}Hover`,"hover",t,""]:[t,""],d=c.getOptionScopes(this.getDataset(),u),h=Object.keys(ot.elements[t]),g=()=>this.getContext(i,s,n),p=c.resolveNamedOptions(d,h,g,f);return p.$shared&&(p.$shared=l,o[r]=Object.freeze(Sr(p,l))),p}_resolveAnimations(t,n,i){const s=this.chart,o=this._cachedDataOpts,r=`animation-${n}`,a=o[r];if(a)return a;let l;if(s.options.animation!==!1){const u=this.chart.config,f=u.datasetAnimationScopeKeys(this._type,n),d=u.getOptionScopes(this.getDataset(),f);l=u.createResolver(d,this.getContext(t,i,n))}const c=new Cl(s,l&&l.animations);return l&&l._cacheable&&(o[r]=Object.freeze(c)),c}getSharedOptions(t){if(t.$shared)return this._sharedOptions||(this._sharedOptions=Object.assign({},t))}includeOptions(t,n){return!n||us(t)||this.chart._animationsDisabled}_getSharedOptions(t,n){const i=this.resolveDataElementOptions(t,n),s=this._sharedOptions,o=this.getSharedOptions(i),r=this.includeOptions(n,o)||o!==s;return this.updateSharedOptions(o,n,i),{sharedOptions:o,includeOptions:r}}updateElement(t,n,i,s){us(s)?Object.assign(t,i):this._resolveAnimations(n,s).update(t,i)}updateSharedOptions(t,n,i){t&&!us(n)&&this._resolveAnimations(void 0,n).update(t,i)}_setStyle(t,n,i,s){t.active=s;const o=this.getStyle(n,s);this._resolveAnimations(n,i,s).update(t,{options:!s&&this.getSharedOptions(o)||o})}removeHoverStyle(t,n,i){this._setStyle(t,i,"active",!1)}setHoverStyle(t,n,i){this._setStyle(t,i,"active",!0)}_removeDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!1)}_setDatasetHoverStyle(){const t=this._cachedMeta.dataset;t&&this._setStyle(t,void 0,"active",!0)}_resyncElements(t){const n=this._data,i=this._cachedMeta.data;for(const[a,l,c]of this._syncList)this[a](l,c);this._syncList=[];const s=i.length,o=n.length,r=Math.min(o,s);r&&this.parse(0,r),o>s?this._insertElements(s,o-s,t):o<s&&this._removeElements(o,s-o)}_insertElements(t,n,i=!0){const s=this._cachedMeta,o=s.data,r=t+n;let a;const l=c=>{for(c.length+=n,a=c.length-1;a>=r;a--)c[a]=c[a-n]};for(l(o),a=t;a<r;++a)o[a]=new this.dataElementType;this._parsing&&l(s._parsed),this.parse(t,n),i&&this.updateElements(o,t,n,"reset")}updateElements(t,n,i,s){}_removeElements(t,n){const i=this._cachedMeta;if(this._parsing){const s=i._parsed.splice(t,n);i._stacked&&un(i,s)}i.data.splice(t,n)}_sync(t){if(this._parsing)this._syncList.push(t);else{const[n,i,s]=t;this[n](i,s)}this.chart._dataChanges.push([this.index,...t])}_onDataPush(){const t=arguments.length;this._sync(["_insertElements",this.getDataset().data.length-t,t])}_onDataPop(){this._sync(["_removeElements",this._cachedMeta.data.length-1,1])}_onDataShift(){this._sync(["_removeElements",0,1])}_onDataSplice(t,n){n&&this._sync(["_removeElements",t,n]);const i=arguments.length-2;i&&this._sync(["_insertElements",t,i])}_onDataUnshift(){this._sync(["_insertElements",0,arguments.length])}}A(Qe,"defaults",{}),A(Qe,"datasetElementType",null),A(Qe,"dataElementType",null);function Pg(e,t,n){let i=1,s=1,o=0,r=0;if(t<lt){const a=e,l=a+t,c=Math.cos(a),u=Math.sin(a),f=Math.cos(l),d=Math.sin(l),h=(x,v,y)=>Ri(x,a,l,!0)?1:Math.max(v,v*n,y,y*n),g=(x,v,y)=>Ri(x,a,l,!0)?-1:Math.min(v,v*n,y,y*n),p=h(0,c,f),m=h(ut,u,d),_=g(rt,c,f),b=g(rt+ut,u,d);i=(p-_)/2,s=(m-b)/2,o=-(p+_)/2,r=-(m+b)/2}return{ratioX:i,ratioY:s,offsetX:o,offsetY:r}}class Ye extends Qe{constructor(t,n){super(t,n),this.enableOptionSharing=!0,this.innerRadius=void 0,this.outerRadius=void 0,this.offsetX=void 0,this.offsetY=void 0}linkScales(){}parse(t,n){const i=this.getDataset().data,s=this._cachedMeta;if(this._parsing===!1)s._parsed=i;else{let o=l=>+i[l];if(W(i[t])){const{key:l="value"}=this._parsing;o=c=>+nn(i[c],l)}let r,a;for(r=t,a=t+n;r<a;++r)s._parsed[r]=o(r)}}_getRotation(){return jt(this.options.rotation-90)}_getCircumference(){return jt(this.options.circumference)}_getRotationExtents(){let t=lt,n=-lt;for(let i=0;i<this.chart.data.datasets.length;++i)if(this.chart.isDatasetVisible(i)&&this.chart.getDatasetMeta(i).type===this._type){const s=this.chart.getDatasetMeta(i).controller,o=s._getRotation(),r=s._getCircumference();t=Math.min(t,o),n=Math.max(n,o+r)}return{rotation:t,circumference:n-t}}update(t){const n=this.chart,{chartArea:i}=n,s=this._cachedMeta,o=s.data,r=this.getMaxBorderWidth()+this.getMaxOffset(o)+this.options.spacing,a=Math.max((Math.min(i.width,i.height)-r)/2,0),l=Math.min(ch(this.options.cutout,a),1),c=this._getRingWeight(this.index),{circumference:u,rotation:f}=this._getRotationExtents(),{ratioX:d,ratioY:h,offsetX:g,offsetY:p}=Pg(f,u,l),m=(i.width-r)/d,_=(i.height-r)/h,b=Math.max(Math.min(m,_)/2,0),x=ll(this.options.radius,b),v=Math.max(x*l,0),y=(x-v)/this._getVisibleDatasetWeightTotal();this.offsetX=g*x,this.offsetY=p*x,s.total=this.calculateTotal(),this.outerRadius=x-y*this._getRingWeightOffset(this.index),this.innerRadius=Math.max(this.outerRadius-y*c,0),this.updateElements(o,0,o.length,t)}_circumference(t,n){const i=this.options,s=this._cachedMeta,o=this._getCircumference();return n&&i.animation.animateRotate||!this.chart.getDataVisibility(t)||s._parsed[t]===null||s.data[t].hidden?0:this.calculateCircumference(s._parsed[t]*o/lt)}updateElements(t,n,i,s){const o=s==="reset",r=this.chart,a=r.chartArea,c=r.options.animation,u=(a.left+a.right)/2,f=(a.top+a.bottom)/2,d=o&&c.animateScale,h=d?0:this.innerRadius,g=d?0:this.outerRadius,{sharedOptions:p,includeOptions:m}=this._getSharedOptions(n,s);let _=this._getRotation(),b;for(b=0;b<n;++b)_+=this._circumference(b,o);for(b=n;b<n+i;++b){const x=this._circumference(b,o),v=t[b],y={x:u+this.offsetX,y:f+this.offsetY,startAngle:_,endAngle:_+x,circumference:x,outerRadius:g,innerRadius:h};m&&(y.options=p||this.resolveDataElementOptions(b,v.active?"active":s)),_+=x,this.updateElement(v,b,y,s)}}calculateTotal(){const t=this._cachedMeta,n=t.data;let i=0,s;for(s=0;s<n.length;s++){const o=t._parsed[s];o!==null&&!isNaN(o)&&this.chart.getDataVisibility(s)&&!n[s].hidden&&(i+=Math.abs(o))}return i}calculateCircumference(t){const n=this._cachedMeta.total;return n>0&&!isNaN(t)?lt*(Math.abs(t)/n):0}getLabelAndValue(t){const n=this._cachedMeta,i=this.chart,s=i.data.labels||[],o=Fn(n._parsed[t],i.options.locale);return{label:s[t]||"",value:o}}getMaxBorderWidth(t){let n=0;const i=this.chart;let s,o,r,a,l;if(!t){for(s=0,o=i.data.datasets.length;s<o;++s)if(i.isDatasetVisible(s)){r=i.getDatasetMeta(s),t=r.data,a=r.controller;break}}if(!t)return 0;for(s=0,o=t.length;s<o;++s)l=a.resolveDataElementOptions(s),l.borderAlign!=="inner"&&(n=Math.max(n,l.borderWidth||0,l.hoverBorderWidth||0));return n}getMaxOffset(t){let n=0;for(let i=0,s=t.length;i<s;++i){const o=this.resolveDataElementOptions(i);n=Math.max(n,o.offset||0,o.hoverOffset||0)}return n}_getRingWeightOffset(t){let n=0;for(let i=0;i<t;++i)this.chart.isDatasetVisible(i)&&(n+=this._getRingWeight(i));return n}_getRingWeight(t){return Math.max(tt(this.chart.data.datasets[t].weight,1),0)}_getVisibleDatasetWeightTotal(){return this._getRingWeightOffset(this.chart.data.datasets.length)||1}}A(Ye,"id","doughnut"),A(Ye,"defaults",{datasetElementType:!1,dataElementType:"arc",animation:{animateRotate:!0,animateScale:!1},animations:{numbers:{type:"number",properties:["circumference","endAngle","innerRadius","outerRadius","startAngle","x","y","offset","borderWidth","spacing"]}},cutout:"50%",rotation:0,circumference:360,radius:"100%",spacing:0,indexAxis:"r"}),A(Ye,"descriptors",{_scriptable:t=>t!=="spacing",_indexable:t=>t!=="spacing"&&!t.startsWith("borderDash")&&!t.startsWith("hoverBorderDash")}),A(Ye,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const n=t.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:i,color:s}}=t.legend.options;return n.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:s,lineWidth:l.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,n,i){i.chart.toggleDataVisibility(n.index),i.chart.update()}}}});class oi extends Qe{constructor(t,n){super(t,n),this.innerRadius=void 0,this.outerRadius=void 0}getLabelAndValue(t){const n=this._cachedMeta,i=this.chart,s=i.data.labels||[],o=Fn(n._parsed[t].r,i.options.locale);return{label:s[t]||"",value:o}}parseObjectData(t,n,i,s){return ng.bind(this)(t,n,i,s)}update(t){const n=this._cachedMeta.data;this._updateRadius(),this.updateElements(n,0,n.length,t)}getMinMax(){const t=this._cachedMeta,n={min:Number.POSITIVE_INFINITY,max:Number.NEGATIVE_INFINITY};return t.data.forEach((i,s)=>{const o=this.getParsed(s).r;!isNaN(o)&&this.chart.getDataVisibility(s)&&(o<n.min&&(n.min=o),o>n.max&&(n.max=o))}),n}_updateRadius(){const t=this.chart,n=t.chartArea,i=t.options,s=Math.min(n.right-n.left,n.bottom-n.top),o=Math.max(s/2,0),r=Math.max(i.cutoutPercentage?o/100*i.cutoutPercentage:1,0),a=(o-r)/t.getVisibleDatasetCount();this.outerRadius=o-a*this.index,this.innerRadius=this.outerRadius-a}updateElements(t,n,i,s){const o=s==="reset",r=this.chart,l=r.options.animation,c=this._cachedMeta.rScale,u=c.xCenter,f=c.yCenter,d=c.getIndexAngle(0)-.5*rt;let h=d,g;const p=360/this.countVisibleElements();for(g=0;g<n;++g)h+=this._computeAngle(g,s,p);for(g=n;g<n+i;g++){const m=t[g];let _=h,b=h+this._computeAngle(g,s,p),x=r.getDataVisibility(g)?c.getDistanceFromCenterForValue(this.getParsed(g).r):0;h=b,o&&(l.animateScale&&(x=0),l.animateRotate&&(_=b=d));const v={x:u,y:f,innerRadius:0,outerRadius:x,startAngle:_,endAngle:b,options:this.resolveDataElementOptions(g,m.active?"active":s)};this.updateElement(m,g,v,s)}}countVisibleElements(){const t=this._cachedMeta;let n=0;return t.data.forEach((i,s)=>{!isNaN(this.getParsed(s).r)&&this.chart.getDataVisibility(s)&&n++}),n}_computeAngle(t,n,i){return this.chart.getDataVisibility(t)?jt(this.resolveDataElementOptions(t,n).angle||i):0}}A(oi,"id","polarArea"),A(oi,"defaults",{dataElementType:"arc",animation:{animateRotate:!0,animateScale:!0},animations:{numbers:{type:"number",properties:["x","y","startAngle","endAngle","innerRadius","outerRadius"]}},indexAxis:"r",startAngle:0}),A(oi,"overrides",{aspectRatio:1,plugins:{legend:{labels:{generateLabels(t){const n=t.data;if(n.labels.length&&n.datasets.length){const{labels:{pointStyle:i,color:s}}=t.legend.options;return n.labels.map((o,r)=>{const l=t.getDatasetMeta(0).controller.getStyle(r);return{text:o,fillStyle:l.backgroundColor,strokeStyle:l.borderColor,fontColor:s,lineWidth:l.borderWidth,pointStyle:i,hidden:!t.getDataVisibility(r),index:r}})}return[]}},onClick(t,n,i){i.chart.toggleDataVisibility(n.index),i.chart.update()}}},scales:{r:{type:"radialLinear",angleLines:{display:!1},beginAtZero:!0,grid:{circular:!0},pointLabels:{display:!1},startAngle:0}}});class Es extends Ye{}A(Es,"id","pie"),A(Es,"defaults",{cutout:0,rotation:0,circumference:360,radius:"100%"});function Re(){throw new Error("This method is not implemented: Check that a complete date adapter is provided.")}class po{constructor(t){A(this,"options");this.options=t||{}}static override(t){Object.assign(po.prototype,t)}init(){}formats(){return Re()}parse(){return Re()}format(){return Re()}add(){return Re()}diff(){return Re()}startOf(){return Re()}endOf(){return Re()}}var Og={_date:po};function Dg(e,t,n,i){const{controller:s,data:o,_sorted:r}=e,a=s._cachedMeta.iScale,l=e.dataset&&e.dataset.options?e.dataset.options.spanGaps:null;if(a&&t===a.axis&&t!=="r"&&r&&o.length){const c=a._reversePixels?wh:Os;if(i){if(s._sharedOptions){const u=o[0],f=typeof u.getRange=="function"&&u.getRange(t);if(f){const d=c(o,t,n-f),h=c(o,t,n+f);return{lo:d.lo,hi:h.hi}}}}else{const u=c(o,t,n);if(l){const{vScale:f}=s._cachedMeta,{_parsed:d}=e,h=d.slice(0,u.lo+1).reverse().findIndex(p=>!Q(p[f.axis]));u.lo-=Math.max(0,h);const g=d.slice(u.hi).findIndex(p=>!Q(p[f.axis]));u.hi+=Math.max(0,g)}return u}}return{lo:0,hi:o.length-1}}function En(e,t,n,i,s){const o=e.getSortedVisibleDatasetMetas(),r=n[t];for(let a=0,l=o.length;a<l;++a){const{index:c,data:u}=o[a],{lo:f,hi:d}=Dg(o[a],t,r,s);for(let h=f;h<=d;++h){const g=u[h];g.skip||i(g,c,h)}}}function Fg(e){const t=e.indexOf("x")!==-1,n=e.indexOf("y")!==-1;return function(i,s){const o=t?Math.abs(i.x-s.x):0,r=n?Math.abs(i.y-s.y):0;return Math.sqrt(Math.pow(o,2)+Math.pow(r,2))}}function fs(e,t,n,i,s){const o=[];return!s&&!e.isPointInArea(t)||En(e,n,t,function(a,l,c){!s&&!qe(a,e.chartArea,0)||a.inRange(t.x,t.y,i)&&o.push({element:a,datasetIndex:l,index:c})},!0),o}function Eg(e,t,n,i){let s=[];function o(r,a,l){const{startAngle:c,endAngle:u}=r.getProps(["startAngle","endAngle"],i),{angle:f}=fl(r,{x:t.x,y:t.y});Ri(f,c,u)&&s.push({element:r,datasetIndex:a,index:l})}return En(e,n,t,o),s}function Lg(e,t,n,i,s,o){let r=[];const a=Fg(n);let l=Number.POSITIVE_INFINITY;function c(u,f,d){const h=u.inRange(t.x,t.y,s);if(i&&!h)return;const g=u.getCenterPoint(s);if(!(!!o||e.isPointInArea(g))&&!h)return;const m=a(t,g);m<l?(r=[{element:u,datasetIndex:f,index:d}],l=m):m===l&&r.push({element:u,datasetIndex:f,index:d})}return En(e,n,t,c),r}function ds(e,t,n,i,s,o){return!o&&!e.isPointInArea(t)?[]:n==="r"&&!i?Eg(e,t,n,s):Lg(e,t,n,i,s,o)}function Cr(e,t,n,i,s){const o=[],r=n==="x"?"inXRange":"inYRange";let a=!1;return En(e,n,t,(l,c,u)=>{l[r]&&l[r](t[n],s)&&(o.push({element:l,datasetIndex:c,index:u}),a=a||l.inRange(t.x,t.y,s))}),i&&!a?[]:o}var Tg={evaluateInteractionItems:En,modes:{index(e,t,n,i){const s=Ae(t,e),o=n.axis||"x",r=n.includeInvisible||!1,a=n.intersect?fs(e,s,o,i,r):ds(e,s,o,!1,i,r),l=[];return a.length?(e.getSortedVisibleDatasetMetas().forEach(c=>{const u=a[0].index,f=c.data[u];f&&!f.skip&&l.push({element:f,datasetIndex:c.index,index:u})}),l):[]},dataset(e,t,n,i){const s=Ae(t,e),o=n.axis||"xy",r=n.includeInvisible||!1;let a=n.intersect?fs(e,s,o,i,r):ds(e,s,o,!1,i,r);if(a.length>0){const l=a[0].datasetIndex,c=e.getDatasetMeta(l).data;a=[];for(let u=0;u<c.length;++u)a.push({element:c[u],datasetIndex:l,index:u})}return a},point(e,t,n,i){const s=Ae(t,e),o=n.axis||"xy",r=n.includeInvisible||!1;return fs(e,s,o,i,r)},nearest(e,t,n,i){const s=Ae(t,e),o=n.axis||"xy",r=n.includeInvisible||!1;return ds(e,s,o,n.intersect,i,r)},x(e,t,n,i){const s=Ae(t,e);return Cr(e,s,"x",n.intersect,i)},y(e,t,n,i){const s=Ae(t,e);return Cr(e,s,"y",n.intersect,i)}}};const kl=["left","top","right","bottom"];function fn(e,t){return e.filter(n=>n.pos===t)}function Mr(e,t){return e.filter(n=>kl.indexOf(n.pos)===-1&&n.box.axis===t)}function dn(e,t){return e.sort((n,i)=>{const s=t?i:n,o=t?n:i;return s.weight===o.weight?s.index-o.index:s.weight-o.weight})}function Ig(e){const t=[];let n,i,s,o,r,a;for(n=0,i=(e||[]).length;n<i;++n)s=e[n],{position:o,options:{stack:r,stackWeight:a=1}}=s,t.push({index:n,box:s,pos:o,horizontal:s.isHorizontal(),weight:s.weight,stack:r&&o+r,stackWeight:a});return t}function Vg(e){const t={};for(const n of e){const{stack:i,pos:s,stackWeight:o}=n;if(!i||!kl.includes(s))continue;const r=t[i]||(t[i]={count:0,placed:0,weight:0,size:0});r.count++,r.weight+=o}return t}function zg(e,t){const n=Vg(e),{vBoxMaxWidth:i,hBoxMaxHeight:s}=t;let o,r,a;for(o=0,r=e.length;o<r;++o){a=e[o];const{fullSize:l}=a.box,c=n[a.stack],u=c&&a.stackWeight/c.weight;a.horizontal?(a.width=u?u*i:l&&t.availableWidth,a.height=s):(a.width=i,a.height=u?u*s:l&&t.availableHeight)}return n}function Hg(e){const t=Ig(e),n=dn(t.filter(c=>c.box.fullSize),!0),i=dn(fn(t,"left"),!0),s=dn(fn(t,"right")),o=dn(fn(t,"top"),!0),r=dn(fn(t,"bottom")),a=Mr(t,"x"),l=Mr(t,"y");return{fullSize:n,leftAndTop:i.concat(o),rightAndBottom:s.concat(l).concat(r).concat(a),chartArea:fn(t,"chartArea"),vertical:i.concat(s).concat(l),horizontal:o.concat(r).concat(a)}}function kr(e,t,n,i){return Math.max(e[n],t[n])+Math.max(e[i],t[i])}function Rl(e,t){e.top=Math.max(e.top,t.top),e.left=Math.max(e.left,t.left),e.bottom=Math.max(e.bottom,t.bottom),e.right=Math.max(e.right,t.right)}function Ng(e,t,n,i){const{pos:s,box:o}=n,r=e.maxPadding;if(!W(s)){n.size&&(e[s]-=n.size);const f=i[n.stack]||{size:0,count:1};f.size=Math.max(f.size,n.horizontal?o.height:o.width),n.size=f.size/f.count,e[s]+=n.size}o.getPadding&&Rl(r,o.getPadding());const a=Math.max(0,t.outerWidth-kr(r,e,"left","right")),l=Math.max(0,t.outerHeight-kr(r,e,"top","bottom")),c=a!==e.w,u=l!==e.h;return e.w=a,e.h=l,n.horizontal?{same:c,other:u}:{same:u,other:c}}function Bg(e){const t=e.maxPadding;function n(i){const s=Math.max(t[i]-e[i],0);return e[i]+=s,s}e.y+=n("top"),e.x+=n("left"),n("right"),n("bottom")}function jg(e,t){const n=t.maxPadding;function i(s){const o={left:0,top:0,right:0,bottom:0};return s.forEach(r=>{o[r]=Math.max(t[r],n[r])}),o}return i(e?["left","right"]:["top","bottom"])}function mn(e,t,n,i){const s=[];let o,r,a,l,c,u;for(o=0,r=e.length,c=0;o<r;++o){a=e[o],l=a.box,l.update(a.width||t.w,a.height||t.h,jg(a.horizontal,t));const{same:f,other:d}=Ng(t,n,a,i);c|=f&&s.length,u=u||d,l.fullSize||s.push(a)}return c&&mn(s,t,n,i)||u}function Un(e,t,n,i,s){e.top=n,e.left=t,e.right=t+i,e.bottom=n+s,e.width=i,e.height=s}function Rr(e,t,n,i){const s=n.padding;let{x:o,y:r}=t;for(const a of e){const l=a.box,c=i[a.stack]||{count:1,placed:0,weight:1},u=a.stackWeight/c.weight||1;if(a.horizontal){const f=t.w*u,d=c.size||l.height;Si(c.start)&&(r=c.start),l.fullSize?Un(l,s.left,r,n.outerWidth-s.right-s.left,d):Un(l,t.left+c.placed,r,f,d),c.start=r,c.placed+=f,r=l.bottom}else{const f=t.h*u,d=c.size||l.width;Si(c.start)&&(o=c.start),l.fullSize?Un(l,o,s.top,d,n.outerHeight-s.bottom-s.top):Un(l,o,t.top+c.placed,d,f),c.start=o,c.placed+=f,o=l.right}}t.x=o,t.y=r}var be={addBox(e,t){e.boxes||(e.boxes=[]),t.fullSize=t.fullSize||!1,t.position=t.position||"top",t.weight=t.weight||0,t._layers=t._layers||function(){return[{z:0,draw(n){t.draw(n)}}]},e.boxes.push(t)},removeBox(e,t){const n=e.boxes?e.boxes.indexOf(t):-1;n!==-1&&e.boxes.splice(n,1)},configure(e,t,n){t.fullSize=n.fullSize,t.position=n.position,t.weight=n.weight},update(e,t,n,i){if(!e)return;const s=At(e.options.layout.padding),o=Math.max(t-s.width,0),r=Math.max(n-s.height,0),a=Hg(e.boxes),l=a.vertical,c=a.horizontal;q(e.boxes,p=>{typeof p.beforeLayout=="function"&&p.beforeLayout()});const u=l.reduce((p,m)=>m.box.options&&m.box.options.display===!1?p:p+1,0)||1,f=Object.freeze({outerWidth:t,outerHeight:n,padding:s,availableWidth:o,availableHeight:r,vBoxMaxWidth:o/2/u,hBoxMaxHeight:r/2}),d=Object.assign({},s);Rl(d,At(i));const h=Object.assign({maxPadding:d,w:o,h:r,x:s.left,y:s.top},s),g=zg(l.concat(c),f);mn(a.fullSize,h,f,g),mn(l,h,f,g),mn(c,h,f,g)&&mn(l,h,f,g),Bg(h),Rr(a.leftAndTop,h,f,g),h.x+=h.w,h.y+=h.h,Rr(a.rightAndBottom,h,f,g),e.chartArea={left:h.left,top:h.top,right:h.left+h.w,bottom:h.top+h.h,height:h.h,width:h.w},q(a.chartArea,p=>{const m=p.box;Object.assign(m,e.chartArea),m.update(h.w,h.h,{left:0,top:0,right:0,bottom:0})})}};class $l{acquireContext(t,n){}releaseContext(t){return!1}addEventListener(t,n,i){}removeEventListener(t,n,i){}getDevicePixelRatio(){return 1}getMaximumSize(t,n,i,s){return n=Math.max(0,n||t.width),i=i||t.height,{width:n,height:Math.max(0,s?Math.floor(n/s):i)}}isAttached(t){return!0}updateConfig(t){}}class Wg extends $l{acquireContext(t){return t&&t.getContext&&t.getContext("2d")||null}updateConfig(t){t.options.animation=!1}}const ri="$chartjs",Gg={touchstart:"mousedown",touchmove:"mousemove",touchend:"mouseup",pointerenter:"mouseenter",pointerdown:"mousedown",pointermove:"mousemove",pointerup:"mouseup",pointerleave:"mouseout",pointerout:"mouseout"},$r=e=>e===null||e==="";function qg(e,t){const n=e.style,i=e.getAttribute("height"),s=e.getAttribute("width");if(e[ri]={initial:{height:i,width:s,style:{display:n.display,height:n.height,width:n.width}}},n.display=n.display||"block",n.boxSizing=n.boxSizing||"border-box",$r(s)){const o=_r(e,"width");o!==void 0&&(e.width=o)}if($r(i))if(e.style.height==="")e.height=e.width/(t||2);else{const o=_r(e,"height");o!==void 0&&(e.height=o)}return e}const Al=cg?{passive:!0}:!1;function Yg(e,t,n){e&&e.addEventListener(t,n,Al)}function Ug(e,t,n){e&&e.canvas&&e.canvas.removeEventListener(t,n,Al)}function Xg(e,t){const n=Gg[e.type]||e.type,{x:i,y:s}=Ae(e,t);return{type:n,chart:t,native:e,x:i!==void 0?i:null,y:s!==void 0?s:null}}function Pi(e,t){for(const n of e)if(n===t||n.contains(t))return!0}function Kg(e,t,n){const i=e.canvas,s=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Pi(a.addedNodes,i),r=r&&!Pi(a.removedNodes,i);r&&n()});return s.observe(document,{childList:!0,subtree:!0}),s}function Qg(e,t,n){const i=e.canvas,s=new MutationObserver(o=>{let r=!1;for(const a of o)r=r||Pi(a.removedNodes,i),r=r&&!Pi(a.addedNodes,i);r&&n()});return s.observe(document,{childList:!0,subtree:!0}),s}const $n=new Map;let Ar=0;function Pl(){const e=window.devicePixelRatio;e!==Ar&&(Ar=e,$n.forEach((t,n)=>{n.currentDevicePixelRatio!==e&&t()}))}function Zg(e,t){$n.size||window.addEventListener("resize",Pl),$n.set(e,t)}function Jg(e){$n.delete(e),$n.size||window.removeEventListener("resize",Pl)}function tp(e,t,n){const i=e.canvas,s=i&&go(i);if(!s)return;const o=gl((a,l)=>{const c=s.clientWidth;n(a,l),c<s.clientWidth&&n()},window),r=new ResizeObserver(a=>{const l=a[0],c=l.contentRect.width,u=l.contentRect.height;c===0&&u===0||o(c,u)});return r.observe(s),Zg(e,o),r}function hs(e,t,n){n&&n.disconnect(),t==="resize"&&Jg(e)}function ep(e,t,n){const i=e.canvas,s=gl(o=>{e.ctx!==null&&n(Xg(o,e))},e);return Yg(i,t,s),s}class np extends $l{acquireContext(t,n){const i=t&&t.getContext&&t.getContext("2d");return i&&i.canvas===t?(qg(t,n),i):null}releaseContext(t){const n=t.canvas;if(!n[ri])return!1;const i=n[ri].initial;["height","width"].forEach(o=>{const r=i[o];Q(r)?n.removeAttribute(o):n.setAttribute(o,r)});const s=i.style||{};return Object.keys(s).forEach(o=>{n.style[o]=s[o]}),n.width=n.width,delete n[ri],!0}addEventListener(t,n,i){this.removeEventListener(t,n);const s=t.$proxies||(t.$proxies={}),r={attach:Kg,detach:Qg,resize:tp}[n]||ep;s[n]=r(t,n,i)}removeEventListener(t,n){const i=t.$proxies||(t.$proxies={}),s=i[n];if(!s)return;({attach:hs,detach:hs,resize:hs}[n]||Ug)(t,n,s),i[n]=void 0}getDevicePixelRatio(){return window.devicePixelRatio}getMaximumSize(t,n,i,s){return lg(t,n,i,s)}isAttached(t){const n=t&&go(t);return!!(n&&n.isConnected)}}function ip(e){return!ho()||typeof OffscreenCanvas<"u"&&e instanceof OffscreenCanvas?Wg:np}var ei;let Ln=(ei=class{constructor(){A(this,"x");A(this,"y");A(this,"active",!1);A(this,"options");A(this,"$animations")}tooltipPosition(t){const{x:n,y:i}=this.getProps(["x","y"],t);return{x:n,y:i}}hasValue(){return ki(this.x)&&ki(this.y)}getProps(t,n){const i=this.$animations;if(!n||!i)return this;const s={};return t.forEach(o=>{s[o]=i[o]&&i[o].active()?i[o]._to:this[o]}),s}},A(ei,"defaults",{}),A(ei,"defaultRoutes"),ei);function sp(e,t){const n=e.options.ticks,i=op(e),s=Math.min(n.maxTicksLimit||i,i),o=n.major.enabled?ap(t):[],r=o.length,a=o[0],l=o[r-1],c=[];if(r>s)return lp(t,c,o,r/s),c;const u=rp(o,t,s);if(r>0){let f,d;const h=r>1?Math.round((l-a)/(r-1)):null;for(Xn(t,c,u,Q(h)?0:a-h,a),f=0,d=r-1;f<d;f++)Xn(t,c,u,o[f],o[f+1]);return Xn(t,c,u,l,Q(h)?t.length:l+h),c}return Xn(t,c,u),c}function op(e){const t=e.options.offset,n=e._tickSize(),i=e._length/n+(t?0:1),s=e._maxLength/n;return Math.floor(Math.min(i,s))}function rp(e,t,n){const i=cp(e),s=t.length/n;if(!i)return Math.max(s,1);const o=mh(i);for(let r=0,a=o.length-1;r<a;r++){const l=o[r];if(l>s)return l}return Math.max(s,1)}function ap(e){const t=[];let n,i;for(n=0,i=e.length;n<i;n++)e[n].major&&t.push(n);return t}function lp(e,t,n,i){let s=0,o=n[0],r;for(i=Math.ceil(i),r=0;r<e.length;r++)r===o&&(t.push(e[r]),s++,o=n[s*i])}function Xn(e,t,n,i,s){const o=tt(i,0),r=Math.min(tt(s,e.length),e.length);let a=0,l,c,u;for(n=Math.ceil(n),s&&(l=s-i,n=l/Math.floor(l/n)),u=o;u<0;)a++,u=Math.round(o+a*n);for(c=Math.max(o,0);c<r;c++)c===u&&(t.push(e[c]),a++,u=Math.round(o+a*n))}function cp(e){const t=e.length;let n,i;if(t<2)return!1;for(i=e[0],n=1;n<t;++n)if(e[n]-e[n-1]!==i)return!1;return i}const up=e=>e==="left"?"right":e==="right"?"left":e,Pr=(e,t,n)=>t==="top"||t==="left"?e[t]+n:e[t]-n,Or=(e,t)=>Math.min(t||e,e);function Dr(e,t){const n=[],i=e.length/t,s=e.length;let o=0;for(;o<s;o+=i)n.push(e[Math.floor(o)]);return n}function fp(e,t,n){const i=e.ticks.length,s=Math.min(t,i-1),o=e._startPixel,r=e._endPixel,a=1e-6;let l=e.getPixelForTick(s),c;if(!(n&&(i===1?c=Math.max(l-o,r-l):t===0?c=(e.getPixelForTick(1)-l)/2:c=(l-e.getPixelForTick(s-1))/2,l+=s<t?c:-c,l<o-a||l>r+a)))return l}function dp(e,t){q(e,n=>{const i=n.gc,s=i.length/2;let o;if(s>t){for(o=0;o<s;++o)delete n.data[i[o]];i.splice(0,s)}})}function hn(e){return e.drawTicks?e.tickLength:0}function Fr(e,t){if(!e.display)return 0;const n=bt(e.font,t),i=At(e.padding);return(st(e.text)?e.text.length:1)*n.lineHeight+i.height}function hp(e,t){return ze(e,{scale:t,type:"scale"})}function gp(e,t,n){return ze(e,{tick:n,index:t,type:"tick"})}function pp(e,t,n){let i=pl(e);return(n&&t!=="right"||!n&&t==="right")&&(i=up(i)),i}function mp(e,t,n,i){const{top:s,left:o,bottom:r,right:a,chart:l}=e,{chartArea:c,scales:u}=l;let f=0,d,h,g;const p=r-s,m=a-o;if(e.isHorizontal()){if(h=xn(i,o,a),W(n)){const _=Object.keys(n)[0],b=n[_];g=u[_].getPixelForValue(b)+p-t}else n==="center"?g=(c.bottom+c.top)/2+p-t:g=Pr(e,n,t);d=a-o}else{if(W(n)){const _=Object.keys(n)[0],b=n[_];h=u[_].getPixelForValue(b)-m+t}else n==="center"?h=(c.left+c.right)/2-m+t:h=Pr(e,n,t);g=xn(i,r,s),f=n==="left"?-ut:ut}return{titleX:h,titleY:g,maxWidth:d,rotation:f}}class He extends Ln{constructor(t){super(),this.id=t.id,this.type=t.type,this.options=void 0,this.ctx=t.ctx,this.chart=t.chart,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this._margins={left:0,right:0,top:0,bottom:0},this.maxWidth=void 0,this.maxHeight=void 0,this.paddingTop=void 0,this.paddingBottom=void 0,this.paddingLeft=void 0,this.paddingRight=void 0,this.axis=void 0,this.labelRotation=void 0,this.min=void 0,this.max=void 0,this._range=void 0,this.ticks=[],this._gridLineItems=null,this._labelItems=null,this._labelSizes=null,this._length=0,this._maxLength=0,this._longestTextCache={},this._startPixel=void 0,this._endPixel=void 0,this._reversePixels=!1,this._userMax=void 0,this._userMin=void 0,this._suggestedMax=void 0,this._suggestedMin=void 0,this._ticksLength=0,this._borderValue=0,this._cache={},this._dataLimitsCached=!1,this.$context=void 0}init(t){this.options=t.setContext(this.getContext()),this.axis=t.axis,this._userMin=this.parse(t.min),this._userMax=this.parse(t.max),this._suggestedMin=this.parse(t.suggestedMin),this._suggestedMax=this.parse(t.suggestedMax)}parse(t,n){return t}getUserBounds(){let{_userMin:t,_userMax:n,_suggestedMin:i,_suggestedMax:s}=this;return t=Ct(t,Number.POSITIVE_INFINITY),n=Ct(n,Number.NEGATIVE_INFINITY),i=Ct(i,Number.POSITIVE_INFINITY),s=Ct(s,Number.NEGATIVE_INFINITY),{min:Ct(t,i),max:Ct(n,s),minDefined:ht(t),maxDefined:ht(n)}}getMinMax(t){let{min:n,max:i,minDefined:s,maxDefined:o}=this.getUserBounds(),r;if(s&&o)return{min:n,max:i};const a=this.getMatchingVisibleMetas();for(let l=0,c=a.length;l<c;++l)r=a[l].controller.getMinMax(this,t),s||(n=Math.min(n,r.min)),o||(i=Math.max(i,r.max));return n=o&&n>i?i:n,i=s&&n>i?n:i,{min:Ct(n,Ct(i,n)),max:Ct(i,Ct(n,i))}}getPadding(){return{left:this.paddingLeft||0,top:this.paddingTop||0,right:this.paddingRight||0,bottom:this.paddingBottom||0}}getTicks(){return this.ticks}getLabels(){const t=this.chart.data;return this.options.labels||(this.isHorizontal()?t.xLabels:t.yLabels)||t.labels||[]}getLabelItems(t=this.chart.chartArea){return this._labelItems||(this._labelItems=this._computeLabelItems(t))}beforeLayout(){this._cache={},this._dataLimitsCached=!1}beforeUpdate(){at(this.options.beforeUpdate,[this])}update(t,n,i){const{beginAtZero:s,grace:o,ticks:r}=this.options,a=r.sampleSize;this.beforeUpdate(),this.maxWidth=t,this.maxHeight=n,this._margins=i=Object.assign({left:0,right:0,top:0,bottom:0},i),this.ticks=null,this._labelSizes=null,this._gridLineItems=null,this._labelItems=null,this.beforeSetDimensions(),this.setDimensions(),this.afterSetDimensions(),this._maxLength=this.isHorizontal()?this.width+i.left+i.right:this.height+i.top+i.bottom,this._dataLimitsCached||(this.beforeDataLimits(),this.determineDataLimits(),this.afterDataLimits(),this._range=qh(this,o,s),this._dataLimitsCached=!0),this.beforeBuildTicks(),this.ticks=this.buildTicks()||[],this.afterBuildTicks();const l=a<this.ticks.length;this._convertTicksToLabels(l?Dr(this.ticks,a):this.ticks),this.configure(),this.beforeCalculateLabelRotation(),this.calculateLabelRotation(),this.afterCalculateLabelRotation(),r.display&&(r.autoSkip||r.source==="auto")&&(this.ticks=sp(this,this.ticks),this._labelSizes=null,this.afterAutoSkip()),l&&this._convertTicksToLabels(this.ticks),this.beforeFit(),this.fit(),this.afterFit(),this.afterUpdate()}configure(){let t=this.options.reverse,n,i;this.isHorizontal()?(n=this.left,i=this.right):(n=this.top,i=this.bottom,t=!t),this._startPixel=n,this._endPixel=i,this._reversePixels=t,this._length=i-n,this._alignToPixels=this.options.alignToPixels}afterUpdate(){at(this.options.afterUpdate,[this])}beforeSetDimensions(){at(this.options.beforeSetDimensions,[this])}setDimensions(){this.isHorizontal()?(this.width=this.maxWidth,this.left=0,this.right=this.width):(this.height=this.maxHeight,this.top=0,this.bottom=this.height),this.paddingLeft=0,this.paddingTop=0,this.paddingRight=0,this.paddingBottom=0}afterSetDimensions(){at(this.options.afterSetDimensions,[this])}_callHooks(t){this.chart.notifyPlugins(t,this.getContext()),at(this.options[t],[this])}beforeDataLimits(){this._callHooks("beforeDataLimits")}determineDataLimits(){}afterDataLimits(){this._callHooks("afterDataLimits")}beforeBuildTicks(){this._callHooks("beforeBuildTicks")}buildTicks(){return[]}afterBuildTicks(){this._callHooks("afterBuildTicks")}beforeTickToLabelConversion(){at(this.options.beforeTickToLabelConversion,[this])}generateTickLabels(t){const n=this.options.ticks;let i,s,o;for(i=0,s=t.length;i<s;i++)o=t[i],o.label=at(n.callback,[o.value,i,t],this)}afterTickToLabelConversion(){at(this.options.afterTickToLabelConversion,[this])}beforeCalculateLabelRotation(){at(this.options.beforeCalculateLabelRotation,[this])}calculateLabelRotation(){const t=this.options,n=t.ticks,i=Or(this.ticks.length,t.ticks.maxTicksLimit),s=n.minRotation||0,o=n.maxRotation;let r=s,a,l,c;if(!this._isVisible()||!n.display||s>=o||i<=1||!this.isHorizontal()){this.labelRotation=s;return}const u=this._getLabelSizes(),f=u.widest.width,d=u.highest.height,h=Mt(this.chart.width-f,0,this.maxWidth);a=t.offset?this.maxWidth/i:h/(i-1),f+6>a&&(a=h/(i-(t.offset?.5:1)),l=this.maxHeight-hn(t.grid)-n.padding-Fr(t.title,this.chart.options.font),c=Math.sqrt(f*f+d*d),r=ro(Math.min(Math.asin(Mt((u.highest.height+6)/a,-1,1)),Math.asin(Mt(l/c,-1,1))-Math.asin(Mt(d/c,-1,1)))),r=Math.max(s,Math.min(o,r))),this.labelRotation=r}afterCalculateLabelRotation(){at(this.options.afterCalculateLabelRotation,[this])}afterAutoSkip(){}beforeFit(){at(this.options.beforeFit,[this])}fit(){const t={width:0,height:0},{chart:n,options:{ticks:i,title:s,grid:o}}=this,r=this._isVisible(),a=this.isHorizontal();if(r){const l=Fr(s,n.options.font);if(a?(t.width=this.maxWidth,t.height=hn(o)+l):(t.height=this.maxHeight,t.width=hn(o)+l),i.display&&this.ticks.length){const{first:c,last:u,widest:f,highest:d}=this._getLabelSizes(),h=i.padding*2,g=jt(this.labelRotation),p=Math.cos(g),m=Math.sin(g);if(a){const _=i.mirror?0:m*f.width+p*d.height;t.height=Math.min(this.maxHeight,t.height+_+h)}else{const _=i.mirror?0:p*f.width+m*d.height;t.width=Math.min(this.maxWidth,t.width+_+h)}this._calculatePadding(c,u,m,p)}}this._handleMargins(),a?(this.width=this._length=n.width-this._margins.left-this._margins.right,this.height=t.height):(this.width=t.width,this.height=this._length=n.height-this._margins.top-this._margins.bottom)}_calculatePadding(t,n,i,s){const{ticks:{align:o,padding:r},position:a}=this.options,l=this.labelRotation!==0,c=a!=="top"&&this.axis==="x";if(this.isHorizontal()){const u=this.getPixelForTick(0)-this.left,f=this.right-this.getPixelForTick(this.ticks.length-1);let d=0,h=0;l?c?(d=s*t.width,h=i*n.height):(d=i*t.height,h=s*n.width):o==="start"?h=n.width:o==="end"?d=t.width:o!=="inner"&&(d=t.width/2,h=n.width/2),this.paddingLeft=Math.max((d-u+r)*this.width/(this.width-u),0),this.paddingRight=Math.max((h-f+r)*this.width/(this.width-f),0)}else{let u=n.height/2,f=t.height/2;o==="start"?(u=0,f=t.height):o==="end"&&(u=n.height,f=0),this.paddingTop=u+r,this.paddingBottom=f+r}}_handleMargins(){this._margins&&(this._margins.left=Math.max(this.paddingLeft,this._margins.left),this._margins.top=Math.max(this.paddingTop,this._margins.top),this._margins.right=Math.max(this.paddingRight,this._margins.right),this._margins.bottom=Math.max(this.paddingBottom,this._margins.bottom))}afterFit(){at(this.options.afterFit,[this])}isHorizontal(){const{axis:t,position:n}=this.options;return n==="top"||n==="bottom"||t==="x"}isFullSize(){return this.options.fullSize}_convertTicksToLabels(t){this.beforeTickToLabelConversion(),this.generateTickLabels(t);let n,i;for(n=0,i=t.length;n<i;n++)Q(t[n].label)&&(t.splice(n,1),i--,n--);this.afterTickToLabelConversion()}_getLabelSizes(){let t=this._labelSizes;if(!t){const n=this.options.ticks.sampleSize;let i=this.ticks;n<i.length&&(i=Dr(i,n)),this._labelSizes=t=this._computeLabelSizes(i,i.length,this.options.ticks.maxTicksLimit)}return t}_computeLabelSizes(t,n,i){const{ctx:s,_longestTextCache:o}=this,r=[],a=[],l=Math.floor(n/Or(n,i));let c=0,u=0,f,d,h,g,p,m,_,b,x,v,y;for(f=0;f<n;f+=l){if(g=t[f].label,p=this._resolveTickFontOptions(f),s.font=m=p.string,_=o[m]=o[m]||{data:{},gc:[]},b=p.lineHeight,x=v=0,!Q(g)&&!st(g))x=$i(s,_.data,_.gc,x,g),v=b;else if(st(g))for(d=0,h=g.length;d<h;++d)y=g[d],!Q(y)&&!st(y)&&(x=$i(s,_.data,_.gc,x,y),v+=b);r.push(x),a.push(v),c=Math.max(x,c),u=Math.max(v,u)}dp(o,n);const k=r.indexOf(c),M=a.indexOf(u),$=P=>({width:r[P]||0,height:a[P]||0});return{first:$(0),last:$(n-1),widest:$(k),highest:$(M),widths:r,heights:a}}getLabelForValue(t){return t}getPixelForValue(t,n){return NaN}getValueForPixel(t){}getPixelForTick(t){const n=this.ticks;return t<0||t>n.length-1?null:this.getPixelForValue(n[t].value)}getPixelForDecimal(t){this._reversePixels&&(t=1-t);const n=this._startPixel+t*this._length;return xh(this._alignToPixels?ke(this.chart,n,0):n)}getDecimalForPixel(t){const n=(t-this._startPixel)/this._length;return this._reversePixels?1-n:n}getBasePixel(){return this.getPixelForValue(this.getBaseValue())}getBaseValue(){const{min:t,max:n}=this;return t<0&&n<0?n:t>0&&n>0?t:0}getContext(t){const n=this.ticks||[];if(t>=0&&t<n.length){const i=n[t];return i.$context||(i.$context=gp(this.getContext(),t,i))}return this.$context||(this.$context=hp(this.chart.getContext(),this))}_tickSize(){const t=this.options.ticks,n=jt(this.labelRotation),i=Math.abs(Math.cos(n)),s=Math.abs(Math.sin(n)),o=this._getLabelSizes(),r=t.autoSkipPadding||0,a=o?o.widest.width+r:0,l=o?o.highest.height+r:0;return this.isHorizontal()?l*i>a*s?a/i:l/s:l*s<a*i?l/i:a/s}_isVisible(){const t=this.options.display;return t!=="auto"?!!t:this.getMatchingVisibleMetas().length>0}_computeGridLineItems(t){const n=this.axis,i=this.chart,s=this.options,{grid:o,position:r,border:a}=s,l=o.offset,c=this.isHorizontal(),f=this.ticks.length+(l?1:0),d=hn(o),h=[],g=a.setContext(this.getContext()),p=g.display?g.width:0,m=p/2,_=function(G){return ke(i,G,p)};let b,x,v,y,k,M,$,P,O,R,z,ct;if(r==="top")b=_(this.bottom),M=this.bottom-d,P=b-m,R=_(t.top)+m,ct=t.bottom;else if(r==="bottom")b=_(this.top),R=t.top,ct=_(t.bottom)-m,M=b+m,P=this.top+d;else if(r==="left")b=_(this.right),k=this.right-d,$=b-m,O=_(t.left)+m,z=t.right;else if(r==="right")b=_(this.left),O=t.left,z=_(t.right)-m,k=b+m,$=this.left+d;else if(n==="x"){if(r==="center")b=_((t.top+t.bottom)/2+.5);else if(W(r)){const G=Object.keys(r)[0],N=r[G];b=_(this.chart.scales[G].getPixelForValue(N))}R=t.top,ct=t.bottom,M=b+m,P=M+d}else if(n==="y"){if(r==="center")b=_((t.left+t.right)/2);else if(W(r)){const G=Object.keys(r)[0],N=r[G];b=_(this.chart.scales[G].getPixelForValue(N))}k=b-m,$=k-d,O=t.left,z=t.right}const dt=tt(s.ticks.maxTicksLimit,f),V=Math.max(1,Math.ceil(f/dt));for(x=0;x<f;x+=V){const G=this.getContext(x),N=o.setContext(G),it=a.setContext(G),B=N.lineWidth,ft=N.color,ne=it.dash||[],wt=it.dashOffset,St=N.tickWidth,ie=N.tickColor,de=N.tickBorderDash||[],yt=N.tickBorderDashOffset;v=fp(this,x,l),v!==void 0&&(y=ke(i,v,B),c?k=$=O=z=y:M=P=R=ct=y,h.push({tx1:k,ty1:M,tx2:$,ty2:P,x1:O,y1:R,x2:z,y2:ct,width:B,color:ft,borderDash:ne,borderDashOffset:wt,tickWidth:St,tickColor:ie,tickBorderDash:de,tickBorderDashOffset:yt}))}return this._ticksLength=f,this._borderValue=b,h}_computeLabelItems(t){const n=this.axis,i=this.options,{position:s,ticks:o}=i,r=this.isHorizontal(),a=this.ticks,{align:l,crossAlign:c,padding:u,mirror:f}=o,d=hn(i.grid),h=d+u,g=f?-u:h,p=-jt(this.labelRotation),m=[];let _,b,x,v,y,k,M,$,P,O,R,z,ct="middle";if(s==="top")k=this.bottom-g,M=this._getXAxisLabelAlignment();else if(s==="bottom")k=this.top+g,M=this._getXAxisLabelAlignment();else if(s==="left"){const V=this._getYAxisLabelAlignment(d);M=V.textAlign,y=V.x}else if(s==="right"){const V=this._getYAxisLabelAlignment(d);M=V.textAlign,y=V.x}else if(n==="x"){if(s==="center")k=(t.top+t.bottom)/2+h;else if(W(s)){const V=Object.keys(s)[0],G=s[V];k=this.chart.scales[V].getPixelForValue(G)+h}M=this._getXAxisLabelAlignment()}else if(n==="y"){if(s==="center")y=(t.left+t.right)/2-h;else if(W(s)){const V=Object.keys(s)[0],G=s[V];y=this.chart.scales[V].getPixelForValue(G)}M=this._getYAxisLabelAlignment(d).textAlign}n==="y"&&(l==="start"?ct="top":l==="end"&&(ct="bottom"));const dt=this._getLabelSizes();for(_=0,b=a.length;_<b;++_){x=a[_],v=x.label;const V=o.setContext(this.getContext(_));$=this.getPixelForTick(_)+o.labelOffset,P=this._resolveTickFontOptions(_),O=P.lineHeight,R=st(v)?v.length:1;const G=R/2,N=V.color,it=V.textStrokeColor,B=V.textStrokeWidth;let ft=M;r?(y=$,M==="inner"&&(_===b-1?ft=this.options.reverse?"left":"right":_===0?ft=this.options.reverse?"right":"left":ft="center"),s==="top"?c==="near"||p!==0?z=-R*O+O/2:c==="center"?z=-dt.highest.height/2-G*O+O:z=-dt.highest.height+O/2:c==="near"||p!==0?z=O/2:c==="center"?z=dt.highest.height/2-G*O:z=dt.highest.height-R*O,f&&(z*=-1),p!==0&&!V.showLabelBackdrop&&(y+=O/2*Math.sin(p))):(k=$,z=(1-R)*O/2);let ne;if(V.showLabelBackdrop){const wt=At(V.backdropPadding),St=dt.heights[_],ie=dt.widths[_];let de=z-wt.top,yt=0-wt.left;switch(ct){case"middle":de-=St/2;break;case"bottom":de-=St;break}switch(M){case"center":yt-=ie/2;break;case"right":yt-=ie;break;case"inner":_===b-1?yt-=ie:_>0&&(yt-=ie/2);break}ne={left:yt,top:de,width:ie+wt.width,height:St+wt.height,color:V.backdropColor}}m.push({label:v,font:P,textOffset:z,options:{rotation:p,color:N,strokeColor:it,strokeWidth:B,textAlign:ft,textBaseline:ct,translation:[y,k],backdrop:ne}})}return m}_getXAxisLabelAlignment(){const{position:t,ticks:n}=this.options;if(-jt(this.labelRotation))return t==="top"?"left":"right";let s="center";return n.align==="start"?s="left":n.align==="end"?s="right":n.align==="inner"&&(s="inner"),s}_getYAxisLabelAlignment(t){const{position:n,ticks:{crossAlign:i,mirror:s,padding:o}}=this.options,r=this._getLabelSizes(),a=t+o,l=r.widest.width;let c,u;return n==="left"?s?(u=this.right+o,i==="near"?c="left":i==="center"?(c="center",u+=l/2):(c="right",u+=l)):(u=this.right-a,i==="near"?c="right":i==="center"?(c="center",u-=l/2):(c="left",u=this.left)):n==="right"?s?(u=this.left+o,i==="near"?c="right":i==="center"?(c="center",u-=l/2):(c="left",u-=l)):(u=this.left+a,i==="near"?c="left":i==="center"?(c="center",u+=l/2):(c="right",u=this.right)):c="right",{textAlign:c,x:u}}_computeLabelArea(){if(this.options.ticks.mirror)return;const t=this.chart,n=this.options.position;if(n==="left"||n==="right")return{top:0,left:this.left,bottom:t.height,right:this.right};if(n==="top"||n==="bottom")return{top:this.top,left:0,bottom:this.bottom,right:t.width}}drawBackground(){const{ctx:t,options:{backgroundColor:n},left:i,top:s,width:o,height:r}=this;n&&(t.save(),t.fillStyle=n,t.fillRect(i,s,o,r),t.restore())}getLineWidthForValue(t){const n=this.options.grid;if(!this._isVisible()||!n.display)return 0;const s=this.ticks.findIndex(o=>o.value===t);return s>=0?n.setContext(this.getContext(s)).lineWidth:0}drawGrid(t){const n=this.options.grid,i=this.ctx,s=this._gridLineItems||(this._gridLineItems=this._computeGridLineItems(t));let o,r;const a=(l,c,u)=>{!u.width||!u.color||(i.save(),i.lineWidth=u.width,i.strokeStyle=u.color,i.setLineDash(u.borderDash||[]),i.lineDashOffset=u.borderDashOffset,i.beginPath(),i.moveTo(l.x,l.y),i.lineTo(c.x,c.y),i.stroke(),i.restore())};if(n.display)for(o=0,r=s.length;o<r;++o){const l=s[o];n.drawOnChartArea&&a({x:l.x1,y:l.y1},{x:l.x2,y:l.y2},l),n.drawTicks&&a({x:l.tx1,y:l.ty1},{x:l.tx2,y:l.ty2},{color:l.tickColor,width:l.tickWidth,borderDash:l.tickBorderDash,borderDashOffset:l.tickBorderDashOffset})}}drawBorder(){const{chart:t,ctx:n,options:{border:i,grid:s}}=this,o=i.setContext(this.getContext()),r=i.display?o.width:0;if(!r)return;const a=s.setContext(this.getContext(0)).lineWidth,l=this._borderValue;let c,u,f,d;this.isHorizontal()?(c=ke(t,this.left,r)-r/2,u=ke(t,this.right,a)+a/2,f=d=l):(f=ke(t,this.top,r)-r/2,d=ke(t,this.bottom,a)+a/2,c=u=l),n.save(),n.lineWidth=o.width,n.strokeStyle=o.color,n.beginPath(),n.moveTo(c,f),n.lineTo(u,d),n.stroke(),n.restore()}drawLabels(t){if(!this.options.ticks.display)return;const i=this.ctx,s=this._computeLabelArea();s&&bl(i,s);const o=this.getLabelItems(t);for(const r of o){const a=r.options,l=r.font,c=r.label,u=r.textOffset;Rn(i,c,0,u,l,a)}s&&yl(i)}drawTitle(){const{ctx:t,options:{position:n,title:i,reverse:s}}=this;if(!i.display)return;const o=bt(i.font),r=At(i.padding),a=i.align;let l=o.lineHeight/2;n==="bottom"||n==="center"||W(n)?(l+=r.bottom,st(i.text)&&(l+=o.lineHeight*(i.text.length-1))):l+=r.top;const{titleX:c,titleY:u,maxWidth:f,rotation:d}=mp(this,l,n,a);Rn(t,i.text,0,0,o,{color:i.color,maxWidth:f,rotation:d,textAlign:pp(a,n,s),textBaseline:"middle",translation:[c,u]})}draw(t){this._isVisible()&&(this.drawBackground(),this.drawGrid(t),this.drawBorder(),this.drawTitle(),this.drawLabels(t))}_layers(){const t=this.options,n=t.ticks&&t.ticks.z||0,i=tt(t.grid&&t.grid.z,-1),s=tt(t.border&&t.border.z,0);return!this._isVisible()||this.draw!==He.prototype.draw?[{z:n,draw:o=>{this.draw(o)}}]:[{z:i,draw:o=>{this.drawBackground(),this.drawGrid(o),this.drawTitle()}},{z:s,draw:()=>{this.drawBorder()}},{z:n,draw:o=>{this.drawLabels(o)}}]}getMatchingVisibleMetas(t){const n=this.chart.getSortedVisibleDatasetMetas(),i=this.axis+"AxisID",s=[];let o,r;for(o=0,r=n.length;o<r;++o){const a=n[o];a[i]===this.id&&(!t||a.type===t)&&s.push(a)}return s}_resolveTickFontOptions(t){const n=this.options.ticks.setContext(this.getContext(t));return bt(n.font)}_maxDigits(){const t=this._resolveTickFontOptions(0).lineHeight;return(this.isHorizontal()?this.width:this.height)/t}}class Kn{constructor(t,n,i){this.type=t,this.scope=n,this.override=i,this.items=Object.create(null)}isForType(t){return Object.prototype.isPrototypeOf.call(this.type.prototype,t.prototype)}register(t){const n=Object.getPrototypeOf(t);let i;yp(n)&&(i=this.register(n));const s=this.items,o=t.id,r=this.scope+"."+o;if(!o)throw new Error("class does not have id: "+t);return o in s||(s[o]=t,_p(t,r,i),this.override&&ot.override(t.id,t.overrides)),r}get(t){return this.items[t]}unregister(t){const n=this.items,i=t.id,s=this.scope;i in n&&delete n[i],s&&i in ot[s]&&(delete ot[s][i],this.override&&delete Ve[i])}}function _p(e,t,n){const i=kn(Object.create(null),[n?ot.get(n):{},ot.get(t),e.defaults]);ot.set(t,i),e.defaultRoutes&&bp(t,e.defaultRoutes),e.descriptors&&ot.describe(t,e.descriptors)}function bp(e,t){Object.keys(t).forEach(n=>{const i=n.split("."),s=i.pop(),o=[e].concat(i).join("."),r=t[n].split("."),a=r.pop(),l=r.join(".");ot.route(o,s,l,a)})}function yp(e){return"id"in e&&"defaults"in e}class xp{constructor(){this.controllers=new Kn(Qe,"datasets",!0),this.elements=new Kn(Ln,"elements"),this.plugins=new Kn(Object,"plugins"),this.scales=new Kn(He,"scales"),this._typedRegistries=[this.controllers,this.scales,this.elements]}add(...t){this._each("register",t)}remove(...t){this._each("unregister",t)}addControllers(...t){this._each("register",t,this.controllers)}addElements(...t){this._each("register",t,this.elements)}addPlugins(...t){this._each("register",t,this.plugins)}addScales(...t){this._each("register",t,this.scales)}getController(t){return this._get(t,this.controllers,"controller")}getElement(t){return this._get(t,this.elements,"element")}getPlugin(t){return this._get(t,this.plugins,"plugin")}getScale(t){return this._get(t,this.scales,"scale")}removeControllers(...t){this._each("unregister",t,this.controllers)}removeElements(...t){this._each("unregister",t,this.elements)}removePlugins(...t){this._each("unregister",t,this.plugins)}removeScales(...t){this._each("unregister",t,this.scales)}_each(t,n,i){[...n].forEach(s=>{const o=i||this._getRegistryForType(s);i||o.isForType(s)||o===this.plugins&&s.id?this._exec(t,o,s):q(s,r=>{const a=i||this._getRegistryForType(r);this._exec(t,a,r)})})}_exec(t,n,i){const s=oo(t);at(i["before"+s],[],i),n[t](i),at(i["after"+s],[],i)}_getRegistryForType(t){for(let n=0;n<this._typedRegistries.length;n++){const i=this._typedRegistries[n];if(i.isForType(t))return i}return this.plugins}_get(t,n,i){const s=n.get(t);if(s===void 0)throw new Error('"'+t+'" is not a registered '+i+".");return s}}var Xt=new xp;class vp{constructor(){this._init=[]}notify(t,n,i,s){n==="beforeInit"&&(this._init=this._createDescriptors(t,!0),this._notify(this._init,t,"install"));const o=s?this._descriptors(t).filter(s):this._descriptors(t),r=this._notify(o,t,n,i);return n==="afterDestroy"&&(this._notify(o,t,"stop"),this._notify(this._init,t,"uninstall")),r}_notify(t,n,i,s){s=s||{};for(const o of t){const r=o.plugin,a=r[i],l=[n,s,o.options];if(at(a,l,r)===!1&&s.cancelable)return!1}return!0}invalidate(){Q(this._cache)||(this._oldCache=this._cache,this._cache=void 0)}_descriptors(t){if(this._cache)return this._cache;const n=this._cache=this._createDescriptors(t);return this._notifyStateChanges(t),n}_createDescriptors(t,n){const i=t&&t.config,s=tt(i.options&&i.options.plugins,{}),o=wp(i);return s===!1&&!n?[]:Cp(t,o,s,n)}_notifyStateChanges(t){const n=this._oldCache||[],i=this._cache,s=(o,r)=>o.filter(a=>!r.some(l=>a.plugin.id===l.plugin.id));this._notify(s(n,i),t,"stop"),this._notify(s(i,n),t,"start")}}function wp(e){const t={},n=[],i=Object.keys(Xt.plugins.items);for(let o=0;o<i.length;o++)n.push(Xt.getPlugin(i[o]));const s=e.plugins||[];for(let o=0;o<s.length;o++){const r=s[o];n.indexOf(r)===-1&&(n.push(r),t[r.id]=!0)}return{plugins:n,localIds:t}}function Sp(e,t){return!t&&e===!1?null:e===!0?{}:e}function Cp(e,{plugins:t,localIds:n},i,s){const o=[],r=e.getContext();for(const a of t){const l=a.id,c=Sp(i[l],s);c!==null&&o.push({plugin:a,options:Mp(e.config,{plugin:a,local:n[l]},c,r)})}return o}function Mp(e,{plugin:t,local:n},i,s){const o=e.pluginScopeKeys(t),r=e.getOptionScopes(i,o);return n&&t.defaults&&r.push(t.defaults),e.createResolver(r,s,[""],{scriptable:!1,indexable:!1,allKeys:!0})}function Ls(e,t){const n=ot.datasets[e]||{};return((t.datasets||{})[e]||{}).indexAxis||t.indexAxis||n.indexAxis||"x"}function kp(e,t){let n=e;return e==="_index_"?n=t:e==="_value_"&&(n=t==="x"?"y":"x"),n}function Rp(e,t){return e===t?"_index_":"_value_"}function Er(e){if(e==="x"||e==="y"||e==="r")return e}function $p(e){if(e==="top"||e==="bottom")return"x";if(e==="left"||e==="right")return"y"}function Ts(e,...t){if(Er(e))return e;for(const n of t){const i=n.axis||$p(n.position)||e.length>1&&Er(e[0].toLowerCase());if(i)return i}throw new Error(`Cannot determine type of '${e}' axis. Please provide 'axis' or 'position' option.`)}function Lr(e,t,n){if(n[t+"AxisID"]===e)return{axis:t}}function Ap(e,t){if(t.data&&t.data.datasets){const n=t.data.datasets.filter(i=>i.xAxisID===e||i.yAxisID===e);if(n.length)return Lr(e,"x",n[0])||Lr(e,"y",n[0])}return{}}function Pp(e,t){const n=Ve[e.type]||{scales:{}},i=t.scales||{},s=Ls(e.type,t),o=Object.create(null);return Object.keys(i).forEach(r=>{const a=i[r];if(!W(a))return console.error(`Invalid scale configuration for scale: ${r}`);if(a._proxy)return console.warn(`Ignoring resolver passed as options for scale: ${r}`);const l=Ts(r,a,Ap(r,e),ot.scales[a.type]),c=Rp(l,s),u=n.scales||{};o[r]=yn(Object.create(null),[{axis:l},a,u[l],u[c]])}),e.data.datasets.forEach(r=>{const a=r.type||e.type,l=r.indexAxis||Ls(a,t),u=(Ve[a]||{}).scales||{};Object.keys(u).forEach(f=>{const d=kp(f,l),h=r[d+"AxisID"]||d;o[h]=o[h]||Object.create(null),yn(o[h],[{axis:d},i[h],u[f]])})}),Object.keys(o).forEach(r=>{const a=o[r];yn(a,[ot.scales[a.type],ot.scale])}),o}function Ol(e){const t=e.options||(e.options={});t.plugins=tt(t.plugins,{}),t.scales=Pp(e,t)}function Dl(e){return e=e||{},e.datasets=e.datasets||[],e.labels=e.labels||[],e}function Op(e){return e=e||{},e.data=Dl(e.data),Ol(e),e}const Tr=new Map,Fl=new Set;function Qn(e,t){let n=Tr.get(e);return n||(n=t(),Tr.set(e,n),Fl.add(n)),n}const gn=(e,t,n)=>{const i=nn(t,n);i!==void 0&&e.add(i)};class Dp{constructor(t){this._config=Op(t),this._scopeCache=new Map,this._resolverCache=new Map}get platform(){return this._config.platform}get type(){return this._config.type}set type(t){this._config.type=t}get data(){return this._config.data}set data(t){this._config.data=Dl(t)}get options(){return this._config.options}set options(t){this._config.options=t}get plugins(){return this._config.plugins}update(){const t=this._config;this.clearCache(),Ol(t)}clearCache(){this._scopeCache.clear(),this._resolverCache.clear()}datasetScopeKeys(t){return Qn(t,()=>[[`datasets.${t}`,""]])}datasetAnimationScopeKeys(t,n){return Qn(`${t}.transition.${n}`,()=>[[`datasets.${t}.transitions.${n}`,`transitions.${n}`],[`datasets.${t}`,""]])}datasetElementScopeKeys(t,n){return Qn(`${t}-${n}`,()=>[[`datasets.${t}.elements.${n}`,`datasets.${t}`,`elements.${n}`,""]])}pluginScopeKeys(t){const n=t.id,i=this.type;return Qn(`${i}-plugin-${n}`,()=>[[`plugins.${n}`,...t.additionalOptionScopes||[]]])}_cachedScopes(t,n){const i=this._scopeCache;let s=i.get(t);return(!s||n)&&(s=new Map,i.set(t,s)),s}getOptionScopes(t,n,i){const{options:s,type:o}=this,r=this._cachedScopes(t,i),a=r.get(n);if(a)return a;const l=new Set;n.forEach(u=>{t&&(l.add(t),u.forEach(f=>gn(l,t,f))),u.forEach(f=>gn(l,s,f)),u.forEach(f=>gn(l,Ve[o]||{},f)),u.forEach(f=>gn(l,ot,f)),u.forEach(f=>gn(l,Ds,f))});const c=Array.from(l);return c.length===0&&c.push(Object.create(null)),Fl.has(n)&&r.set(n,c),c}chartOptionScopes(){const{options:t,type:n}=this;return[t,Ve[n]||{},ot.datasets[n]||{},{type:n},ot,Ds]}resolveNamedOptions(t,n,i,s=[""]){const o={$shared:!0},{resolver:r,subPrefixes:a}=Ir(this._resolverCache,t,s);let l=r;if(Ep(r,n)){o.$shared=!1,i=Se(i)?i():i;const c=this.createResolver(t,i,a);l=sn(r,i,c)}for(const c of n)o[c]=l[c];return o}createResolver(t,n,i=[""],s){const{resolver:o}=Ir(this._resolverCache,t,i);return W(n)?sn(o,n,void 0,s):o}}function Ir(e,t,n){let i=e.get(t);i||(i=new Map,e.set(t,i));const s=n.join();let o=i.get(s);return o||(o={resolver:co(t,n),subPrefixes:n.filter(a=>!a.toLowerCase().includes("hover"))},i.set(s,o)),o}const Fp=e=>W(e)&&Object.getOwnPropertyNames(e).some(t=>Se(e[t]));function Ep(e,t){const{isScriptable:n,isIndexable:i}=xl(e);for(const s of t){const o=n(s),r=i(s),a=(r||o)&&e[s];if(o&&(Se(a)||Fp(a))||r&&st(a))return!0}return!1}var Lp="4.4.9";const Tp=["top","bottom","left","right","chartArea"];function Vr(e,t){return e==="top"||e==="bottom"||Tp.indexOf(e)===-1&&t==="x"}function zr(e,t){return function(n,i){return n[e]===i[e]?n[t]-i[t]:n[e]-i[e]}}function Hr(e){const t=e.chart,n=t.options.animation;t.notifyPlugins("afterRender"),at(n&&n.onComplete,[e],t)}function Ip(e){const t=e.chart,n=t.options.animation;at(n&&n.onProgress,[e],t)}function El(e){return ho()&&typeof e=="string"?e=document.getElementById(e):e&&e.length&&(e=e[0]),e&&e.canvas&&(e=e.canvas),e}const ai={},Nr=e=>{const t=El(e);return Object.values(ai).filter(n=>n.canvas===t).pop()};function Vp(e,t,n){const i=Object.keys(e);for(const s of i){const o=+s;if(o>=t){const r=e[s];delete e[s],(n>0||o>t)&&(e[o+n]=r)}}}function zp(e,t,n,i){return!n||e.type==="mouseout"?null:i?t:e}var ge;let ji=(ge=class{static register(...t){Xt.add(...t),Br()}static unregister(...t){Xt.remove(...t),Br()}constructor(t,n){const i=this.config=new Dp(n),s=El(t),o=Nr(s);if(o)throw new Error("Canvas is already in use. Chart with ID '"+o.id+"' must be destroyed before the canvas with ID '"+o.canvas.id+"' can be reused.");const r=i.createResolver(i.chartOptionScopes(),this.getContext());this.platform=new(i.platform||ip(s)),this.platform.updateConfig(i);const a=this.platform.acquireContext(s,r.aspectRatio),l=a&&a.canvas,c=l&&l.height,u=l&&l.width;if(this.id=lh(),this.ctx=a,this.canvas=l,this.width=u,this.height=c,this._options=r,this._aspectRatio=this.aspectRatio,this._layers=[],this._metasets=[],this._stacks=void 0,this.boxes=[],this.currentDevicePixelRatio=void 0,this.chartArea=void 0,this._active=[],this._lastEvent=void 0,this._listeners={},this._responsiveListeners=void 0,this._sortedMetasets=[],this.scales={},this._plugins=new vp,this.$proxies={},this._hiddenIndices={},this.attached=!1,this._animationsDisabled=void 0,this.$context=void 0,this._doResize=kh(f=>this.update(f),r.resizeDelay||0),this._dataChanges=[],ai[this.id]=this,!a||!l){console.error("Failed to create chart: can't acquire context from the given item");return}re.listen(this,"complete",Hr),re.listen(this,"progress",Ip),this._initialize(),this.attached&&this.update()}get aspectRatio(){const{options:{aspectRatio:t,maintainAspectRatio:n},width:i,height:s,_aspectRatio:o}=this;return Q(t)?n&&o?o:s?i/s:null:t}get data(){return this.config.data}set data(t){this.config.data=t}get options(){return this._options}set options(t){this.config.options=t}get registry(){return Xt}_initialize(){return this.notifyPlugins("beforeInit"),this.options.responsive?this.resize():mr(this,this.options.devicePixelRatio),this.bindEvents(),this.notifyPlugins("afterInit"),this}clear(){return dr(this.canvas,this.ctx),this}stop(){return re.stop(this),this}resize(t,n){re.running(this)?this._resizeBeforeDraw={width:t,height:n}:this._resize(t,n)}_resize(t,n){const i=this.options,s=this.canvas,o=i.maintainAspectRatio&&this.aspectRatio,r=this.platform.getMaximumSize(s,t,n,o),a=i.devicePixelRatio||this.platform.getDevicePixelRatio(),l=this.width?"resize":"attach";this.width=r.width,this.height=r.height,this._aspectRatio=this.aspectRatio,mr(this,a,!0)&&(this.notifyPlugins("resize",{size:r}),at(i.onResize,[this,r],this),this.attached&&this._doResize(l)&&this.render())}ensureScalesHaveIDs(){const n=this.options.scales||{};q(n,(i,s)=>{i.id=s})}buildOrUpdateScales(){const t=this.options,n=t.scales,i=this.scales,s=Object.keys(i).reduce((r,a)=>(r[a]=!1,r),{});let o=[];n&&(o=o.concat(Object.keys(n).map(r=>{const a=n[r],l=Ts(r,a),c=l==="r",u=l==="x";return{options:a,dposition:c?"chartArea":u?"bottom":"left",dtype:c?"radialLinear":u?"category":"linear"}}))),q(o,r=>{const a=r.options,l=a.id,c=Ts(l,a),u=tt(a.type,r.dtype);(a.position===void 0||Vr(a.position,c)!==Vr(r.dposition))&&(a.position=r.dposition),s[l]=!0;let f=null;if(l in i&&i[l].type===u)f=i[l];else{const d=Xt.getScale(u);f=new d({id:l,type:u,ctx:this.ctx,chart:this}),i[f.id]=f}f.init(a,t)}),q(s,(r,a)=>{r||delete i[a]}),q(i,r=>{be.configure(this,r,r.options),be.addBox(this,r)})}_updateMetasets(){const t=this._metasets,n=this.data.datasets.length,i=t.length;if(t.sort((s,o)=>s.index-o.index),i>n){for(let s=n;s<i;++s)this._destroyDatasetMeta(s);t.splice(n,i-n)}this._sortedMetasets=t.slice(0).sort(zr("order","index"))}_removeUnreferencedMetasets(){const{_metasets:t,data:{datasets:n}}=this;t.length>n.length&&delete this._stacks,t.forEach((i,s)=>{n.filter(o=>o===i._dataset).length===0&&this._destroyDatasetMeta(s)})}buildOrUpdateControllers(){const t=[],n=this.data.datasets;let i,s;for(this._removeUnreferencedMetasets(),i=0,s=n.length;i<s;i++){const o=n[i];let r=this.getDatasetMeta(i);const a=o.type||this.config.type;if(r.type&&r.type!==a&&(this._destroyDatasetMeta(i),r=this.getDatasetMeta(i)),r.type=a,r.indexAxis=o.indexAxis||Ls(a,this.options),r.order=o.order||0,r.index=i,r.label=""+o.label,r.visible=this.isDatasetVisible(i),r.controller)r.controller.updateIndex(i),r.controller.linkScales();else{const l=Xt.getController(a),{datasetElementType:c,dataElementType:u}=ot.datasets[a];Object.assign(l,{dataElementType:Xt.getElement(u),datasetElementType:c&&Xt.getElement(c)}),r.controller=new l(this,i),t.push(r.controller)}}return this._updateMetasets(),t}_resetElements(){q(this.data.datasets,(t,n)=>{this.getDatasetMeta(n).controller.reset()},this)}reset(){this._resetElements(),this.notifyPlugins("reset")}update(t){const n=this.config;n.update();const i=this._options=n.createResolver(n.chartOptionScopes(),this.getContext()),s=this._animationsDisabled=!i.animation;if(this._updateScales(),this._checkEventBindings(),this._updateHiddenIndices(),this._plugins.invalidate(),this.notifyPlugins("beforeUpdate",{mode:t,cancelable:!0})===!1)return;const o=this.buildOrUpdateControllers();this.notifyPlugins("beforeElementsUpdate");let r=0;for(let c=0,u=this.data.datasets.length;c<u;c++){const{controller:f}=this.getDatasetMeta(c),d=!s&&o.indexOf(f)===-1;f.buildOrUpdateElements(d),r=Math.max(+f.getMaxOverflow(),r)}r=this._minPadding=i.layout.autoPadding?r:0,this._updateLayout(r),s||q(o,c=>{c.reset()}),this._updateDatasets(t),this.notifyPlugins("afterUpdate",{mode:t}),this._layers.sort(zr("z","_idx"));const{_active:a,_lastEvent:l}=this;l?this._eventHandler(l,!0):a.length&&this._updateHoverStyles(a,a,!0),this.render()}_updateScales(){q(this.scales,t=>{be.removeBox(this,t)}),this.ensureScalesHaveIDs(),this.buildOrUpdateScales()}_checkEventBindings(){const t=this.options,n=new Set(Object.keys(this._listeners)),i=new Set(t.events);(!ir(n,i)||!!this._responsiveListeners!==t.responsive)&&(this.unbindEvents(),this.bindEvents())}_updateHiddenIndices(){const{_hiddenIndices:t}=this,n=this._getUniformDataChanges()||[];for(const{method:i,start:s,count:o}of n){const r=i==="_removeElements"?-o:o;Vp(t,s,r)}}_getUniformDataChanges(){const t=this._dataChanges;if(!t||!t.length)return;this._dataChanges=[];const n=this.data.datasets.length,i=o=>new Set(t.filter(r=>r[0]===o).map((r,a)=>a+","+r.splice(1).join(","))),s=i(0);for(let o=1;o<n;o++)if(!ir(s,i(o)))return;return Array.from(s).map(o=>o.split(",")).map(o=>({method:o[1],start:+o[2],count:+o[3]}))}_updateLayout(t){if(this.notifyPlugins("beforeLayout",{cancelable:!0})===!1)return;be.update(this,this.width,this.height,t);const n=this.chartArea,i=n.width<=0||n.height<=0;this._layers=[],q(this.boxes,s=>{i&&s.position==="chartArea"||(s.configure&&s.configure(),this._layers.push(...s._layers()))},this),this._layers.forEach((s,o)=>{s._idx=o}),this.notifyPlugins("afterLayout")}_updateDatasets(t){if(this.notifyPlugins("beforeDatasetsUpdate",{mode:t,cancelable:!0})!==!1){for(let n=0,i=this.data.datasets.length;n<i;++n)this.getDatasetMeta(n).controller.configure();for(let n=0,i=this.data.datasets.length;n<i;++n)this._updateDataset(n,Se(t)?t({datasetIndex:n}):t);this.notifyPlugins("afterDatasetsUpdate",{mode:t})}}_updateDataset(t,n){const i=this.getDatasetMeta(t),s={meta:i,index:t,mode:n,cancelable:!0};this.notifyPlugins("beforeDatasetUpdate",s)!==!1&&(i.controller._update(n),s.cancelable=!1,this.notifyPlugins("afterDatasetUpdate",s))}render(){this.notifyPlugins("beforeRender",{cancelable:!0})!==!1&&(re.has(this)?this.attached&&!re.running(this)&&re.start(this):(this.draw(),Hr({chart:this})))}draw(){let t;if(this._resizeBeforeDraw){const{width:i,height:s}=this._resizeBeforeDraw;this._resizeBeforeDraw=null,this._resize(i,s)}if(this.clear(),this.width<=0||this.height<=0||this.notifyPlugins("beforeDraw",{cancelable:!0})===!1)return;const n=this._layers;for(t=0;t<n.length&&n[t].z<=0;++t)n[t].draw(this.chartArea);for(this._drawDatasets();t<n.length;++t)n[t].draw(this.chartArea);this.notifyPlugins("afterDraw")}_getSortedDatasetMetas(t){const n=this._sortedMetasets,i=[];let s,o;for(s=0,o=n.length;s<o;++s){const r=n[s];(!t||r.visible)&&i.push(r)}return i}getSortedVisibleDatasetMetas(){return this._getSortedDatasetMetas(!0)}_drawDatasets(){if(this.notifyPlugins("beforeDatasetsDraw",{cancelable:!0})===!1)return;const t=this.getSortedVisibleDatasetMetas();for(let n=t.length-1;n>=0;--n)this._drawDataset(t[n]);this.notifyPlugins("afterDatasetsDraw")}_drawDataset(t){const n=this.ctx,i={meta:t,index:t.index,cancelable:!0},s=pg(this,t);this.notifyPlugins("beforeDatasetDraw",i)!==!1&&(s&&bl(n,s),t.controller.draw(),s&&yl(n),i.cancelable=!1,this.notifyPlugins("afterDatasetDraw",i))}isPointInArea(t){return qe(t,this.chartArea,this._minPadding)}getElementsAtEventForMode(t,n,i,s){const o=Tg.modes[n];return typeof o=="function"?o(this,t,i,s):[]}getDatasetMeta(t){const n=this.data.datasets[t],i=this._metasets;let s=i.filter(o=>o&&o._dataset===n).pop();return s||(s={type:null,data:[],dataset:null,controller:null,hidden:null,xAxisID:null,yAxisID:null,order:n&&n.order||0,index:t,_dataset:n,_parsed:[],_sorted:!1},i.push(s)),s}getContext(){return this.$context||(this.$context=ze(null,{chart:this,type:"chart"}))}getVisibleDatasetCount(){return this.getSortedVisibleDatasetMetas().length}isDatasetVisible(t){const n=this.data.datasets[t];if(!n)return!1;const i=this.getDatasetMeta(t);return typeof i.hidden=="boolean"?!i.hidden:!n.hidden}setDatasetVisibility(t,n){const i=this.getDatasetMeta(t);i.hidden=!n}toggleDataVisibility(t){this._hiddenIndices[t]=!this._hiddenIndices[t]}getDataVisibility(t){return!this._hiddenIndices[t]}_updateVisibility(t,n,i){const s=i?"show":"hide",o=this.getDatasetMeta(t),r=o.controller._resolveAnimations(void 0,s);Si(n)?(o.data[n].hidden=!i,this.update()):(this.setDatasetVisibility(t,i),r.update(o,{visible:i}),this.update(a=>a.datasetIndex===t?s:void 0))}hide(t,n){this._updateVisibility(t,n,!1)}show(t,n){this._updateVisibility(t,n,!0)}_destroyDatasetMeta(t){const n=this._metasets[t];n&&n.controller&&n.controller._destroy(),delete this._metasets[t]}_stop(){let t,n;for(this.stop(),re.remove(this),t=0,n=this.data.datasets.length;t<n;++t)this._destroyDatasetMeta(t)}destroy(){this.notifyPlugins("beforeDestroy");const{canvas:t,ctx:n}=this;this._stop(),this.config.clearCache(),t&&(this.unbindEvents(),dr(t,n),this.platform.releaseContext(n),this.canvas=null,this.ctx=null),delete ai[this.id],this.notifyPlugins("afterDestroy")}toBase64Image(...t){return this.canvas.toDataURL(...t)}bindEvents(){this.bindUserEvents(),this.options.responsive?this.bindResponsiveEvents():this.attached=!0}bindUserEvents(){const t=this._listeners,n=this.platform,i=(o,r)=>{n.addEventListener(this,o,r),t[o]=r},s=(o,r,a)=>{o.offsetX=r,o.offsetY=a,this._eventHandler(o)};q(this.options.events,o=>i(o,s))}bindResponsiveEvents(){this._responsiveListeners||(this._responsiveListeners={});const t=this._responsiveListeners,n=this.platform,i=(l,c)=>{n.addEventListener(this,l,c),t[l]=c},s=(l,c)=>{t[l]&&(n.removeEventListener(this,l,c),delete t[l])},o=(l,c)=>{this.canvas&&this.resize(l,c)};let r;const a=()=>{s("attach",a),this.attached=!0,this.resize(),i("resize",o),i("detach",r)};r=()=>{this.attached=!1,s("resize",o),this._stop(),this._resize(0,0),i("attach",a)},n.isAttached(this.canvas)?a():r()}unbindEvents(){q(this._listeners,(t,n)=>{this.platform.removeEventListener(this,n,t)}),this._listeners={},q(this._responsiveListeners,(t,n)=>{this.platform.removeEventListener(this,n,t)}),this._responsiveListeners=void 0}updateHoverStyle(t,n,i){const s=i?"set":"remove";let o,r,a,l;for(n==="dataset"&&(o=this.getDatasetMeta(t[0].datasetIndex),o.controller["_"+s+"DatasetHoverStyle"]()),a=0,l=t.length;a<l;++a){r=t[a];const c=r&&this.getDatasetMeta(r.datasetIndex).controller;c&&c[s+"HoverStyle"](r.element,r.datasetIndex,r.index)}}getActiveElements(){return this._active||[]}setActiveElements(t){const n=this._active||[],i=t.map(({datasetIndex:o,index:r})=>{const a=this.getDatasetMeta(o);if(!a)throw new Error("No dataset found at index "+o);return{datasetIndex:o,element:a.data[r],index:r}});!vi(i,n)&&(this._active=i,this._lastEvent=null,this._updateHoverStyles(i,n))}notifyPlugins(t,n,i){return this._plugins.notify(this,t,n,i)}isPluginEnabled(t){return this._plugins._cache.filter(n=>n.plugin.id===t).length===1}_updateHoverStyles(t,n,i){const s=this.options.hover,o=(l,c)=>l.filter(u=>!c.some(f=>u.datasetIndex===f.datasetIndex&&u.index===f.index)),r=o(n,t),a=i?t:o(t,n);r.length&&this.updateHoverStyle(r,s.mode,!1),a.length&&s.mode&&this.updateHoverStyle(a,s.mode,!0)}_eventHandler(t,n){const i={event:t,replay:n,cancelable:!0,inChartArea:this.isPointInArea(t)},s=r=>(r.options.events||this.options.events).includes(t.native.type);if(this.notifyPlugins("beforeEvent",i,s)===!1)return;const o=this._handleEvent(t,n,i.inChartArea);return i.cancelable=!1,this.notifyPlugins("afterEvent",i,s),(o||i.changed)&&this.render(),this}_handleEvent(t,n,i){const{_active:s=[],options:o}=this,r=n,a=this._getActiveElements(t,s,i,r),l=gh(t),c=zp(t,this._lastEvent,i,l);i&&(this._lastEvent=null,at(o.onHover,[t,a,this],this),l&&at(o.onClick,[t,a,this],this));const u=!vi(a,s);return(u||n)&&(this._active=a,this._updateHoverStyles(a,s,n)),this._lastEvent=c,u}_getActiveElements(t,n,i,s){if(t.type==="mouseout")return[];if(!i)return n;const o=this.options.hover;return this.getElementsAtEventForMode(t,o.mode,o,s)}},A(ge,"defaults",ot),A(ge,"instances",ai),A(ge,"overrides",Ve),A(ge,"registry",Xt),A(ge,"version",Lp),A(ge,"getChart",Nr),ge);function Br(){return q(ji.instances,e=>e._plugins.invalidate())}function Hp(e,t,n){const{startAngle:i,pixelMargin:s,x:o,y:r,outerRadius:a,innerRadius:l}=t;let c=s/a;e.beginPath(),e.arc(o,r,a,i-c,n+c),l>s?(c=s/l,e.arc(o,r,l,n+c,i-c,!0)):e.arc(o,r,s,n+ut,i-ut),e.closePath(),e.clip()}function Np(e){return lo(e,["outerStart","outerEnd","innerStart","innerEnd"])}function Bp(e,t,n,i){const s=Np(e.options.borderRadius),o=(n-t)/2,r=Math.min(o,i*t/2),a=l=>{const c=(n-Math.min(o,l))*i/2;return Mt(l,0,Math.min(o,c))};return{outerStart:a(s.outerStart),outerEnd:a(s.outerEnd),innerStart:Mt(s.innerStart,0,r),innerEnd:Mt(s.innerEnd,0,r)}}function We(e,t,n,i){return{x:n+e*Math.cos(t),y:i+e*Math.sin(t)}}function Oi(e,t,n,i,s,o){const{x:r,y:a,startAngle:l,pixelMargin:c,innerRadius:u}=t,f=Math.max(t.outerRadius+i+n-c,0),d=u>0?u+i+n+c:0;let h=0;const g=s-l;if(i){const V=u>0?u-i:0,G=f>0?f-i:0,N=(V+G)/2,it=N!==0?g*N/(N+i):g;h=(g-it)/2}const p=Math.max(.001,g*f-n/rt)/f,m=(g-p)/2,_=l+m+h,b=s-m-h,{outerStart:x,outerEnd:v,innerStart:y,innerEnd:k}=Bp(t,d,f,b-_),M=f-x,$=f-v,P=_+x/M,O=b-v/$,R=d+y,z=d+k,ct=_+y/R,dt=b-k/z;if(e.beginPath(),o){const V=(P+O)/2;if(e.arc(r,a,f,P,V),e.arc(r,a,f,V,O),v>0){const B=We($,O,r,a);e.arc(B.x,B.y,v,O,b+ut)}const G=We(z,b,r,a);if(e.lineTo(G.x,G.y),k>0){const B=We(z,dt,r,a);e.arc(B.x,B.y,k,b+ut,dt+Math.PI)}const N=(b-k/d+(_+y/d))/2;if(e.arc(r,a,d,b-k/d,N,!0),e.arc(r,a,d,N,_+y/d,!0),y>0){const B=We(R,ct,r,a);e.arc(B.x,B.y,y,ct+Math.PI,_-ut)}const it=We(M,_,r,a);if(e.lineTo(it.x,it.y),x>0){const B=We(M,P,r,a);e.arc(B.x,B.y,x,_-ut,P)}}else{e.moveTo(r,a);const V=Math.cos(P)*f+r,G=Math.sin(P)*f+a;e.lineTo(V,G);const N=Math.cos(O)*f+r,it=Math.sin(O)*f+a;e.lineTo(N,it)}e.closePath()}function jp(e,t,n,i,s){const{fullCircles:o,startAngle:r,circumference:a}=t;let l=t.endAngle;if(o){Oi(e,t,n,i,l,s);for(let c=0;c<o;++c)e.fill();isNaN(a)||(l=r+(a%lt||lt))}return Oi(e,t,n,i,l,s),e.fill(),l}function Wp(e,t,n,i,s){const{fullCircles:o,startAngle:r,circumference:a,options:l}=t,{borderWidth:c,borderJoinStyle:u,borderDash:f,borderDashOffset:d}=l,h=l.borderAlign==="inner";if(!c)return;e.setLineDash(f||[]),e.lineDashOffset=d,h?(e.lineWidth=c*2,e.lineJoin=u||"round"):(e.lineWidth=c,e.lineJoin=u||"bevel");let g=t.endAngle;if(o){Oi(e,t,n,i,g,s);for(let p=0;p<o;++p)e.stroke();isNaN(a)||(g=r+(a%lt||lt))}h&&Hp(e,t,g),o||(Oi(e,t,n,i,g,s),e.stroke())}class _n extends Ln{constructor(n){super();A(this,"circumference");A(this,"endAngle");A(this,"fullCircles");A(this,"innerRadius");A(this,"outerRadius");A(this,"pixelMargin");A(this,"startAngle");this.options=void 0,this.circumference=void 0,this.startAngle=void 0,this.endAngle=void 0,this.innerRadius=void 0,this.outerRadius=void 0,this.pixelMargin=0,this.fullCircles=0,n&&Object.assign(this,n)}inRange(n,i,s){const o=this.getProps(["x","y"],s),{angle:r,distance:a}=fl(o,{x:n,y:i}),{startAngle:l,endAngle:c,innerRadius:u,outerRadius:f,circumference:d}=this.getProps(["startAngle","endAngle","innerRadius","outerRadius","circumference"],s),h=(this.options.spacing+this.options.borderWidth)/2,g=tt(d,c-l),p=Ri(r,l,c)&&l!==c,m=g>=lt||p,_=vh(a,u+h,f+h);return m&&_}getCenterPoint(n){const{x:i,y:s,startAngle:o,endAngle:r,innerRadius:a,outerRadius:l}=this.getProps(["x","y","startAngle","endAngle","innerRadius","outerRadius"],n),{offset:c,spacing:u}=this.options,f=(o+r)/2,d=(a+l+u+c)/2;return{x:i+Math.cos(f)*d,y:s+Math.sin(f)*d}}tooltipPosition(n){return this.getCenterPoint(n)}draw(n){const{options:i,circumference:s}=this,o=(i.offset||0)/4,r=(i.spacing||0)/2,a=i.circular;if(this.pixelMargin=i.borderAlign==="inner"?.33:0,this.fullCircles=s>lt?Math.floor(s/lt):0,s===0||this.innerRadius<0||this.outerRadius<0)return;n.save();const l=(this.startAngle+this.endAngle)/2;n.translate(Math.cos(l)*o,Math.sin(l)*o);const c=1-Math.sin(Math.min(rt,s||0)),u=o*c;n.fillStyle=i.backgroundColor,n.strokeStyle=i.borderColor,jp(n,this,u,r,a),Wp(n,this,u,r,a),n.restore()}}A(_n,"id","arc"),A(_n,"defaults",{borderAlign:"center",borderColor:"#fff",borderDash:[],borderDashOffset:0,borderJoinStyle:void 0,borderRadius:0,borderWidth:2,offset:0,spacing:0,angle:void 0,circular:!0}),A(_n,"defaultRoutes",{backgroundColor:"backgroundColor"}),A(_n,"descriptors",{_scriptable:!0,_indexable:n=>n!=="borderDash"});const Is=["rgb(54, 162, 235)","rgb(255, 99, 132)","rgb(255, 159, 64)","rgb(255, 205, 86)","rgb(75, 192, 192)","rgb(153, 102, 255)","rgb(201, 203, 207)"],jr=Is.map(e=>e.replace("rgb(","rgba(").replace(")",", 0.5)"));function Ll(e){return Is[e%Is.length]}function Tl(e){return jr[e%jr.length]}function Gp(e,t){return e.borderColor=Ll(t),e.backgroundColor=Tl(t),++t}function qp(e,t){return e.backgroundColor=e.data.map(()=>Ll(t++)),t}function Yp(e,t){return e.backgroundColor=e.data.map(()=>Tl(t++)),t}function Up(e){let t=0;return(n,i)=>{const s=e.getDatasetMeta(i).controller;s instanceof Ye?t=qp(n,t):s instanceof oi?t=Yp(n,t):s&&(t=Gp(n,t))}}function Wr(e){let t;for(t in e)if(e[t].borderColor||e[t].backgroundColor)return!0;return!1}function Xp(e){return e&&(e.borderColor||e.backgroundColor)}function Kp(){return ot.borderColor!=="rgba(0,0,0,0.1)"||ot.backgroundColor!=="rgba(0,0,0,0.1)"}var Qp={id:"colors",defaults:{enabled:!0,forceOverride:!1},beforeLayout(e,t,n){if(!n.enabled)return;const{data:{datasets:i},options:s}=e.config,{elements:o}=s,r=Wr(i)||Xp(s)||o&&Wr(o)||Kp();if(!n.forceOverride&&r)return;const a=Up(e);i.forEach(a)}};class Il extends Ln{constructor(t){super(),this.chart=t.chart,this.options=t.options,this.ctx=t.ctx,this._padding=void 0,this.top=void 0,this.bottom=void 0,this.left=void 0,this.right=void 0,this.width=void 0,this.height=void 0,this.position=void 0,this.weight=void 0,this.fullSize=void 0}update(t,n){const i=this.options;if(this.left=0,this.top=0,!i.display){this.width=this.height=this.right=this.bottom=0;return}this.width=this.right=t,this.height=this.bottom=n;const s=st(i.text)?i.text.length:1;this._padding=At(i.padding);const o=s*bt(i.font).lineHeight+this._padding.height;this.isHorizontal()?this.height=o:this.width=o}isHorizontal(){const t=this.options.position;return t==="top"||t==="bottom"}_drawArgs(t){const{top:n,left:i,bottom:s,right:o,options:r}=this,a=r.align;let l=0,c,u,f;return this.isHorizontal()?(u=xn(a,i,o),f=n+t,c=o-i):(r.position==="left"?(u=i+t,f=xn(a,s,n),l=rt*-.5):(u=o-t,f=xn(a,n,s),l=rt*.5),c=s-n),{titleX:u,titleY:f,maxWidth:c,rotation:l}}draw(){const t=this.ctx,n=this.options;if(!n.display)return;const i=bt(n.font),o=i.lineHeight/2+this._padding.top,{titleX:r,titleY:a,maxWidth:l,rotation:c}=this._drawArgs(o);Rn(t,n.text,0,0,i,{color:n.color,maxWidth:l,rotation:c,textAlign:pl(n.align),textBaseline:"middle",translation:[r,a]})}}function Zp(e,t){const n=new Il({ctx:e.ctx,options:t,chart:e});be.configure(e,n,t),be.addBox(e,n),e.titleBlock=n}var Jp={id:"title",_element:Il,start(e,t,n){Zp(e,n)},stop(e){const t=e.titleBlock;be.removeBox(e,t),delete e.titleBlock},beforeUpdate(e,t,n){const i=e.titleBlock;be.configure(e,i,n),i.options=n},defaults:{align:"center",display:!1,font:{weight:"bold"},fullSize:!0,padding:10,position:"top",text:"",weight:2e3},defaultRoutes:{color:"color"},descriptors:{_scriptable:!0,_indexable:!1}};const bn={average(e){if(!e.length)return!1;let t,n,i=new Set,s=0,o=0;for(t=0,n=e.length;t<n;++t){const a=e[t].element;if(a&&a.hasValue()){const l=a.tooltipPosition();i.add(l.x),s+=l.y,++o}}return o===0||i.size===0?!1:{x:[...i].reduce((a,l)=>a+l)/i.size,y:s/o}},nearest(e,t){if(!e.length)return!1;let n=t.x,i=t.y,s=Number.POSITIVE_INFINITY,o,r,a;for(o=0,r=e.length;o<r;++o){const l=e[o].element;if(l&&l.hasValue()){const c=l.getCenterPoint(),u=yh(t,c);u<s&&(s=u,a=l)}}if(a){const l=a.tooltipPosition();n=l.x,i=l.y}return{x:n,y:i}}};function Ut(e,t){return t&&(st(t)?Array.prototype.push.apply(e,t):e.push(t)),e}function ae(e){return(typeof e=="string"||e instanceof String)&&e.indexOf(`
`)>-1?e.split(`
`):e}function tm(e,t){const{element:n,datasetIndex:i,index:s}=t,o=e.getDatasetMeta(i).controller,{label:r,value:a}=o.getLabelAndValue(s);return{chart:e,label:r,parsed:o.getParsed(s),raw:e.data.datasets[i].data[s],formattedValue:a,dataset:o.getDataset(),dataIndex:s,datasetIndex:i,element:n}}function Gr(e,t){const n=e.chart.ctx,{body:i,footer:s,title:o}=e,{boxWidth:r,boxHeight:a}=t,l=bt(t.bodyFont),c=bt(t.titleFont),u=bt(t.footerFont),f=o.length,d=s.length,h=i.length,g=At(t.padding);let p=g.height,m=0,_=i.reduce((v,y)=>v+y.before.length+y.lines.length+y.after.length,0);if(_+=e.beforeBody.length+e.afterBody.length,f&&(p+=f*c.lineHeight+(f-1)*t.titleSpacing+t.titleMarginBottom),_){const v=t.displayColors?Math.max(a,l.lineHeight):l.lineHeight;p+=h*v+(_-h)*l.lineHeight+(_-1)*t.bodySpacing}d&&(p+=t.footerMarginTop+d*u.lineHeight+(d-1)*t.footerSpacing);let b=0;const x=function(v){m=Math.max(m,n.measureText(v).width+b)};return n.save(),n.font=c.string,q(e.title,x),n.font=l.string,q(e.beforeBody.concat(e.afterBody),x),b=t.displayColors?r+2+t.boxPadding:0,q(i,v=>{q(v.before,x),q(v.lines,x),q(v.after,x)}),b=0,n.font=u.string,q(e.footer,x),n.restore(),m+=g.width,{width:m,height:p}}function em(e,t){const{y:n,height:i}=t;return n<i/2?"top":n>e.height-i/2?"bottom":"center"}function nm(e,t,n,i){const{x:s,width:o}=i,r=n.caretSize+n.caretPadding;if(e==="left"&&s+o+r>t.width||e==="right"&&s-o-r<0)return!0}function im(e,t,n,i){const{x:s,width:o}=n,{width:r,chartArea:{left:a,right:l}}=e;let c="center";return i==="center"?c=s<=(a+l)/2?"left":"right":s<=o/2?c="left":s>=r-o/2&&(c="right"),nm(c,e,t,n)&&(c="center"),c}function qr(e,t,n){const i=n.yAlign||t.yAlign||em(e,n);return{xAlign:n.xAlign||t.xAlign||im(e,t,n,i),yAlign:i}}function sm(e,t){let{x:n,width:i}=e;return t==="right"?n-=i:t==="center"&&(n-=i/2),n}function om(e,t,n){let{y:i,height:s}=e;return t==="top"?i+=n:t==="bottom"?i-=s+n:i-=s/2,i}function Yr(e,t,n,i){const{caretSize:s,caretPadding:o,cornerRadius:r}=e,{xAlign:a,yAlign:l}=n,c=s+o,{topLeft:u,topRight:f,bottomLeft:d,bottomRight:h}=Sn(r);let g=sm(t,a);const p=om(t,l,c);return l==="center"?a==="left"?g+=c:a==="right"&&(g-=c):a==="left"?g-=Math.max(u,d)+s:a==="right"&&(g+=Math.max(f,h)+s),{x:Mt(g,0,i.width-t.width),y:Mt(p,0,i.height-t.height)}}function Zn(e,t,n){const i=At(n.padding);return t==="center"?e.x+e.width/2:t==="right"?e.x+e.width-i.right:e.x+i.left}function Ur(e){return Ut([],ae(e))}function rm(e,t,n){return ze(e,{tooltip:t,tooltipItems:n,type:"tooltip"})}function Xr(e,t){const n=t&&t.dataset&&t.dataset.tooltip&&t.dataset.tooltip.callbacks;return n?e.override(n):e}const Vl={beforeTitle:oe,title(e){if(e.length>0){const t=e[0],n=t.chart.data.labels,i=n?n.length:0;if(this&&this.options&&this.options.mode==="dataset")return t.dataset.label||"";if(t.label)return t.label;if(i>0&&t.dataIndex<i)return n[t.dataIndex]}return""},afterTitle:oe,beforeBody:oe,beforeLabel:oe,label(e){if(this&&this.options&&this.options.mode==="dataset")return e.label+": "+e.formattedValue||e.formattedValue;let t=e.dataset.label||"";t&&(t+=": ");const n=e.formattedValue;return Q(n)||(t+=n),t},labelColor(e){const n=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{borderColor:n.borderColor,backgroundColor:n.backgroundColor,borderWidth:n.borderWidth,borderDash:n.borderDash,borderDashOffset:n.borderDashOffset,borderRadius:0}},labelTextColor(){return this.options.bodyColor},labelPointStyle(e){const n=e.chart.getDatasetMeta(e.datasetIndex).controller.getStyle(e.dataIndex);return{pointStyle:n.pointStyle,rotation:n.rotation}},afterLabel:oe,afterBody:oe,beforeFooter:oe,footer:oe,afterFooter:oe};function xt(e,t,n,i){const s=e[t].call(n,i);return typeof s>"u"?Vl[t].call(n,i):s}class Vs extends Ln{constructor(t){super(),this.opacity=0,this._active=[],this._eventPosition=void 0,this._size=void 0,this._cachedAnimations=void 0,this._tooltipItems=[],this.$animations=void 0,this.$context=void 0,this.chart=t.chart,this.options=t.options,this.dataPoints=void 0,this.title=void 0,this.beforeBody=void 0,this.body=void 0,this.afterBody=void 0,this.footer=void 0,this.xAlign=void 0,this.yAlign=void 0,this.x=void 0,this.y=void 0,this.height=void 0,this.width=void 0,this.caretX=void 0,this.caretY=void 0,this.labelColors=void 0,this.labelPointStyles=void 0,this.labelTextColors=void 0}initialize(t){this.options=t,this._cachedAnimations=void 0,this.$context=void 0}_resolveAnimations(){const t=this._cachedAnimations;if(t)return t;const n=this.chart,i=this.options.setContext(this.getContext()),s=i.enabled&&n.options.animation&&i.animations,o=new Cl(this.chart,s);return s._cacheable&&(this._cachedAnimations=Object.freeze(o)),o}getContext(){return this.$context||(this.$context=rm(this.chart.getContext(),this,this._tooltipItems))}getTitle(t,n){const{callbacks:i}=n,s=xt(i,"beforeTitle",this,t),o=xt(i,"title",this,t),r=xt(i,"afterTitle",this,t);let a=[];return a=Ut(a,ae(s)),a=Ut(a,ae(o)),a=Ut(a,ae(r)),a}getBeforeBody(t,n){return Ur(xt(n.callbacks,"beforeBody",this,t))}getBody(t,n){const{callbacks:i}=n,s=[];return q(t,o=>{const r={before:[],lines:[],after:[]},a=Xr(i,o);Ut(r.before,ae(xt(a,"beforeLabel",this,o))),Ut(r.lines,xt(a,"label",this,o)),Ut(r.after,ae(xt(a,"afterLabel",this,o))),s.push(r)}),s}getAfterBody(t,n){return Ur(xt(n.callbacks,"afterBody",this,t))}getFooter(t,n){const{callbacks:i}=n,s=xt(i,"beforeFooter",this,t),o=xt(i,"footer",this,t),r=xt(i,"afterFooter",this,t);let a=[];return a=Ut(a,ae(s)),a=Ut(a,ae(o)),a=Ut(a,ae(r)),a}_createItems(t){const n=this._active,i=this.chart.data,s=[],o=[],r=[];let a=[],l,c;for(l=0,c=n.length;l<c;++l)a.push(tm(this.chart,n[l]));return t.filter&&(a=a.filter((u,f,d)=>t.filter(u,f,d,i))),t.itemSort&&(a=a.sort((u,f)=>t.itemSort(u,f,i))),q(a,u=>{const f=Xr(t.callbacks,u);s.push(xt(f,"labelColor",this,u)),o.push(xt(f,"labelPointStyle",this,u)),r.push(xt(f,"labelTextColor",this,u))}),this.labelColors=s,this.labelPointStyles=o,this.labelTextColors=r,this.dataPoints=a,a}update(t,n){const i=this.options.setContext(this.getContext()),s=this._active;let o,r=[];if(!s.length)this.opacity!==0&&(o={opacity:0});else{const a=bn[i.position].call(this,s,this._eventPosition);r=this._createItems(i),this.title=this.getTitle(r,i),this.beforeBody=this.getBeforeBody(r,i),this.body=this.getBody(r,i),this.afterBody=this.getAfterBody(r,i),this.footer=this.getFooter(r,i);const l=this._size=Gr(this,i),c=Object.assign({},a,l),u=qr(this.chart,i,c),f=Yr(i,c,u,this.chart);this.xAlign=u.xAlign,this.yAlign=u.yAlign,o={opacity:1,x:f.x,y:f.y,width:l.width,height:l.height,caretX:a.x,caretY:a.y}}this._tooltipItems=r,this.$context=void 0,o&&this._resolveAnimations().update(this,o),t&&i.external&&i.external.call(this,{chart:this.chart,tooltip:this,replay:n})}drawCaret(t,n,i,s){const o=this.getCaretPosition(t,i,s);n.lineTo(o.x1,o.y1),n.lineTo(o.x2,o.y2),n.lineTo(o.x3,o.y3)}getCaretPosition(t,n,i){const{xAlign:s,yAlign:o}=this,{caretSize:r,cornerRadius:a}=i,{topLeft:l,topRight:c,bottomLeft:u,bottomRight:f}=Sn(a),{x:d,y:h}=t,{width:g,height:p}=n;let m,_,b,x,v,y;return o==="center"?(v=h+p/2,s==="left"?(m=d,_=m-r,x=v+r,y=v-r):(m=d+g,_=m+r,x=v-r,y=v+r),b=m):(s==="left"?_=d+Math.max(l,u)+r:s==="right"?_=d+g-Math.max(c,f)-r:_=this.caretX,o==="top"?(x=h,v=x-r,m=_-r,b=_+r):(x=h+p,v=x+r,m=_+r,b=_-r),y=x),{x1:m,x2:_,x3:b,y1:x,y2:v,y3:y}}drawTitle(t,n,i){const s=this.title,o=s.length;let r,a,l;if(o){const c=as(i.rtl,this.x,this.width);for(t.x=Zn(this,i.titleAlign,i),n.textAlign=c.textAlign(i.titleAlign),n.textBaseline="middle",r=bt(i.titleFont),a=i.titleSpacing,n.fillStyle=i.titleColor,n.font=r.string,l=0;l<o;++l)n.fillText(s[l],c.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+a,l+1===o&&(t.y+=i.titleMarginBottom-a)}}_drawColorBox(t,n,i,s,o){const r=this.labelColors[i],a=this.labelPointStyles[i],{boxHeight:l,boxWidth:c}=o,u=bt(o.bodyFont),f=Zn(this,"left",o),d=s.x(f),h=l<u.lineHeight?(u.lineHeight-l)/2:0,g=n.y+h;if(o.usePointStyle){const p={radius:Math.min(c,l)/2,pointStyle:a.pointStyle,rotation:a.rotation,borderWidth:1},m=s.leftForLtr(d,c)+c/2,_=g+l/2;t.strokeStyle=o.multiKeyBackground,t.fillStyle=o.multiKeyBackground,hr(t,p,m,_),t.strokeStyle=r.borderColor,t.fillStyle=r.backgroundColor,hr(t,p,m,_)}else{t.lineWidth=W(r.borderWidth)?Math.max(...Object.values(r.borderWidth)):r.borderWidth||1,t.strokeStyle=r.borderColor,t.setLineDash(r.borderDash||[]),t.lineDashOffset=r.borderDashOffset||0;const p=s.leftForLtr(d,c),m=s.leftForLtr(s.xPlus(d,1),c-2),_=Sn(r.borderRadius);Object.values(_).some(b=>b!==0)?(t.beginPath(),t.fillStyle=o.multiKeyBackground,Fs(t,{x:p,y:g,w:c,h:l,radius:_}),t.fill(),t.stroke(),t.fillStyle=r.backgroundColor,t.beginPath(),Fs(t,{x:m,y:g+1,w:c-2,h:l-2,radius:_}),t.fill()):(t.fillStyle=o.multiKeyBackground,t.fillRect(p,g,c,l),t.strokeRect(p,g,c,l),t.fillStyle=r.backgroundColor,t.fillRect(m,g+1,c-2,l-2))}t.fillStyle=this.labelTextColors[i]}drawBody(t,n,i){const{body:s}=this,{bodySpacing:o,bodyAlign:r,displayColors:a,boxHeight:l,boxWidth:c,boxPadding:u}=i,f=bt(i.bodyFont);let d=f.lineHeight,h=0;const g=as(i.rtl,this.x,this.width),p=function($){n.fillText($,g.x(t.x+h),t.y+d/2),t.y+=d+o},m=g.textAlign(r);let _,b,x,v,y,k,M;for(n.textAlign=r,n.textBaseline="middle",n.font=f.string,t.x=Zn(this,m,i),n.fillStyle=i.bodyColor,q(this.beforeBody,p),h=a&&m!=="right"?r==="center"?c/2+u:c+2+u:0,v=0,k=s.length;v<k;++v){for(_=s[v],b=this.labelTextColors[v],n.fillStyle=b,q(_.before,p),x=_.lines,a&&x.length&&(this._drawColorBox(n,t,v,g,i),d=Math.max(f.lineHeight,l)),y=0,M=x.length;y<M;++y)p(x[y]),d=f.lineHeight;q(_.after,p)}h=0,d=f.lineHeight,q(this.afterBody,p),t.y-=o}drawFooter(t,n,i){const s=this.footer,o=s.length;let r,a;if(o){const l=as(i.rtl,this.x,this.width);for(t.x=Zn(this,i.footerAlign,i),t.y+=i.footerMarginTop,n.textAlign=l.textAlign(i.footerAlign),n.textBaseline="middle",r=bt(i.footerFont),n.fillStyle=i.footerColor,n.font=r.string,a=0;a<o;++a)n.fillText(s[a],l.x(t.x),t.y+r.lineHeight/2),t.y+=r.lineHeight+i.footerSpacing}}drawBackground(t,n,i,s){const{xAlign:o,yAlign:r}=this,{x:a,y:l}=t,{width:c,height:u}=i,{topLeft:f,topRight:d,bottomLeft:h,bottomRight:g}=Sn(s.cornerRadius);n.fillStyle=s.backgroundColor,n.strokeStyle=s.borderColor,n.lineWidth=s.borderWidth,n.beginPath(),n.moveTo(a+f,l),r==="top"&&this.drawCaret(t,n,i,s),n.lineTo(a+c-d,l),n.quadraticCurveTo(a+c,l,a+c,l+d),r==="center"&&o==="right"&&this.drawCaret(t,n,i,s),n.lineTo(a+c,l+u-g),n.quadraticCurveTo(a+c,l+u,a+c-g,l+u),r==="bottom"&&this.drawCaret(t,n,i,s),n.lineTo(a+h,l+u),n.quadraticCurveTo(a,l+u,a,l+u-h),r==="center"&&o==="left"&&this.drawCaret(t,n,i,s),n.lineTo(a,l+f),n.quadraticCurveTo(a,l,a+f,l),n.closePath(),n.fill(),s.borderWidth>0&&n.stroke()}_updateAnimationTarget(t){const n=this.chart,i=this.$animations,s=i&&i.x,o=i&&i.y;if(s||o){const r=bn[t.position].call(this,this._active,this._eventPosition);if(!r)return;const a=this._size=Gr(this,t),l=Object.assign({},r,this._size),c=qr(n,t,l),u=Yr(t,l,c,n);(s._to!==u.x||o._to!==u.y)&&(this.xAlign=c.xAlign,this.yAlign=c.yAlign,this.width=a.width,this.height=a.height,this.caretX=r.x,this.caretY=r.y,this._resolveAnimations().update(this,u))}}_willRender(){return!!this.opacity}draw(t){const n=this.options.setContext(this.getContext());let i=this.opacity;if(!i)return;this._updateAnimationTarget(n);const s={width:this.width,height:this.height},o={x:this.x,y:this.y};i=Math.abs(i)<.001?0:i;const r=At(n.padding),a=this.title.length||this.beforeBody.length||this.body.length||this.afterBody.length||this.footer.length;n.enabled&&a&&(t.save(),t.globalAlpha=i,this.drawBackground(o,t,s,n),dg(t,n.textDirection),o.y+=r.top,this.drawTitle(o,t,n),this.drawBody(o,t,n),this.drawFooter(o,t,n),hg(t,n.textDirection),t.restore())}getActiveElements(){return this._active||[]}setActiveElements(t,n){const i=this._active,s=t.map(({datasetIndex:a,index:l})=>{const c=this.chart.getDatasetMeta(a);if(!c)throw new Error("Cannot find a dataset at index "+a);return{datasetIndex:a,element:c.data[l],index:l}}),o=!vi(i,s),r=this._positionChanged(s,n);(o||r)&&(this._active=s,this._eventPosition=n,this._ignoreReplayEvents=!0,this.update(!0))}handleEvent(t,n,i=!0){if(n&&this._ignoreReplayEvents)return!1;this._ignoreReplayEvents=!1;const s=this.options,o=this._active||[],r=this._getActiveElements(t,o,n,i),a=this._positionChanged(r,t),l=n||!vi(r,o)||a;return l&&(this._active=r,(s.enabled||s.external)&&(this._eventPosition={x:t.x,y:t.y},this.update(!0,n))),l}_getActiveElements(t,n,i,s){const o=this.options;if(t.type==="mouseout")return[];if(!s)return n.filter(a=>this.chart.data.datasets[a.datasetIndex]&&this.chart.getDatasetMeta(a.datasetIndex).controller.getParsed(a.index)!==void 0);const r=this.chart.getElementsAtEventForMode(t,o.mode,o,i);return o.reverse&&r.reverse(),r}_positionChanged(t,n){const{caretX:i,caretY:s,options:o}=this,r=bn[o.position].call(this,t,n);return r!==!1&&(i!==r.x||s!==r.y)}}A(Vs,"positioners",bn);var am={id:"tooltip",_element:Vs,positioners:bn,afterInit(e,t,n){n&&(e.tooltip=new Vs({chart:e,options:n}))},beforeUpdate(e,t,n){e.tooltip&&e.tooltip.initialize(n)},reset(e,t,n){e.tooltip&&e.tooltip.initialize(n)},afterDraw(e){const t=e.tooltip;if(t&&t._willRender()){const n={tooltip:t};if(e.notifyPlugins("beforeTooltipDraw",{...n,cancelable:!0})===!1)return;t.draw(e.ctx),e.notifyPlugins("afterTooltipDraw",n)}},afterEvent(e,t){if(e.tooltip){const n=t.replay;e.tooltip.handleEvent(t.event,n,t.inChartArea)&&(t.changed=!0)}},defaults:{enabled:!0,external:null,position:"average",backgroundColor:"rgba(0,0,0,0.8)",titleColor:"#fff",titleFont:{weight:"bold"},titleSpacing:2,titleMarginBottom:6,titleAlign:"left",bodyColor:"#fff",bodySpacing:2,bodyFont:{},bodyAlign:"left",footerColor:"#fff",footerSpacing:2,footerMarginTop:6,footerFont:{weight:"bold"},footerAlign:"left",padding:6,caretPadding:2,caretSize:5,cornerRadius:6,boxHeight:(e,t)=>t.bodyFont.size,boxWidth:(e,t)=>t.bodyFont.size,multiKeyBackground:"#fff",displayColors:!0,boxPadding:0,borderColor:"rgba(0,0,0,0)",borderWidth:0,animation:{duration:400,easing:"easeOutQuart"},animations:{numbers:{type:"number",properties:["x","y","width","height","caretX","caretY"]},opacity:{easing:"linear",duration:200}},callbacks:Vl},defaultRoutes:{bodyFont:"font",footerFont:"font",titleFont:"font"},descriptors:{_scriptable:e=>e!=="filter"&&e!=="itemSort"&&e!=="external",_indexable:!1,callbacks:{_scriptable:!1,_indexable:!1},animation:{_fallback:!1},animations:{_fallback:"animation"}},additionalOptionScopes:["interaction"]};const lm=(e,t,n,i)=>(typeof t=="string"?(n=e.push(t)-1,i.unshift({index:n,label:t})):isNaN(t)&&(n=null),n);function cm(e,t,n,i){const s=e.indexOf(t);if(s===-1)return lm(e,t,n,i);const o=e.lastIndexOf(t);return s!==o?n:s}const um=(e,t)=>e===null?null:Mt(Math.round(e),0,t);function Kr(e){const t=this.getLabels();return e>=0&&e<t.length?t[e]:e}class zs extends He{constructor(t){super(t),this._startValue=void 0,this._valueRange=0,this._addedLabels=[]}init(t){const n=this._addedLabels;if(n.length){const i=this.getLabels();for(const{index:s,label:o}of n)i[s]===o&&i.splice(s,1);this._addedLabels=[]}super.init(t)}parse(t,n){if(Q(t))return null;const i=this.getLabels();return n=isFinite(n)&&i[n]===t?n:cm(i,t,tt(n,t),this._addedLabels),um(n,i.length-1)}determineDataLimits(){const{minDefined:t,maxDefined:n}=this.getUserBounds();let{min:i,max:s}=this.getMinMax(!0);this.options.bounds==="ticks"&&(t||(i=0),n||(s=this.getLabels().length-1)),this.min=i,this.max=s}buildTicks(){const t=this.min,n=this.max,i=this.options.offset,s=[];let o=this.getLabels();o=t===0&&n===o.length-1?o:o.slice(t,n+1),this._valueRange=Math.max(o.length-(i?0:1),1),this._startValue=this.min-(i?.5:0);for(let r=t;r<=n;r++)s.push({value:r});return s}getLabelForValue(t){return Kr.call(this,t)}configure(){super.configure(),this.isHorizontal()||(this._reversePixels=!this._reversePixels)}getPixelForValue(t){return typeof t!="number"&&(t=this.parse(t)),t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getPixelForTick(t){const n=this.ticks;return t<0||t>n.length-1?null:this.getPixelForValue(n[t].value)}getValueForPixel(t){return Math.round(this._startValue+this.getDecimalForPixel(t)*this._valueRange)}getBasePixel(){return this.bottom}}A(zs,"id","category"),A(zs,"defaults",{ticks:{callback:Kr}});function fm(e,t){const n=[],{bounds:s,step:o,min:r,max:a,precision:l,count:c,maxTicks:u,maxDigits:f,includeBounds:d}=e,h=o||1,g=u-1,{min:p,max:m}=t,_=!Q(r),b=!Q(a),x=!Q(c),v=(m-p)/(f+1);let y=or((m-p)/g/h)*h,k,M,$,P;if(y<1e-14&&!_&&!b)return[{value:p},{value:m}];P=Math.ceil(m/y)-Math.floor(p/y),P>g&&(y=or(P*y/g/h)*h),Q(l)||(k=Math.pow(10,l),y=Math.ceil(y*k)/k),s==="ticks"?(M=Math.floor(p/y)*y,$=Math.ceil(m/y)*y):(M=p,$=m),_&&b&&o&&bh((a-r)/o,y/1e3)?(P=Math.round(Math.min((a-r)/y,u)),y=(a-r)/P,M=r,$=a):x?(M=_?r:M,$=b?a:$,P=c-1,y=($-M)/P):(P=($-M)/y,si(P,Math.round(P),y/1e3)?P=Math.round(P):P=Math.ceil(P));const O=Math.max(rr(y),rr(M));k=Math.pow(10,Q(l)?O:l),M=Math.round(M*k)/k,$=Math.round($*k)/k;let R=0;for(_&&(d&&M!==r?(n.push({value:r}),M<r&&R++,si(Math.round((M+R*y)*k)/k,r,Qr(r,v,e))&&R++):M<r&&R++);R<P;++R){const z=Math.round((M+R*y)*k)/k;if(b&&z>a)break;n.push({value:z})}return b&&d&&$!==a?n.length&&si(n[n.length-1].value,a,Qr(a,v,e))?n[n.length-1].value=a:n.push({value:a}):(!b||$===a)&&n.push({value:$}),n}function Qr(e,t,{horizontal:n,minRotation:i}){const s=jt(i),o=(n?Math.sin(s):Math.cos(s))||.001,r=.75*t*(""+e).length;return Math.min(t/o,r)}class Di extends He{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._endValue=void 0,this._valueRange=0}parse(t,n){return Q(t)||(typeof t=="number"||t instanceof Number)&&!isFinite(+t)?null:+t}handleTickRangeOptions(){const{beginAtZero:t}=this.options,{minDefined:n,maxDefined:i}=this.getUserBounds();let{min:s,max:o}=this;const r=l=>s=n?s:l,a=l=>o=i?o:l;if(t){const l=Mi(s),c=Mi(o);l<0&&c<0?a(0):l>0&&c>0&&r(0)}if(s===o){let l=o===0?1:Math.abs(o*.05);a(o+l),t||r(s-l)}this.min=s,this.max=o}getTickLimit(){const t=this.options.ticks;let{maxTicksLimit:n,stepSize:i}=t,s;return i?(s=Math.ceil(this.max/i)-Math.floor(this.min/i)+1,s>1e3&&(console.warn(`scales.${this.id}.ticks.stepSize: ${i} would result generating up to ${s} ticks. Limiting to 1000.`),s=1e3)):(s=this.computeTickLimit(),n=n||11),n&&(s=Math.min(n,s)),s}computeTickLimit(){return Number.POSITIVE_INFINITY}buildTicks(){const t=this.options,n=t.ticks;let i=this.getTickLimit();i=Math.max(2,i);const s={maxTicks:i,bounds:t.bounds,min:t.min,max:t.max,precision:n.precision,step:n.stepSize,count:n.count,maxDigits:this._maxDigits(),horizontal:this.isHorizontal(),minRotation:n.minRotation||0,includeBounds:n.includeBounds!==!1},o=this._range||this,r=fm(s,o);return t.bounds==="ticks"&&ul(r,this,"value"),t.reverse?(r.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),r}configure(){const t=this.ticks;let n=this.min,i=this.max;if(super.configure(),this.options.offset&&t.length){const s=(i-n)/Math.max(t.length-1,1)/2;n-=s,i+=s}this._startValue=n,this._endValue=i,this._valueRange=i-n}getLabelForValue(t){return Fn(t,this.chart.options.locale,this.options.ticks.format)}}class Zr extends Di{determineDataLimits(){const{min:t,max:n}=this.getMinMax(!0);this.min=ht(t)?t:0,this.max=ht(n)?n:1,this.handleTickRangeOptions()}computeTickLimit(){const t=this.isHorizontal(),n=t?this.width:this.height,i=jt(this.options.ticks.minRotation),s=(t?Math.sin(i):Math.cos(i))||.001,o=this._resolveTickFontOptions(0);return Math.ceil(n/Math.min(40,o.lineHeight/s))}getPixelForValue(t){return t===null?NaN:this.getPixelForDecimal((t-this._startValue)/this._valueRange)}getValueForPixel(t){return this._startValue+this.getDecimalForPixel(t)*this._valueRange}}A(Zr,"id","linear"),A(Zr,"defaults",{ticks:{callback:Ni.formatters.numeric}});const An=e=>Math.floor(_e(e)),$e=(e,t)=>Math.pow(10,An(e)+t);function Jr(e){return e/Math.pow(10,An(e))===1}function ta(e,t,n){const i=Math.pow(10,n),s=Math.floor(e/i);return Math.ceil(t/i)-s}function dm(e,t){const n=t-e;let i=An(n);for(;ta(e,t,i)>10;)i++;for(;ta(e,t,i)<10;)i--;return Math.min(i,An(e))}function hm(e,{min:t,max:n}){t=Ct(e.min,t);const i=[],s=An(t);let o=dm(t,n),r=o<0?Math.pow(10,Math.abs(o)):1;const a=Math.pow(10,o),l=s>o?Math.pow(10,s):0,c=Math.round((t-l)*r)/r,u=Math.floor((t-l)/a/10)*a*10;let f=Math.floor((c-u)/Math.pow(10,o)),d=Ct(e.min,Math.round((l+u+f*Math.pow(10,o))*r)/r);for(;d<n;)i.push({value:d,major:Jr(d),significand:f}),f>=10?f=f<15?15:20:f++,f>=20&&(o++,f=2,r=o>=0?1:r),d=Math.round((l+u+f*Math.pow(10,o))*r)/r;const h=Ct(e.max,d);return i.push({value:h,major:Jr(h),significand:f}),i}class ea extends He{constructor(t){super(t),this.start=void 0,this.end=void 0,this._startValue=void 0,this._valueRange=0}parse(t,n){const i=Di.prototype.parse.apply(this,[t,n]);if(i===0){this._zero=!0;return}return ht(i)&&i>0?i:null}determineDataLimits(){const{min:t,max:n}=this.getMinMax(!0);this.min=ht(t)?Math.max(0,t):null,this.max=ht(n)?Math.max(0,n):null,this.options.beginAtZero&&(this._zero=!0),this._zero&&this.min!==this._suggestedMin&&!ht(this._userMin)&&(this.min=t===$e(this.min,0)?$e(this.min,-1):$e(this.min,0)),this.handleTickRangeOptions()}handleTickRangeOptions(){const{minDefined:t,maxDefined:n}=this.getUserBounds();let i=this.min,s=this.max;const o=a=>i=t?i:a,r=a=>s=n?s:a;i===s&&(i<=0?(o(1),r(10)):(o($e(i,-1)),r($e(s,1)))),i<=0&&o($e(s,-1)),s<=0&&r($e(i,1)),this.min=i,this.max=s}buildTicks(){const t=this.options,n={min:this._userMin,max:this._userMax},i=hm(n,this);return t.bounds==="ticks"&&ul(i,this,"value"),t.reverse?(i.reverse(),this.start=this.max,this.end=this.min):(this.start=this.min,this.end=this.max),i}getLabelForValue(t){return t===void 0?"0":Fn(t,this.chart.options.locale,this.options.ticks.format)}configure(){const t=this.min;super.configure(),this._startValue=_e(t),this._valueRange=_e(this.max)-_e(t)}getPixelForValue(t){return(t===void 0||t===0)&&(t=this.min),t===null||isNaN(t)?NaN:this.getPixelForDecimal(t===this.min?0:(_e(t)-this._startValue)/this._valueRange)}getValueForPixel(t){const n=this.getDecimalForPixel(t);return Math.pow(10,this._startValue+n*this._valueRange)}}A(ea,"id","logarithmic"),A(ea,"defaults",{ticks:{callback:Ni.formatters.logarithmic,major:{enabled:!0}}});function Hs(e){const t=e.ticks;if(t.display&&e.display){const n=At(t.backdropPadding);return tt(t.font&&t.font.size,ot.font.size)+n.height}return 0}function gm(e,t,n){return n=st(n)?n:[n],{w:Th(e,t.string,n),h:n.length*t.lineHeight}}function na(e,t,n,i,s){return e===i||e===s?{start:t-n/2,end:t+n/2}:e<i||e>s?{start:t-n,end:t}:{start:t,end:t+n}}function pm(e){const t={l:e.left+e._padding.left,r:e.right-e._padding.right,t:e.top+e._padding.top,b:e.bottom-e._padding.bottom},n=Object.assign({},t),i=[],s=[],o=e._pointLabels.length,r=e.options.pointLabels,a=r.centerPointLabels?rt/o:0;for(let l=0;l<o;l++){const c=r.setContext(e.getPointLabelContext(l));s[l]=c.padding;const u=e.getPointPosition(l,e.drawingArea+s[l],a),f=bt(c.font),d=gm(e.ctx,f,e._pointLabels[l]);i[l]=d;const h=Kt(e.getIndexAngle(l)+a),g=Math.round(ro(h)),p=na(g,u.x,d.w,0,180),m=na(g,u.y,d.h,90,270);mm(n,t,h,p,m)}e.setCenterPoint(t.l-n.l,n.r-t.r,t.t-n.t,n.b-t.b),e._pointLabelItems=ym(e,i,s)}function mm(e,t,n,i,s){const o=Math.abs(Math.sin(n)),r=Math.abs(Math.cos(n));let a=0,l=0;i.start<t.l?(a=(t.l-i.start)/o,e.l=Math.min(e.l,t.l-a)):i.end>t.r&&(a=(i.end-t.r)/o,e.r=Math.max(e.r,t.r+a)),s.start<t.t?(l=(t.t-s.start)/r,e.t=Math.min(e.t,t.t-l)):s.end>t.b&&(l=(s.end-t.b)/r,e.b=Math.max(e.b,t.b+l))}function _m(e,t,n){const i=e.drawingArea,{extra:s,additionalAngle:o,padding:r,size:a}=n,l=e.getPointPosition(t,i+s+r,o),c=Math.round(ro(Kt(l.angle+ut))),u=wm(l.y,a.h,c),f=xm(c),d=vm(l.x,a.w,f);return{visible:!0,x:l.x,y:u,textAlign:f,left:d,top:u,right:d+a.w,bottom:u+a.h}}function bm(e,t){if(!t)return!0;const{left:n,top:i,right:s,bottom:o}=e;return!(qe({x:n,y:i},t)||qe({x:n,y:o},t)||qe({x:s,y:i},t)||qe({x:s,y:o},t))}function ym(e,t,n){const i=[],s=e._pointLabels.length,o=e.options,{centerPointLabels:r,display:a}=o.pointLabels,l={extra:Hs(o)/2,additionalAngle:r?rt/s:0};let c;for(let u=0;u<s;u++){l.padding=n[u],l.size=t[u];const f=_m(e,u,l);i.push(f),a==="auto"&&(f.visible=bm(f,c),f.visible&&(c=f))}return i}function xm(e){return e===0||e===180?"center":e<180?"left":"right"}function vm(e,t,n){return n==="right"?e-=t:n==="center"&&(e-=t/2),e}function wm(e,t,n){return n===90||n===270?e-=t/2:(n>270||n<90)&&(e-=t),e}function Sm(e,t,n){const{left:i,top:s,right:o,bottom:r}=n,{backdropColor:a}=t;if(!Q(a)){const l=Sn(t.borderRadius),c=At(t.backdropPadding);e.fillStyle=a;const u=i-c.left,f=s-c.top,d=o-i+c.width,h=r-s+c.height;Object.values(l).some(g=>g!==0)?(e.beginPath(),Fs(e,{x:u,y:f,w:d,h,radius:l}),e.fill()):e.fillRect(u,f,d,h)}}function Cm(e,t){const{ctx:n,options:{pointLabels:i}}=e;for(let s=t-1;s>=0;s--){const o=e._pointLabelItems[s];if(!o.visible)continue;const r=i.setContext(e.getPointLabelContext(s));Sm(n,r,o);const a=bt(r.font),{x:l,y:c,textAlign:u}=o;Rn(n,e._pointLabels[s],l,c+a.lineHeight/2,a,{color:r.color,textAlign:u,textBaseline:"middle"})}}function zl(e,t,n,i){const{ctx:s}=e;if(n)s.arc(e.xCenter,e.yCenter,t,0,lt);else{let o=e.getPointPosition(0,t);s.moveTo(o.x,o.y);for(let r=1;r<i;r++)o=e.getPointPosition(r,t),s.lineTo(o.x,o.y)}}function Mm(e,t,n,i,s){const o=e.ctx,r=t.circular,{color:a,lineWidth:l}=t;!r&&!i||!a||!l||n<0||(o.save(),o.strokeStyle=a,o.lineWidth=l,o.setLineDash(s.dash||[]),o.lineDashOffset=s.dashOffset,o.beginPath(),zl(e,n,r,i),o.closePath(),o.stroke(),o.restore())}function km(e,t,n){return ze(e,{label:n,index:t,type:"pointLabel"})}class Jn extends Di{constructor(t){super(t),this.xCenter=void 0,this.yCenter=void 0,this.drawingArea=void 0,this._pointLabels=[],this._pointLabelItems=[]}setDimensions(){const t=this._padding=At(Hs(this.options)/2),n=this.width=this.maxWidth-t.width,i=this.height=this.maxHeight-t.height;this.xCenter=Math.floor(this.left+n/2+t.left),this.yCenter=Math.floor(this.top+i/2+t.top),this.drawingArea=Math.floor(Math.min(n,i)/2)}determineDataLimits(){const{min:t,max:n}=this.getMinMax(!1);this.min=ht(t)&&!isNaN(t)?t:0,this.max=ht(n)&&!isNaN(n)?n:0,this.handleTickRangeOptions()}computeTickLimit(){return Math.ceil(this.drawingArea/Hs(this.options))}generateTickLabels(t){Di.prototype.generateTickLabels.call(this,t),this._pointLabels=this.getLabels().map((n,i)=>{const s=at(this.options.pointLabels.callback,[n,i],this);return s||s===0?s:""}).filter((n,i)=>this.chart.getDataVisibility(i))}fit(){const t=this.options;t.display&&t.pointLabels.display?pm(this):this.setCenterPoint(0,0,0,0)}setCenterPoint(t,n,i,s){this.xCenter+=Math.floor((t-n)/2),this.yCenter+=Math.floor((i-s)/2),this.drawingArea-=Math.min(this.drawingArea/2,Math.max(t,n,i,s))}getIndexAngle(t){const n=lt/(this._pointLabels.length||1),i=this.options.startAngle||0;return Kt(t*n+jt(i))}getDistanceFromCenterForValue(t){if(Q(t))return NaN;const n=this.drawingArea/(this.max-this.min);return this.options.reverse?(this.max-t)*n:(t-this.min)*n}getValueForDistanceFromCenter(t){if(Q(t))return NaN;const n=t/(this.drawingArea/(this.max-this.min));return this.options.reverse?this.max-n:this.min+n}getPointLabelContext(t){const n=this._pointLabels||[];if(t>=0&&t<n.length){const i=n[t];return km(this.getContext(),t,i)}}getPointPosition(t,n,i=0){const s=this.getIndexAngle(t)-ut+i;return{x:Math.cos(s)*n+this.xCenter,y:Math.sin(s)*n+this.yCenter,angle:s}}getPointPositionForValue(t,n){return this.getPointPosition(t,this.getDistanceFromCenterForValue(n))}getBasePosition(t){return this.getPointPositionForValue(t||0,this.getBaseValue())}getPointLabelPosition(t){const{left:n,top:i,right:s,bottom:o}=this._pointLabelItems[t];return{left:n,top:i,right:s,bottom:o}}drawBackground(){const{backgroundColor:t,grid:{circular:n}}=this.options;if(t){const i=this.ctx;i.save(),i.beginPath(),zl(this,this.getDistanceFromCenterForValue(this._endValue),n,this._pointLabels.length),i.closePath(),i.fillStyle=t,i.fill(),i.restore()}}drawGrid(){const t=this.ctx,n=this.options,{angleLines:i,grid:s,border:o}=n,r=this._pointLabels.length;let a,l,c;if(n.pointLabels.display&&Cm(this,r),s.display&&this.ticks.forEach((u,f)=>{if(f!==0||f===0&&this.min<0){l=this.getDistanceFromCenterForValue(u.value);const d=this.getContext(f),h=s.setContext(d),g=o.setContext(d);Mm(this,h,l,r,g)}}),i.display){for(t.save(),a=r-1;a>=0;a--){const u=i.setContext(this.getPointLabelContext(a)),{color:f,lineWidth:d}=u;!d||!f||(t.lineWidth=d,t.strokeStyle=f,t.setLineDash(u.borderDash),t.lineDashOffset=u.borderDashOffset,l=this.getDistanceFromCenterForValue(n.reverse?this.min:this.max),c=this.getPointPosition(a,l),t.beginPath(),t.moveTo(this.xCenter,this.yCenter),t.lineTo(c.x,c.y),t.stroke())}t.restore()}}drawBorder(){}drawLabels(){const t=this.ctx,n=this.options,i=n.ticks;if(!i.display)return;const s=this.getIndexAngle(0);let o,r;t.save(),t.translate(this.xCenter,this.yCenter),t.rotate(s),t.textAlign="center",t.textBaseline="middle",this.ticks.forEach((a,l)=>{if(l===0&&this.min>=0&&!n.reverse)return;const c=i.setContext(this.getContext(l)),u=bt(c.font);if(o=this.getDistanceFromCenterForValue(this.ticks[l].value),c.showLabelBackdrop){t.font=u.string,r=t.measureText(a.label).width,t.fillStyle=c.backdropColor;const f=At(c.backdropPadding);t.fillRect(-r/2-f.left,-o-u.size/2-f.top,r+f.width,u.size+f.height)}Rn(t,a.label,0,-o,u,{color:c.color,strokeColor:c.textStrokeColor,strokeWidth:c.textStrokeWidth})}),t.restore()}drawTitle(){}}A(Jn,"id","radialLinear"),A(Jn,"defaults",{display:!0,animate:!0,position:"chartArea",angleLines:{display:!0,lineWidth:1,borderDash:[],borderDashOffset:0},grid:{circular:!1},startAngle:0,ticks:{showLabelBackdrop:!0,callback:Ni.formatters.numeric},pointLabels:{backdropColor:void 0,backdropPadding:2,display:!0,font:{size:10},callback(t){return t},padding:5,centerPointLabels:!1}}),A(Jn,"defaultRoutes",{"angleLines.color":"borderColor","pointLabels.color":"color","ticks.color":"color"}),A(Jn,"descriptors",{angleLines:{_fallback:"grid"}});const Wi={millisecond:{common:!0,size:1,steps:1e3},second:{common:!0,size:1e3,steps:60},minute:{common:!0,size:6e4,steps:60},hour:{common:!0,size:36e5,steps:24},day:{common:!0,size:864e5,steps:30},week:{common:!1,size:6048e5,steps:4},month:{common:!0,size:2628e6,steps:12},quarter:{common:!1,size:7884e6,steps:4},year:{common:!0,size:3154e7}},vt=Object.keys(Wi);function ia(e,t){return e-t}function sa(e,t){if(Q(t))return null;const n=e._adapter,{parser:i,round:s,isoWeekday:o}=e._parseOpts;let r=t;return typeof i=="function"&&(r=i(r)),ht(r)||(r=typeof i=="string"?n.parse(r,i):n.parse(r)),r===null?null:(s&&(r=s==="week"&&(ki(o)||o===!0)?n.startOf(r,"isoWeek",o):n.startOf(r,s)),+r)}function oa(e,t,n,i){const s=vt.length;for(let o=vt.indexOf(e);o<s-1;++o){const r=Wi[vt[o]],a=r.steps?r.steps:Number.MAX_SAFE_INTEGER;if(r.common&&Math.ceil((n-t)/(a*r.size))<=i)return vt[o]}return vt[s-1]}function Rm(e,t,n,i,s){for(let o=vt.length-1;o>=vt.indexOf(n);o--){const r=vt[o];if(Wi[r].common&&e._adapter.diff(s,i,r)>=t-1)return r}return vt[n?vt.indexOf(n):0]}function $m(e){for(let t=vt.indexOf(e)+1,n=vt.length;t<n;++t)if(Wi[vt[t]].common)return vt[t]}function ra(e,t,n){if(!n)e[t]=!0;else if(n.length){const{lo:i,hi:s}=ao(n,t),o=n[i]>=t?n[i]:n[s];e[o]=!0}}function Am(e,t,n,i){const s=e._adapter,o=+s.startOf(t[0].value,i),r=t[t.length-1].value;let a,l;for(a=o;a<=r;a=+s.add(a,1,i))l=n[a],l>=0&&(t[l].major=!0);return t}function aa(e,t,n){const i=[],s={},o=t.length;let r,a;for(r=0;r<o;++r)a=t[r],s[a]=r,i.push({value:a,major:!1});return o===0||!n?i:Am(e,i,s,n)}class Fi extends He{constructor(t){super(t),this._cache={data:[],labels:[],all:[]},this._unit="day",this._majorUnit=void 0,this._offsets={},this._normalized=!1,this._parseOpts=void 0}init(t,n={}){const i=t.time||(t.time={}),s=this._adapter=new Og._date(t.adapters.date);s.init(n),yn(i.displayFormats,s.formats()),this._parseOpts={parser:i.parser,round:i.round,isoWeekday:i.isoWeekday},super.init(t),this._normalized=n.normalized}parse(t,n){return t===void 0?null:sa(this,t)}beforeLayout(){super.beforeLayout(),this._cache={data:[],labels:[],all:[]}}determineDataLimits(){const t=this.options,n=this._adapter,i=t.time.unit||"day";let{min:s,max:o,minDefined:r,maxDefined:a}=this.getUserBounds();function l(c){!r&&!isNaN(c.min)&&(s=Math.min(s,c.min)),!a&&!isNaN(c.max)&&(o=Math.max(o,c.max))}(!r||!a)&&(l(this._getLabelBounds()),(t.bounds!=="ticks"||t.ticks.source!=="labels")&&l(this.getMinMax(!1))),s=ht(s)&&!isNaN(s)?s:+n.startOf(Date.now(),i),o=ht(o)&&!isNaN(o)?o:+n.endOf(Date.now(),i)+1,this.min=Math.min(s,o-1),this.max=Math.max(s+1,o)}_getLabelBounds(){const t=this.getLabelTimestamps();let n=Number.POSITIVE_INFINITY,i=Number.NEGATIVE_INFINITY;return t.length&&(n=t[0],i=t[t.length-1]),{min:n,max:i}}buildTicks(){const t=this.options,n=t.time,i=t.ticks,s=i.source==="labels"?this.getLabelTimestamps():this._generate();t.bounds==="ticks"&&s.length&&(this.min=this._userMin||s[0],this.max=this._userMax||s[s.length-1]);const o=this.min,r=this.max,a=Sh(s,o,r);return this._unit=n.unit||(i.autoSkip?oa(n.minUnit,this.min,this.max,this._getLabelCapacity(o)):Rm(this,a.length,n.minUnit,this.min,this.max)),this._majorUnit=!i.major.enabled||this._unit==="year"?void 0:$m(this._unit),this.initOffsets(s),t.reverse&&a.reverse(),aa(this,a,this._majorUnit)}afterAutoSkip(){this.options.offsetAfterAutoskip&&this.initOffsets(this.ticks.map(t=>+t.value))}initOffsets(t=[]){let n=0,i=0,s,o;this.options.offset&&t.length&&(s=this.getDecimalForValue(t[0]),t.length===1?n=1-s:n=(this.getDecimalForValue(t[1])-s)/2,o=this.getDecimalForValue(t[t.length-1]),t.length===1?i=o:i=(o-this.getDecimalForValue(t[t.length-2]))/2);const r=t.length<3?.5:.25;n=Mt(n,0,r),i=Mt(i,0,r),this._offsets={start:n,end:i,factor:1/(n+1+i)}}_generate(){const t=this._adapter,n=this.min,i=this.max,s=this.options,o=s.time,r=o.unit||oa(o.minUnit,n,i,this._getLabelCapacity(n)),a=tt(s.ticks.stepSize,1),l=r==="week"?o.isoWeekday:!1,c=ki(l)||l===!0,u={};let f=n,d,h;if(c&&(f=+t.startOf(f,"isoWeek",l)),f=+t.startOf(f,c?"day":r),t.diff(i,n,r)>1e5*a)throw new Error(n+" and "+i+" are too far apart with stepSize of "+a+" "+r);const g=s.ticks.source==="data"&&this.getDataTimestamps();for(d=f,h=0;d<i;d=+t.add(d,a,r),h++)ra(u,d,g);return(d===i||s.bounds==="ticks"||h===1)&&ra(u,d,g),Object.keys(u).sort(ia).map(p=>+p)}getLabelForValue(t){const n=this._adapter,i=this.options.time;return i.tooltipFormat?n.format(t,i.tooltipFormat):n.format(t,i.displayFormats.datetime)}format(t,n){const s=this.options.time.displayFormats,o=this._unit,r=n||s[o];return this._adapter.format(t,r)}_tickFormatFunction(t,n,i,s){const o=this.options,r=o.ticks.callback;if(r)return at(r,[t,n,i],this);const a=o.time.displayFormats,l=this._unit,c=this._majorUnit,u=l&&a[l],f=c&&a[c],d=i[n],h=c&&f&&d&&d.major;return this._adapter.format(t,s||(h?f:u))}generateTickLabels(t){let n,i,s;for(n=0,i=t.length;n<i;++n)s=t[n],s.label=this._tickFormatFunction(s.value,n,t)}getDecimalForValue(t){return t===null?NaN:(t-this.min)/(this.max-this.min)}getPixelForValue(t){const n=this._offsets,i=this.getDecimalForValue(t);return this.getPixelForDecimal((n.start+i)*n.factor)}getValueForPixel(t){const n=this._offsets,i=this.getDecimalForPixel(t)/n.factor-n.end;return this.min+i*(this.max-this.min)}_getLabelSize(t){const n=this.options.ticks,i=this.ctx.measureText(t).width,s=jt(this.isHorizontal()?n.maxRotation:n.minRotation),o=Math.cos(s),r=Math.sin(s),a=this._resolveTickFontOptions(0).size;return{w:i*o+a*r,h:i*r+a*o}}_getLabelCapacity(t){const n=this.options.time,i=n.displayFormats,s=i[n.unit]||i.millisecond,o=this._tickFormatFunction(t,0,aa(this,[t],this._majorUnit),s),r=this._getLabelSize(o),a=Math.floor(this.isHorizontal()?this.width/r.w:this.height/r.h)-1;return a>0?a:1}getDataTimestamps(){let t=this._cache.data||[],n,i;if(t.length)return t;const s=this.getMatchingVisibleMetas();if(this._normalized&&s.length)return this._cache.data=s[0].controller.getAllParsedValues(this);for(n=0,i=s.length;n<i;++n)t=t.concat(s[n].controller.getAllParsedValues(this));return this._cache.data=this.normalize(t)}getLabelTimestamps(){const t=this._cache.labels||[];let n,i;if(t.length)return t;const s=this.getLabels();for(n=0,i=s.length;n<i;++n)t.push(sa(this,s[n]));return this._cache.labels=this._normalized?t:this.normalize(t)}normalize(t){return Mh(t.sort(ia))}}A(Fi,"id","time"),A(Fi,"defaults",{bounds:"data",adapters:{},time:{parser:!1,unit:!1,round:!1,isoWeekday:!1,minUnit:"millisecond",displayFormats:{}},ticks:{source:"auto",callback:!1,major:{enabled:!1}}});function ti(e,t,n){let i=0,s=e.length-1,o,r,a,l;n?(t>=e[i].pos&&t<=e[s].pos&&({lo:i,hi:s}=Os(e,"pos",t)),{pos:o,time:a}=e[i],{pos:r,time:l}=e[s]):(t>=e[i].time&&t<=e[s].time&&({lo:i,hi:s}=Os(e,"time",t)),{time:o,pos:a}=e[i],{time:r,pos:l}=e[s]);const c=r-o;return c?a+(l-a)*(t-o)/c:a}class la extends Fi{constructor(t){super(t),this._table=[],this._minPos=void 0,this._tableRange=void 0}initOffsets(){const t=this._getTimestampsForTable(),n=this._table=this.buildLookupTable(t);this._minPos=ti(n,this.min),this._tableRange=ti(n,this.max)-this._minPos,super.initOffsets(t)}buildLookupTable(t){const{min:n,max:i}=this,s=[],o=[];let r,a,l,c,u;for(r=0,a=t.length;r<a;++r)c=t[r],c>=n&&c<=i&&s.push(c);if(s.length<2)return[{time:n,pos:0},{time:i,pos:1}];for(r=0,a=s.length;r<a;++r)u=s[r+1],l=s[r-1],c=s[r],Math.round((u+l)/2)!==c&&o.push({time:c,pos:r/(a-1)});return o}_generate(){const t=this.min,n=this.max;let i=super.getDataTimestamps();return(!i.includes(t)||!i.length)&&i.splice(0,0,t),(!i.includes(n)||i.length===1)&&i.push(n),i.sort((s,o)=>s-o)}_getTimestampsForTable(){let t=this._cache.all||[];if(t.length)return t;const n=this.getDataTimestamps(),i=this.getLabelTimestamps();return n.length&&i.length?t=this.normalize(n.concat(i)):t=n.length?n:i,t=this._cache.all=t,t}getDecimalForValue(t){return(ti(this._table,t)-this._minPos)/this._tableRange}getValueForPixel(t){const n=this._offsets,i=this.getDecimalForPixel(t)/n.factor-n.end;return ti(this._table,i*this._tableRange+this._minPos,!0)}}A(la,"id","timeseries"),A(la,"defaults",Fi.defaults);const ca=/^on/,Hl=[];Object.keys(globalThis).forEach(e=>{ca.test(e)&&Hl.push(e.replace(ca,""))});function Nl(e){const t=Ee,n=[];function i(s){mc(t,s)}Li(()=>{const s=e();Hl.forEach(s instanceof Element?o=>n.push(It(s,o,i)):o=>n.push(s.$on(o,i)))}),rn(()=>{for(;n.length;)n.pop()()})}function Pm(e){let t,n=[e[1]],i={};for(let s=0;s<n.length;s+=1)i=H(i,n[s]);return{c(){t=D("canvas"),ic(t,i)},m(s,o){nt(s,t,o),e[8](t)},p:gt,i:gt,o:gt,d(s){s&&X(t),e[8](null)}}}function Om(e){let{data:t,type:n,options:i,plugins:s,children:o,$$scope:r,$$slots:a,...l}=e;return l}function Dm(e,t,n){let{type:i}=t,{data:s={datasets:[]}}=t,{options:o={}}=t,{plugins:r=[]}=t,{updateMode:a=void 0}=t,{chart:l=null}=t,c,u=Om(t);Li(()=>{n(2,l=new ji(c,{type:i,data:s,options:o,plugins:r}))}),hc(()=>{l&&(n(2,l.data=s,l),Object.assign(l.options,o),l.update(a))}),rn(()=>{l&&l.destroy(),n(2,l=null)}),Nl(()=>c);function f(d){Je[d?"unshift":"push"](()=>{c=d,n(0,c)})}return e.$$set=d=>{n(9,t=H(H({},t),et(d))),"type"in d&&n(3,i=d.type),"data"in d&&n(4,s=d.data),"options"in d&&n(5,o=d.options),"plugins"in d&&n(6,r=d.plugins),"updateMode"in d&&n(7,a=d.updateMode),"chart"in d&&n(2,l=d.chart)},t=et(t),[c,u,l,i,s,o,r,a,f]}let Fm=class extends K{constructor(t){super(),Z(this,t,Dm,Pm,U,{type:3,data:4,options:5,plugins:6,updateMode:7,chart:2})}};function Em(e){let t,n,i;const s=[{type:"pie"},e[1]];function o(a){e[4](a)}let r={};for(let a=0;a<s.length;a+=1)r=H(r,s[a]);return e[0]!==void 0&&(r.chart=e[0]),t=new Fm({props:r}),e[3](t),Je.push(()=>Ma(t,"chart",o)),{c(){I(t.$$.fragment)},m(a,l){E(t,a,l),i=!0},p(a,[l]){const c=l&2?zt(s,[s[0],te(a[1])]):{};!n&&l&1&&(n=!0,c.chart=a[0],wa(()=>n=!1)),t.$set(c)},i(a){i||(S(t.$$.fragment,a),i=!0)},o(a){C(t.$$.fragment,a),i=!1},d(a){e[3](null),L(t,a)}}}function Lm(e,t,n){ji.register(Es);let{chart:i=null}=t,s,o;Nl(()=>o);function r(l){Je[l?"unshift":"push"](()=>{o=l,n(2,o)})}function a(l){i=l,n(0,i)}return e.$$set=l=>{n(5,t=H(H({},t),et(l))),"chart"in l&&n(0,i=l.chart)},e.$$.update=()=>{n(1,s=t)},t=et(t),[i,s,o,r,a]}class Tm extends K{constructor(t){super(),Z(this,t,Lm,Em,U,{chart:0})}}function Im(e){let t,n;return t=new Tm({props:{class:"self-center",width:256,height:256,data:{labels:e[0].labels,datasets:[{data:e[0].data}]},options:{maintainAspectRatio:!1,responsive:!1,parsing:{key:"time"},animation:!1,plugins:{tooltip:{callbacks:{label:Vm}}}}}}),{c(){I(t.$$.fragment)},m(i,s){E(t,i,s),n=!0},p(i,[s]){const o={};s&1&&(o.data={labels:i[0].labels,datasets:[{data:i[0].data}]}),t.$set(o)},i(i){n||(S(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){L(t,i)}}}const Vm=e=>`Queries: ${e.raw.queries}, Time: ${e.raw.time.toFixed(4)} ms`;function zm(e,t,n){let i;return pt(e,ws,s=>n(0,i=s)),ji.register(Jp,am,_n,zs,Qp),[i]}class Hm extends K{constructor(t){super(),Z(this,t,zm,Im,U,{})}}function ua(e,t,n){const i=e.slice();return i[5]=t[n],i}function fa(e){let t,n=e[5]+"",i,s,o,r;function a(){return e[4](e[5])}return{c(){t=D("button"),i=J(n),s=Y(),F(t,"class","bg-dark-600 p-3 border-[1px] outline-none border-transparent focus-visible:border-cyan-600 text-left hover:bg-dark-400 rounded-md")},m(l,c){nt(l,t,c),w(t,i),w(t,s),o||(r=It(t,"click",a),o=!0)},p(l,c){e=l,c&2&&n!==(n=e[5]+"")&&Wt(i,n)},d(l){l&&X(t),o=!1,r()}}}function Nm(e){let t,n,i,s,o,r,a,l,c,u,f,d,h,g,p,m,_,b,x,v,y,k,M,$=e[2].queries+"",P,O,R,z,ct=e[2].timeQuerying.toFixed(4)+"",dt,V,G,N,it,B=e[2].slowQueries+"",ft,ne,wt,St;a=new zd({});function ie(j){e[3](j)}let de={icon:sl};e[0]!==void 0&&(de.value=e[0]),c=new Ad({props:de}),Je.push(()=>Ma(c,"value",ie));let yt=e[1],mt=[];for(let j=0;j<yt.length;j+=1)mt[j]=fa(ua(e,yt,j));return x=new Ed({}),wt=new Hm({}),{c(){t=D("div"),n=D("div"),i=D("div"),s=D("div"),o=D("p"),o.textContent="Resources",r=Y(),I(a.$$.fragment),l=Y(),I(c.$$.fragment),f=Y(),d=D("div");for(let j=0;j<mt.length;j+=1)mt[j].c();h=Y(),g=D("div"),p=D("div"),m=D("div"),_=D("p"),_.textContent="General data",b=Y(),I(x.$$.fragment),v=Y(),y=D("div"),k=D("p"),M=J("Queries: "),P=J($),O=Y(),R=D("p"),z=J("Time querying: "),dt=J(ct),V=J(" ms"),G=Y(),N=D("p"),it=J("Slow queries: "),ft=J(B),ne=Y(),I(wt.$$.fragment),F(o,"class","text-2xl"),F(s,"class","flex gap-3 items-center "),F(i,"class","pr-4 flex justify-between items-center"),F(d,"class","flex flex-col gap-3 mt-6 overflow-y-auto pr-4"),F(n,"class","bg-dark-700 p-4 pr-0 flex flex-col w-2/3 rounded-md"),F(_,"class","text-2xl"),F(m,"class","flex gap-3 items-center mb-4"),F(N,"class","text-yellow-500"),F(y,"class","flex flex-col text-dark-50"),F(p,"class","flex flex-col"),F(g,"class","bg-dark-700 p-4 flex flex-col justify-between w-1/3 rounded-md"),F(t,"class","p-2 w-full h-full flex justify-between gap-2")},m(j,he){nt(j,t,he),w(t,n),w(n,i),w(i,s),w(s,o),w(s,r),E(a,s,null),w(i,l),E(c,i,null),w(n,f),w(n,d);for(let Ce=0;Ce<mt.length;Ce+=1)mt[Ce]&&mt[Ce].m(d,null);w(t,h),w(t,g),w(g,p),w(p,m),w(m,_),w(m,b),E(x,m,null),w(p,v),w(p,y),w(y,k),w(k,M),w(k,P),w(y,O),w(y,R),w(R,z),w(R,dt),w(R,V),w(y,G),w(y,N),w(N,it),w(N,ft),w(g,ne),E(wt,g,null),St=!0},p(j,[he]){const Ce={};if(!u&&he&1&&(u=!0,Ce.value=j[0],wa(()=>u=!1)),c.$set(Ce),he&2){yt=j[1];let Lt;for(Lt=0;Lt<yt.length;Lt+=1){const mo=ua(j,yt,Lt);mt[Lt]?mt[Lt].p(mo,he):(mt[Lt]=fa(mo),mt[Lt].c(),mt[Lt].m(d,null))}for(;Lt<mt.length;Lt+=1)mt[Lt].d(1);mt.length=yt.length}(!St||he&4)&&$!==($=j[2].queries+"")&&Wt(P,$),(!St||he&4)&&ct!==(ct=j[2].timeQuerying.toFixed(4)+"")&&Wt(dt,ct),(!St||he&4)&&B!==(B=j[2].slowQueries+"")&&Wt(ft,B)},i(j){St||(S(a.$$.fragment,j),S(c.$$.fragment,j),S(x.$$.fragment,j),S(wt.$$.fragment,j),St=!0)},o(j){C(a.$$.fragment,j),C(c.$$.fragment,j),C(x.$$.fragment,j),C(wt.$$.fragment,j),St=!1},d(j){j&&X(t),L(a),L(c),Ze(mt,j),L(x),L(wt)}}}function Bm(e,t,n){let i,s,o;pt(e,ys,l=>n(0,i=l)),pt(e,uu,l=>n(1,s=l)),pt(e,vs,l=>n(2,o=l));function r(l){i=l,ys.set(i)}return[i,s,o,r,l=>Le.goto(`/${l}`)]}class jm extends K{constructor(t){super(),Z(this,t,Bm,Nm,U,{})}}function da(e){let t,n,i,s,o,r,a;return i=new So({props:{path:"/",$$slots:{default:[Wm]},$$scope:{ctx:e}}}),o=new So({props:{path:"/:resource",$$slots:{default:[Gm]},$$scope:{ctx:e}}}),{c(){t=D("main"),n=D("div"),I(i.$$.fragment),s=Y(),I(o.$$.fragment),F(n,"class","bg-dark-800 flex h-[700px] w-[1200px] rounded-md text-white"),F(t,"class","font-main flex h-full w-full items-center justify-center")},m(l,c){nt(l,t,c),w(t,n),E(i,n,null),w(n,s),E(o,n,null),a=!0},i(l){a||(S(i.$$.fragment,l),S(o.$$.fragment,l),tn(()=>{a&&(r||(r=di(t,Ho,{start:.95,duration:150},!0)),r.run(1))}),a=!0)},o(l){C(i.$$.fragment,l),C(o.$$.fragment,l),r||(r=di(t,Ho,{start:.95,duration:150},!1)),r.run(0),a=!1},d(l){l&&X(t),L(i),L(o),l&&r&&r.end()}}}function Wm(e){let t,n;return t=new jm({}),{c(){I(t.$$.fragment)},m(i,s){E(t,i,s),n=!0},i(i){n||(S(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){L(t,i)}}}function Gm(e){let t,n;return t=new kd({}),{c(){I(t.$$.fragment)},m(i,s){E(t,i,s),n=!0},i(i){n||(S(t.$$.fragment,i),n=!0)},o(i){C(t.$$.fragment,i),n=!1},d(i){L(t,i)}}}function qm(e){let t,n,i=e[0]&&da(e);return{c(){i&&i.c(),t=Pn()},m(s,o){i&&i.m(s,o),nt(s,t,o),n=!0},p(s,[o]){s[0]?i?o&1&&S(i,1):(i=da(s),i.c(),S(i,1),i.m(t.parentNode,t)):i&&(Rt(),C(i,1,1,()=>{i=null}),$t())},i(s){n||(S(i),n=!0)},o(s){C(i),n=!1},d(s){i&&i.d(s),s&&X(t)}}}function Ym(e,t,n){let i,s,o,r;pt(e,Ki,l=>n(0,i=l)),pt(e,ws,l=>n(1,s=l)),pt(e,vs,l=>n(2,o=l)),pt(e,xs,l=>n(3,r=l)),Le.mode.hash(),Le.goto("/"),el("openUI",l=>{_t(Ki,i=!0,i),_t(xs,r=l.resources,r),_t(vs,o={queries:l.totalQueries,slowQueries:l.slowQueries,timeQuerying:l.totalTime},o),_t(ws,s={labels:l.chartData.labels,data:l.chartData.data},s)}),il([{action:"openUI",data:{resources:["ox_core","oxmysql","ox_inventory","ox_doorlock","ox_lib","ox_vehicleshop","ox_target"],slowQueries:13,totalQueries:332,totalTime:230123,chartData:{labels:["oxmysql","ox_core","ox_inventory","ox_doorlock"],data:[{queries:25,time:133},{queries:5,time:12},{queries:3,time:2},{queries:72,time:133}]}}}]);const a=l=>{l.key==="Escape"&&(_t(Ki,i=!1,i),Oa("exit"))};return e.$$.update=()=>{e.$$.dirty&1&&(i?window.addEventListener("keydown",a):window.removeEventListener("keydown",a))},[i]}class Um extends K{constructor(t){super(),Z(this,t,Ym,qm,U,{})}}new Um({target:document.getElementById("app")});if(nl()){const e=document.getElementById("app");e.style.backgroundImage='url("https://i.imgur.com/3pzRj9n.png")',e.style.backgroundSize="cover",e.style.backgroundRepeat="no-repeat",e.style.backgroundPosition="center"}
