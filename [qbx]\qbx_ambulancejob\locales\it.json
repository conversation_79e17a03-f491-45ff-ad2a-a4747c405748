{"error": {"canceled": "<PERSON><PERSON><PERSON>", "impossible": "Azione impossibile...", "no_player": "<PERSON><PERSON><PERSON> gio<PERSON>ore nelle vicinanze", "no_firstaid": "Hai bisogno di un kit di pronto soccorso", "no_bandage": "Hai bisogno di una benda", "beds_taken": "I letti sono occupati...", "possessions_taken": "Hai perso i tuoi effetti personali...", "cant_help": "Non puoi aiutare questa persona...", "not_ems": "Non sei EMS o non sei in servizio"}, "success": {"revived": "<PERSON> rianimato una persona", "healthy_player": "Il giocatore è in salute", "helped_player": "Hai aiutato una persona", "being_helped": "Ti stanno a<PERSON>tando..."}, "info": {"civ_died": "Civile morto", "civ_down": "Civile ferito", "civ_call": "Richiesta di intervento", "ems_down": "Doctor %s Down", "respawn_txt": "RESPAWN TRA: ~r~%s~s~ SECONDI", "respawn_revive": "PREMI [~r~E~s~] PER %s SECONDI PER IL RESPAWN AL COSTO DI $~r~%s~s~", "bleed_out": "MORIRAI DISSANGUATO TRA: ~r~%s~s~ SECONDI", "bleed_out_help": "MORIRAI DISSANGUATO TRA: ~r~%s~s~ SECONDI, PUOI ESSERE AIUTATO", "request_help": "PREMI [~r~G~s~] PER RICHIEDERE AIUTO", "help_requested": "IL PERSONALE EMS È STATO AVVISATO", "amb_plate": "AMBU", "heli_plate": "LIFE", "status": "Verifica stato", "is_status": "È %s", "healthy": "Sei di completamente in salute!", "safe": "Hospital Safe", "ems_alert": "EMS Alert - %s", "mr": "Sig.", "mrs": "Sig.ra", "dr_needed": "È richiesto un medico al Pillbox Hospital", "ems_report": "EMS Report", "message_sent": "Messaggio inviato", "check_health": "Controlla la salute di un giocatore", "heal_player": "<PERSON><PERSON><PERSON><PERSON> gio<PERSON><PERSON>", "revive_player": "<PERSON><PERSON><PERSON> gio<PERSON>"}, "mail": {"sender": "Pillbox Hospital", "subject": "Spese ospedaliere", "message": "Salve %s %s, <br /><br />Con la presente hai ricevuto un'e-mail con i costi dell'ultima visita in ospedale.<br />I costi totali sono: <strong>$%s</strong><br /><br />Le auguriamo una pronta guarigione!"}, "menu": {"amb_vehicles": "Veicoli EMS", "status": "Stato"}, "text": {"pstash_button": "[E] - Inventario Personale", "pstash": "Inventario Personale", "onduty_button": "[E] - Vai in servizio", "offduty_button": "[E] - Vai fuori servizio", "duty": "Entra/Esci dal Servizio", "armory_button": "[E] - Farmacia", "armory": "Farmacia", "veh_button": "[E] - Prendi / Deposita <PERSON>ei<PERSON>lo", "elevator_roof": "[E] - Prendi l'ascensore fino al tetto", "elevator_main": "[E] - Prendi l'ascensore fino al piano", "el_roof": "Take the elevator to the roof", "el_main": "Take the elevator to the main floor", "call_doc": "[E] - <PERSON><PERSON> un dottore", "call": "Chiama", "check_in": "[E] Check in", "check": "Check In", "lie_bed": "[E] - <PERSON><PERSON><PERSON><PERSON> sul letto", "bed": "Lay in bed", "put_bed": "Posizionare il cittadino a letto", "bed_out": "[E] - Per <PERSON> dal letto..", "alert": "<PERSON><PERSON>!"}, "progress": {"ifaks": "Assumendo iFaks...", "bandage": "Usando una benda...", "painkillers": "Assumendo antidolorifici...", "revive": "Rianimazione...", "healing": "Gua<PERSON>do ferite...", "checking_in": "Check-in..."}}