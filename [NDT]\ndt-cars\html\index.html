<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NDT Cars</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div id="ndt-cars-container" class="hidden">
        <div class="menu-overlay"></div>
        <div class="menu-container">
            <!-- Header -->
            <div class="menu-header">
                <div class="header-content">
                    <h1><i class="fas fa-car"></i> NDT Cars</h1>
                    <p>Select your vehicle</p>
                </div>
                <button class="close-btn" onclick="closeMenu()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Categories -->
            <div class="categories-section">
                <h2>Categories</h2>
                <div class="categories-grid" id="categories-grid">
                    <!-- Categories will be loaded here -->
                </div>
            </div>

            <!-- Vehicles -->
            <div class="vehicles-section hidden" id="vehicles-section">
                <div class="vehicles-header">
                    <button class="back-btn" onclick="showCategories()">
                        <i class="fas fa-arrow-left"></i> Back
                    </button>
                    <h2 id="category-title">Vehicles</h2>
                </div>
                <div class="vehicles-grid" id="vehicles-grid">
                    <!-- Vehicles will be loaded here -->
                </div>
            </div>

            <!-- Loading -->
            <div class="loading hidden" id="loading">
                <div class="spinner"></div>
                <p>Loading...</p>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html> 