name: Create release

on:
  push:
    tags:
      - 'v*.*.*'

jobs:
  create-release:
    if: github.actor_id != 210085057
    runs-on: ubuntu-latest
    steps:
      - name: Install zip
        run: sudo apt install zip

      - name: Install Bun
        uses: oven-sh/setup-bun@v2
        with:
          bun-version: latest


      - name: Generate GitHub App token
        id: app_token
        uses: tibdex/github-app-token@v2
        with:
          app_id: ${{ secrets.APP_ID }}
          private_key: ${{ secrets.APP_PRIVATE_KEY }}

      - name: Get latest code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: ${{ github.event.repository.default_branch }}
          token: ${{ steps.app_token.outputs.token }}


      - name: Bump manifest version
        run: bun run .github/actions/bump-manifest-version.js
        env:
          TGT_RELEASE_VERSION: ${{ github.ref_name }}

      - name: Push version bump change
        uses: EndBug/add-and-commit@v9
        with:
          add: fxmanifest.lua
          push: true
          default_author: github_actions
          message: 'chore: bump version to ${{ github.ref_name }}'


      - name: Bundle files
        run: |
          mkdir -p ./temp/ox_fuel
          cp ./{LICENSE,README.md,fxmanifest.lua,config.lua,server.lua} ./temp/ox_fuel
          cp -r ./{client,data,locales} ./temp/ox_fuel
          cd ./temp && zip -r ../ox_fuel.zip ./ox_fuel

      - name: Create release
        uses: 'marvinpinto/action-automatic-releases@v1.2.1'
        with:
          repo_token: ${{ secrets.GITHUB_TOKEN }}
          prerelease: false
          files: ox_fuel.zip

      - name: Update tag
        uses: EndBug/latest-tag@v1
        with:
          ref: ${{ github.ref_name }}
