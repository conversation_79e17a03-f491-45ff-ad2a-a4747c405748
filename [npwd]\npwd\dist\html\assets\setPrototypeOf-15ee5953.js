import{g as u}from"./_commonjsHelpers-de833af9.js";var c={exports:{}},m="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",h=m,l=h;function i(){}function y(){}y.resetWarningCache=i;var T=function(){function e(o,n,O,P,g,f){if(f!==l){var p=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw p.name="Invariant Violation",p}}e.isRequired=e;function t(){return e}var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:y,resetWarningCache:i};return r.PropTypes=r,r};c.exports=T();var _=c.exports;const b=u(_);function a(){return a=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e},a.apply(this,arguments)}function s(e,t){return s=Object.setPrototypeOf||function(o,n){return o.__proto__=n,o},s(e,t)}export{b as P,s as _,a};
