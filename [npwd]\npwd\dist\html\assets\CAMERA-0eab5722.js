import{j as l}from"./jsx-runtime-5fe4d0a7.js";import{S as c,__tla as i}from"./__federation_expose_Input-51304708.js";let s,t=Promise.all([(()=>{try{return i}catch{}})()]).then(async()=>{s=a=>l.jsx(c,{...a,children:l.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",fillRule:"evenodd",strokeLinejoin:"round",strokeMiterlimit:"2",clipRule:"evenodd",viewBox:"0 0 176 178",children:[l.jsx("metadata",{id:"author",children:"<EMAIL>"}),l.jsx("path",{fill:"none",d:"M0 0H176V178H0z"}),l.jsx("clipPath",{id:"_clip1",children:l.jsx("path",{d:"M0 0H176V178H0z"})}),l.jsxs("g",{clipPath:"url(#_clip1)",children:[l.jsx("path",{fill:"#444e87",d:"M87.594.286C152.799-3.983 176 40.142 176 89.135s-29.665 91.97-84.913 88.689C45.254 175.102-1.31 150.139.028 87.556.642 58.856 24.249 4.434 87.594.286z"}),l.jsxs("g",{children:[l.jsx("path",{fill:"#c1365b",d:"M99.654 54.684s-2.012-8 .736-11.33c1.881-2.279 8.157-2.518 12.72-2.477 4.377.04 10.633-.486 12.029 1.876 1.7 2.877 1.361 12.153 1.361 12.153"}),l.jsx("path",{fill:"#fff",d:"M121.986 44.845c.***************.17.322.402.947.615 2.403.792 3.823.369 2.948.447 5.803.447 5.803a3.11 3.11 0 002.991 3.218 3.108 3.108 0 003.218-2.991s.457-5.164.084-9.237c-.185-2.032-.703-3.804-1.307-4.946-.74-1.389-2.177-2.461-4.172-3.142-2.829-.965-7.506-.85-11.062-.984-3.218.045-7.206.218-10.191 1.041-2.495.688-4.435 1.802-5.488 3.191-1.265 1.715-1.989 4.062-2.014 6.551-.038 3.892 1.188 7.948 1.188 7.948a3.108 3.108 0 003.77 2.255 3.109 3.109 0 002.255-3.771s-.4-2.455-.254-5.032c.066-1.166.22-2.392.898-3.129.123-.137.385-.054.621-.128.863-.271 1.917-.359 3.028-.456 2.022-.175 4.225-.185 6.112-.137 2.012-.027 4.426-.173 6.564-.115.706.019 1.804-.155 2.35-.084zm0 0l.017.002.095.188.058.132c.043.061.087.077.124.008.108-.2-.019-.293-.277-.328a2.7 2.7 0 00-.106-.178l.089.176z"}),l.jsx("path",{fill:"#2b3465",d:"M63.171 54.181s-26.273.099-28.281 19.337c-1.735 16.626-8.46 61.154 8.944 62.846 17.403 1.692 51.968-.242 64.779 0 12.811.242 35.29-2.659 36.015-14.503.725-11.844 1.523-52.933 0-56.561-2.692-6.413-6.284-10.394-20.062-10.394-13.778 0-61.395-.725-61.395-.725z"}),l.jsx("path",{fill:"#fff",d:"M63.236 49.879l-.081-.001s-14.336.143-23.796 7.513c-4.496 3.502-7.978 8.514-8.721 15.684-1.121 10.819-4.225 33.19-1.811 48.484 1.715 10.868 6.549 18.238 14.597 19.017 5.373.52 12.348.707 19.827.713 16.871.014 36.361-.884 45.282-.719 8.918.17 22.188-1.191 30.618-5.567 5.71-2.964 9.343-7.301 9.688-12.884.453-7.353.941-25.902.889-39.901-.036-9.482-.547-17.034-1.186-18.56-1.708-4.075-3.813-7.291-7.562-9.579-3.424-2.089-8.421-3.425-16.414-3.429l-61.33-.771zm77.402 16.884c.04.305.244 1.89.326 3.269.248 4.175.321 10.721.29 17.846-.056 12.786-.453 27.424-.836 33.725-.23 3.774-4.038 5.762-8.471 7.354-7.21 2.588-16.617 3.325-23.255 3.201-8.951-.173-28.505.709-45.431.682-7.173-.012-13.864-.185-19.018-.688-2.268-.222-3.71-1.829-4.797-4.029-1.786-3.612-2.572-8.651-2.886-14.219-.787-13.954 1.645-31.027 2.582-39.942.608-5.782 4.092-9.298 8.063-11.494 6.861-3.795 15.184-3.975 15.928-3.983h.028l61.405.678c4.666-.003 7.998.447 10.453 1.417 3.127 1.235 4.452 3.441 5.619 6.183z"}),l.jsx("path",{fill:"#ffcd55",d:"M123.858 69.357s-10.744-.909-10.881 4.24c-.099 3.711-1.075 6.139 1.017 7.447 3.442 2.154 11.407 2.155 16.888 1.017.987-.205.882-7.855.231-10.531-.5-2.056-4.433-2.27-7.255-2.173z"}),l.jsx("path",{fill:"#fff",d:"M123.894 67.143c-.989-.08-6.661-.452-10.031 1.233-2.069 1.034-3.366 2.746-3.498 5.151-.127 2.56-.56 4.557-.335 6.051.24 1.588.974 2.834 2.498 3.809 1.816 1.143 4.666 1.894 7.875 2.113 3.636.247 7.744-.131 11.015-.858.738-.168 2.103-.93 2.489-2.96.459-2.412.175-8.345-.459-10.72-.391-1.449-1.47-2.595-3.185-3.205-1.758-.626-4.301-.726-6.369-.614zm4.919 5.138c.333 1.729.394 5.3.128 7.426-2.057.292-4.357.404-6.552.338-2.781-.083-5.404-.389-6.929-1.344l.129-5.034c-.004-1.093 1.277-1.287 2.4-1.572 2.654-.675 5.682-.537 5.682-.537.087.007.175.009.263.006 1.194-.013 2.613.041 3.809.305.375.082.864.31 1.07.412z"}),l.jsx("path",{fill:"#00dea7",d:"M78.637 71.045s-21.444-4.467-27.029 16.53c-5.584 20.998 7.148 32.614 23.902 31.497 16.753-1.117 30.055-13.238 22.114-33.954-5.137-13.403-18.987-14.073-18.987-14.073z"}),l.jsx("path",{fill:"#fff",d:"M79.042 67.956c-1.568-.314-10.918-1.915-19.259 2.577-4.991 2.687-9.608 7.526-12.085 16.002-3.186 11.306-1.446 20.313 3.057 26.563 5.259 7.3 14.466 11.035 25.043 10.305 10.192-.821 19.179-5.261 24.04-12.675 4.344-6.625 5.652-15.697 1.141-26.896-2.145-5.296-5.417-8.854-8.832-11.214-5.744-3.971-11.804-4.576-13.105-4.662zm-1.039 6.131c.16.033.321.053.484.061 0 0 5.064.413 9.734 3.918 2.396 1.798 4.646 4.461 6.049 8.338 3.047 8.369 2.292 15.044-1.061 19.831-3.724 5.316-10.512 8.111-17.988 8.506-7.29.469-13.677-1.843-17.363-6.828-3.329-4.503-4.413-11.013-2.34-19.298 1.458-6.074 4.44-9.72 7.863-11.806 6.709-4.089 14.622-2.722 14.622-2.722z"}),l.jsx("path",{fill:"#fff",d:"M80.736 80.841s-2.04-.493-4.947-.415a17.035 17.035 0 00-5.045.914c-2.154.736-4.312 2.035-6 4.072-1.084 1.307-1.948 2.949-2.461 4.898a16.483 16.483 0 00-.556 4.8c.065 2.014.386 4.182 1.109 6.687a1.308 1.308 0 001.452 1.143 1.308 1.308 0 001.142-1.451c.112-2.761.635-4.948 1.256-6.873.281-.868.649-1.612.99-2.333.546-1.154 1.244-2.058 1.93-2.887.64-.774 1.267-1.477 1.979-2.047.847-.678 1.669-1.286 2.542-1.742 3.311-1.731 6.403-2.162 6.403-2.162a1.307 1.307 0 00.206-2.604z"})]})]})]})})});export{t as __tla,s as default};
