import{j as a}from"./jsx-runtime-5fe4d0a7.js";import{c,__tla as r}from"./__federation_expose_Input-51304708.js";let t,o=Promise.all([(()=>{try{return r}catch{}})()]).then(async()=>{t=c(a.jsx("path",{d:"M20.94 11c-.46-4.17-3.77-7.48-7.94-7.94V1h-2v2.06C6.83 3.52 3.52 6.83 3.06 11H1v2h2.06c.46 4.17 3.77 7.48 7.94 7.94V23h2v-2.06c4.17-.46 7.48-3.77 7.94-7.94H23v-2h-2.06zM12 19c-3.87 0-7-3.13-7-7s3.13-7 7-7 7 3.13 7 7-3.13 7-7 7z"}),"LocationSearching")});export{t as L,o as __tla};
