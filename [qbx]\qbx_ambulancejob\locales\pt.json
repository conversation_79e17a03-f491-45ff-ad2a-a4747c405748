{"error": {"canceled": "Cancelado", "impossible": "Acção Impossível...", "no_player": "<PERSON><PERSON><PERSON>", "no_firstaid": "Necessitas de um Estojo de Primeiros Socorros", "no_bandage": "Necessitas de uma Ligadura", "beds_taken": "Camas ocupadas...", "possessions_taken": "<PERSON><PERSON> as tuas posses foram removidas...", "cant_help": "<PERSON>ão podes ajudar esta pessoa...", "not_ems": "Não pertences ao EMS"}, "success": {"revived": "Reviveste uma pessoa", "healthy_player": "<PERSON><PERSON><PERSON>", "helped_player": "Ajudaste a pessoa", "being_helped": "Estão a ajudar-te..."}, "info": {"civ_died": "Cidadão morreu", "civ_down": "Cidadão caído", "civ_call": "Chamada de cidadão", "ems_down": "Doctor %s Down", "respawn_txt": "RENASCER EM: ~r~%s~s~ SEGUNDOS", "respawn_revive": "PRESSIONA [~r~E~s~] DURANTE %s SGUNDOS PARA RENASCERES POR $~r~%s~s~", "bleed_out": "ESVAIR-TE-ÁS EM SANGUE DENTRO DE: ~r~%s~s~ SEGUNDOS", "bleed_out_help": "ESVAIR-TE-<PERSON><PERSON> EM SANGUE DENTRO DE: ~r~%s~s~ SEGUNDOS, AINDA PODES SER AJUDADO", "request_help": "PRESSIONA [~r~G~s~] PARA PEDIR AJUDA", "help_requested": "OS SERVIÇOS DE EMS FORAM NOTIFICADOS", "amb_plate": "AMBU", "heli_plate": "VIDA", "status": "Verificação de Estado", "is_status": "Encontra-se %s", "healthy": "Estás novamente saudável!", "safe": "Hospital Seguro", "ems_alert": "Alerta EMS - %s", "mr": "Sr.", "mrs": "<PERSON><PERSON>.", "dr_needed": "Solicita-se a presença de um Médico no Pillbox Hospital", "ems_report": "Relatório <PERSON>", "message_sent": "Mensagem a ser enviada", "check_health": "Verificar a Saúde de Paciente", "heal_player": "<PERSON><PERSON><PERSON> um Pac<PERSON>e", "revive_player": "Reviver um Paciente"}, "mail": {"sender": "Ho<PERSON>ita <PERSON>", "subject": "<PERSON>ustos <PERSON>", "message": "Caro(a) %s %s, <br /><br />Anexado a este email encontram-se os custos da sua ultima visita ao nosso Hospital.<br />O valor total foi de: <strong>$%s</strong><br /><br />Desejamos-lhe uma rápida recuperação!"}, "menu": {"amb_vehicles": "Veiculos de Emergência", "status": "Health Status"}, "text": {"pstash_button": "[E] - <PERSON><PERSON><PERSON><PERSON>", "pstash": "Cacifo <PERSON>l", "onduty_button": "[E] - Entrar <PERSON> Servi<PERSON>", "offduty_button": "~r~E~w~ - <PERSON><PERSON>", "duty": "Entrar/<PERSON><PERSON>", "armory_button": "[E] - <PERSON><PERSON>", "armory": "<PERSON><PERSON>", "veh_button": "[E] - <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elevator_roof": "[E] - <PERSON><PERSON><PERSON> elevador para a cobertura", "elevator_main": "[E] - <PERSON><PERSON><PERSON> elevador para baixo", "el_roof": "Take the elevator to the roof", "el_main": "Take the elevator to the main floor", "call_doc": "[E] - <PERSON><PERSON>", "call": "<PERSON><PERSON>", "check_in": "[E] - <PERSON>", "check": "Entrada", "lie_bed": "[E] - <PERSON><PERSON><PERSON> na cama", "bed": "Lay in bed", "put_bed": "Colocar o cidadão na cama", "bed_out": "[E] - <PERSON><PERSON> da cama...", "alert": "<PERSON><PERSON>!"}, "progress": {"ifaks": "A tomar ifaks...", "bandage": "A utilizar Ligadura...", "painkillers": "A tomar <PERSON>...", "revive": "A Reanimar <PERSON>...", "healing": "A Curar Ferimentos...", "checking_in": "A Dar Entrada..."}}