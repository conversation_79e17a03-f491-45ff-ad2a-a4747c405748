{"error": {"canceled": "Abgebrochen", "impossible": "Aktion kann nicht ausgeführt werden...", "no_player": "<PERSON><PERSON> in der nähe", "no_firstaid": "<PERSON>ö<PERSON>gst ein Erste-Hilf<PERSON>-<PERSON><PERSON>", "no_bandage": "<PERSON> benötigst einen Verband", "beds_taken": "Die Betten sind belegt...", "possessions_taken": "Dir wurde alles abgenommen...", "cant_help": "Du kannst dieser Person nicht helfen...", "not_ems": "Du bist kein Rettungsdienst"}, "success": {"revived": "Du hast eine Person wiederbelebt", "healthy_player": "<PERSON><PERSON><PERSON> ist gesund", "helped_player": "Du hast der Person geholfen", "being_helped": "Dir wurde geholfen..."}, "info": {"civ_died": "Zivilist verstorben", "civ_down": "Zivilist Down", "civ_call": "Zivilisten Anruf", "ems_down": "Doctor %s Down", "respawn_txt": "Wiederbelebung in: ~r~%s~s~ Sekunden", "respawn_revive": "Halte [~r~E~s~] für %s Sekunden um Wiederbelebt zu werden zu Preis von $~r~%s~s~", "bleed_out": "Du blutest aus in: ~r~%s~s~ Sekunden", "bleed_out_help": "Du blutest aus in: ~r~%s~s~ <PERSON><PERSON><PERSON>, dir kann geholfen werden", "request_help": "Drücke [~r~G~s~] für eine hilfeanfrage", "help_requested": "Rettungsdienst wurde verständigt", "amb_plate": "AMBU", "heli_plate": "LIFE", "status": "Status Prüfung", "is_status": "Ist %s", "healthy": "Du bist wieder ganz gesund!", "safe": "Krankenhaus Safe", "ems_alert": "Rettungsdienst Alarm - %s", "mr": "<PERSON>.", "mrs": "<PERSON><PERSON><PERSON>", "dr_needed": "Ein Doktor wird im Pillbox Krankenhaus benötigt", "ems_report": "Rettungsdienst Report", "message_sent": "<PERSON><PERSON><PERSON><PERSON> zu senden", "check_health": "Prüfe die Gesunheit eines Spielers", "heal_player": "<PERSON><PERSON> e<PERSON>", "revive_player": "<PERSON><PERSON><PERSON> e<PERSON>ler"}, "mail": {"sender": "Pillbox Krankenhaus", "subject": "Krankenhauskosten", "message": "Hallo %s %s, <br /><br />Hiermit erhalten Sie eine Email über die Kosten ihres letzten Krankenhaus besuchs.<br />Die gesammt Kosten betragen: <strong>$%s</strong><br /><br />Wir wünschen eine schnelle Genesung!"}, "menu": {"amb_vehicles": "Rettungsdienst Fahrzeuge", "status": "Health Status"}, "text": {"pstash_button": "[E] - Persönliches Fach", "pstash": "Persönliches Fach", "onduty_button": "[E] - <PERSON><PERSON>", "offduty_button": "~r~E~w~ - <PERSON><PERSON>", "duty": "In/<PERSON><PERSON>", "armory_button": "[E] - <PERSON><PERSON><PERSON><PERSON><PERSON>", "armory": "Waffensch<PERSON>", "veh_button": "[E] - Fahrzeuge", "elevator_roof": "[E] - <PERSON><PERSON><PERSON>zu<PERSON> aufs Dach", "elevator_main": "[E] - <PERSON><PERSON><PERSON> runter", "el_roof": "Take the elevator to the roof", "el_main": "Take the elevator to the main floor", "call_doc": "[E] - <PERSON><PERSON><PERSON> rufen", "call": "Rufe", "check_in": "[E] - <PERSON><PERSON><PERSON><PERSON>", "check": "Einchecken", "lie_bed": "[E] - Um ins Bett zu legen", "bed": "Lay in bed", "put_bed": "<PERSON><PERSON>rger ins Bett legen", "bed_out": "[E] - Um aus dem Bett zu steigen..", "alert": "<PERSON><PERSON>!"}, "progress": {"ifaks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON>...", "bandage": "Benutze Verband...", "painkillers": "Nehme Schmerzmittel...", "revive": "<PERSON><PERSON>erbelebe Person...", "healing": "<PERSON><PERSON> heilen...", "checking_in": "Checke ein..."}}