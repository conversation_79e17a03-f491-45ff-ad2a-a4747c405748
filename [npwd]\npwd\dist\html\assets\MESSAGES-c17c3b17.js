import{j as a}from"./jsx-runtime-5fe4d0a7.js";import{c as l,__tla as s}from"./__federation_expose_Input-51304708.js";let e,m=Promise.all([(()=>{try{return s}catch{}})()]).then(async()=>{let t;t=l(a.jsx("path",{d:"M20 2H4c-1.1 0-1.99.9-1.99 2L2 22l4-4h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"}),"Message"),e=()=>a.jsx(t,{fontSize:"small"})});export{m as __tla,e as default};
