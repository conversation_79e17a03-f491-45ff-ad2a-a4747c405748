{"error": {"canceled": "<PERSON><PERSON><PERSON>", "impossible": "Action Impossible...", "no_player": "<PERSON><PERSON>n joueur proche", "no_firstaid": "Vous avez besoin d'un kit de premier secours", "no_bandage": "Vous avez besoin d'un bandage", "beds_taken": "Les lits sont tous occupés...", "possessions_taken": "Tout vos objets ont été saisis...", "cant_help": "Vous ne pouvez pas aider cette personne...", "not_ems": "Vous n'êtes pas EMS"}, "success": {"revived": "Vous avez réanimé quelqu'un", "healthy_player": "La personne est en bonne santé", "helped_player": "Vous avez aidé la personne", "being_helped": "Quelqu'un vous aide..."}, "info": {"civ_died": "Civil décédé", "civ_down": "Civil blessé", "civ_call": "Appel Civil", "ems_down": "Doctor %s Down", "wep_unknown": "Inconnu", "respawn_txt": "RÉAPPARAITRE DANS: ~r~%s~s~ SECONDES", "respawn_revive": "MAINTENEZ [~r~E~s~] POUR %s SECONDES POUR RÉAPPARAITRE PO $~r~%s~s~", "bleed_out": "VOUS ALLEZ VOUS VIDER DE VOTRE SANG DANS: ~r~%s~s~ SECONDES", "bleed_out_help": "VOUS ALLEZ VOUS VIDER DE VOTRE SANG DANS: ~r~%s~s~ SECONDES, VOUS POUVEZ ÊTRE AIDÉ", "request_help": "APPUYEZ SUR [~r~G~s~] POUR DEMANDER DE L'AIDE", "help_requested": "LES EMS ONT ÉTÉ NOTIFIÉ", "amb_plate": "AMBU", "heli_plate": "LIFE", "status": "Check Status", "is_status": "Est %s", "healthy": "Vous êtes maintenant en parfaite santé !", "safe": "<PERSON><PERSON><PERSON> l'hopital", "ems_alert": "Alerte EMS - %s", "mr": "<PERSON>.", "mrs": "Mme.", "dr_needed": "Un docteur est demandé a l'hopital de Pillbox", "dr_alert": "Doctor has already been notified", "ems_report": "Rapport EMS", "message_sent": "Message à envoyer", "check_health": "Verifier la santé de quelqu'un", "heal_player": "<PERSON><PERSON><PERSON> quelqu'un", "revive_player": "<PERSON><PERSON><PERSON>mer une personne"}, "mail": {"sender": "Hopital de Pillbox", "subject": "Coût Hopital", "message": "Cher(e) %s %s, <br /><br />Par la présente, vous avez reçu un e-mail avec les coûts de la dernière visite à l'hôpital.<br />Le coût final est: <strong>$%s</strong><br /><br />Nous-vous souhaitons un bon rétablissement !"}, "menu": {"amb_vehicles": "Véhicules ambulanciers", "status": "Etat de santé"}, "text": {"pstash_button": "[E] - <PERSON><PERSON>re Personnel", "pstash": "Coffre personnel", "onduty_button": "[E] - <PERSON><PERSON><PERSON> son service", "offduty_button": "[E] - <PERSON><PERSON><PERSON> son service", "duty": "En/Hors Service", "armory_button": "[E] - Armurer<PERSON>", "armory": "Armurerie", "veh_button": "[E] - <PERSON><PERSON><PERSON> / Ranger un vehicule", "elevator_roof": "[E] - <PERSON><PERSON><PERSON> l'ascenseur jusqu'au toit", "elevator_main": "[E] - <PERSON><PERSON><PERSON> l'ascenseur", "el_roof": "Take the elevator to the roof", "el_main": "Take the elevator to the main floor", "call_doc": "[E] - <PERSON><PERSON><PERSON> un docteur", "call": "<PERSON><PERSON><PERSON>", "check_in": "[E] - S'hospitaliser", "check": "Enregistrement", "lie_bed": "[E] - <PERSON><PERSON> s'allonger dans un lit", "bed": "Lay in bed", "put_bed": "Placer le citoyen dans son lit", "bed_out": "[E] - Pour sortir du lit..", "alert": "<PERSON><PERSON>!"}, "progress": {"ifaks": "Prend un Kit de Soin Individuel...", "bandage": "Utilise un Bandage...", "painkillers": "Prend des anti-douleurs...", "revive": "<PERSON><PERSON><PERSON>me la personne...", "healing": "Soigne les blessures...", "checking_in": "S'enregistre..."}}