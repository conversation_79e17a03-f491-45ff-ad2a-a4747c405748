{"notifications": {"success": {"repaired": "<PERSON><PERSON> tamir edildi!", "paid": "Banka hesabından $%s ödedin"}, "error": {"money": "<PERSON><PERSON><PERSON> paran yok!", "alreadyInstalled": "Bu mod zaten yüklü"}, "props": {"installTitle": "<PERSON><PERSON>"}}, "dragCam": {"zoomIn": "Yakınlaştırmayı arttır", "zoomOut": "Yakınlaştırmayı azalt", "toggleDoors": "Kapıları aç/kapa", "changeView": "Birinci <PERSON>ı<PERSON> gö<PERSON>ü<PERSON>ümünü aç/kapa"}, "textUI": {"tune": "[E]'ye basarak arabanı ayarla"}, "menus": {"main": {"title": "Popcorn Customs", "repair": "<PERSON><PERSON>", "performance": "Performans", "parts": "Kosmetik - Parçalar", "colors": "Kosmetik - Renkler", "extras": "Ekstralar"}, "colors": {"primary": "<PERSON><PERSON><PERSON><PERSON> boya", "secondary": "<PERSON><PERSON><PERSON><PERSON> boya", "neon": "Neon", "cosmetics_colors": "Kosmetik - Renkler"}, "neon": {"title": "Neon", "neon": "Neon %s%s", "color": "<PERSON>n rengi", "installed": "%s neon yüklendi"}, "paint": {"title": "Kosmetik - Renkler", "primary": "<PERSON><PERSON><PERSON><PERSON> boya", "secondary": "<PERSON><PERSON><PERSON><PERSON> boya"}, "parts": {"title": "Kosmetik - Parçalar", "wheels": "Tekerlekler"}, "performance": {"title": "Performans", "turbo": "Turbo"}, "wheels": {"title": "Tekerlekler", "bikeRear": "Motosiklet arka tekerleği", "installed": "%s %s yüklendi"}, "options": {"interior": "<PERSON>ç <PERSON>", "livery": "Livery", "pearlescent": "<PERSON><PERSON><PERSON><PERSON>", "plateIndex": {"title": "<PERSON><PERSON><PERSON>", "installed": "%s plaka y<PERSON>"}, "tyreSmoke": "<PERSON><PERSON> du<PERSON>", "wheelColor": "Tekerlek rengi", "windowTint": {"title": "<PERSON>", "installed": "%s cam filmi yüklendi"}, "xenon": {"title": "Xenon", "installed": "%s xenon yükle<PERSON>"}}, "general": {"stock": "Orijinal", "enabled": "<PERSON><PERSON><PERSON>", "disabled": "Devre Dışı", "installed": "%s yüklendi", "applied": "%s uygulandı"}}, "general": {"payReason": "<PERSON><PERSON>"}}