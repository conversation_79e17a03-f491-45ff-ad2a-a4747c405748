# ox_target

![](https://img.shields.io/github/downloads/communityox/ox_target/total?logo=github)
![](https://img.shields.io/github/downloads/communityox/ox_target/latest/total?logo=github)
![](https://img.shields.io/github/contributors/communityox/ox_target?logo=github)
![](https://img.shields.io/github/v/release/communityox/ox_target?logo=github) 


A performant and flexible standalone "third-eye" targeting resource, with additional functionality for supported frameworks.

ox_target is the successor to qtarget, which was a mostly-compatible fork of bt-target.
To improve many design flaws, ox_target has been written from scratch and drops support for bt-target/qtarget standards, though partial compatibility is being implemented where possible.


## 📚 Documentation

https://coxdocs.dev/ox_target

## 💾 Download

https://github.com/communityox/ox_target/releases/latest/download/ox_target.zip

## ✨ Features

- Improved entity and world collision than its predecessor.
- Improved error handling when running external code.
- Menus for nested target options.
- Partial compatibility for qtarget (the thing qb-target is based on, I made the original idiots).
- Registering options no longer overrides existing options.
- Groups and items checking for supported frameworks.
