{"error": {"not_online": "Giocatore Offline", "wrong_format": "Formato sbagliato", "missing_args": "Devi inserire ancora qualcosa(x, y, z)", "missing_args2": "Tutti gli argomenti devono essere compilati!", "no_access": "Non hai accesso a questo comando", "company_too_poor": "La tua azienda è povera", "item_not_exist": "Oggetto inesistente", "too_heavy": "Inventario pieno", "location_not_exist": "Destinazione Inesistente", "duplicate_license": "Licenza Rockstar Duplicata", "no_valid_license": "Licenza Rockstar non Valida", "not_whitelisted": "Non sei nella Allowlist", "server_already_open": "Il server è già aperto", "server_already_closed": "Il server è già chiuso", "no_permission": "Non hai i permessi necessari..", "no_waypoint": "Nessun marker impostato.", "tp_error": "Errore durante il TP.", "connecting_database_timeout": "Connection to database timed out. (Is the SQL server on?)", "connecting_error": "An error occurred while connecting to the server. (Check your server console)", "no_match_character_registration": "Anything other than letters aren't allowed, trailing whitespaces aren't allowed either and words must start with a capital letter in input fields. You can however add words with spaces inbetween.", "already_in_queue": "You are already in queue.", "no_subqueue": "You were not let in any sub-queue."}, "success": {"server_opened": "Il server ora è aperto", "server_closed": "Il server ora è chiuso", "teleported_waypoint": "TP al marker.", "character_deleted": "Character deleted!", "character_deleted_citizenid": "You successfully deleted the character with Citizen ID %s."}, "info": {"received_paycheck": "Hai ricevuto la paga di $%s", "job_info": "Lavoro: %s | Grado: %s | Stato: %s", "gang_info": "Gang: %s | Grado: %s", "on_duty": "Sei in servizio!", "off_duty": "Sei fuori servizio!", "checking_ban": "Ciao %s. <PERSON>o controllando che tu non sia bannato!", "join_server": "Benvenuto %s su %s.", "checking_whitelisted": "Ciao %s. Sto controllando la allowlist.", "exploit_banned": "Sei stato bannato per Cheating o Exploit. Apri un ticket per maggiori informazioni: %s", "exploit_dropped": "Sei stato espulso per Exploit", "multichar_title": "Qbox Multichar", "multichar_new_character": "New Character #%s", "char_male": "Male", "char_female": "Female", "play": "Play", "play_description": "Play as %s", "delete_character": "Delete Character", "delete_character_description": "Delete %s", "logout_command_help": "Logs you out of your current character", "check_id": "Check your Server ID", "deletechar_command_help": "Delete a players character", "deletechar_command_arg_player_id": "Player ID", "character_registration_title": "Character Registration", "first_name": "First Name", "last_name": "Last Name", "nationality": "Nationality", "gender": "Sex", "birth_date": "Birth Date", "select_gender": "Select your gender...", "confirm_delete": "Are you sure you wish to delete this character?", "in_queue": "🐌 You are %s/%s in queue. (%s) %s"}, "command": {"tp": {"help": "TP su ID Gioctore o Coordinate (Solo Admin)", "params": {"x": {"name": "id/x", "help": "ID Giocatore o Posizione X"}, "y": {"name": "y", "help": "Posizione Y"}, "z": {"name": "z", "help": "Posizione Z"}}}, "tpm": {"help": "<PERSON><PERSON> <PERSON> (Solo Admin)"}, "togglepvp": {"help": "Togli PVP al server (Solo Admin)"}, "addpermission": {"help": "<PERSON> i permessi ad un Giocatore (Solo God)", "params": {"id": {"name": "id", "help": "ID Giocatore"}, "permission": {"name": "permessi", "help": "<PERSON><PERSON>"}}}, "removepermission": {"help": "R<PERSON><PERSON><PERSON> i permessi ad un Giocatore (Solo God)", "params": {"id": {"name": "id", "help": "ID Giocatore"}, "permission": {"name": "permessi", "help": "<PERSON><PERSON>"}}}, "openserver": {"help": "<PERSON>i il server a tutti (Solo Admin)"}, "closeserver": {"help": "<PERSON><PERSON> il server e rendilo accessibile solo a chi ha i permessi (Solo Admin)", "params": {"reason": {"name": "motivo", "help": "Motivo di chiusura del server (opzionale)"}}}, "car": {"help": "<PERSON><PERSON><PERSON> (Solo Admin)", "params": {"model": {"name": "modello", "help": "Nome del veicolo"}}}, "dv": {"help": "<PERSON><PERSON> (Solo Admin)"}, "givemoney": {"help": "Dai soldi ad un Giocatore (Solo Admin)", "params": {"id": {"name": "id", "help": "ID Giocatore"}, "moneytype": {"name": "tipo", "help": "Tipo di soldi (cash, bank, crypto)"}, "amount": {"name": "importo", "help": "Importo"}}}, "setmoney": {"help": "Imposta i soldi ad un Giocatore (Solo Admin)", "params": {"id": {"name": "id", "help": "ID Giocatore"}, "moneytype": {"name": "tipo", "help": "Tipo di soldi (cash, bank, crypto)"}, "amount": {"name": "importo", "help": "Importo"}}}, "job": {"help": "Controlla il tuo Lavoro"}, "setjob": {"help": "Imposta Lavoro ad un Giocatore (Solo Admin)", "params": {"id": {"name": "id", "help": "ID Giocatore"}, "job": {"name": "lavoro", "help": "Nome La<PERSON>o"}, "grade": {"name": "grado", "help": "Grado"}}}, "changejob": {"help": "Change Active Job of Player (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}}}, "addjob": {"help": "Add Job to Player (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}, "grade": {"name": "grade", "help": "Job grade"}}}, "removejob": {"help": "Re<PERSON><PERSON> Job from Player (Admin Only)", "params": {"id": {"name": "id", "help": "Player ID"}, "job": {"name": "job", "help": "Job name"}}}, "gang": {"help": "Controlla la tua Fazione"}, "setgang": {"help": "Imposta Fazione ad un Giocatore (Solo Admin)", "params": {"id": {"name": "id", "help": "ID Giocatore"}, "gang": {"name": "fazione", "help": "Nome Fazione"}, "grade": {"name": "grado", "help": "Grado"}}}, "ooc": {"help": "Messaggio OOC"}, "me": {"help": "Mostra Messaggio circostante", "params": {"message": {"name": "messaggio", "help": "Messaggio"}}}}}