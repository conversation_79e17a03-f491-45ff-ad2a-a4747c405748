function bA(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const l=Object.getOwnPropertyDescriptor(r,i);l&&Object.defineProperty(e,i,l.get?l:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const l of i)if(l.type==="childList")for(const s of l.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&r(s)}).observe(document,{childList:!0,subtree:!0});function n(i){const l={};return i.integrity&&(l.integrity=i.integrity),i.referrerPolicy&&(l.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?l.credentials="include":i.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function r(i){if(i.ep)return;i.ep=!0;const l=n(i);fetch(i.href,l)}})();var ku=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function io(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var fS={exports:{}},tf={},dS={exports:{}},Ne={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var _s=Symbol.for("react.element"),_A=Symbol.for("react.portal"),OA=Symbol.for("react.fragment"),TA=Symbol.for("react.strict_mode"),IA=Symbol.for("react.profiler"),RA=Symbol.for("react.provider"),PA=Symbol.for("react.context"),AA=Symbol.for("react.forward_ref"),DA=Symbol.for("react.suspense"),NA=Symbol.for("react.memo"),MA=Symbol.for("react.lazy"),p1=Symbol.iterator;function LA(e){return e===null||typeof e!="object"?null:(e=p1&&e[p1]||e["@@iterator"],typeof e=="function"?e:null)}var pS={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},hS=Object.assign,gS={};function Jl(e,t,n){this.props=e,this.context=t,this.refs=gS,this.updater=n||pS}Jl.prototype.isReactComponent={};Jl.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Jl.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function mS(){}mS.prototype=Jl.prototype;function Ug(e,t,n){this.props=e,this.context=t,this.refs=gS,this.updater=n||pS}var Hg=Ug.prototype=new mS;Hg.constructor=Ug;hS(Hg,Jl.prototype);Hg.isPureReactComponent=!0;var h1=Array.isArray,vS=Object.prototype.hasOwnProperty,jg={current:null},yS={key:!0,ref:!0,__self:!0,__source:!0};function wS(e,t,n){var r,i={},l=null,s=null;if(t!=null)for(r in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(l=""+t.key),t)vS.call(t,r)&&!yS.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var f=Array(a),d=0;d<a;d++)f[d]=arguments[d+2];i.children=f}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:_s,type:e,key:l,ref:s,props:i,_owner:jg.current}}function FA(e,t){return{$$typeof:_s,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Wg(e){return typeof e=="object"&&e!==null&&e.$$typeof===_s}function zA(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var g1=/\/+/g;function dp(e,t){return typeof e=="object"&&e!==null&&e.key!=null?zA(""+e.key):t.toString(36)}function Ya(e,t,n,r,i){var l=typeof e;(l==="undefined"||l==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(l){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case _s:case _A:s=!0}}if(s)return s=e,i=i(s),e=r===""?"."+dp(s,0):r,h1(i)?(n="",e!=null&&(n=e.replace(g1,"$&/")+"/"),Ya(i,t,n,"",function(d){return d})):i!=null&&(Wg(i)&&(i=FA(i,n+(!i.key||s&&s.key===i.key?"":(""+i.key).replace(g1,"$&/")+"/")+e)),t.push(i)),1;if(s=0,r=r===""?".":r+":",h1(e))for(var a=0;a<e.length;a++){l=e[a];var f=r+dp(l,a);s+=Ya(l,t,n,f,i)}else if(f=LA(e),typeof f=="function")for(e=f.call(e),a=0;!(l=e.next()).done;)l=l.value,f=r+dp(l,a++),s+=Ya(l,t,n,f,i);else if(l==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function ba(e,t,n){if(e==null)return e;var r=[],i=0;return Ya(e,r,"","",function(l){return t.call(n,l,i++)}),r}function $A(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var ln={current:null},Xa={transition:null},BA={ReactCurrentDispatcher:ln,ReactCurrentBatchConfig:Xa,ReactCurrentOwner:jg};function xS(){throw Error("act(...) is not supported in production builds of React.")}Ne.Children={map:ba,forEach:function(e,t,n){ba(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ba(e,function(){t++}),t},toArray:function(e){return ba(e,function(t){return t})||[]},only:function(e){if(!Wg(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Ne.Component=Jl;Ne.Fragment=OA;Ne.Profiler=IA;Ne.PureComponent=Ug;Ne.StrictMode=TA;Ne.Suspense=DA;Ne.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=BA;Ne.act=xS;Ne.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=hS({},e.props),i=e.key,l=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(l=t.ref,s=jg.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(f in t)vS.call(t,f)&&!yS.hasOwnProperty(f)&&(r[f]=t[f]===void 0&&a!==void 0?a[f]:t[f])}var f=arguments.length-2;if(f===1)r.children=n;else if(1<f){a=Array(f);for(var d=0;d<f;d++)a[d]=arguments[d+2];r.children=a}return{$$typeof:_s,type:e.type,key:i,ref:l,props:r,_owner:s}};Ne.createContext=function(e){return e={$$typeof:PA,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:RA,_context:e},e.Consumer=e};Ne.createElement=wS;Ne.createFactory=function(e){var t=wS.bind(null,e);return t.type=e,t};Ne.createRef=function(){return{current:null}};Ne.forwardRef=function(e){return{$$typeof:AA,render:e}};Ne.isValidElement=Wg;Ne.lazy=function(e){return{$$typeof:MA,_payload:{_status:-1,_result:e},_init:$A}};Ne.memo=function(e,t){return{$$typeof:NA,type:e,compare:t===void 0?null:t}};Ne.startTransition=function(e){var t=Xa.transition;Xa.transition={};try{e()}finally{Xa.transition=t}};Ne.unstable_act=xS;Ne.useCallback=function(e,t){return ln.current.useCallback(e,t)};Ne.useContext=function(e){return ln.current.useContext(e)};Ne.useDebugValue=function(){};Ne.useDeferredValue=function(e){return ln.current.useDeferredValue(e)};Ne.useEffect=function(e,t){return ln.current.useEffect(e,t)};Ne.useId=function(){return ln.current.useId()};Ne.useImperativeHandle=function(e,t,n){return ln.current.useImperativeHandle(e,t,n)};Ne.useInsertionEffect=function(e,t){return ln.current.useInsertionEffect(e,t)};Ne.useLayoutEffect=function(e,t){return ln.current.useLayoutEffect(e,t)};Ne.useMemo=function(e,t){return ln.current.useMemo(e,t)};Ne.useReducer=function(e,t,n){return ln.current.useReducer(e,t,n)};Ne.useRef=function(e){return ln.current.useRef(e)};Ne.useState=function(e){return ln.current.useState(e)};Ne.useSyncExternalStore=function(e,t,n){return ln.current.useSyncExternalStore(e,t,n)};Ne.useTransition=function(){return ln.current.useTransition()};Ne.version="18.3.1";dS.exports=Ne;var I=dS.exports;const Ve=io(I),SS=bA({__proto__:null,default:Ve},[I]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var UA=I,HA=Symbol.for("react.element"),jA=Symbol.for("react.fragment"),WA=Object.prototype.hasOwnProperty,VA=UA.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,GA={key:!0,ref:!0,__self:!0,__source:!0};function ES(e,t,n){var r,i={},l=null,s=null;n!==void 0&&(l=""+n),t.key!==void 0&&(l=""+t.key),t.ref!==void 0&&(s=t.ref);for(r in t)WA.call(t,r)&&!GA.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:HA,type:e,key:l,ref:s,props:i,_owner:VA.current}}tf.Fragment=jA;tf.jsx=ES;tf.jsxs=ES;fS.exports=tf;var Vg=fS.exports;const En=Vg.Fragment,W=Vg.jsx,we=Vg.jsxs;var CS={exports:{}},Un={},kS={exports:{}},bS={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t($,Z){var _=$.length;$.push(Z);e:for(;0<_;){var ne=_-1>>>1,ae=$[ne];if(0<i(ae,Z))$[ne]=Z,$[_]=ae,_=ne;else break e}}function n($){return $.length===0?null:$[0]}function r($){if($.length===0)return null;var Z=$[0],_=$.pop();if(_!==Z){$[0]=_;e:for(var ne=0,ae=$.length,O=ae>>>1;ne<O;){var de=2*(ne+1)-1,Re=$[de],me=de+1,Ae=$[me];if(0>i(Re,_))me<ae&&0>i(Ae,Re)?($[ne]=Ae,$[me]=_,ne=me):($[ne]=Re,$[de]=_,ne=de);else if(me<ae&&0>i(Ae,_))$[ne]=Ae,$[me]=_,ne=me;else break e}}return Z}function i($,Z){var _=$.sortIndex-Z.sortIndex;return _!==0?_:$.id-Z.id}if(typeof performance=="object"&&typeof performance.now=="function"){var l=performance;e.unstable_now=function(){return l.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var f=[],d=[],p=1,g=null,v=3,m=!1,E=!1,S=!1,A=typeof setTimeout=="function"?setTimeout:null,y=typeof clearTimeout=="function"?clearTimeout:null,x=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function k($){for(var Z=n(d);Z!==null;){if(Z.callback===null)r(d);else if(Z.startTime<=$)r(d),Z.sortIndex=Z.expirationTime,t(f,Z);else break;Z=n(d)}}function P($){if(S=!1,k($),!E)if(n(f)!==null)E=!0,le(D);else{var Z=n(d);Z!==null&&oe(P,Z.startTime-$)}}function D($,Z){E=!1,S&&(S=!1,y(z),z=-1),m=!0;var _=v;try{for(k(Z),g=n(f);g!==null&&(!(g.expirationTime>Z)||$&&!B());){var ne=g.callback;if(typeof ne=="function"){g.callback=null,v=g.priorityLevel;var ae=ne(g.expirationTime<=Z);Z=e.unstable_now(),typeof ae=="function"?g.callback=ae:g===n(f)&&r(f),k(Z)}else r(f);g=n(f)}if(g!==null)var O=!0;else{var de=n(d);de!==null&&oe(P,de.startTime-Z),O=!1}return O}finally{g=null,v=_,m=!1}}var T=!1,F=null,z=-1,G=5,q=-1;function B(){return!(e.unstable_now()-q<G)}function Q(){if(F!==null){var $=e.unstable_now();q=$;var Z=!0;try{Z=F(!0,$)}finally{Z?U():(T=!1,F=null)}}else T=!1}var U;if(typeof x=="function")U=function(){x(Q)};else if(typeof MessageChannel<"u"){var H=new MessageChannel,ee=H.port2;H.port1.onmessage=Q,U=function(){ee.postMessage(null)}}else U=function(){A(Q,0)};function le($){F=$,T||(T=!0,U())}function oe($,Z){z=A(function(){$(e.unstable_now())},Z)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function($){$.callback=null},e.unstable_continueExecution=function(){E||m||(E=!0,le(D))},e.unstable_forceFrameRate=function($){0>$||125<$?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):G=0<$?Math.floor(1e3/$):5},e.unstable_getCurrentPriorityLevel=function(){return v},e.unstable_getFirstCallbackNode=function(){return n(f)},e.unstable_next=function($){switch(v){case 1:case 2:case 3:var Z=3;break;default:Z=v}var _=v;v=Z;try{return $()}finally{v=_}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function($,Z){switch($){case 1:case 2:case 3:case 4:case 5:break;default:$=3}var _=v;v=$;try{return Z()}finally{v=_}},e.unstable_scheduleCallback=function($,Z,_){var ne=e.unstable_now();switch(typeof _=="object"&&_!==null?(_=_.delay,_=typeof _=="number"&&0<_?ne+_:ne):_=ne,$){case 1:var ae=-1;break;case 2:ae=250;break;case 5:ae=**********;break;case 4:ae=1e4;break;default:ae=5e3}return ae=_+ae,$={id:p++,callback:Z,priorityLevel:$,startTime:_,expirationTime:ae,sortIndex:-1},_>ne?($.sortIndex=_,t(d,$),n(f)===null&&$===n(d)&&(S?(y(z),z=-1):S=!0,oe(P,_-ne))):($.sortIndex=ae,t(f,$),E||m||(E=!0,le(D))),$},e.unstable_shouldYield=B,e.unstable_wrapCallback=function($){var Z=v;return function(){var _=v;v=Z;try{return $.apply(this,arguments)}finally{v=_}}}})(bS);kS.exports=bS;var qA=kS.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var KA=I,$n=qA;function te(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var _S=new Set,is={};function Ho(e,t){zl(e,t),zl(e+"Capture",t)}function zl(e,t){for(is[e]=t,e=0;e<t.length;e++)_S.add(t[e])}var ci=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),wh=Object.prototype.hasOwnProperty,QA=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,m1={},v1={};function YA(e){return wh.call(v1,e)?!0:wh.call(m1,e)?!1:QA.test(e)?v1[e]=!0:(m1[e]=!0,!1)}function XA(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function ZA(e,t,n,r){if(t===null||typeof t>"u"||XA(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function un(e,t,n,r,i,l,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=l,this.removeEmptyString=s}var Ut={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ut[e]=new un(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ut[t]=new un(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ut[e]=new un(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ut[e]=new un(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ut[e]=new un(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ut[e]=new un(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ut[e]=new un(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ut[e]=new un(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ut[e]=new un(e,5,!1,e.toLowerCase(),null,!1,!1)});var Gg=/[\-:]([a-z])/g;function qg(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Gg,qg);Ut[t]=new un(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Gg,qg);Ut[t]=new un(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Gg,qg);Ut[t]=new un(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ut[e]=new un(e,1,!1,e.toLowerCase(),null,!1,!1)});Ut.xlinkHref=new un("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ut[e]=new un(e,1,!1,e.toLowerCase(),null,!0,!0)});function Kg(e,t,n,r){var i=Ut.hasOwnProperty(t)?Ut[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(ZA(t,n,i,r)&&(n=null),r||i===null?YA(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var gi=KA.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,_a=Symbol.for("react.element"),pl=Symbol.for("react.portal"),hl=Symbol.for("react.fragment"),Qg=Symbol.for("react.strict_mode"),xh=Symbol.for("react.profiler"),OS=Symbol.for("react.provider"),TS=Symbol.for("react.context"),Yg=Symbol.for("react.forward_ref"),Sh=Symbol.for("react.suspense"),Eh=Symbol.for("react.suspense_list"),Xg=Symbol.for("react.memo"),Ai=Symbol.for("react.lazy"),IS=Symbol.for("react.offscreen"),y1=Symbol.iterator;function bu(e){return e===null||typeof e!="object"?null:(e=y1&&e[y1]||e["@@iterator"],typeof e=="function"?e:null)}var vt=Object.assign,pp;function Mu(e){if(pp===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);pp=t&&t[1]||""}return`
`+pp+e}var hp=!1;function gp(e,t){if(!e||hp)return"";hp=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(d){var r=d}Reflect.construct(e,[],t)}else{try{t.call()}catch(d){r=d}e.call(t.prototype)}else{try{throw Error()}catch(d){r=d}e()}}catch(d){if(d&&r&&typeof d.stack=="string"){for(var i=d.stack.split(`
`),l=r.stack.split(`
`),s=i.length-1,a=l.length-1;1<=s&&0<=a&&i[s]!==l[a];)a--;for(;1<=s&&0<=a;s--,a--)if(i[s]!==l[a]){if(s!==1||a!==1)do if(s--,a--,0>a||i[s]!==l[a]){var f=`
`+i[s].replace(" at new "," at ");return e.displayName&&f.includes("<anonymous>")&&(f=f.replace("<anonymous>",e.displayName)),f}while(1<=s&&0<=a);break}}}finally{hp=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Mu(e):""}function JA(e){switch(e.tag){case 5:return Mu(e.type);case 16:return Mu("Lazy");case 13:return Mu("Suspense");case 19:return Mu("SuspenseList");case 0:case 2:case 15:return e=gp(e.type,!1),e;case 11:return e=gp(e.type.render,!1),e;case 1:return e=gp(e.type,!0),e;default:return""}}function Ch(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case hl:return"Fragment";case pl:return"Portal";case xh:return"Profiler";case Qg:return"StrictMode";case Sh:return"Suspense";case Eh:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case TS:return(e.displayName||"Context")+".Consumer";case OS:return(e._context.displayName||"Context")+".Provider";case Yg:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Xg:return t=e.displayName||null,t!==null?t:Ch(e.type)||"Memo";case Ai:t=e._payload,e=e._init;try{return Ch(e(t))}catch{}}return null}function eD(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ch(t);case 8:return t===Qg?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Ji(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function RS(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function tD(e){var t=RS(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,l=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(s){r=""+s,l.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(s){r=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Oa(e){e._valueTracker||(e._valueTracker=tD(e))}function PS(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=RS(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function pc(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function kh(e,t){var n=t.checked;return vt({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function w1(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=Ji(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function AS(e,t){t=t.checked,t!=null&&Kg(e,"checked",t,!1)}function bh(e,t){AS(e,t);var n=Ji(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?_h(e,t.type,n):t.hasOwnProperty("defaultValue")&&_h(e,t.type,Ji(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function x1(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function _h(e,t,n){(t!=="number"||pc(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Lu=Array.isArray;function bl(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+Ji(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function Oh(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(te(91));return vt({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function S1(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(te(92));if(Lu(n)){if(1<n.length)throw Error(te(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Ji(n)}}function DS(e,t){var n=Ji(t.value),r=Ji(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function E1(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function NS(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Th(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?NS(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Ta,MS=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Ta=Ta||document.createElement("div"),Ta.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Ta.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function os(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Hu={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},nD=["Webkit","ms","Moz","O"];Object.keys(Hu).forEach(function(e){nD.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Hu[t]=Hu[e]})});function LS(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Hu.hasOwnProperty(e)&&Hu[e]?(""+t).trim():t+"px"}function FS(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=LS(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var rD=vt({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ih(e,t){if(t){if(rD[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(te(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(te(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(te(61))}if(t.style!=null&&typeof t.style!="object")throw Error(te(62))}}function Rh(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ph=null;function Zg(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Ah=null,_l=null,Ol=null;function C1(e){if(e=Is(e)){if(typeof Ah!="function")throw Error(te(280));var t=e.stateNode;t&&(t=uf(t),Ah(e.stateNode,e.type,t))}}function zS(e){_l?Ol?Ol.push(e):Ol=[e]:_l=e}function $S(){if(_l){var e=_l,t=Ol;if(Ol=_l=null,C1(e),t)for(e=0;e<t.length;e++)C1(t[e])}}function BS(e,t){return e(t)}function US(){}var mp=!1;function HS(e,t,n){if(mp)return e(t,n);mp=!0;try{return BS(e,t,n)}finally{mp=!1,(_l!==null||Ol!==null)&&(US(),$S())}}function ls(e,t){var n=e.stateNode;if(n===null)return null;var r=uf(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(te(231,t,typeof n));return n}var Dh=!1;if(ci)try{var _u={};Object.defineProperty(_u,"passive",{get:function(){Dh=!0}}),window.addEventListener("test",_u,_u),window.removeEventListener("test",_u,_u)}catch{Dh=!1}function iD(e,t,n,r,i,l,s,a,f){var d=Array.prototype.slice.call(arguments,3);try{t.apply(n,d)}catch(p){this.onError(p)}}var ju=!1,hc=null,gc=!1,Nh=null,oD={onError:function(e){ju=!0,hc=e}};function lD(e,t,n,r,i,l,s,a,f){ju=!1,hc=null,iD.apply(oD,arguments)}function uD(e,t,n,r,i,l,s,a,f){if(lD.apply(this,arguments),ju){if(ju){var d=hc;ju=!1,hc=null}else throw Error(te(198));gc||(gc=!0,Nh=d)}}function jo(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function jS(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function k1(e){if(jo(e)!==e)throw Error(te(188))}function sD(e){var t=e.alternate;if(!t){if(t=jo(e),t===null)throw Error(te(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var l=i.alternate;if(l===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===l.child){for(l=i.child;l;){if(l===n)return k1(i),e;if(l===r)return k1(i),t;l=l.sibling}throw Error(te(188))}if(n.return!==r.return)n=i,r=l;else{for(var s=!1,a=i.child;a;){if(a===n){s=!0,n=i,r=l;break}if(a===r){s=!0,r=i,n=l;break}a=a.sibling}if(!s){for(a=l.child;a;){if(a===n){s=!0,n=l,r=i;break}if(a===r){s=!0,r=l,n=i;break}a=a.sibling}if(!s)throw Error(te(189))}}if(n.alternate!==r)throw Error(te(190))}if(n.tag!==3)throw Error(te(188));return n.stateNode.current===n?e:t}function WS(e){return e=sD(e),e!==null?VS(e):null}function VS(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=VS(e);if(t!==null)return t;e=e.sibling}return null}var GS=$n.unstable_scheduleCallback,b1=$n.unstable_cancelCallback,aD=$n.unstable_shouldYield,cD=$n.unstable_requestPaint,Ct=$n.unstable_now,fD=$n.unstable_getCurrentPriorityLevel,Jg=$n.unstable_ImmediatePriority,qS=$n.unstable_UserBlockingPriority,mc=$n.unstable_NormalPriority,dD=$n.unstable_LowPriority,KS=$n.unstable_IdlePriority,nf=null,$r=null;function pD(e){if($r&&typeof $r.onCommitFiberRoot=="function")try{$r.onCommitFiberRoot(nf,e,void 0,(e.current.flags&128)===128)}catch{}}var Cr=Math.clz32?Math.clz32:mD,hD=Math.log,gD=Math.LN2;function mD(e){return e>>>=0,e===0?32:31-(hD(e)/gD|0)|0}var Ia=64,Ra=4194304;function Fu(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function vc(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,l=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~i;a!==0?r=Fu(a):(l&=s,l!==0&&(r=Fu(l)))}else s=n&~i,s!==0?r=Fu(s):l!==0&&(r=Fu(l));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,l=t&-t,i>=l||i===16&&(l&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Cr(t),i=1<<n,r|=e[n],t&=~i;return r}function vD(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function yD(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,l=e.pendingLanes;0<l;){var s=31-Cr(l),a=1<<s,f=i[s];f===-1?(!(a&n)||a&r)&&(i[s]=vD(a,t)):f<=t&&(e.expiredLanes|=a),l&=~a}}function Mh(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function QS(){var e=Ia;return Ia<<=1,!(Ia&4194240)&&(Ia=64),e}function vp(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Os(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Cr(t),e[t]=n}function wD(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-Cr(n),l=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~l}}function em(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Cr(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var Ye=0;function YS(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var XS,tm,ZS,JS,eE,Lh=!1,Pa=[],Bi=null,Ui=null,Hi=null,us=new Map,ss=new Map,Ni=[],xD="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function _1(e,t){switch(e){case"focusin":case"focusout":Bi=null;break;case"dragenter":case"dragleave":Ui=null;break;case"mouseover":case"mouseout":Hi=null;break;case"pointerover":case"pointerout":us.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ss.delete(t.pointerId)}}function Ou(e,t,n,r,i,l){return e===null||e.nativeEvent!==l?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:l,targetContainers:[i]},t!==null&&(t=Is(t),t!==null&&tm(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function SD(e,t,n,r,i){switch(t){case"focusin":return Bi=Ou(Bi,e,t,n,r,i),!0;case"dragenter":return Ui=Ou(Ui,e,t,n,r,i),!0;case"mouseover":return Hi=Ou(Hi,e,t,n,r,i),!0;case"pointerover":var l=i.pointerId;return us.set(l,Ou(us.get(l)||null,e,t,n,r,i)),!0;case"gotpointercapture":return l=i.pointerId,ss.set(l,Ou(ss.get(l)||null,e,t,n,r,i)),!0}return!1}function tE(e){var t=_o(e.target);if(t!==null){var n=jo(t);if(n!==null){if(t=n.tag,t===13){if(t=jS(n),t!==null){e.blockedOn=t,eE(e.priority,function(){ZS(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Za(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Fh(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);Ph=r,n.target.dispatchEvent(r),Ph=null}else return t=Is(n),t!==null&&tm(t),e.blockedOn=n,!1;t.shift()}return!0}function O1(e,t,n){Za(e)&&n.delete(t)}function ED(){Lh=!1,Bi!==null&&Za(Bi)&&(Bi=null),Ui!==null&&Za(Ui)&&(Ui=null),Hi!==null&&Za(Hi)&&(Hi=null),us.forEach(O1),ss.forEach(O1)}function Tu(e,t){e.blockedOn===t&&(e.blockedOn=null,Lh||(Lh=!0,$n.unstable_scheduleCallback($n.unstable_NormalPriority,ED)))}function as(e){function t(i){return Tu(i,e)}if(0<Pa.length){Tu(Pa[0],e);for(var n=1;n<Pa.length;n++){var r=Pa[n];r.blockedOn===e&&(r.blockedOn=null)}}for(Bi!==null&&Tu(Bi,e),Ui!==null&&Tu(Ui,e),Hi!==null&&Tu(Hi,e),us.forEach(t),ss.forEach(t),n=0;n<Ni.length;n++)r=Ni[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Ni.length&&(n=Ni[0],n.blockedOn===null);)tE(n),n.blockedOn===null&&Ni.shift()}var Tl=gi.ReactCurrentBatchConfig,yc=!0;function CD(e,t,n,r){var i=Ye,l=Tl.transition;Tl.transition=null;try{Ye=1,nm(e,t,n,r)}finally{Ye=i,Tl.transition=l}}function kD(e,t,n,r){var i=Ye,l=Tl.transition;Tl.transition=null;try{Ye=4,nm(e,t,n,r)}finally{Ye=i,Tl.transition=l}}function nm(e,t,n,r){if(yc){var i=Fh(e,t,n,r);if(i===null)Op(e,t,r,wc,n),_1(e,r);else if(SD(i,e,t,n,r))r.stopPropagation();else if(_1(e,r),t&4&&-1<xD.indexOf(e)){for(;i!==null;){var l=Is(i);if(l!==null&&XS(l),l=Fh(e,t,n,r),l===null&&Op(e,t,r,wc,n),l===i)break;i=l}i!==null&&r.stopPropagation()}else Op(e,t,r,null,n)}}var wc=null;function Fh(e,t,n,r){if(wc=null,e=Zg(r),e=_o(e),e!==null)if(t=jo(e),t===null)e=null;else if(n=t.tag,n===13){if(e=jS(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return wc=e,null}function nE(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(fD()){case Jg:return 1;case qS:return 4;case mc:case dD:return 16;case KS:return 536870912;default:return 16}default:return 16}}var zi=null,rm=null,Ja=null;function rE(){if(Ja)return Ja;var e,t=rm,n=t.length,r,i="value"in zi?zi.value:zi.textContent,l=i.length;for(e=0;e<n&&t[e]===i[e];e++);var s=n-e;for(r=1;r<=s&&t[n-r]===i[l-r];r++);return Ja=i.slice(e,1<r?1-r:void 0)}function ec(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Aa(){return!0}function T1(){return!1}function Hn(e){function t(n,r,i,l,s){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=l,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(l):l[a]);return this.isDefaultPrevented=(l.defaultPrevented!=null?l.defaultPrevented:l.returnValue===!1)?Aa:T1,this.isPropagationStopped=T1,this}return vt(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Aa)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Aa)},persist:function(){},isPersistent:Aa}),t}var eu={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},im=Hn(eu),Ts=vt({},eu,{view:0,detail:0}),bD=Hn(Ts),yp,wp,Iu,rf=vt({},Ts,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:om,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Iu&&(Iu&&e.type==="mousemove"?(yp=e.screenX-Iu.screenX,wp=e.screenY-Iu.screenY):wp=yp=0,Iu=e),yp)},movementY:function(e){return"movementY"in e?e.movementY:wp}}),I1=Hn(rf),_D=vt({},rf,{dataTransfer:0}),OD=Hn(_D),TD=vt({},Ts,{relatedTarget:0}),xp=Hn(TD),ID=vt({},eu,{animationName:0,elapsedTime:0,pseudoElement:0}),RD=Hn(ID),PD=vt({},eu,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),AD=Hn(PD),DD=vt({},eu,{data:0}),R1=Hn(DD),ND={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},MD={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},LD={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function FD(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=LD[e])?!!t[e]:!1}function om(){return FD}var zD=vt({},Ts,{key:function(e){if(e.key){var t=ND[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ec(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?MD[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:om,charCode:function(e){return e.type==="keypress"?ec(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ec(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),$D=Hn(zD),BD=vt({},rf,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),P1=Hn(BD),UD=vt({},Ts,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:om}),HD=Hn(UD),jD=vt({},eu,{propertyName:0,elapsedTime:0,pseudoElement:0}),WD=Hn(jD),VD=vt({},rf,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),GD=Hn(VD),qD=[9,13,27,32],lm=ci&&"CompositionEvent"in window,Wu=null;ci&&"documentMode"in document&&(Wu=document.documentMode);var KD=ci&&"TextEvent"in window&&!Wu,iE=ci&&(!lm||Wu&&8<Wu&&11>=Wu),A1=String.fromCharCode(32),D1=!1;function oE(e,t){switch(e){case"keyup":return qD.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function lE(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var gl=!1;function QD(e,t){switch(e){case"compositionend":return lE(t);case"keypress":return t.which!==32?null:(D1=!0,A1);case"textInput":return e=t.data,e===A1&&D1?null:e;default:return null}}function YD(e,t){if(gl)return e==="compositionend"||!lm&&oE(e,t)?(e=rE(),Ja=rm=zi=null,gl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return iE&&t.locale!=="ko"?null:t.data;default:return null}}var XD={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function N1(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!XD[e.type]:t==="textarea"}function uE(e,t,n,r){zS(r),t=xc(t,"onChange"),0<t.length&&(n=new im("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Vu=null,cs=null;function ZD(e){yE(e,0)}function of(e){var t=yl(e);if(PS(t))return e}function JD(e,t){if(e==="change")return t}var sE=!1;if(ci){var Sp;if(ci){var Ep="oninput"in document;if(!Ep){var M1=document.createElement("div");M1.setAttribute("oninput","return;"),Ep=typeof M1.oninput=="function"}Sp=Ep}else Sp=!1;sE=Sp&&(!document.documentMode||9<document.documentMode)}function L1(){Vu&&(Vu.detachEvent("onpropertychange",aE),cs=Vu=null)}function aE(e){if(e.propertyName==="value"&&of(cs)){var t=[];uE(t,cs,e,Zg(e)),HS(ZD,t)}}function eN(e,t,n){e==="focusin"?(L1(),Vu=t,cs=n,Vu.attachEvent("onpropertychange",aE)):e==="focusout"&&L1()}function tN(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return of(cs)}function nN(e,t){if(e==="click")return of(t)}function rN(e,t){if(e==="input"||e==="change")return of(t)}function iN(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var br=typeof Object.is=="function"?Object.is:iN;function fs(e,t){if(br(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!wh.call(t,i)||!br(e[i],t[i]))return!1}return!0}function F1(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function z1(e,t){var n=F1(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=F1(n)}}function cE(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?cE(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function fE(){for(var e=window,t=pc();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=pc(e.document)}return t}function um(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function oN(e){var t=fE(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&cE(n.ownerDocument.documentElement,n)){if(r!==null&&um(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,l=Math.min(r.start,i);r=r.end===void 0?l:Math.min(r.end,i),!e.extend&&l>r&&(i=r,r=l,l=i),i=z1(n,l);var s=z1(n,r);i&&s&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),l>r?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var lN=ci&&"documentMode"in document&&11>=document.documentMode,ml=null,zh=null,Gu=null,$h=!1;function $1(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;$h||ml==null||ml!==pc(r)||(r=ml,"selectionStart"in r&&um(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),Gu&&fs(Gu,r)||(Gu=r,r=xc(zh,"onSelect"),0<r.length&&(t=new im("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=ml)))}function Da(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var vl={animationend:Da("Animation","AnimationEnd"),animationiteration:Da("Animation","AnimationIteration"),animationstart:Da("Animation","AnimationStart"),transitionend:Da("Transition","TransitionEnd")},Cp={},dE={};ci&&(dE=document.createElement("div").style,"AnimationEvent"in window||(delete vl.animationend.animation,delete vl.animationiteration.animation,delete vl.animationstart.animation),"TransitionEvent"in window||delete vl.transitionend.transition);function lf(e){if(Cp[e])return Cp[e];if(!vl[e])return e;var t=vl[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in dE)return Cp[e]=t[n];return e}var pE=lf("animationend"),hE=lf("animationiteration"),gE=lf("animationstart"),mE=lf("transitionend"),vE=new Map,B1="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function oo(e,t){vE.set(e,t),Ho(t,[e])}for(var kp=0;kp<B1.length;kp++){var bp=B1[kp],uN=bp.toLowerCase(),sN=bp[0].toUpperCase()+bp.slice(1);oo(uN,"on"+sN)}oo(pE,"onAnimationEnd");oo(hE,"onAnimationIteration");oo(gE,"onAnimationStart");oo("dblclick","onDoubleClick");oo("focusin","onFocus");oo("focusout","onBlur");oo(mE,"onTransitionEnd");zl("onMouseEnter",["mouseout","mouseover"]);zl("onMouseLeave",["mouseout","mouseover"]);zl("onPointerEnter",["pointerout","pointerover"]);zl("onPointerLeave",["pointerout","pointerover"]);Ho("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Ho("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Ho("onBeforeInput",["compositionend","keypress","textInput","paste"]);Ho("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Ho("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Ho("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var zu="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),aN=new Set("cancel close invalid load scroll toggle".split(" ").concat(zu));function U1(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,uD(r,t,void 0,e),e.currentTarget=null}function yE(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var l=void 0;if(t)for(var s=r.length-1;0<=s;s--){var a=r[s],f=a.instance,d=a.currentTarget;if(a=a.listener,f!==l&&i.isPropagationStopped())break e;U1(i,a,d),l=f}else for(s=0;s<r.length;s++){if(a=r[s],f=a.instance,d=a.currentTarget,a=a.listener,f!==l&&i.isPropagationStopped())break e;U1(i,a,d),l=f}}}if(gc)throw e=Nh,gc=!1,Nh=null,e}function lt(e,t){var n=t[Wh];n===void 0&&(n=t[Wh]=new Set);var r=e+"__bubble";n.has(r)||(wE(t,e,2,!1),n.add(r))}function _p(e,t,n){var r=0;t&&(r|=4),wE(n,e,r,t)}var Na="_reactListening"+Math.random().toString(36).slice(2);function ds(e){if(!e[Na]){e[Na]=!0,_S.forEach(function(n){n!=="selectionchange"&&(aN.has(n)||_p(n,!1,e),_p(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Na]||(t[Na]=!0,_p("selectionchange",!1,t))}}function wE(e,t,n,r){switch(nE(t)){case 1:var i=CD;break;case 4:i=kD;break;default:i=nm}n=i.bind(null,t,n,e),i=void 0,!Dh||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Op(e,t,n,r,i){var l=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var s=r.tag;if(s===3||s===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(s===4)for(s=r.return;s!==null;){var f=s.tag;if((f===3||f===4)&&(f=s.stateNode.containerInfo,f===i||f.nodeType===8&&f.parentNode===i))return;s=s.return}for(;a!==null;){if(s=_o(a),s===null)return;if(f=s.tag,f===5||f===6){r=l=s;continue e}a=a.parentNode}}r=r.return}HS(function(){var d=l,p=Zg(n),g=[];e:{var v=vE.get(e);if(v!==void 0){var m=im,E=e;switch(e){case"keypress":if(ec(n)===0)break e;case"keydown":case"keyup":m=$D;break;case"focusin":E="focus",m=xp;break;case"focusout":E="blur",m=xp;break;case"beforeblur":case"afterblur":m=xp;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":m=I1;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":m=OD;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":m=HD;break;case pE:case hE:case gE:m=RD;break;case mE:m=WD;break;case"scroll":m=bD;break;case"wheel":m=GD;break;case"copy":case"cut":case"paste":m=AD;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":m=P1}var S=(t&4)!==0,A=!S&&e==="scroll",y=S?v!==null?v+"Capture":null:v;S=[];for(var x=d,k;x!==null;){k=x;var P=k.stateNode;if(k.tag===5&&P!==null&&(k=P,y!==null&&(P=ls(x,y),P!=null&&S.push(ps(x,P,k)))),A)break;x=x.return}0<S.length&&(v=new m(v,E,null,n,p),g.push({event:v,listeners:S}))}}if(!(t&7)){e:{if(v=e==="mouseover"||e==="pointerover",m=e==="mouseout"||e==="pointerout",v&&n!==Ph&&(E=n.relatedTarget||n.fromElement)&&(_o(E)||E[fi]))break e;if((m||v)&&(v=p.window===p?p:(v=p.ownerDocument)?v.defaultView||v.parentWindow:window,m?(E=n.relatedTarget||n.toElement,m=d,E=E?_o(E):null,E!==null&&(A=jo(E),E!==A||E.tag!==5&&E.tag!==6)&&(E=null)):(m=null,E=d),m!==E)){if(S=I1,P="onMouseLeave",y="onMouseEnter",x="mouse",(e==="pointerout"||e==="pointerover")&&(S=P1,P="onPointerLeave",y="onPointerEnter",x="pointer"),A=m==null?v:yl(m),k=E==null?v:yl(E),v=new S(P,x+"leave",m,n,p),v.target=A,v.relatedTarget=k,P=null,_o(p)===d&&(S=new S(y,x+"enter",E,n,p),S.target=k,S.relatedTarget=A,P=S),A=P,m&&E)t:{for(S=m,y=E,x=0,k=S;k;k=ul(k))x++;for(k=0,P=y;P;P=ul(P))k++;for(;0<x-k;)S=ul(S),x--;for(;0<k-x;)y=ul(y),k--;for(;x--;){if(S===y||y!==null&&S===y.alternate)break t;S=ul(S),y=ul(y)}S=null}else S=null;m!==null&&H1(g,v,m,S,!1),E!==null&&A!==null&&H1(g,A,E,S,!0)}}e:{if(v=d?yl(d):window,m=v.nodeName&&v.nodeName.toLowerCase(),m==="select"||m==="input"&&v.type==="file")var D=JD;else if(N1(v))if(sE)D=rN;else{D=tN;var T=eN}else(m=v.nodeName)&&m.toLowerCase()==="input"&&(v.type==="checkbox"||v.type==="radio")&&(D=nN);if(D&&(D=D(e,d))){uE(g,D,n,p);break e}T&&T(e,v,d),e==="focusout"&&(T=v._wrapperState)&&T.controlled&&v.type==="number"&&_h(v,"number",v.value)}switch(T=d?yl(d):window,e){case"focusin":(N1(T)||T.contentEditable==="true")&&(ml=T,zh=d,Gu=null);break;case"focusout":Gu=zh=ml=null;break;case"mousedown":$h=!0;break;case"contextmenu":case"mouseup":case"dragend":$h=!1,$1(g,n,p);break;case"selectionchange":if(lN)break;case"keydown":case"keyup":$1(g,n,p)}var F;if(lm)e:{switch(e){case"compositionstart":var z="onCompositionStart";break e;case"compositionend":z="onCompositionEnd";break e;case"compositionupdate":z="onCompositionUpdate";break e}z=void 0}else gl?oE(e,n)&&(z="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(z="onCompositionStart");z&&(iE&&n.locale!=="ko"&&(gl||z!=="onCompositionStart"?z==="onCompositionEnd"&&gl&&(F=rE()):(zi=p,rm="value"in zi?zi.value:zi.textContent,gl=!0)),T=xc(d,z),0<T.length&&(z=new R1(z,e,null,n,p),g.push({event:z,listeners:T}),F?z.data=F:(F=lE(n),F!==null&&(z.data=F)))),(F=KD?QD(e,n):YD(e,n))&&(d=xc(d,"onBeforeInput"),0<d.length&&(p=new R1("onBeforeInput","beforeinput",null,n,p),g.push({event:p,listeners:d}),p.data=F))}yE(g,t)})}function ps(e,t,n){return{instance:e,listener:t,currentTarget:n}}function xc(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,l=i.stateNode;i.tag===5&&l!==null&&(i=l,l=ls(e,n),l!=null&&r.unshift(ps(e,l,i)),l=ls(e,t),l!=null&&r.push(ps(e,l,i))),e=e.return}return r}function ul(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function H1(e,t,n,r,i){for(var l=t._reactName,s=[];n!==null&&n!==r;){var a=n,f=a.alternate,d=a.stateNode;if(f!==null&&f===r)break;a.tag===5&&d!==null&&(a=d,i?(f=ls(n,l),f!=null&&s.unshift(ps(n,f,a))):i||(f=ls(n,l),f!=null&&s.push(ps(n,f,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var cN=/\r\n?/g,fN=/\u0000|\uFFFD/g;function j1(e){return(typeof e=="string"?e:""+e).replace(cN,`
`).replace(fN,"")}function Ma(e,t,n){if(t=j1(t),j1(e)!==t&&n)throw Error(te(425))}function Sc(){}var Bh=null,Uh=null;function Hh(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var jh=typeof setTimeout=="function"?setTimeout:void 0,dN=typeof clearTimeout=="function"?clearTimeout:void 0,W1=typeof Promise=="function"?Promise:void 0,pN=typeof queueMicrotask=="function"?queueMicrotask:typeof W1<"u"?function(e){return W1.resolve(null).then(e).catch(hN)}:jh;function hN(e){setTimeout(function(){throw e})}function Tp(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),as(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);as(t)}function ji(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function V1(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var tu=Math.random().toString(36).slice(2),Lr="__reactFiber$"+tu,hs="__reactProps$"+tu,fi="__reactContainer$"+tu,Wh="__reactEvents$"+tu,gN="__reactListeners$"+tu,mN="__reactHandles$"+tu;function _o(e){var t=e[Lr];if(t)return t;for(var n=e.parentNode;n;){if(t=n[fi]||n[Lr]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=V1(e);e!==null;){if(n=e[Lr])return n;e=V1(e)}return t}e=n,n=e.parentNode}return null}function Is(e){return e=e[Lr]||e[fi],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function yl(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(te(33))}function uf(e){return e[hs]||null}var Vh=[],wl=-1;function lo(e){return{current:e}}function st(e){0>wl||(e.current=Vh[wl],Vh[wl]=null,wl--)}function nt(e,t){wl++,Vh[wl]=e.current,e.current=t}var eo={},Xt=lo(eo),yn=lo(!1),Do=eo;function $l(e,t){var n=e.type.contextTypes;if(!n)return eo;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},l;for(l in n)i[l]=t[l];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function wn(e){return e=e.childContextTypes,e!=null}function Ec(){st(yn),st(Xt)}function G1(e,t,n){if(Xt.current!==eo)throw Error(te(168));nt(Xt,t),nt(yn,n)}function xE(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(te(108,eD(e)||"Unknown",i));return vt({},n,r)}function Cc(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||eo,Do=Xt.current,nt(Xt,e),nt(yn,yn.current),!0}function q1(e,t,n){var r=e.stateNode;if(!r)throw Error(te(169));n?(e=xE(e,t,Do),r.__reactInternalMemoizedMergedChildContext=e,st(yn),st(Xt),nt(Xt,e)):st(yn),nt(yn,n)}var li=null,sf=!1,Ip=!1;function SE(e){li===null?li=[e]:li.push(e)}function vN(e){sf=!0,SE(e)}function uo(){if(!Ip&&li!==null){Ip=!0;var e=0,t=Ye;try{var n=li;for(Ye=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}li=null,sf=!1}catch(i){throw li!==null&&(li=li.slice(e+1)),GS(Jg,uo),i}finally{Ye=t,Ip=!1}}return null}var xl=[],Sl=0,kc=null,bc=0,tr=[],nr=0,No=null,ui=1,si="";function xo(e,t){xl[Sl++]=bc,xl[Sl++]=kc,kc=e,bc=t}function EE(e,t,n){tr[nr++]=ui,tr[nr++]=si,tr[nr++]=No,No=e;var r=ui;e=si;var i=32-Cr(r)-1;r&=~(1<<i),n+=1;var l=32-Cr(t)+i;if(30<l){var s=i-i%5;l=(r&(1<<s)-1).toString(32),r>>=s,i-=s,ui=1<<32-Cr(t)+i|n<<i|r,si=l+e}else ui=1<<l|n<<i|r,si=e}function sm(e){e.return!==null&&(xo(e,1),EE(e,1,0))}function am(e){for(;e===kc;)kc=xl[--Sl],xl[Sl]=null,bc=xl[--Sl],xl[Sl]=null;for(;e===No;)No=tr[--nr],tr[nr]=null,si=tr[--nr],tr[nr]=null,ui=tr[--nr],tr[nr]=null}var Fn=null,Mn=null,pt=!1,Sr=null;function CE(e,t){var n=or(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function K1(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Fn=e,Mn=ji(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Fn=e,Mn=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=No!==null?{id:ui,overflow:si}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=or(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Fn=e,Mn=null,!0):!1;default:return!1}}function Gh(e){return(e.mode&1)!==0&&(e.flags&128)===0}function qh(e){if(pt){var t=Mn;if(t){var n=t;if(!K1(e,t)){if(Gh(e))throw Error(te(418));t=ji(n.nextSibling);var r=Fn;t&&K1(e,t)?CE(r,n):(e.flags=e.flags&-4097|2,pt=!1,Fn=e)}}else{if(Gh(e))throw Error(te(418));e.flags=e.flags&-4097|2,pt=!1,Fn=e}}}function Q1(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Fn=e}function La(e){if(e!==Fn)return!1;if(!pt)return Q1(e),pt=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Hh(e.type,e.memoizedProps)),t&&(t=Mn)){if(Gh(e))throw kE(),Error(te(418));for(;t;)CE(e,t),t=ji(t.nextSibling)}if(Q1(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(te(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Mn=ji(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Mn=null}}else Mn=Fn?ji(e.stateNode.nextSibling):null;return!0}function kE(){for(var e=Mn;e;)e=ji(e.nextSibling)}function Bl(){Mn=Fn=null,pt=!1}function cm(e){Sr===null?Sr=[e]:Sr.push(e)}var yN=gi.ReactCurrentBatchConfig;function Ru(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(te(309));var r=n.stateNode}if(!r)throw Error(te(147,e));var i=r,l=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===l?t.ref:(t=function(s){var a=i.refs;s===null?delete a[l]:a[l]=s},t._stringRef=l,t)}if(typeof e!="string")throw Error(te(284));if(!n._owner)throw Error(te(290,e))}return e}function Fa(e,t){throw e=Object.prototype.toString.call(t),Error(te(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Y1(e){var t=e._init;return t(e._payload)}function bE(e){function t(y,x){if(e){var k=y.deletions;k===null?(y.deletions=[x],y.flags|=16):k.push(x)}}function n(y,x){if(!e)return null;for(;x!==null;)t(y,x),x=x.sibling;return null}function r(y,x){for(y=new Map;x!==null;)x.key!==null?y.set(x.key,x):y.set(x.index,x),x=x.sibling;return y}function i(y,x){return y=qi(y,x),y.index=0,y.sibling=null,y}function l(y,x,k){return y.index=k,e?(k=y.alternate,k!==null?(k=k.index,k<x?(y.flags|=2,x):k):(y.flags|=2,x)):(y.flags|=1048576,x)}function s(y){return e&&y.alternate===null&&(y.flags|=2),y}function a(y,x,k,P){return x===null||x.tag!==6?(x=Lp(k,y.mode,P),x.return=y,x):(x=i(x,k),x.return=y,x)}function f(y,x,k,P){var D=k.type;return D===hl?p(y,x,k.props.children,P,k.key):x!==null&&(x.elementType===D||typeof D=="object"&&D!==null&&D.$$typeof===Ai&&Y1(D)===x.type)?(P=i(x,k.props),P.ref=Ru(y,x,k),P.return=y,P):(P=uc(k.type,k.key,k.props,null,y.mode,P),P.ref=Ru(y,x,k),P.return=y,P)}function d(y,x,k,P){return x===null||x.tag!==4||x.stateNode.containerInfo!==k.containerInfo||x.stateNode.implementation!==k.implementation?(x=Fp(k,y.mode,P),x.return=y,x):(x=i(x,k.children||[]),x.return=y,x)}function p(y,x,k,P,D){return x===null||x.tag!==7?(x=Po(k,y.mode,P,D),x.return=y,x):(x=i(x,k),x.return=y,x)}function g(y,x,k){if(typeof x=="string"&&x!==""||typeof x=="number")return x=Lp(""+x,y.mode,k),x.return=y,x;if(typeof x=="object"&&x!==null){switch(x.$$typeof){case _a:return k=uc(x.type,x.key,x.props,null,y.mode,k),k.ref=Ru(y,null,x),k.return=y,k;case pl:return x=Fp(x,y.mode,k),x.return=y,x;case Ai:var P=x._init;return g(y,P(x._payload),k)}if(Lu(x)||bu(x))return x=Po(x,y.mode,k,null),x.return=y,x;Fa(y,x)}return null}function v(y,x,k,P){var D=x!==null?x.key:null;if(typeof k=="string"&&k!==""||typeof k=="number")return D!==null?null:a(y,x,""+k,P);if(typeof k=="object"&&k!==null){switch(k.$$typeof){case _a:return k.key===D?f(y,x,k,P):null;case pl:return k.key===D?d(y,x,k,P):null;case Ai:return D=k._init,v(y,x,D(k._payload),P)}if(Lu(k)||bu(k))return D!==null?null:p(y,x,k,P,null);Fa(y,k)}return null}function m(y,x,k,P,D){if(typeof P=="string"&&P!==""||typeof P=="number")return y=y.get(k)||null,a(x,y,""+P,D);if(typeof P=="object"&&P!==null){switch(P.$$typeof){case _a:return y=y.get(P.key===null?k:P.key)||null,f(x,y,P,D);case pl:return y=y.get(P.key===null?k:P.key)||null,d(x,y,P,D);case Ai:var T=P._init;return m(y,x,k,T(P._payload),D)}if(Lu(P)||bu(P))return y=y.get(k)||null,p(x,y,P,D,null);Fa(x,P)}return null}function E(y,x,k,P){for(var D=null,T=null,F=x,z=x=0,G=null;F!==null&&z<k.length;z++){F.index>z?(G=F,F=null):G=F.sibling;var q=v(y,F,k[z],P);if(q===null){F===null&&(F=G);break}e&&F&&q.alternate===null&&t(y,F),x=l(q,x,z),T===null?D=q:T.sibling=q,T=q,F=G}if(z===k.length)return n(y,F),pt&&xo(y,z),D;if(F===null){for(;z<k.length;z++)F=g(y,k[z],P),F!==null&&(x=l(F,x,z),T===null?D=F:T.sibling=F,T=F);return pt&&xo(y,z),D}for(F=r(y,F);z<k.length;z++)G=m(F,y,z,k[z],P),G!==null&&(e&&G.alternate!==null&&F.delete(G.key===null?z:G.key),x=l(G,x,z),T===null?D=G:T.sibling=G,T=G);return e&&F.forEach(function(B){return t(y,B)}),pt&&xo(y,z),D}function S(y,x,k,P){var D=bu(k);if(typeof D!="function")throw Error(te(150));if(k=D.call(k),k==null)throw Error(te(151));for(var T=D=null,F=x,z=x=0,G=null,q=k.next();F!==null&&!q.done;z++,q=k.next()){F.index>z?(G=F,F=null):G=F.sibling;var B=v(y,F,q.value,P);if(B===null){F===null&&(F=G);break}e&&F&&B.alternate===null&&t(y,F),x=l(B,x,z),T===null?D=B:T.sibling=B,T=B,F=G}if(q.done)return n(y,F),pt&&xo(y,z),D;if(F===null){for(;!q.done;z++,q=k.next())q=g(y,q.value,P),q!==null&&(x=l(q,x,z),T===null?D=q:T.sibling=q,T=q);return pt&&xo(y,z),D}for(F=r(y,F);!q.done;z++,q=k.next())q=m(F,y,z,q.value,P),q!==null&&(e&&q.alternate!==null&&F.delete(q.key===null?z:q.key),x=l(q,x,z),T===null?D=q:T.sibling=q,T=q);return e&&F.forEach(function(Q){return t(y,Q)}),pt&&xo(y,z),D}function A(y,x,k,P){if(typeof k=="object"&&k!==null&&k.type===hl&&k.key===null&&(k=k.props.children),typeof k=="object"&&k!==null){switch(k.$$typeof){case _a:e:{for(var D=k.key,T=x;T!==null;){if(T.key===D){if(D=k.type,D===hl){if(T.tag===7){n(y,T.sibling),x=i(T,k.props.children),x.return=y,y=x;break e}}else if(T.elementType===D||typeof D=="object"&&D!==null&&D.$$typeof===Ai&&Y1(D)===T.type){n(y,T.sibling),x=i(T,k.props),x.ref=Ru(y,T,k),x.return=y,y=x;break e}n(y,T);break}else t(y,T);T=T.sibling}k.type===hl?(x=Po(k.props.children,y.mode,P,k.key),x.return=y,y=x):(P=uc(k.type,k.key,k.props,null,y.mode,P),P.ref=Ru(y,x,k),P.return=y,y=P)}return s(y);case pl:e:{for(T=k.key;x!==null;){if(x.key===T)if(x.tag===4&&x.stateNode.containerInfo===k.containerInfo&&x.stateNode.implementation===k.implementation){n(y,x.sibling),x=i(x,k.children||[]),x.return=y,y=x;break e}else{n(y,x);break}else t(y,x);x=x.sibling}x=Fp(k,y.mode,P),x.return=y,y=x}return s(y);case Ai:return T=k._init,A(y,x,T(k._payload),P)}if(Lu(k))return E(y,x,k,P);if(bu(k))return S(y,x,k,P);Fa(y,k)}return typeof k=="string"&&k!==""||typeof k=="number"?(k=""+k,x!==null&&x.tag===6?(n(y,x.sibling),x=i(x,k),x.return=y,y=x):(n(y,x),x=Lp(k,y.mode,P),x.return=y,y=x),s(y)):n(y,x)}return A}var Ul=bE(!0),_E=bE(!1),_c=lo(null),Oc=null,El=null,fm=null;function dm(){fm=El=Oc=null}function pm(e){var t=_c.current;st(_c),e._currentValue=t}function Kh(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Il(e,t){Oc=e,fm=El=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(mn=!0),e.firstContext=null)}function sr(e){var t=e._currentValue;if(fm!==e)if(e={context:e,memoizedValue:t,next:null},El===null){if(Oc===null)throw Error(te(308));El=e,Oc.dependencies={lanes:0,firstContext:e}}else El=El.next=e;return t}var Oo=null;function hm(e){Oo===null?Oo=[e]:Oo.push(e)}function OE(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,hm(t)):(n.next=i.next,i.next=n),t.interleaved=n,di(e,r)}function di(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Di=!1;function gm(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function TE(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function ai(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Wi(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,$e&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,di(e,n)}return i=r.interleaved,i===null?(t.next=t,hm(r)):(t.next=i.next,i.next=t),r.interleaved=t,di(e,n)}function tc(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,em(e,n)}}function X1(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,l=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};l===null?i=l=s:l=l.next=s,n=n.next}while(n!==null);l===null?i=l=t:l=l.next=t}else i=l=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:l,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Tc(e,t,n,r){var i=e.updateQueue;Di=!1;var l=i.firstBaseUpdate,s=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var f=a,d=f.next;f.next=null,s===null?l=d:s.next=d,s=f;var p=e.alternate;p!==null&&(p=p.updateQueue,a=p.lastBaseUpdate,a!==s&&(a===null?p.firstBaseUpdate=d:a.next=d,p.lastBaseUpdate=f))}if(l!==null){var g=i.baseState;s=0,p=d=f=null,a=l;do{var v=a.lane,m=a.eventTime;if((r&v)===v){p!==null&&(p=p.next={eventTime:m,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var E=e,S=a;switch(v=t,m=n,S.tag){case 1:if(E=S.payload,typeof E=="function"){g=E.call(m,g,v);break e}g=E;break e;case 3:E.flags=E.flags&-65537|128;case 0:if(E=S.payload,v=typeof E=="function"?E.call(m,g,v):E,v==null)break e;g=vt({},g,v);break e;case 2:Di=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,v=i.effects,v===null?i.effects=[a]:v.push(a))}else m={eventTime:m,lane:v,tag:a.tag,payload:a.payload,callback:a.callback,next:null},p===null?(d=p=m,f=g):p=p.next=m,s|=v;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;v=a,a=v.next,v.next=null,i.lastBaseUpdate=v,i.shared.pending=null}}while(1);if(p===null&&(f=g),i.baseState=f,i.firstBaseUpdate=d,i.lastBaseUpdate=p,t=i.shared.interleaved,t!==null){i=t;do s|=i.lane,i=i.next;while(i!==t)}else l===null&&(i.shared.lanes=0);Lo|=s,e.lanes=s,e.memoizedState=g}}function Z1(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(te(191,i));i.call(r)}}}var Rs={},Br=lo(Rs),gs=lo(Rs),ms=lo(Rs);function To(e){if(e===Rs)throw Error(te(174));return e}function mm(e,t){switch(nt(ms,t),nt(gs,e),nt(Br,Rs),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Th(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Th(t,e)}st(Br),nt(Br,t)}function Hl(){st(Br),st(gs),st(ms)}function IE(e){To(ms.current);var t=To(Br.current),n=Th(t,e.type);t!==n&&(nt(gs,e),nt(Br,n))}function vm(e){gs.current===e&&(st(Br),st(gs))}var gt=lo(0);function Ic(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Rp=[];function ym(){for(var e=0;e<Rp.length;e++)Rp[e]._workInProgressVersionPrimary=null;Rp.length=0}var nc=gi.ReactCurrentDispatcher,Pp=gi.ReactCurrentBatchConfig,Mo=0,mt=null,Tt=null,At=null,Rc=!1,qu=!1,vs=0,wN=0;function Vt(){throw Error(te(321))}function wm(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!br(e[n],t[n]))return!1;return!0}function xm(e,t,n,r,i,l){if(Mo=l,mt=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,nc.current=e===null||e.memoizedState===null?CN:kN,e=n(r,i),qu){l=0;do{if(qu=!1,vs=0,25<=l)throw Error(te(301));l+=1,At=Tt=null,t.updateQueue=null,nc.current=bN,e=n(r,i)}while(qu)}if(nc.current=Pc,t=Tt!==null&&Tt.next!==null,Mo=0,At=Tt=mt=null,Rc=!1,t)throw Error(te(300));return e}function Sm(){var e=vs!==0;return vs=0,e}function Nr(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return At===null?mt.memoizedState=At=e:At=At.next=e,At}function ar(){if(Tt===null){var e=mt.alternate;e=e!==null?e.memoizedState:null}else e=Tt.next;var t=At===null?mt.memoizedState:At.next;if(t!==null)At=t,Tt=e;else{if(e===null)throw Error(te(310));Tt=e,e={memoizedState:Tt.memoizedState,baseState:Tt.baseState,baseQueue:Tt.baseQueue,queue:Tt.queue,next:null},At===null?mt.memoizedState=At=e:At=At.next=e}return At}function ys(e,t){return typeof t=="function"?t(e):t}function Ap(e){var t=ar(),n=t.queue;if(n===null)throw Error(te(311));n.lastRenderedReducer=e;var r=Tt,i=r.baseQueue,l=n.pending;if(l!==null){if(i!==null){var s=i.next;i.next=l.next,l.next=s}r.baseQueue=i=l,n.pending=null}if(i!==null){l=i.next,r=r.baseState;var a=s=null,f=null,d=l;do{var p=d.lane;if((Mo&p)===p)f!==null&&(f=f.next={lane:0,action:d.action,hasEagerState:d.hasEagerState,eagerState:d.eagerState,next:null}),r=d.hasEagerState?d.eagerState:e(r,d.action);else{var g={lane:p,action:d.action,hasEagerState:d.hasEagerState,eagerState:d.eagerState,next:null};f===null?(a=f=g,s=r):f=f.next=g,mt.lanes|=p,Lo|=p}d=d.next}while(d!==null&&d!==l);f===null?s=r:f.next=a,br(r,t.memoizedState)||(mn=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=f,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do l=i.lane,mt.lanes|=l,Lo|=l,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Dp(e){var t=ar(),n=t.queue;if(n===null)throw Error(te(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,l=t.memoizedState;if(i!==null){n.pending=null;var s=i=i.next;do l=e(l,s.action),s=s.next;while(s!==i);br(l,t.memoizedState)||(mn=!0),t.memoizedState=l,t.baseQueue===null&&(t.baseState=l),n.lastRenderedState=l}return[l,r]}function RE(){}function PE(e,t){var n=mt,r=ar(),i=t(),l=!br(r.memoizedState,i);if(l&&(r.memoizedState=i,mn=!0),r=r.queue,Em(NE.bind(null,n,r,e),[e]),r.getSnapshot!==t||l||At!==null&&At.memoizedState.tag&1){if(n.flags|=2048,ws(9,DE.bind(null,n,r,i,t),void 0,null),Dt===null)throw Error(te(349));Mo&30||AE(n,t,i)}return i}function AE(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=mt.updateQueue,t===null?(t={lastEffect:null,stores:null},mt.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function DE(e,t,n,r){t.value=n,t.getSnapshot=r,ME(t)&&LE(e)}function NE(e,t,n){return n(function(){ME(t)&&LE(e)})}function ME(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!br(e,n)}catch{return!0}}function LE(e){var t=di(e,1);t!==null&&kr(t,e,1,-1)}function J1(e){var t=Nr();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ys,lastRenderedState:e},t.queue=e,e=e.dispatch=EN.bind(null,mt,e),[t.memoizedState,e]}function ws(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=mt.updateQueue,t===null?(t={lastEffect:null,stores:null},mt.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function FE(){return ar().memoizedState}function rc(e,t,n,r){var i=Nr();mt.flags|=e,i.memoizedState=ws(1|t,n,void 0,r===void 0?null:r)}function af(e,t,n,r){var i=ar();r=r===void 0?null:r;var l=void 0;if(Tt!==null){var s=Tt.memoizedState;if(l=s.destroy,r!==null&&wm(r,s.deps)){i.memoizedState=ws(t,n,l,r);return}}mt.flags|=e,i.memoizedState=ws(1|t,n,l,r)}function ew(e,t){return rc(8390656,8,e,t)}function Em(e,t){return af(2048,8,e,t)}function zE(e,t){return af(4,2,e,t)}function $E(e,t){return af(4,4,e,t)}function BE(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function UE(e,t,n){return n=n!=null?n.concat([e]):null,af(4,4,BE.bind(null,t,e),n)}function Cm(){}function HE(e,t){var n=ar();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&wm(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function jE(e,t){var n=ar();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&wm(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function WE(e,t,n){return Mo&21?(br(n,t)||(n=QS(),mt.lanes|=n,Lo|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,mn=!0),e.memoizedState=n)}function xN(e,t){var n=Ye;Ye=n!==0&&4>n?n:4,e(!0);var r=Pp.transition;Pp.transition={};try{e(!1),t()}finally{Ye=n,Pp.transition=r}}function VE(){return ar().memoizedState}function SN(e,t,n){var r=Gi(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},GE(e))qE(t,n);else if(n=OE(e,t,n,r),n!==null){var i=on();kr(n,e,r,i),KE(n,t,r)}}function EN(e,t,n){var r=Gi(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(GE(e))qE(t,i);else{var l=e.alternate;if(e.lanes===0&&(l===null||l.lanes===0)&&(l=t.lastRenderedReducer,l!==null))try{var s=t.lastRenderedState,a=l(s,n);if(i.hasEagerState=!0,i.eagerState=a,br(a,s)){var f=t.interleaved;f===null?(i.next=i,hm(t)):(i.next=f.next,f.next=i),t.interleaved=i;return}}catch{}finally{}n=OE(e,t,i,r),n!==null&&(i=on(),kr(n,e,r,i),KE(n,t,r))}}function GE(e){var t=e.alternate;return e===mt||t!==null&&t===mt}function qE(e,t){qu=Rc=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function KE(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,em(e,n)}}var Pc={readContext:sr,useCallback:Vt,useContext:Vt,useEffect:Vt,useImperativeHandle:Vt,useInsertionEffect:Vt,useLayoutEffect:Vt,useMemo:Vt,useReducer:Vt,useRef:Vt,useState:Vt,useDebugValue:Vt,useDeferredValue:Vt,useTransition:Vt,useMutableSource:Vt,useSyncExternalStore:Vt,useId:Vt,unstable_isNewReconciler:!1},CN={readContext:sr,useCallback:function(e,t){return Nr().memoizedState=[e,t===void 0?null:t],e},useContext:sr,useEffect:ew,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,rc(4194308,4,BE.bind(null,t,e),n)},useLayoutEffect:function(e,t){return rc(4194308,4,e,t)},useInsertionEffect:function(e,t){return rc(4,2,e,t)},useMemo:function(e,t){var n=Nr();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=Nr();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=SN.bind(null,mt,e),[r.memoizedState,e]},useRef:function(e){var t=Nr();return e={current:e},t.memoizedState=e},useState:J1,useDebugValue:Cm,useDeferredValue:function(e){return Nr().memoizedState=e},useTransition:function(){var e=J1(!1),t=e[0];return e=xN.bind(null,e[1]),Nr().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=mt,i=Nr();if(pt){if(n===void 0)throw Error(te(407));n=n()}else{if(n=t(),Dt===null)throw Error(te(349));Mo&30||AE(r,t,n)}i.memoizedState=n;var l={value:n,getSnapshot:t};return i.queue=l,ew(NE.bind(null,r,l,e),[e]),r.flags|=2048,ws(9,DE.bind(null,r,l,n,t),void 0,null),n},useId:function(){var e=Nr(),t=Dt.identifierPrefix;if(pt){var n=si,r=ui;n=(r&~(1<<32-Cr(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=vs++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=wN++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},kN={readContext:sr,useCallback:HE,useContext:sr,useEffect:Em,useImperativeHandle:UE,useInsertionEffect:zE,useLayoutEffect:$E,useMemo:jE,useReducer:Ap,useRef:FE,useState:function(){return Ap(ys)},useDebugValue:Cm,useDeferredValue:function(e){var t=ar();return WE(t,Tt.memoizedState,e)},useTransition:function(){var e=Ap(ys)[0],t=ar().memoizedState;return[e,t]},useMutableSource:RE,useSyncExternalStore:PE,useId:VE,unstable_isNewReconciler:!1},bN={readContext:sr,useCallback:HE,useContext:sr,useEffect:Em,useImperativeHandle:UE,useInsertionEffect:zE,useLayoutEffect:$E,useMemo:jE,useReducer:Dp,useRef:FE,useState:function(){return Dp(ys)},useDebugValue:Cm,useDeferredValue:function(e){var t=ar();return Tt===null?t.memoizedState=e:WE(t,Tt.memoizedState,e)},useTransition:function(){var e=Dp(ys)[0],t=ar().memoizedState;return[e,t]},useMutableSource:RE,useSyncExternalStore:PE,useId:VE,unstable_isNewReconciler:!1};function wr(e,t){if(e&&e.defaultProps){t=vt({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Qh(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:vt({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var cf={isMounted:function(e){return(e=e._reactInternals)?jo(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=on(),i=Gi(e),l=ai(r,i);l.payload=t,n!=null&&(l.callback=n),t=Wi(e,l,i),t!==null&&(kr(t,e,i,r),tc(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=on(),i=Gi(e),l=ai(r,i);l.tag=1,l.payload=t,n!=null&&(l.callback=n),t=Wi(e,l,i),t!==null&&(kr(t,e,i,r),tc(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=on(),r=Gi(e),i=ai(n,r);i.tag=2,t!=null&&(i.callback=t),t=Wi(e,i,r),t!==null&&(kr(t,e,r,n),tc(t,e,r))}};function tw(e,t,n,r,i,l,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,l,s):t.prototype&&t.prototype.isPureReactComponent?!fs(n,r)||!fs(i,l):!0}function QE(e,t,n){var r=!1,i=eo,l=t.contextType;return typeof l=="object"&&l!==null?l=sr(l):(i=wn(t)?Do:Xt.current,r=t.contextTypes,l=(r=r!=null)?$l(e,i):eo),t=new t(n,l),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=cf,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=l),t}function nw(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&cf.enqueueReplaceState(t,t.state,null)}function Yh(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},gm(e);var l=t.contextType;typeof l=="object"&&l!==null?i.context=sr(l):(l=wn(t)?Do:Xt.current,i.context=$l(e,l)),i.state=e.memoizedState,l=t.getDerivedStateFromProps,typeof l=="function"&&(Qh(e,t,l,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&cf.enqueueReplaceState(i,i.state,null),Tc(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function jl(e,t){try{var n="",r=t;do n+=JA(r),r=r.return;while(r);var i=n}catch(l){i=`
Error generating stack: `+l.message+`
`+l.stack}return{value:e,source:t,stack:i,digest:null}}function Np(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Xh(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var _N=typeof WeakMap=="function"?WeakMap:Map;function YE(e,t,n){n=ai(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Dc||(Dc=!0,ug=r),Xh(e,t)},n}function XE(e,t,n){n=ai(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Xh(e,t)}}var l=e.stateNode;return l!==null&&typeof l.componentDidCatch=="function"&&(n.callback=function(){Xh(e,t),typeof r!="function"&&(Vi===null?Vi=new Set([this]):Vi.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function rw(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new _N;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=BN.bind(null,e,t,n),t.then(e,e))}function iw(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function ow(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=ai(-1,1),t.tag=2,Wi(n,t,1))),n.lanes|=1),e)}var ON=gi.ReactCurrentOwner,mn=!1;function nn(e,t,n,r){t.child=e===null?_E(t,null,n,r):Ul(t,e.child,n,r)}function lw(e,t,n,r,i){n=n.render;var l=t.ref;return Il(t,i),r=xm(e,t,n,r,l,i),n=Sm(),e!==null&&!mn?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,pi(e,t,i)):(pt&&n&&sm(t),t.flags|=1,nn(e,t,r,i),t.child)}function uw(e,t,n,r,i){if(e===null){var l=n.type;return typeof l=="function"&&!Pm(l)&&l.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=l,ZE(e,t,l,r,i)):(e=uc(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(l=e.child,!(e.lanes&i)){var s=l.memoizedProps;if(n=n.compare,n=n!==null?n:fs,n(s,r)&&e.ref===t.ref)return pi(e,t,i)}return t.flags|=1,e=qi(l,r),e.ref=t.ref,e.return=t,t.child=e}function ZE(e,t,n,r,i){if(e!==null){var l=e.memoizedProps;if(fs(l,r)&&e.ref===t.ref)if(mn=!1,t.pendingProps=r=l,(e.lanes&i)!==0)e.flags&131072&&(mn=!0);else return t.lanes=e.lanes,pi(e,t,i)}return Zh(e,t,n,r,i)}function JE(e,t,n){var r=t.pendingProps,i=r.children,l=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},nt(kl,Dn),Dn|=n;else{if(!(n&1073741824))return e=l!==null?l.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,nt(kl,Dn),Dn|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=l!==null?l.baseLanes:n,nt(kl,Dn),Dn|=r}else l!==null?(r=l.baseLanes|n,t.memoizedState=null):r=n,nt(kl,Dn),Dn|=r;return nn(e,t,i,n),t.child}function eC(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Zh(e,t,n,r,i){var l=wn(n)?Do:Xt.current;return l=$l(t,l),Il(t,i),n=xm(e,t,n,r,l,i),r=Sm(),e!==null&&!mn?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,pi(e,t,i)):(pt&&r&&sm(t),t.flags|=1,nn(e,t,n,i),t.child)}function sw(e,t,n,r,i){if(wn(n)){var l=!0;Cc(t)}else l=!1;if(Il(t,i),t.stateNode===null)ic(e,t),QE(t,n,r),Yh(t,n,r,i),r=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var f=s.context,d=n.contextType;typeof d=="object"&&d!==null?d=sr(d):(d=wn(n)?Do:Xt.current,d=$l(t,d));var p=n.getDerivedStateFromProps,g=typeof p=="function"||typeof s.getSnapshotBeforeUpdate=="function";g||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==r||f!==d)&&nw(t,s,r,d),Di=!1;var v=t.memoizedState;s.state=v,Tc(t,r,s,i),f=t.memoizedState,a!==r||v!==f||yn.current||Di?(typeof p=="function"&&(Qh(t,n,p,r),f=t.memoizedState),(a=Di||tw(t,n,a,r,v,f,d))?(g||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=f),s.props=r,s.state=f,s.context=d,r=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,TE(e,t),a=t.memoizedProps,d=t.type===t.elementType?a:wr(t.type,a),s.props=d,g=t.pendingProps,v=s.context,f=n.contextType,typeof f=="object"&&f!==null?f=sr(f):(f=wn(n)?Do:Xt.current,f=$l(t,f));var m=n.getDerivedStateFromProps;(p=typeof m=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==g||v!==f)&&nw(t,s,r,f),Di=!1,v=t.memoizedState,s.state=v,Tc(t,r,s,i);var E=t.memoizedState;a!==g||v!==E||yn.current||Di?(typeof m=="function"&&(Qh(t,n,m,r),E=t.memoizedState),(d=Di||tw(t,n,d,r,v,E,f)||!1)?(p||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(r,E,f),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(r,E,f)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&v===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&v===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=E),s.props=r,s.state=E,s.context=f,r=d):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&v===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&v===e.memoizedState||(t.flags|=1024),r=!1)}return Jh(e,t,n,r,l,i)}function Jh(e,t,n,r,i,l){eC(e,t);var s=(t.flags&128)!==0;if(!r&&!s)return i&&q1(t,n,!1),pi(e,t,l);r=t.stateNode,ON.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&s?(t.child=Ul(t,e.child,null,l),t.child=Ul(t,null,a,l)):nn(e,t,a,l),t.memoizedState=r.state,i&&q1(t,n,!0),t.child}function tC(e){var t=e.stateNode;t.pendingContext?G1(e,t.pendingContext,t.pendingContext!==t.context):t.context&&G1(e,t.context,!1),mm(e,t.containerInfo)}function aw(e,t,n,r,i){return Bl(),cm(i),t.flags|=256,nn(e,t,n,r),t.child}var eg={dehydrated:null,treeContext:null,retryLane:0};function tg(e){return{baseLanes:e,cachePool:null,transitions:null}}function nC(e,t,n){var r=t.pendingProps,i=gt.current,l=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(l=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),nt(gt,i&1),e===null)return qh(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=r.children,e=r.fallback,l?(r=t.mode,l=t.child,s={mode:"hidden",children:s},!(r&1)&&l!==null?(l.childLanes=0,l.pendingProps=s):l=pf(s,r,0,null),e=Po(e,r,n,null),l.return=t,e.return=t,l.sibling=e,t.child=l,t.child.memoizedState=tg(n),t.memoizedState=eg,e):km(t,s));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return TN(e,t,s,r,a,i,n);if(l){l=r.fallback,s=t.mode,i=e.child,a=i.sibling;var f={mode:"hidden",children:r.children};return!(s&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=f,t.deletions=null):(r=qi(i,f),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?l=qi(a,l):(l=Po(l,s,n,null),l.flags|=2),l.return=t,r.return=t,r.sibling=l,t.child=r,r=l,l=t.child,s=e.child.memoizedState,s=s===null?tg(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},l.memoizedState=s,l.childLanes=e.childLanes&~n,t.memoizedState=eg,r}return l=e.child,e=l.sibling,r=qi(l,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function km(e,t){return t=pf({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function za(e,t,n,r){return r!==null&&cm(r),Ul(t,e.child,null,n),e=km(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function TN(e,t,n,r,i,l,s){if(n)return t.flags&256?(t.flags&=-257,r=Np(Error(te(422))),za(e,t,s,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(l=r.fallback,i=t.mode,r=pf({mode:"visible",children:r.children},i,0,null),l=Po(l,i,s,null),l.flags|=2,r.return=t,l.return=t,r.sibling=l,t.child=r,t.mode&1&&Ul(t,e.child,null,s),t.child.memoizedState=tg(s),t.memoizedState=eg,l);if(!(t.mode&1))return za(e,t,s,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,l=Error(te(419)),r=Np(l,r,void 0),za(e,t,s,r)}if(a=(s&e.childLanes)!==0,mn||a){if(r=Dt,r!==null){switch(s&-s){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|s)?0:i,i!==0&&i!==l.retryLane&&(l.retryLane=i,di(e,i),kr(r,e,i,-1))}return Rm(),r=Np(Error(te(421))),za(e,t,s,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=UN.bind(null,e),i._reactRetry=t,null):(e=l.treeContext,Mn=ji(i.nextSibling),Fn=t,pt=!0,Sr=null,e!==null&&(tr[nr++]=ui,tr[nr++]=si,tr[nr++]=No,ui=e.id,si=e.overflow,No=t),t=km(t,r.children),t.flags|=4096,t)}function cw(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Kh(e.return,t,n)}function Mp(e,t,n,r,i){var l=e.memoizedState;l===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(l.isBackwards=t,l.rendering=null,l.renderingStartTime=0,l.last=r,l.tail=n,l.tailMode=i)}function rC(e,t,n){var r=t.pendingProps,i=r.revealOrder,l=r.tail;if(nn(e,t,r.children,n),r=gt.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&cw(e,n,t);else if(e.tag===19)cw(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(nt(gt,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&Ic(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Mp(t,!1,i,n,l);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&Ic(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Mp(t,!0,n,null,l);break;case"together":Mp(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ic(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function pi(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Lo|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(te(153));if(t.child!==null){for(e=t.child,n=qi(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=qi(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function IN(e,t,n){switch(t.tag){case 3:tC(t),Bl();break;case 5:IE(t);break;case 1:wn(t.type)&&Cc(t);break;case 4:mm(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;nt(_c,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(nt(gt,gt.current&1),t.flags|=128,null):n&t.child.childLanes?nC(e,t,n):(nt(gt,gt.current&1),e=pi(e,t,n),e!==null?e.sibling:null);nt(gt,gt.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return rC(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),nt(gt,gt.current),r)break;return null;case 22:case 23:return t.lanes=0,JE(e,t,n)}return pi(e,t,n)}var iC,ng,oC,lC;iC=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};ng=function(){};oC=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,To(Br.current);var l=null;switch(n){case"input":i=kh(e,i),r=kh(e,r),l=[];break;case"select":i=vt({},i,{value:void 0}),r=vt({},r,{value:void 0}),l=[];break;case"textarea":i=Oh(e,i),r=Oh(e,r),l=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Sc)}Ih(n,r);var s;n=null;for(d in i)if(!r.hasOwnProperty(d)&&i.hasOwnProperty(d)&&i[d]!=null)if(d==="style"){var a=i[d];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else d!=="dangerouslySetInnerHTML"&&d!=="children"&&d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&d!=="autoFocus"&&(is.hasOwnProperty(d)?l||(l=[]):(l=l||[]).push(d,null));for(d in r){var f=r[d];if(a=i?.[d],r.hasOwnProperty(d)&&f!==a&&(f!=null||a!=null))if(d==="style")if(a){for(s in a)!a.hasOwnProperty(s)||f&&f.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in f)f.hasOwnProperty(s)&&a[s]!==f[s]&&(n||(n={}),n[s]=f[s])}else n||(l||(l=[]),l.push(d,n)),n=f;else d==="dangerouslySetInnerHTML"?(f=f?f.__html:void 0,a=a?a.__html:void 0,f!=null&&a!==f&&(l=l||[]).push(d,f)):d==="children"?typeof f!="string"&&typeof f!="number"||(l=l||[]).push(d,""+f):d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&(is.hasOwnProperty(d)?(f!=null&&d==="onScroll"&&lt("scroll",e),l||a===f||(l=[])):(l=l||[]).push(d,f))}n&&(l=l||[]).push("style",n);var d=l;(t.updateQueue=d)&&(t.flags|=4)}};lC=function(e,t,n,r){n!==r&&(t.flags|=4)};function Pu(e,t){if(!pt)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Gt(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function RN(e,t,n){var r=t.pendingProps;switch(am(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Gt(t),null;case 1:return wn(t.type)&&Ec(),Gt(t),null;case 3:return r=t.stateNode,Hl(),st(yn),st(Xt),ym(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(La(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Sr!==null&&(cg(Sr),Sr=null))),ng(e,t),Gt(t),null;case 5:vm(t);var i=To(ms.current);if(n=t.type,e!==null&&t.stateNode!=null)oC(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(te(166));return Gt(t),null}if(e=To(Br.current),La(t)){r=t.stateNode,n=t.type;var l=t.memoizedProps;switch(r[Lr]=t,r[hs]=l,e=(t.mode&1)!==0,n){case"dialog":lt("cancel",r),lt("close",r);break;case"iframe":case"object":case"embed":lt("load",r);break;case"video":case"audio":for(i=0;i<zu.length;i++)lt(zu[i],r);break;case"source":lt("error",r);break;case"img":case"image":case"link":lt("error",r),lt("load",r);break;case"details":lt("toggle",r);break;case"input":w1(r,l),lt("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!l.multiple},lt("invalid",r);break;case"textarea":S1(r,l),lt("invalid",r)}Ih(n,l),i=null;for(var s in l)if(l.hasOwnProperty(s)){var a=l[s];s==="children"?typeof a=="string"?r.textContent!==a&&(l.suppressHydrationWarning!==!0&&Ma(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(l.suppressHydrationWarning!==!0&&Ma(r.textContent,a,e),i=["children",""+a]):is.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&lt("scroll",r)}switch(n){case"input":Oa(r),x1(r,l,!0);break;case"textarea":Oa(r),E1(r);break;case"select":case"option":break;default:typeof l.onClick=="function"&&(r.onclick=Sc)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{s=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=NS(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=s.createElement(n,{is:r.is}):(e=s.createElement(n),n==="select"&&(s=e,r.multiple?s.multiple=!0:r.size&&(s.size=r.size))):e=s.createElementNS(e,n),e[Lr]=t,e[hs]=r,iC(e,t,!1,!1),t.stateNode=e;e:{switch(s=Rh(n,r),n){case"dialog":lt("cancel",e),lt("close",e),i=r;break;case"iframe":case"object":case"embed":lt("load",e),i=r;break;case"video":case"audio":for(i=0;i<zu.length;i++)lt(zu[i],e);i=r;break;case"source":lt("error",e),i=r;break;case"img":case"image":case"link":lt("error",e),lt("load",e),i=r;break;case"details":lt("toggle",e),i=r;break;case"input":w1(e,r),i=kh(e,r),lt("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=vt({},r,{value:void 0}),lt("invalid",e);break;case"textarea":S1(e,r),i=Oh(e,r),lt("invalid",e);break;default:i=r}Ih(n,i),a=i;for(l in a)if(a.hasOwnProperty(l)){var f=a[l];l==="style"?FS(e,f):l==="dangerouslySetInnerHTML"?(f=f?f.__html:void 0,f!=null&&MS(e,f)):l==="children"?typeof f=="string"?(n!=="textarea"||f!=="")&&os(e,f):typeof f=="number"&&os(e,""+f):l!=="suppressContentEditableWarning"&&l!=="suppressHydrationWarning"&&l!=="autoFocus"&&(is.hasOwnProperty(l)?f!=null&&l==="onScroll"&&lt("scroll",e):f!=null&&Kg(e,l,f,s))}switch(n){case"input":Oa(e),x1(e,r,!1);break;case"textarea":Oa(e),E1(e);break;case"option":r.value!=null&&e.setAttribute("value",""+Ji(r.value));break;case"select":e.multiple=!!r.multiple,l=r.value,l!=null?bl(e,!!r.multiple,l,!1):r.defaultValue!=null&&bl(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=Sc)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Gt(t),null;case 6:if(e&&t.stateNode!=null)lC(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(te(166));if(n=To(ms.current),To(Br.current),La(t)){if(r=t.stateNode,n=t.memoizedProps,r[Lr]=t,(l=r.nodeValue!==n)&&(e=Fn,e!==null))switch(e.tag){case 3:Ma(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Ma(r.nodeValue,n,(e.mode&1)!==0)}l&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[Lr]=t,t.stateNode=r}return Gt(t),null;case 13:if(st(gt),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(pt&&Mn!==null&&t.mode&1&&!(t.flags&128))kE(),Bl(),t.flags|=98560,l=!1;else if(l=La(t),r!==null&&r.dehydrated!==null){if(e===null){if(!l)throw Error(te(318));if(l=t.memoizedState,l=l!==null?l.dehydrated:null,!l)throw Error(te(317));l[Lr]=t}else Bl(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Gt(t),l=!1}else Sr!==null&&(cg(Sr),Sr=null),l=!0;if(!l)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||gt.current&1?It===0&&(It=3):Rm())),t.updateQueue!==null&&(t.flags|=4),Gt(t),null);case 4:return Hl(),ng(e,t),e===null&&ds(t.stateNode.containerInfo),Gt(t),null;case 10:return pm(t.type._context),Gt(t),null;case 17:return wn(t.type)&&Ec(),Gt(t),null;case 19:if(st(gt),l=t.memoizedState,l===null)return Gt(t),null;if(r=(t.flags&128)!==0,s=l.rendering,s===null)if(r)Pu(l,!1);else{if(It!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Ic(e),s!==null){for(t.flags|=128,Pu(l,!1),r=s.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)l=n,e=r,l.flags&=14680066,s=l.alternate,s===null?(l.childLanes=0,l.lanes=e,l.child=null,l.subtreeFlags=0,l.memoizedProps=null,l.memoizedState=null,l.updateQueue=null,l.dependencies=null,l.stateNode=null):(l.childLanes=s.childLanes,l.lanes=s.lanes,l.child=s.child,l.subtreeFlags=0,l.deletions=null,l.memoizedProps=s.memoizedProps,l.memoizedState=s.memoizedState,l.updateQueue=s.updateQueue,l.type=s.type,e=s.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return nt(gt,gt.current&1|2),t.child}e=e.sibling}l.tail!==null&&Ct()>Wl&&(t.flags|=128,r=!0,Pu(l,!1),t.lanes=4194304)}else{if(!r)if(e=Ic(s),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Pu(l,!0),l.tail===null&&l.tailMode==="hidden"&&!s.alternate&&!pt)return Gt(t),null}else 2*Ct()-l.renderingStartTime>Wl&&n!==1073741824&&(t.flags|=128,r=!0,Pu(l,!1),t.lanes=4194304);l.isBackwards?(s.sibling=t.child,t.child=s):(n=l.last,n!==null?n.sibling=s:t.child=s,l.last=s)}return l.tail!==null?(t=l.tail,l.rendering=t,l.tail=t.sibling,l.renderingStartTime=Ct(),t.sibling=null,n=gt.current,nt(gt,r?n&1|2:n&1),t):(Gt(t),null);case 22:case 23:return Im(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Dn&1073741824&&(Gt(t),t.subtreeFlags&6&&(t.flags|=8192)):Gt(t),null;case 24:return null;case 25:return null}throw Error(te(156,t.tag))}function PN(e,t){switch(am(t),t.tag){case 1:return wn(t.type)&&Ec(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Hl(),st(yn),st(Xt),ym(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return vm(t),null;case 13:if(st(gt),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(te(340));Bl()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return st(gt),null;case 4:return Hl(),null;case 10:return pm(t.type._context),null;case 22:case 23:return Im(),null;case 24:return null;default:return null}}var $a=!1,Qt=!1,AN=typeof WeakSet=="function"?WeakSet:Set,ce=null;function Cl(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){xt(e,t,r)}else n.current=null}function rg(e,t,n){try{n()}catch(r){xt(e,t,r)}}var fw=!1;function DN(e,t){if(Bh=yc,e=fE(),um(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,l=r.focusNode;r=r.focusOffset;try{n.nodeType,l.nodeType}catch{n=null;break e}var s=0,a=-1,f=-1,d=0,p=0,g=e,v=null;t:for(;;){for(var m;g!==n||i!==0&&g.nodeType!==3||(a=s+i),g!==l||r!==0&&g.nodeType!==3||(f=s+r),g.nodeType===3&&(s+=g.nodeValue.length),(m=g.firstChild)!==null;)v=g,g=m;for(;;){if(g===e)break t;if(v===n&&++d===i&&(a=s),v===l&&++p===r&&(f=s),(m=g.nextSibling)!==null)break;g=v,v=g.parentNode}g=m}n=a===-1||f===-1?null:{start:a,end:f}}else n=null}n=n||{start:0,end:0}}else n=null;for(Uh={focusedElem:e,selectionRange:n},yc=!1,ce=t;ce!==null;)if(t=ce,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,ce=e;else for(;ce!==null;){t=ce;try{var E=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(E!==null){var S=E.memoizedProps,A=E.memoizedState,y=t.stateNode,x=y.getSnapshotBeforeUpdate(t.elementType===t.type?S:wr(t.type,S),A);y.__reactInternalSnapshotBeforeUpdate=x}break;case 3:var k=t.stateNode.containerInfo;k.nodeType===1?k.textContent="":k.nodeType===9&&k.documentElement&&k.removeChild(k.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(te(163))}}catch(P){xt(t,t.return,P)}if(e=t.sibling,e!==null){e.return=t.return,ce=e;break}ce=t.return}return E=fw,fw=!1,E}function Ku(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var l=i.destroy;i.destroy=void 0,l!==void 0&&rg(t,n,l)}i=i.next}while(i!==r)}}function ff(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ig(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function uC(e){var t=e.alternate;t!==null&&(e.alternate=null,uC(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Lr],delete t[hs],delete t[Wh],delete t[gN],delete t[mN])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function sC(e){return e.tag===5||e.tag===3||e.tag===4}function dw(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||sC(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function og(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Sc));else if(r!==4&&(e=e.child,e!==null))for(og(e,t,n),e=e.sibling;e!==null;)og(e,t,n),e=e.sibling}function lg(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(lg(e,t,n),e=e.sibling;e!==null;)lg(e,t,n),e=e.sibling}var zt=null,xr=!1;function Ti(e,t,n){for(n=n.child;n!==null;)aC(e,t,n),n=n.sibling}function aC(e,t,n){if($r&&typeof $r.onCommitFiberUnmount=="function")try{$r.onCommitFiberUnmount(nf,n)}catch{}switch(n.tag){case 5:Qt||Cl(n,t);case 6:var r=zt,i=xr;zt=null,Ti(e,t,n),zt=r,xr=i,zt!==null&&(xr?(e=zt,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):zt.removeChild(n.stateNode));break;case 18:zt!==null&&(xr?(e=zt,n=n.stateNode,e.nodeType===8?Tp(e.parentNode,n):e.nodeType===1&&Tp(e,n),as(e)):Tp(zt,n.stateNode));break;case 4:r=zt,i=xr,zt=n.stateNode.containerInfo,xr=!0,Ti(e,t,n),zt=r,xr=i;break;case 0:case 11:case 14:case 15:if(!Qt&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var l=i,s=l.destroy;l=l.tag,s!==void 0&&(l&2||l&4)&&rg(n,t,s),i=i.next}while(i!==r)}Ti(e,t,n);break;case 1:if(!Qt&&(Cl(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){xt(n,t,a)}Ti(e,t,n);break;case 21:Ti(e,t,n);break;case 22:n.mode&1?(Qt=(r=Qt)||n.memoizedState!==null,Ti(e,t,n),Qt=r):Ti(e,t,n);break;default:Ti(e,t,n)}}function pw(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new AN),t.forEach(function(r){var i=HN.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function yr(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var l=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:zt=a.stateNode,xr=!1;break e;case 3:zt=a.stateNode.containerInfo,xr=!0;break e;case 4:zt=a.stateNode.containerInfo,xr=!0;break e}a=a.return}if(zt===null)throw Error(te(160));aC(l,s,i),zt=null,xr=!1;var f=i.alternate;f!==null&&(f.return=null),i.return=null}catch(d){xt(i,t,d)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)cC(t,e),t=t.sibling}function cC(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(yr(t,e),Dr(e),r&4){try{Ku(3,e,e.return),ff(3,e)}catch(S){xt(e,e.return,S)}try{Ku(5,e,e.return)}catch(S){xt(e,e.return,S)}}break;case 1:yr(t,e),Dr(e),r&512&&n!==null&&Cl(n,n.return);break;case 5:if(yr(t,e),Dr(e),r&512&&n!==null&&Cl(n,n.return),e.flags&32){var i=e.stateNode;try{os(i,"")}catch(S){xt(e,e.return,S)}}if(r&4&&(i=e.stateNode,i!=null)){var l=e.memoizedProps,s=n!==null?n.memoizedProps:l,a=e.type,f=e.updateQueue;if(e.updateQueue=null,f!==null)try{a==="input"&&l.type==="radio"&&l.name!=null&&AS(i,l),Rh(a,s);var d=Rh(a,l);for(s=0;s<f.length;s+=2){var p=f[s],g=f[s+1];p==="style"?FS(i,g):p==="dangerouslySetInnerHTML"?MS(i,g):p==="children"?os(i,g):Kg(i,p,g,d)}switch(a){case"input":bh(i,l);break;case"textarea":DS(i,l);break;case"select":var v=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!l.multiple;var m=l.value;m!=null?bl(i,!!l.multiple,m,!1):v!==!!l.multiple&&(l.defaultValue!=null?bl(i,!!l.multiple,l.defaultValue,!0):bl(i,!!l.multiple,l.multiple?[]:"",!1))}i[hs]=l}catch(S){xt(e,e.return,S)}}break;case 6:if(yr(t,e),Dr(e),r&4){if(e.stateNode===null)throw Error(te(162));i=e.stateNode,l=e.memoizedProps;try{i.nodeValue=l}catch(S){xt(e,e.return,S)}}break;case 3:if(yr(t,e),Dr(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{as(t.containerInfo)}catch(S){xt(e,e.return,S)}break;case 4:yr(t,e),Dr(e);break;case 13:yr(t,e),Dr(e),i=e.child,i.flags&8192&&(l=i.memoizedState!==null,i.stateNode.isHidden=l,!l||i.alternate!==null&&i.alternate.memoizedState!==null||(Om=Ct())),r&4&&pw(e);break;case 22:if(p=n!==null&&n.memoizedState!==null,e.mode&1?(Qt=(d=Qt)||p,yr(t,e),Qt=d):yr(t,e),Dr(e),r&8192){if(d=e.memoizedState!==null,(e.stateNode.isHidden=d)&&!p&&e.mode&1)for(ce=e,p=e.child;p!==null;){for(g=ce=p;ce!==null;){switch(v=ce,m=v.child,v.tag){case 0:case 11:case 14:case 15:Ku(4,v,v.return);break;case 1:Cl(v,v.return);var E=v.stateNode;if(typeof E.componentWillUnmount=="function"){r=v,n=v.return;try{t=r,E.props=t.memoizedProps,E.state=t.memoizedState,E.componentWillUnmount()}catch(S){xt(r,n,S)}}break;case 5:Cl(v,v.return);break;case 22:if(v.memoizedState!==null){gw(g);continue}}m!==null?(m.return=v,ce=m):gw(g)}p=p.sibling}e:for(p=null,g=e;;){if(g.tag===5){if(p===null){p=g;try{i=g.stateNode,d?(l=i.style,typeof l.setProperty=="function"?l.setProperty("display","none","important"):l.display="none"):(a=g.stateNode,f=g.memoizedProps.style,s=f!=null&&f.hasOwnProperty("display")?f.display:null,a.style.display=LS("display",s))}catch(S){xt(e,e.return,S)}}}else if(g.tag===6){if(p===null)try{g.stateNode.nodeValue=d?"":g.memoizedProps}catch(S){xt(e,e.return,S)}}else if((g.tag!==22&&g.tag!==23||g.memoizedState===null||g===e)&&g.child!==null){g.child.return=g,g=g.child;continue}if(g===e)break e;for(;g.sibling===null;){if(g.return===null||g.return===e)break e;p===g&&(p=null),g=g.return}p===g&&(p=null),g.sibling.return=g.return,g=g.sibling}}break;case 19:yr(t,e),Dr(e),r&4&&pw(e);break;case 21:break;default:yr(t,e),Dr(e)}}function Dr(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(sC(n)){var r=n;break e}n=n.return}throw Error(te(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(os(i,""),r.flags&=-33);var l=dw(e);lg(e,l,i);break;case 3:case 4:var s=r.stateNode.containerInfo,a=dw(e);og(e,a,s);break;default:throw Error(te(161))}}catch(f){xt(e,e.return,f)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function NN(e,t,n){ce=e,fC(e)}function fC(e,t,n){for(var r=(e.mode&1)!==0;ce!==null;){var i=ce,l=i.child;if(i.tag===22&&r){var s=i.memoizedState!==null||$a;if(!s){var a=i.alternate,f=a!==null&&a.memoizedState!==null||Qt;a=$a;var d=Qt;if($a=s,(Qt=f)&&!d)for(ce=i;ce!==null;)s=ce,f=s.child,s.tag===22&&s.memoizedState!==null?mw(i):f!==null?(f.return=s,ce=f):mw(i);for(;l!==null;)ce=l,fC(l),l=l.sibling;ce=i,$a=a,Qt=d}hw(e)}else i.subtreeFlags&8772&&l!==null?(l.return=i,ce=l):hw(e)}}function hw(e){for(;ce!==null;){var t=ce;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Qt||ff(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Qt)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:wr(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var l=t.updateQueue;l!==null&&Z1(t,l,r);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Z1(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var f=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":f.autoFocus&&n.focus();break;case"img":f.src&&(n.src=f.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var d=t.alternate;if(d!==null){var p=d.memoizedState;if(p!==null){var g=p.dehydrated;g!==null&&as(g)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(te(163))}Qt||t.flags&512&&ig(t)}catch(v){xt(t,t.return,v)}}if(t===e){ce=null;break}if(n=t.sibling,n!==null){n.return=t.return,ce=n;break}ce=t.return}}function gw(e){for(;ce!==null;){var t=ce;if(t===e){ce=null;break}var n=t.sibling;if(n!==null){n.return=t.return,ce=n;break}ce=t.return}}function mw(e){for(;ce!==null;){var t=ce;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ff(4,t)}catch(f){xt(t,n,f)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(f){xt(t,i,f)}}var l=t.return;try{ig(t)}catch(f){xt(t,l,f)}break;case 5:var s=t.return;try{ig(t)}catch(f){xt(t,s,f)}}}catch(f){xt(t,t.return,f)}if(t===e){ce=null;break}var a=t.sibling;if(a!==null){a.return=t.return,ce=a;break}ce=t.return}}var MN=Math.ceil,Ac=gi.ReactCurrentDispatcher,bm=gi.ReactCurrentOwner,lr=gi.ReactCurrentBatchConfig,$e=0,Dt=null,bt=null,Bt=0,Dn=0,kl=lo(0),It=0,xs=null,Lo=0,df=0,_m=0,Qu=null,pn=null,Om=0,Wl=1/0,ii=null,Dc=!1,ug=null,Vi=null,Ba=!1,$i=null,Nc=0,Yu=0,sg=null,oc=-1,lc=0;function on(){return $e&6?Ct():oc!==-1?oc:oc=Ct()}function Gi(e){return e.mode&1?$e&2&&Bt!==0?Bt&-Bt:yN.transition!==null?(lc===0&&(lc=QS()),lc):(e=Ye,e!==0||(e=window.event,e=e===void 0?16:nE(e.type)),e):1}function kr(e,t,n,r){if(50<Yu)throw Yu=0,sg=null,Error(te(185));Os(e,n,r),(!($e&2)||e!==Dt)&&(e===Dt&&(!($e&2)&&(df|=n),It===4&&Mi(e,Bt)),xn(e,r),n===1&&$e===0&&!(t.mode&1)&&(Wl=Ct()+500,sf&&uo()))}function xn(e,t){var n=e.callbackNode;yD(e,t);var r=vc(e,e===Dt?Bt:0);if(r===0)n!==null&&b1(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&b1(n),t===1)e.tag===0?vN(vw.bind(null,e)):SE(vw.bind(null,e)),pN(function(){!($e&6)&&uo()}),n=null;else{switch(YS(r)){case 1:n=Jg;break;case 4:n=qS;break;case 16:n=mc;break;case 536870912:n=KS;break;default:n=mc}n=wC(n,dC.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function dC(e,t){if(oc=-1,lc=0,$e&6)throw Error(te(327));var n=e.callbackNode;if(Rl()&&e.callbackNode!==n)return null;var r=vc(e,e===Dt?Bt:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=Mc(e,r);else{t=r;var i=$e;$e|=2;var l=hC();(Dt!==e||Bt!==t)&&(ii=null,Wl=Ct()+500,Ro(e,t));do try{zN();break}catch(a){pC(e,a)}while(1);dm(),Ac.current=l,$e=i,bt!==null?t=0:(Dt=null,Bt=0,t=It)}if(t!==0){if(t===2&&(i=Mh(e),i!==0&&(r=i,t=ag(e,i))),t===1)throw n=xs,Ro(e,0),Mi(e,r),xn(e,Ct()),n;if(t===6)Mi(e,r);else{if(i=e.current.alternate,!(r&30)&&!LN(i)&&(t=Mc(e,r),t===2&&(l=Mh(e),l!==0&&(r=l,t=ag(e,l))),t===1))throw n=xs,Ro(e,0),Mi(e,r),xn(e,Ct()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(te(345));case 2:So(e,pn,ii);break;case 3:if(Mi(e,r),(r&130023424)===r&&(t=Om+500-Ct(),10<t)){if(vc(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){on(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=jh(So.bind(null,e,pn,ii),t);break}So(e,pn,ii);break;case 4:if(Mi(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var s=31-Cr(r);l=1<<s,s=t[s],s>i&&(i=s),r&=~l}if(r=i,r=Ct()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*MN(r/1960))-r,10<r){e.timeoutHandle=jh(So.bind(null,e,pn,ii),r);break}So(e,pn,ii);break;case 5:So(e,pn,ii);break;default:throw Error(te(329))}}}return xn(e,Ct()),e.callbackNode===n?dC.bind(null,e):null}function ag(e,t){var n=Qu;return e.current.memoizedState.isDehydrated&&(Ro(e,t).flags|=256),e=Mc(e,t),e!==2&&(t=pn,pn=n,t!==null&&cg(t)),e}function cg(e){pn===null?pn=e:pn.push.apply(pn,e)}function LN(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],l=i.getSnapshot;i=i.value;try{if(!br(l(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Mi(e,t){for(t&=~_m,t&=~df,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Cr(t),r=1<<n;e[n]=-1,t&=~r}}function vw(e){if($e&6)throw Error(te(327));Rl();var t=vc(e,0);if(!(t&1))return xn(e,Ct()),null;var n=Mc(e,t);if(e.tag!==0&&n===2){var r=Mh(e);r!==0&&(t=r,n=ag(e,r))}if(n===1)throw n=xs,Ro(e,0),Mi(e,t),xn(e,Ct()),n;if(n===6)throw Error(te(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,So(e,pn,ii),xn(e,Ct()),null}function Tm(e,t){var n=$e;$e|=1;try{return e(t)}finally{$e=n,$e===0&&(Wl=Ct()+500,sf&&uo())}}function Fo(e){$i!==null&&$i.tag===0&&!($e&6)&&Rl();var t=$e;$e|=1;var n=lr.transition,r=Ye;try{if(lr.transition=null,Ye=1,e)return e()}finally{Ye=r,lr.transition=n,$e=t,!($e&6)&&uo()}}function Im(){Dn=kl.current,st(kl)}function Ro(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,dN(n)),bt!==null)for(n=bt.return;n!==null;){var r=n;switch(am(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ec();break;case 3:Hl(),st(yn),st(Xt),ym();break;case 5:vm(r);break;case 4:Hl();break;case 13:st(gt);break;case 19:st(gt);break;case 10:pm(r.type._context);break;case 22:case 23:Im()}n=n.return}if(Dt=e,bt=e=qi(e.current,null),Bt=Dn=t,It=0,xs=null,_m=df=Lo=0,pn=Qu=null,Oo!==null){for(t=0;t<Oo.length;t++)if(n=Oo[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,l=n.pending;if(l!==null){var s=l.next;l.next=i,r.next=s}n.pending=r}Oo=null}return e}function pC(e,t){do{var n=bt;try{if(dm(),nc.current=Pc,Rc){for(var r=mt.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}Rc=!1}if(Mo=0,At=Tt=mt=null,qu=!1,vs=0,bm.current=null,n===null||n.return===null){It=1,xs=t,bt=null;break}e:{var l=e,s=n.return,a=n,f=t;if(t=Bt,a.flags|=32768,f!==null&&typeof f=="object"&&typeof f.then=="function"){var d=f,p=a,g=p.tag;if(!(p.mode&1)&&(g===0||g===11||g===15)){var v=p.alternate;v?(p.updateQueue=v.updateQueue,p.memoizedState=v.memoizedState,p.lanes=v.lanes):(p.updateQueue=null,p.memoizedState=null)}var m=iw(s);if(m!==null){m.flags&=-257,ow(m,s,a,l,t),m.mode&1&&rw(l,d,t),t=m,f=d;var E=t.updateQueue;if(E===null){var S=new Set;S.add(f),t.updateQueue=S}else E.add(f);break e}else{if(!(t&1)){rw(l,d,t),Rm();break e}f=Error(te(426))}}else if(pt&&a.mode&1){var A=iw(s);if(A!==null){!(A.flags&65536)&&(A.flags|=256),ow(A,s,a,l,t),cm(jl(f,a));break e}}l=f=jl(f,a),It!==4&&(It=2),Qu===null?Qu=[l]:Qu.push(l),l=s;do{switch(l.tag){case 3:l.flags|=65536,t&=-t,l.lanes|=t;var y=YE(l,f,t);X1(l,y);break e;case 1:a=f;var x=l.type,k=l.stateNode;if(!(l.flags&128)&&(typeof x.getDerivedStateFromError=="function"||k!==null&&typeof k.componentDidCatch=="function"&&(Vi===null||!Vi.has(k)))){l.flags|=65536,t&=-t,l.lanes|=t;var P=XE(l,a,t);X1(l,P);break e}}l=l.return}while(l!==null)}mC(n)}catch(D){t=D,bt===n&&n!==null&&(bt=n=n.return);continue}break}while(1)}function hC(){var e=Ac.current;return Ac.current=Pc,e===null?Pc:e}function Rm(){(It===0||It===3||It===2)&&(It=4),Dt===null||!(Lo&268435455)&&!(df&268435455)||Mi(Dt,Bt)}function Mc(e,t){var n=$e;$e|=2;var r=hC();(Dt!==e||Bt!==t)&&(ii=null,Ro(e,t));do try{FN();break}catch(i){pC(e,i)}while(1);if(dm(),$e=n,Ac.current=r,bt!==null)throw Error(te(261));return Dt=null,Bt=0,It}function FN(){for(;bt!==null;)gC(bt)}function zN(){for(;bt!==null&&!aD();)gC(bt)}function gC(e){var t=yC(e.alternate,e,Dn);e.memoizedProps=e.pendingProps,t===null?mC(e):bt=t,bm.current=null}function mC(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=PN(n,t),n!==null){n.flags&=32767,bt=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{It=6,bt=null;return}}else if(n=RN(n,t,Dn),n!==null){bt=n;return}if(t=t.sibling,t!==null){bt=t;return}bt=t=e}while(t!==null);It===0&&(It=5)}function So(e,t,n){var r=Ye,i=lr.transition;try{lr.transition=null,Ye=1,$N(e,t,n,r)}finally{lr.transition=i,Ye=r}return null}function $N(e,t,n,r){do Rl();while($i!==null);if($e&6)throw Error(te(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(te(177));e.callbackNode=null,e.callbackPriority=0;var l=n.lanes|n.childLanes;if(wD(e,l),e===Dt&&(bt=Dt=null,Bt=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Ba||(Ba=!0,wC(mc,function(){return Rl(),null})),l=(n.flags&15990)!==0,n.subtreeFlags&15990||l){l=lr.transition,lr.transition=null;var s=Ye;Ye=1;var a=$e;$e|=4,bm.current=null,DN(e,n),cC(n,e),oN(Uh),yc=!!Bh,Uh=Bh=null,e.current=n,NN(n),cD(),$e=a,Ye=s,lr.transition=l}else e.current=n;if(Ba&&(Ba=!1,$i=e,Nc=i),l=e.pendingLanes,l===0&&(Vi=null),pD(n.stateNode),xn(e,Ct()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(Dc)throw Dc=!1,e=ug,ug=null,e;return Nc&1&&e.tag!==0&&Rl(),l=e.pendingLanes,l&1?e===sg?Yu++:(Yu=0,sg=e):Yu=0,uo(),null}function Rl(){if($i!==null){var e=YS(Nc),t=lr.transition,n=Ye;try{if(lr.transition=null,Ye=16>e?16:e,$i===null)var r=!1;else{if(e=$i,$i=null,Nc=0,$e&6)throw Error(te(331));var i=$e;for($e|=4,ce=e.current;ce!==null;){var l=ce,s=l.child;if(ce.flags&16){var a=l.deletions;if(a!==null){for(var f=0;f<a.length;f++){var d=a[f];for(ce=d;ce!==null;){var p=ce;switch(p.tag){case 0:case 11:case 15:Ku(8,p,l)}var g=p.child;if(g!==null)g.return=p,ce=g;else for(;ce!==null;){p=ce;var v=p.sibling,m=p.return;if(uC(p),p===d){ce=null;break}if(v!==null){v.return=m,ce=v;break}ce=m}}}var E=l.alternate;if(E!==null){var S=E.child;if(S!==null){E.child=null;do{var A=S.sibling;S.sibling=null,S=A}while(S!==null)}}ce=l}}if(l.subtreeFlags&2064&&s!==null)s.return=l,ce=s;else e:for(;ce!==null;){if(l=ce,l.flags&2048)switch(l.tag){case 0:case 11:case 15:Ku(9,l,l.return)}var y=l.sibling;if(y!==null){y.return=l.return,ce=y;break e}ce=l.return}}var x=e.current;for(ce=x;ce!==null;){s=ce;var k=s.child;if(s.subtreeFlags&2064&&k!==null)k.return=s,ce=k;else e:for(s=x;ce!==null;){if(a=ce,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:ff(9,a)}}catch(D){xt(a,a.return,D)}if(a===s){ce=null;break e}var P=a.sibling;if(P!==null){P.return=a.return,ce=P;break e}ce=a.return}}if($e=i,uo(),$r&&typeof $r.onPostCommitFiberRoot=="function")try{$r.onPostCommitFiberRoot(nf,e)}catch{}r=!0}return r}finally{Ye=n,lr.transition=t}}return!1}function yw(e,t,n){t=jl(n,t),t=YE(e,t,1),e=Wi(e,t,1),t=on(),e!==null&&(Os(e,1,t),xn(e,t))}function xt(e,t,n){if(e.tag===3)yw(e,e,n);else for(;t!==null;){if(t.tag===3){yw(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Vi===null||!Vi.has(r))){e=jl(n,e),e=XE(t,e,1),t=Wi(t,e,1),e=on(),t!==null&&(Os(t,1,e),xn(t,e));break}}t=t.return}}function BN(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=on(),e.pingedLanes|=e.suspendedLanes&n,Dt===e&&(Bt&n)===n&&(It===4||It===3&&(Bt&130023424)===Bt&&500>Ct()-Om?Ro(e,0):_m|=n),xn(e,t)}function vC(e,t){t===0&&(e.mode&1?(t=Ra,Ra<<=1,!(Ra&130023424)&&(Ra=4194304)):t=1);var n=on();e=di(e,t),e!==null&&(Os(e,t,n),xn(e,n))}function UN(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),vC(e,n)}function HN(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(te(314))}r!==null&&r.delete(t),vC(e,n)}var yC;yC=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||yn.current)mn=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return mn=!1,IN(e,t,n);mn=!!(e.flags&131072)}else mn=!1,pt&&t.flags&1048576&&EE(t,bc,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;ic(e,t),e=t.pendingProps;var i=$l(t,Xt.current);Il(t,n),i=xm(null,t,r,e,i,n);var l=Sm();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,wn(r)?(l=!0,Cc(t)):l=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,gm(t),i.updater=cf,t.stateNode=i,i._reactInternals=t,Yh(t,r,e,n),t=Jh(null,t,r,!0,l,n)):(t.tag=0,pt&&l&&sm(t),nn(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(ic(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=WN(r),e=wr(r,e),i){case 0:t=Zh(null,t,r,e,n);break e;case 1:t=sw(null,t,r,e,n);break e;case 11:t=lw(null,t,r,e,n);break e;case 14:t=uw(null,t,r,wr(r.type,e),n);break e}throw Error(te(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:wr(r,i),Zh(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:wr(r,i),sw(e,t,r,i,n);case 3:e:{if(tC(t),e===null)throw Error(te(387));r=t.pendingProps,l=t.memoizedState,i=l.element,TE(e,t),Tc(t,r,null,n);var s=t.memoizedState;if(r=s.element,l.isDehydrated)if(l={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=l,t.memoizedState=l,t.flags&256){i=jl(Error(te(423)),t),t=aw(e,t,r,n,i);break e}else if(r!==i){i=jl(Error(te(424)),t),t=aw(e,t,r,n,i);break e}else for(Mn=ji(t.stateNode.containerInfo.firstChild),Fn=t,pt=!0,Sr=null,n=_E(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Bl(),r===i){t=pi(e,t,n);break e}nn(e,t,r,n)}t=t.child}return t;case 5:return IE(t),e===null&&qh(t),r=t.type,i=t.pendingProps,l=e!==null?e.memoizedProps:null,s=i.children,Hh(r,i)?s=null:l!==null&&Hh(r,l)&&(t.flags|=32),eC(e,t),nn(e,t,s,n),t.child;case 6:return e===null&&qh(t),null;case 13:return nC(e,t,n);case 4:return mm(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Ul(t,null,r,n):nn(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:wr(r,i),lw(e,t,r,i,n);case 7:return nn(e,t,t.pendingProps,n),t.child;case 8:return nn(e,t,t.pendingProps.children,n),t.child;case 12:return nn(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,l=t.memoizedProps,s=i.value,nt(_c,r._currentValue),r._currentValue=s,l!==null)if(br(l.value,s)){if(l.children===i.children&&!yn.current){t=pi(e,t,n);break e}}else for(l=t.child,l!==null&&(l.return=t);l!==null;){var a=l.dependencies;if(a!==null){s=l.child;for(var f=a.firstContext;f!==null;){if(f.context===r){if(l.tag===1){f=ai(-1,n&-n),f.tag=2;var d=l.updateQueue;if(d!==null){d=d.shared;var p=d.pending;p===null?f.next=f:(f.next=p.next,p.next=f),d.pending=f}}l.lanes|=n,f=l.alternate,f!==null&&(f.lanes|=n),Kh(l.return,n,t),a.lanes|=n;break}f=f.next}}else if(l.tag===10)s=l.type===t.type?null:l.child;else if(l.tag===18){if(s=l.return,s===null)throw Error(te(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),Kh(s,n,t),s=l.sibling}else s=l.child;if(s!==null)s.return=l;else for(s=l;s!==null;){if(s===t){s=null;break}if(l=s.sibling,l!==null){l.return=s.return,s=l;break}s=s.return}l=s}nn(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,Il(t,n),i=sr(i),r=r(i),t.flags|=1,nn(e,t,r,n),t.child;case 14:return r=t.type,i=wr(r,t.pendingProps),i=wr(r.type,i),uw(e,t,r,i,n);case 15:return ZE(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:wr(r,i),ic(e,t),t.tag=1,wn(r)?(e=!0,Cc(t)):e=!1,Il(t,n),QE(t,r,i),Yh(t,r,i,n),Jh(null,t,r,!0,e,n);case 19:return rC(e,t,n);case 22:return JE(e,t,n)}throw Error(te(156,t.tag))};function wC(e,t){return GS(e,t)}function jN(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function or(e,t,n,r){return new jN(e,t,n,r)}function Pm(e){return e=e.prototype,!(!e||!e.isReactComponent)}function WN(e){if(typeof e=="function")return Pm(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Yg)return 11;if(e===Xg)return 14}return 2}function qi(e,t){var n=e.alternate;return n===null?(n=or(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function uc(e,t,n,r,i,l){var s=2;if(r=e,typeof e=="function")Pm(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case hl:return Po(n.children,i,l,t);case Qg:s=8,i|=8;break;case xh:return e=or(12,n,t,i|2),e.elementType=xh,e.lanes=l,e;case Sh:return e=or(13,n,t,i),e.elementType=Sh,e.lanes=l,e;case Eh:return e=or(19,n,t,i),e.elementType=Eh,e.lanes=l,e;case IS:return pf(n,i,l,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case OS:s=10;break e;case TS:s=9;break e;case Yg:s=11;break e;case Xg:s=14;break e;case Ai:s=16,r=null;break e}throw Error(te(130,e==null?e:typeof e,""))}return t=or(s,n,t,i),t.elementType=e,t.type=r,t.lanes=l,t}function Po(e,t,n,r){return e=or(7,e,r,t),e.lanes=n,e}function pf(e,t,n,r){return e=or(22,e,r,t),e.elementType=IS,e.lanes=n,e.stateNode={isHidden:!1},e}function Lp(e,t,n){return e=or(6,e,null,t),e.lanes=n,e}function Fp(e,t,n){return t=or(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function VN(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=vp(0),this.expirationTimes=vp(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=vp(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function Am(e,t,n,r,i,l,s,a,f){return e=new VN(e,t,n,a,f),t===1?(t=1,l===!0&&(t|=8)):t=0,l=or(3,null,null,t),e.current=l,l.stateNode=e,l.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},gm(l),e}function GN(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:pl,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function xC(e){if(!e)return eo;e=e._reactInternals;e:{if(jo(e)!==e||e.tag!==1)throw Error(te(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(wn(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(te(171))}if(e.tag===1){var n=e.type;if(wn(n))return xE(e,n,t)}return t}function SC(e,t,n,r,i,l,s,a,f){return e=Am(n,r,!0,e,i,l,s,a,f),e.context=xC(null),n=e.current,r=on(),i=Gi(n),l=ai(r,i),l.callback=t??null,Wi(n,l,i),e.current.lanes=i,Os(e,i,r),xn(e,r),e}function hf(e,t,n,r){var i=t.current,l=on(),s=Gi(i);return n=xC(n),t.context===null?t.context=n:t.pendingContext=n,t=ai(l,s),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=Wi(i,t,s),e!==null&&(kr(e,i,s,l),tc(e,i,s)),s}function Lc(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function ww(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Dm(e,t){ww(e,t),(e=e.alternate)&&ww(e,t)}function qN(){return null}var EC=typeof reportError=="function"?reportError:function(e){console.error(e)};function Nm(e){this._internalRoot=e}gf.prototype.render=Nm.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(te(409));hf(e,t,null,null)};gf.prototype.unmount=Nm.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Fo(function(){hf(null,e,null,null)}),t[fi]=null}};function gf(e){this._internalRoot=e}gf.prototype.unstable_scheduleHydration=function(e){if(e){var t=JS();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Ni.length&&t!==0&&t<Ni[n].priority;n++);Ni.splice(n,0,e),n===0&&tE(e)}};function Mm(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function mf(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function xw(){}function KN(e,t,n,r,i){if(i){if(typeof r=="function"){var l=r;r=function(){var d=Lc(s);l.call(d)}}var s=SC(t,r,e,0,null,!1,!1,"",xw);return e._reactRootContainer=s,e[fi]=s.current,ds(e.nodeType===8?e.parentNode:e),Fo(),s}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var d=Lc(f);a.call(d)}}var f=Am(e,0,!1,null,null,!1,!1,"",xw);return e._reactRootContainer=f,e[fi]=f.current,ds(e.nodeType===8?e.parentNode:e),Fo(function(){hf(t,f,n,r)}),f}function vf(e,t,n,r,i){var l=n._reactRootContainer;if(l){var s=l;if(typeof i=="function"){var a=i;i=function(){var f=Lc(s);a.call(f)}}hf(t,s,e,i)}else s=KN(n,t,e,i,r);return Lc(s)}XS=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Fu(t.pendingLanes);n!==0&&(em(t,n|1),xn(t,Ct()),!($e&6)&&(Wl=Ct()+500,uo()))}break;case 13:Fo(function(){var r=di(e,1);if(r!==null){var i=on();kr(r,e,1,i)}}),Dm(e,1)}};tm=function(e){if(e.tag===13){var t=di(e,134217728);if(t!==null){var n=on();kr(t,e,134217728,n)}Dm(e,134217728)}};ZS=function(e){if(e.tag===13){var t=Gi(e),n=di(e,t);if(n!==null){var r=on();kr(n,e,t,r)}Dm(e,t)}};JS=function(){return Ye};eE=function(e,t){var n=Ye;try{return Ye=e,t()}finally{Ye=n}};Ah=function(e,t,n){switch(t){case"input":if(bh(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=uf(r);if(!i)throw Error(te(90));PS(r),bh(r,i)}}}break;case"textarea":DS(e,n);break;case"select":t=n.value,t!=null&&bl(e,!!n.multiple,t,!1)}};BS=Tm;US=Fo;var QN={usingClientEntryPoint:!1,Events:[Is,yl,uf,zS,$S,Tm]},Au={findFiberByHostInstance:_o,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},YN={bundleType:Au.bundleType,version:Au.version,rendererPackageName:Au.rendererPackageName,rendererConfig:Au.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:gi.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=WS(e),e===null?null:e.stateNode},findFiberByHostInstance:Au.findFiberByHostInstance||qN,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ua=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ua.isDisabled&&Ua.supportsFiber)try{nf=Ua.inject(YN),$r=Ua}catch{}}Un.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=QN;Un.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Mm(t))throw Error(te(200));return GN(e,t,null,n)};Un.createRoot=function(e,t){if(!Mm(e))throw Error(te(299));var n=!1,r="",i=EC;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=Am(e,1,!1,null,null,n,!1,r,i),e[fi]=t.current,ds(e.nodeType===8?e.parentNode:e),new Nm(t)};Un.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(te(188)):(e=Object.keys(e).join(","),Error(te(268,e)));return e=WS(t),e=e===null?null:e.stateNode,e};Un.flushSync=function(e){return Fo(e)};Un.hydrate=function(e,t,n){if(!mf(t))throw Error(te(200));return vf(null,e,t,!0,n)};Un.hydrateRoot=function(e,t,n){if(!Mm(e))throw Error(te(405));var r=n!=null&&n.hydratedSources||null,i=!1,l="",s=EC;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(l=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=SC(t,null,e,1,n??null,i,!1,l,s),e[fi]=t.current,ds(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new gf(t)};Un.render=function(e,t,n){if(!mf(t))throw Error(te(200));return vf(null,e,t,!1,n)};Un.unmountComponentAtNode=function(e){if(!mf(e))throw Error(te(40));return e._reactRootContainer?(Fo(function(){vf(null,null,e,!1,function(){e._reactRootContainer=null,e[fi]=null})}),!0):!1};Un.unstable_batchedUpdates=Tm;Un.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!mf(n))throw Error(te(200));if(e==null||e._reactInternals===void 0)throw Error(te(38));return vf(e,t,n,!1,r)};Un.version="18.3.1-next-f1338f8080-20240426";function CC(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(CC)}catch(e){console.error(e)}}CC(),CS.exports=Un;var nu=CS.exports;const Ha=io(nu);var kC,Sw=nu;kC=Sw.createRoot,Sw.hydrateRoot;var bC={exports:{}},_C={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Vl=I;function XN(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ZN=typeof Object.is=="function"?Object.is:XN,JN=Vl.useState,eM=Vl.useEffect,tM=Vl.useLayoutEffect,nM=Vl.useDebugValue;function rM(e,t){var n=t(),r=JN({inst:{value:n,getSnapshot:t}}),i=r[0].inst,l=r[1];return tM(function(){i.value=n,i.getSnapshot=t,zp(i)&&l({inst:i})},[e,n,t]),eM(function(){return zp(i)&&l({inst:i}),e(function(){zp(i)&&l({inst:i})})},[e]),nM(n),n}function zp(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ZN(e,n)}catch{return!0}}function iM(e,t){return t()}var oM=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?iM:rM;_C.useSyncExternalStore=Vl.useSyncExternalStore!==void 0?Vl.useSyncExternalStore:oM;bC.exports=_C;var lM=bC.exports,OC={exports:{}},TC={};/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var yf=I,uM=lM;function sM(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var aM=typeof Object.is=="function"?Object.is:sM,cM=uM.useSyncExternalStore,fM=yf.useRef,dM=yf.useEffect,pM=yf.useMemo,hM=yf.useDebugValue;TC.useSyncExternalStoreWithSelector=function(e,t,n,r,i){var l=fM(null);if(l.current===null){var s={hasValue:!1,value:null};l.current=s}else s=l.current;l=pM(function(){function f(m){if(!d){if(d=!0,p=m,m=r(m),i!==void 0&&s.hasValue){var E=s.value;if(i(E,m))return g=E}return g=m}if(E=g,aM(p,m))return E;var S=r(m);return i!==void 0&&i(E,S)?(p=m,E):(p=m,g=S)}var d=!1,p,g,v=n===void 0?null:n;return[function(){return f(t())},v===null?void 0:function(){return f(v())}]},[t,n,r,i]);var a=cM(e,l[0],l[1]);return dM(function(){s.hasValue=!0,s.value=a},[a]),hM(a),a};OC.exports=TC;var gM=OC.exports;function mM(e){e()}let IC=mM;const vM=e=>IC=e,yM=()=>IC,Ew=Symbol.for("react-redux-context"),Cw=typeof globalThis<"u"?globalThis:{};function wM(){var e;if(!I.createContext)return{};const t=(e=Cw[Ew])!=null?e:Cw[Ew]=new Map;let n=t.get(I.createContext);return n||(n=I.createContext(null),t.set(I.createContext,n)),n}const to=wM();function Lm(e=to){return function(){return I.useContext(e)}}const RC=Lm(),xM=()=>{throw new Error("uSES not initialized!")};let PC=xM;const SM=e=>{PC=e},EM=(e,t)=>e===t;function CM(e=to){const t=e===to?RC:Lm(e);return function(r,i={}){const{equalityFn:l=EM,stabilityCheck:s=void 0,noopCheck:a=void 0}=typeof i=="function"?{equalityFn:i}:i,{store:f,subscription:d,getServerState:p,stabilityCheck:g,noopCheck:v}=t();I.useRef(!0);const m=I.useCallback({[r.name](S){return r(S)}}[r.name],[r,g,s]),E=PC(d.addNestedSub,f.getState,p||f.getState,m,l);return I.useDebugValue(E),E}}const kM=CM();function Fc(){return Fc=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Fc.apply(null,arguments)}function Fm(e,t){if(e==null)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.indexOf(r)!==-1)continue;n[r]=e[r]}return n}var AC={exports:{}},Xe={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Nt=typeof Symbol=="function"&&Symbol.for,zm=Nt?Symbol.for("react.element"):60103,$m=Nt?Symbol.for("react.portal"):60106,wf=Nt?Symbol.for("react.fragment"):60107,xf=Nt?Symbol.for("react.strict_mode"):60108,Sf=Nt?Symbol.for("react.profiler"):60114,Ef=Nt?Symbol.for("react.provider"):60109,Cf=Nt?Symbol.for("react.context"):60110,Bm=Nt?Symbol.for("react.async_mode"):60111,kf=Nt?Symbol.for("react.concurrent_mode"):60111,bf=Nt?Symbol.for("react.forward_ref"):60112,_f=Nt?Symbol.for("react.suspense"):60113,bM=Nt?Symbol.for("react.suspense_list"):60120,Of=Nt?Symbol.for("react.memo"):60115,Tf=Nt?Symbol.for("react.lazy"):60116,_M=Nt?Symbol.for("react.block"):60121,OM=Nt?Symbol.for("react.fundamental"):60117,TM=Nt?Symbol.for("react.responder"):60118,IM=Nt?Symbol.for("react.scope"):60119;function jn(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case zm:switch(e=e.type,e){case Bm:case kf:case wf:case Sf:case xf:case _f:return e;default:switch(e=e&&e.$$typeof,e){case Cf:case bf:case Tf:case Of:case Ef:return e;default:return t}}case $m:return t}}}function DC(e){return jn(e)===kf}Xe.AsyncMode=Bm;Xe.ConcurrentMode=kf;Xe.ContextConsumer=Cf;Xe.ContextProvider=Ef;Xe.Element=zm;Xe.ForwardRef=bf;Xe.Fragment=wf;Xe.Lazy=Tf;Xe.Memo=Of;Xe.Portal=$m;Xe.Profiler=Sf;Xe.StrictMode=xf;Xe.Suspense=_f;Xe.isAsyncMode=function(e){return DC(e)||jn(e)===Bm};Xe.isConcurrentMode=DC;Xe.isContextConsumer=function(e){return jn(e)===Cf};Xe.isContextProvider=function(e){return jn(e)===Ef};Xe.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===zm};Xe.isForwardRef=function(e){return jn(e)===bf};Xe.isFragment=function(e){return jn(e)===wf};Xe.isLazy=function(e){return jn(e)===Tf};Xe.isMemo=function(e){return jn(e)===Of};Xe.isPortal=function(e){return jn(e)===$m};Xe.isProfiler=function(e){return jn(e)===Sf};Xe.isStrictMode=function(e){return jn(e)===xf};Xe.isSuspense=function(e){return jn(e)===_f};Xe.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===wf||e===kf||e===Sf||e===xf||e===_f||e===bM||typeof e=="object"&&e!==null&&(e.$$typeof===Tf||e.$$typeof===Of||e.$$typeof===Ef||e.$$typeof===Cf||e.$$typeof===bf||e.$$typeof===OM||e.$$typeof===TM||e.$$typeof===IM||e.$$typeof===_M)};Xe.typeOf=jn;AC.exports=Xe;var RM=AC.exports,NC=RM,PM={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},AM={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},MC={};MC[NC.ForwardRef]=PM;MC[NC.Memo]=AM;var LC={exports:{}},Ze={};/**
 * @license React
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Um=Symbol.for("react.element"),Hm=Symbol.for("react.portal"),If=Symbol.for("react.fragment"),Rf=Symbol.for("react.strict_mode"),Pf=Symbol.for("react.profiler"),Af=Symbol.for("react.provider"),Df=Symbol.for("react.context"),DM=Symbol.for("react.server_context"),Nf=Symbol.for("react.forward_ref"),Mf=Symbol.for("react.suspense"),Lf=Symbol.for("react.suspense_list"),Ff=Symbol.for("react.memo"),zf=Symbol.for("react.lazy"),NM=Symbol.for("react.offscreen"),FC;FC=Symbol.for("react.module.reference");function cr(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case Um:switch(e=e.type,e){case If:case Pf:case Rf:case Mf:case Lf:return e;default:switch(e=e&&e.$$typeof,e){case DM:case Df:case Nf:case zf:case Ff:case Af:return e;default:return t}}case Hm:return t}}}Ze.ContextConsumer=Df;Ze.ContextProvider=Af;Ze.Element=Um;Ze.ForwardRef=Nf;Ze.Fragment=If;Ze.Lazy=zf;Ze.Memo=Ff;Ze.Portal=Hm;Ze.Profiler=Pf;Ze.StrictMode=Rf;Ze.Suspense=Mf;Ze.SuspenseList=Lf;Ze.isAsyncMode=function(){return!1};Ze.isConcurrentMode=function(){return!1};Ze.isContextConsumer=function(e){return cr(e)===Df};Ze.isContextProvider=function(e){return cr(e)===Af};Ze.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===Um};Ze.isForwardRef=function(e){return cr(e)===Nf};Ze.isFragment=function(e){return cr(e)===If};Ze.isLazy=function(e){return cr(e)===zf};Ze.isMemo=function(e){return cr(e)===Ff};Ze.isPortal=function(e){return cr(e)===Hm};Ze.isProfiler=function(e){return cr(e)===Pf};Ze.isStrictMode=function(e){return cr(e)===Rf};Ze.isSuspense=function(e){return cr(e)===Mf};Ze.isSuspenseList=function(e){return cr(e)===Lf};Ze.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===If||e===Pf||e===Rf||e===Mf||e===Lf||e===NM||typeof e=="object"&&e!==null&&(e.$$typeof===zf||e.$$typeof===Ff||e.$$typeof===Af||e.$$typeof===Df||e.$$typeof===Nf||e.$$typeof===FC||e.getModuleId!==void 0)};Ze.typeOf=cr;LC.exports=Ze;var MM=LC.exports;const LM=io(MM);function FM(){const e=yM();let t=null,n=null;return{clear(){t=null,n=null},notify(){e(()=>{let r=t;for(;r;)r.callback(),r=r.next})},get(){let r=[],i=t;for(;i;)r.push(i),i=i.next;return r},subscribe(r){let i=!0,l=n={callback:r,next:null,prev:n};return l.prev?l.prev.next=l:t=l,function(){!i||t===null||(i=!1,l.next?l.next.prev=l.prev:n=l.prev,l.prev?l.prev.next=l.next:t=l.next)}}}}const kw={notify(){},get:()=>[]};function zM(e,t){let n,r=kw,i=0,l=!1;function s(S){p();const A=r.subscribe(S);let y=!1;return()=>{y||(y=!0,A(),g())}}function a(){r.notify()}function f(){E.onStateChange&&E.onStateChange()}function d(){return l}function p(){i++,n||(n=t?t.addNestedSub(f):e.subscribe(f),r=FM())}function g(){i--,n&&i===0&&(n(),n=void 0,r.clear(),r=kw)}function v(){l||(l=!0,p())}function m(){l&&(l=!1,g())}const E={addNestedSub:s,notifyNestedSubs:a,handleChangeWrapper:f,isSubscribed:d,trySubscribe:v,tryUnsubscribe:m,getListeners:()=>r};return E}const $M=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",BM=$M?I.useLayoutEffect:I.useEffect;function UM({store:e,context:t,children:n,serverState:r,stabilityCheck:i="once",noopCheck:l="once"}){const s=I.useMemo(()=>{const d=zM(e);return{store:e,subscription:d,getServerState:r?()=>r:void 0,stabilityCheck:i,noopCheck:l}},[e,r,i,l]),a=I.useMemo(()=>e.getState(),[e]);BM(()=>{const{subscription:d}=s;return d.onStateChange=d.notifyNestedSubs,d.trySubscribe(),a!==e.getState()&&d.notifyNestedSubs(),()=>{d.tryUnsubscribe(),d.onStateChange=void 0}},[s,a]);const f=t||to;return I.createElement(f.Provider,{value:s},n)}function zC(e=to){const t=e===to?RC:Lm(e);return function(){const{store:r}=t();return r}}const HM=zC();function jM(e=to){const t=e===to?HM:zC(e);return function(){return t().dispatch}}const WM=jM();SM(gM.useSyncExternalStoreWithSelector);vM(nu.unstable_batchedUpdates);const $C=I.createContext({dragDropManager:void 0});function Ss(e){"@babel/helpers - typeof";return Ss=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ss(e)}function VM(e,t){if(Ss(e)!="object"||!e)return e;var n=e[Symbol.toPrimitive];if(n!==void 0){var r=n.call(e,t||"default");if(Ss(r)!="object")return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return(t==="string"?String:Number)(e)}function GM(e){var t=VM(e,"string");return Ss(t)=="symbol"?t:t+""}function qM(e,t,n){return(t=GM(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function bw(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function _w(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?bw(Object(n),!0).forEach(function(r){qM(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):bw(Object(n)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function qt(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var Ow=function(){return typeof Symbol=="function"&&Symbol.observable||"@@observable"}(),$p=function(){return Math.random().toString(36).substring(7).split("").join(".")},zc={INIT:"@@redux/INIT"+$p(),REPLACE:"@@redux/REPLACE"+$p(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+$p()}};function KM(e){if(typeof e!="object"||e===null)return!1;for(var t=e;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function jm(e,t,n){var r;if(typeof t=="function"&&typeof n=="function"||typeof n=="function"&&typeof arguments[3]=="function")throw new Error(qt(0));if(typeof t=="function"&&typeof n>"u"&&(n=t,t=void 0),typeof n<"u"){if(typeof n!="function")throw new Error(qt(1));return n(jm)(e,t)}if(typeof e!="function")throw new Error(qt(2));var i=e,l=t,s=[],a=s,f=!1;function d(){a===s&&(a=s.slice())}function p(){if(f)throw new Error(qt(3));return l}function g(S){if(typeof S!="function")throw new Error(qt(4));if(f)throw new Error(qt(5));var A=!0;return d(),a.push(S),function(){if(A){if(f)throw new Error(qt(6));A=!1,d();var x=a.indexOf(S);a.splice(x,1),s=null}}}function v(S){if(!KM(S))throw new Error(qt(7));if(typeof S.type>"u")throw new Error(qt(8));if(f)throw new Error(qt(9));try{f=!0,l=i(l,S)}finally{f=!1}for(var A=s=a,y=0;y<A.length;y++){var x=A[y];x()}return S}function m(S){if(typeof S!="function")throw new Error(qt(10));i=S,v({type:zc.REPLACE})}function E(){var S,A=g;return S={subscribe:function(x){if(typeof x!="object"||x===null)throw new Error(qt(11));function k(){x.next&&x.next(p())}k();var P=A(k);return{unsubscribe:P}}},S[Ow]=function(){return this},S}return v({type:zc.INIT}),r={dispatch:v,subscribe:g,getState:p,replaceReducer:m},r[Ow]=E,r}function QM(e){Object.keys(e).forEach(function(t){var n=e[t],r=n(void 0,{type:zc.INIT});if(typeof r>"u")throw new Error(qt(12));if(typeof n(void 0,{type:zc.PROBE_UNKNOWN_ACTION()})>"u")throw new Error(qt(13))})}function YM(e){for(var t=Object.keys(e),n={},r=0;r<t.length;r++){var i=t[r];typeof e[i]=="function"&&(n[i]=e[i])}var l=Object.keys(n),s;try{QM(n)}catch(a){s=a}return function(f,d){if(f===void 0&&(f={}),s)throw s;for(var p=!1,g={},v=0;v<l.length;v++){var m=l[v],E=n[m],S=f[m],A=E(S,d);if(typeof A>"u")throw d&&d.type,new Error(qt(14));g[m]=A,p=p||A!==S}return p=p||l.length!==Object.keys(f).length,p?g:f}}function $c(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.length===0?function(r){return r}:t.length===1?t[0]:t.reduce(function(r,i){return function(){return r(i.apply(void 0,arguments))}})}function XM(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(r){return function(){var i=r.apply(void 0,arguments),l=function(){throw new Error(qt(15))},s={getState:i.getState,dispatch:function(){return l.apply(void 0,arguments)}},a=t.map(function(f){return f(s)});return l=$c.apply(void 0,a)(i.dispatch),_w(_w({},i),{},{dispatch:l})}}}function be(e,t,...n){if(ZM()&&t===void 0)throw new Error("invariant requires an error message argument");if(!e){let r;if(t===void 0)r=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{let i=0;r=new Error(t.replace(/%s/g,function(){return n[i++]})),r.name="Invariant Violation"}throw r.framesToPop=1,r}}function ZM(){return typeof process<"u"&&{}.NODE_ENV==="production"}function JM(e,t,n){return t.split(".").reduce((r,i)=>r&&r[i]?r[i]:n||null,e)}function eL(e,t){return e.filter(n=>n!==t)}function BC(e){return typeof e=="object"}function tL(e,t){const n=new Map,r=l=>{n.set(l,n.has(l)?n.get(l)+1:1)};e.forEach(r),t.forEach(r);const i=[];return n.forEach((l,s)=>{l===1&&i.push(s)}),i}function nL(e,t){return e.filter(n=>t.indexOf(n)>-1)}const Wm="dnd-core/INIT_COORDS",$f="dnd-core/BEGIN_DRAG",Vm="dnd-core/PUBLISH_DRAG_SOURCE",Bf="dnd-core/HOVER",Uf="dnd-core/DROP",Hf="dnd-core/END_DRAG";function Tw(e,t){return{type:Wm,payload:{sourceClientOffset:t||null,clientOffset:e||null}}}const rL={type:Wm,payload:{clientOffset:null,sourceClientOffset:null}};function iL(e){return function(n=[],r={publishSource:!0}){const{publishSource:i=!0,clientOffset:l,getSourceClientOffset:s}=r,a=e.getMonitor(),f=e.getRegistry();e.dispatch(Tw(l)),oL(n,a,f);const d=sL(n,a);if(d==null){e.dispatch(rL);return}let p=null;if(l){if(!s)throw new Error("getSourceClientOffset must be defined");lL(s),p=s(d)}e.dispatch(Tw(l,p));const v=f.getSource(d).beginDrag(a,d);if(v==null)return;uL(v),f.pinSource(d);const m=f.getSourceType(d);return{type:$f,payload:{itemType:m,item:v,sourceId:d,clientOffset:l||null,sourceClientOffset:p||null,isSourcePublic:!!i}}}}function oL(e,t,n){be(!t.isDragging(),"Cannot call beginDrag while dragging."),e.forEach(function(r){be(n.getSource(r),"Expected sourceIds to be registered.")})}function lL(e){be(typeof e=="function","When clientOffset is provided, getSourceClientOffset must be a function.")}function uL(e){be(BC(e),"Item must be an object.")}function sL(e,t){let n=null;for(let r=e.length-1;r>=0;r--)if(t.canDragSource(e[r])){n=e[r];break}return n}function aL(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function cL(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable}))),r.forEach(function(i){aL(e,i,n[i])})}return e}function fL(e){return function(n={}){const r=e.getMonitor(),i=e.getRegistry();dL(r),gL(r).forEach((s,a)=>{const f=pL(s,a,i,r),d={type:Uf,payload:{dropResult:cL({},n,f)}};e.dispatch(d)})}}function dL(e){be(e.isDragging(),"Cannot call drop while not dragging."),be(!e.didDrop(),"Cannot call drop twice during one drag operation.")}function pL(e,t,n,r){const i=n.getTarget(e);let l=i?i.drop(r,e):void 0;return hL(l),typeof l>"u"&&(l=t===0?{}:r.getDropResult()),l}function hL(e){be(typeof e>"u"||BC(e),"Drop result must either be an object or undefined.")}function gL(e){const t=e.getTargetIds().filter(e.canDropOnTarget,e);return t.reverse(),t}function mL(e){return function(){const n=e.getMonitor(),r=e.getRegistry();vL(n);const i=n.getSourceId();return i!=null&&(r.getSource(i,!0).endDrag(n,i),r.unpinSource()),{type:Hf}}}function vL(e){be(e.isDragging(),"Cannot call endDrag while not dragging.")}function fg(e,t){return t===null?e===null:Array.isArray(e)?e.some(n=>n===t):e===t}function yL(e){return function(n,{clientOffset:r}={}){wL(n);const i=n.slice(0),l=e.getMonitor(),s=e.getRegistry(),a=l.getItemType();return SL(i,s,a),xL(i,l,s),EL(i,l,s),{type:Bf,payload:{targetIds:i,clientOffset:r||null}}}}function wL(e){be(Array.isArray(e),"Expected targetIds to be an array.")}function xL(e,t,n){be(t.isDragging(),"Cannot call hover while not dragging."),be(!t.didDrop(),"Cannot call hover after drop.");for(let r=0;r<e.length;r++){const i=e[r];be(e.lastIndexOf(i)===r,"Expected targetIds to be unique in the passed array.");const l=n.getTarget(i);be(l,"Expected targetIds to be registered.")}}function SL(e,t,n){for(let r=e.length-1;r>=0;r--){const i=e[r],l=t.getTargetType(i);fg(l,n)||e.splice(r,1)}}function EL(e,t,n){e.forEach(function(r){n.getTarget(r).hover(t,r)})}function CL(e){return function(){if(e.getMonitor().isDragging())return{type:Vm}}}function kL(e){return{beginDrag:iL(e),publishDragSource:CL(e),hover:yL(e),drop:fL(e),endDrag:mL(e)}}class bL{receiveBackend(t){this.backend=t}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){const t=this,{dispatch:n}=this.store;function r(l){return(...s)=>{const a=l.apply(t,s);typeof a<"u"&&n(a)}}const i=kL(this);return Object.keys(i).reduce((l,s)=>{const a=i[s];return l[s]=r(a),l},{})}dispatch(t){this.store.dispatch(t)}constructor(t,n){this.isSetUp=!1,this.handleRefCountChange=()=>{const r=this.store.getState().refCount>0;this.backend&&(r&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!r&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1))},this.store=t,this.monitor=n,t.subscribe(this.handleRefCountChange)}}function _L(e,t){return{x:e.x+t.x,y:e.y+t.y}}function UC(e,t){return{x:e.x-t.x,y:e.y-t.y}}function OL(e){const{clientOffset:t,initialClientOffset:n,initialSourceClientOffset:r}=e;return!t||!n||!r?null:UC(_L(t,r),n)}function TL(e){const{clientOffset:t,initialClientOffset:n}=e;return!t||!n?null:UC(t,n)}const Xu=[],Gm=[];Xu.__IS_NONE__=!0;Gm.__IS_ALL__=!0;function IL(e,t){return e===Xu?!1:e===Gm||typeof t>"u"?!0:nL(t,e).length>0}class RL{subscribeToStateChange(t,n={}){const{handlerIds:r}=n;be(typeof t=="function","listener must be a function."),be(typeof r>"u"||Array.isArray(r),"handlerIds, when specified, must be an array of strings.");let i=this.store.getState().stateId;const l=()=>{const s=this.store.getState(),a=s.stateId;try{a===i||a===i+1&&!IL(s.dirtyHandlerIds,r)||t()}finally{i=a}};return this.store.subscribe(l)}subscribeToOffsetChange(t){be(typeof t=="function","listener must be a function.");let n=this.store.getState().dragOffset;const r=()=>{const i=this.store.getState().dragOffset;i!==n&&(n=i,t())};return this.store.subscribe(r)}canDragSource(t){if(!t)return!1;const n=this.registry.getSource(t);return be(n,`Expected to find a valid source. sourceId=${t}`),this.isDragging()?!1:n.canDrag(this,t)}canDropOnTarget(t){if(!t)return!1;const n=this.registry.getTarget(t);if(be(n,`Expected to find a valid target. targetId=${t}`),!this.isDragging()||this.didDrop())return!1;const r=this.registry.getTargetType(t),i=this.getItemType();return fg(r,i)&&n.canDrop(this,t)}isDragging(){return!!this.getItemType()}isDraggingSource(t){if(!t)return!1;const n=this.registry.getSource(t,!0);if(be(n,`Expected to find a valid source. sourceId=${t}`),!this.isDragging()||!this.isSourcePublic())return!1;const r=this.registry.getSourceType(t),i=this.getItemType();return r!==i?!1:n.isDragging(this,t)}isOverTarget(t,n={shallow:!1}){if(!t)return!1;const{shallow:r}=n;if(!this.isDragging())return!1;const i=this.registry.getTargetType(t),l=this.getItemType();if(l&&!fg(i,l))return!1;const s=this.getTargetIds();if(!s.length)return!1;const a=s.indexOf(t);return r?a===s.length-1:a>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return!!this.store.getState().dragOperation.isSourcePublic}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return OL(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return TL(this.store.getState().dragOffset)}constructor(t,n){this.store=t,this.registry=n}}const Iw=typeof global<"u"?global:self,HC=Iw.MutationObserver||Iw.WebKitMutationObserver;function jC(e){return function(){const n=setTimeout(i,0),r=setInterval(i,50);function i(){clearTimeout(n),clearInterval(r),e()}}}function PL(e){let t=1;const n=new HC(e),r=document.createTextNode("");return n.observe(r,{characterData:!0}),function(){t=-t,r.data=t}}const AL=typeof HC=="function"?PL:jC;class DL{enqueueTask(t){const{queue:n,requestFlush:r}=this;n.length||(r(),this.flushing=!0),n[n.length]=t}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{const{queue:t}=this;for(;this.index<t.length;){const n=this.index;if(this.index++,t[n].call(),this.index>this.capacity){for(let r=0,i=t.length-this.index;r<i;r++)t[r]=t[r+this.index];t.length-=this.index,this.index=0}}t.length=0,this.index=0,this.flushing=!1},this.registerPendingError=t=>{this.pendingErrors.push(t),this.requestErrorThrow()},this.requestFlush=AL(this.flush),this.requestErrorThrow=jC(()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()})}}class NL{call(){try{this.task&&this.task()}catch(t){this.onError(t)}finally{this.task=null,this.release(this)}}constructor(t,n){this.onError=t,this.release=n,this.task=null}}class ML{create(t){const n=this.freeTasks,r=n.length?n.pop():new NL(this.onError,i=>n[n.length]=i);return r.task=t,r}constructor(t){this.onError=t,this.freeTasks=[]}}const WC=new DL,LL=new ML(WC.registerPendingError);function FL(e){WC.enqueueTask(LL.create(e))}const qm="dnd-core/ADD_SOURCE",Km="dnd-core/ADD_TARGET",Qm="dnd-core/REMOVE_SOURCE",jf="dnd-core/REMOVE_TARGET";function zL(e){return{type:qm,payload:{sourceId:e}}}function $L(e){return{type:Km,payload:{targetId:e}}}function BL(e){return{type:Qm,payload:{sourceId:e}}}function UL(e){return{type:jf,payload:{targetId:e}}}function HL(e){be(typeof e.canDrag=="function","Expected canDrag to be a function."),be(typeof e.beginDrag=="function","Expected beginDrag to be a function."),be(typeof e.endDrag=="function","Expected endDrag to be a function.")}function jL(e){be(typeof e.canDrop=="function","Expected canDrop to be a function."),be(typeof e.hover=="function","Expected hover to be a function."),be(typeof e.drop=="function","Expected beginDrag to be a function.")}function dg(e,t){if(t&&Array.isArray(e)){e.forEach(n=>dg(n,!1));return}be(typeof e=="string"||typeof e=="symbol",t?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}var rr;(function(e){e.SOURCE="SOURCE",e.TARGET="TARGET"})(rr||(rr={}));let WL=0;function VL(){return WL++}function GL(e){const t=VL().toString();switch(e){case rr.SOURCE:return`S${t}`;case rr.TARGET:return`T${t}`;default:throw new Error(`Unknown Handler Role: ${e}`)}}function Rw(e){switch(e[0]){case"S":return rr.SOURCE;case"T":return rr.TARGET;default:throw new Error(`Cannot parse handler ID: ${e}`)}}function Pw(e,t){const n=e.entries();let r=!1;do{const{done:i,value:[,l]}=n.next();if(l===t)return!0;r=!!i}while(!r);return!1}class qL{addSource(t,n){dg(t),HL(n);const r=this.addHandler(rr.SOURCE,t,n);return this.store.dispatch(zL(r)),r}addTarget(t,n){dg(t,!0),jL(n);const r=this.addHandler(rr.TARGET,t,n);return this.store.dispatch($L(r)),r}containsHandler(t){return Pw(this.dragSources,t)||Pw(this.dropTargets,t)}getSource(t,n=!1){return be(this.isSourceId(t),"Expected a valid source ID."),n&&t===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(t)}getTarget(t){return be(this.isTargetId(t),"Expected a valid target ID."),this.dropTargets.get(t)}getSourceType(t){return be(this.isSourceId(t),"Expected a valid source ID."),this.types.get(t)}getTargetType(t){return be(this.isTargetId(t),"Expected a valid target ID."),this.types.get(t)}isSourceId(t){return Rw(t)===rr.SOURCE}isTargetId(t){return Rw(t)===rr.TARGET}removeSource(t){be(this.getSource(t),"Expected an existing source."),this.store.dispatch(BL(t)),FL(()=>{this.dragSources.delete(t),this.types.delete(t)})}removeTarget(t){be(this.getTarget(t),"Expected an existing target."),this.store.dispatch(UL(t)),this.dropTargets.delete(t),this.types.delete(t)}pinSource(t){const n=this.getSource(t);be(n,"Expected an existing source."),this.pinnedSourceId=t,this.pinnedSource=n}unpinSource(){be(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(t,n,r){const i=GL(t);return this.types.set(i,n),t===rr.SOURCE?this.dragSources.set(i,r):t===rr.TARGET&&this.dropTargets.set(i,r),i}constructor(t){this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null,this.store=t}}const KL=(e,t)=>e===t;function QL(e,t){return!e&&!t?!0:!e||!t?!1:e.x===t.x&&e.y===t.y}function YL(e,t,n=KL){if(e.length!==t.length)return!1;for(let r=0;r<e.length;++r)if(!n(e[r],t[r]))return!1;return!0}function XL(e=Xu,t){switch(t.type){case Bf:break;case qm:case Km:case jf:case Qm:return Xu;case $f:case Vm:case Hf:case Uf:default:return Gm}const{targetIds:n=[],prevTargetIds:r=[]}=t.payload,i=tL(n,r);if(!(i.length>0||!YL(n,r)))return Xu;const s=r[r.length-1],a=n[n.length-1];return s!==a&&(s&&i.push(s),a&&i.push(a)),i}function ZL(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function JL(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable}))),r.forEach(function(i){ZL(e,i,n[i])})}return e}const Aw={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function eF(e=Aw,t){const{payload:n}=t;switch(t.type){case Wm:case $f:return{initialSourceClientOffset:n.sourceClientOffset,initialClientOffset:n.clientOffset,clientOffset:n.clientOffset};case Bf:return QL(e.clientOffset,n.clientOffset)?e:JL({},e,{clientOffset:n.clientOffset});case Hf:case Uf:return Aw;default:return e}}function tF(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function sl(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable}))),r.forEach(function(i){tF(e,i,n[i])})}return e}const nF={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function rF(e=nF,t){const{payload:n}=t;switch(t.type){case $f:return sl({},e,{itemType:n.itemType,item:n.item,sourceId:n.sourceId,isSourcePublic:n.isSourcePublic,dropResult:null,didDrop:!1});case Vm:return sl({},e,{isSourcePublic:!0});case Bf:return sl({},e,{targetIds:n.targetIds});case jf:return e.targetIds.indexOf(n.targetId)===-1?e:sl({},e,{targetIds:eL(e.targetIds,n.targetId)});case Uf:return sl({},e,{dropResult:n.dropResult,didDrop:!0,targetIds:[]});case Hf:return sl({},e,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return e}}function iF(e=0,t){switch(t.type){case qm:case Km:return e+1;case Qm:case jf:return e-1;default:return e}}function oF(e=0){return e+1}function lF(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function uF(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{},r=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(i){return Object.getOwnPropertyDescriptor(n,i).enumerable}))),r.forEach(function(i){lF(e,i,n[i])})}return e}function sF(e={},t){return{dirtyHandlerIds:XL(e.dirtyHandlerIds,{type:t.type,payload:uF({},t.payload,{prevTargetIds:JM(e,"dragOperation.targetIds",[])})}),dragOffset:eF(e.dragOffset,t),refCount:iF(e.refCount,t),dragOperation:rF(e.dragOperation,t),stateId:oF(e.stateId)}}function aF(e,t=void 0,n={},r=!1){const i=cF(r),l=new RL(i,new qL(i)),s=new bL(i,l),a=e(s,t,n);return s.receiveBackend(a),s}function cF(e){const t=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION__;return jm(sF,e&&t&&t({name:"dnd-core",instanceId:"dnd-core"}))}function fF(e,t){if(e==null)return{};var n=dF(e,t),r,i;if(Object.getOwnPropertySymbols){var l=Object.getOwnPropertySymbols(e);for(i=0;i<l.length;i++)r=l[i],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function dF(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,l;for(l=0;l<r.length;l++)i=r[l],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}let Dw=0;const sc=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");var pF=I.memo(function(t){var{children:n}=t,r=fF(t,["children"]);const[i,l]=hF(r);return I.useEffect(()=>{if(l){const s=VC();return++Dw,()=>{--Dw===0&&(s[sc]=null)}}},[]),W($C.Provider,{value:i,children:n})});function hF(e){if("manager"in e)return[{dragDropManager:e.manager},!1];const t=gF(e.backend,e.context,e.options,e.debugMode),n=!e.context;return[t,n]}function gF(e,t=VC(),n,r){const i=t;return i[sc]||(i[sc]={dragDropManager:aF(e,t,n,r)}),i[sc]}function VC(){return typeof global<"u"?global:window}var mF=function e(t,n){if(t===n)return!0;if(t&&n&&typeof t=="object"&&typeof n=="object"){if(t.constructor!==n.constructor)return!1;var r,i,l;if(Array.isArray(t)){if(r=t.length,r!=n.length)return!1;for(i=r;i--!==0;)if(!e(t[i],n[i]))return!1;return!0}if(t.constructor===RegExp)return t.source===n.source&&t.flags===n.flags;if(t.valueOf!==Object.prototype.valueOf)return t.valueOf()===n.valueOf();if(t.toString!==Object.prototype.toString)return t.toString()===n.toString();if(l=Object.keys(t),r=l.length,r!==Object.keys(n).length)return!1;for(i=r;i--!==0;)if(!Object.prototype.hasOwnProperty.call(n,l[i]))return!1;for(i=r;i--!==0;){var s=l[i];if(!e(t[s],n[s]))return!1}return!0}return t!==t&&n!==n};const vF=io(mF),zo=typeof window<"u"?I.useLayoutEffect:I.useEffect;function GC(e,t,n){const[r,i]=I.useState(()=>t(e)),l=I.useCallback(()=>{const s=t(e);vF(r,s)||(i(s),n&&n())},[r,e,n]);return zo(l),[r,l]}function yF(e,t,n){const[r,i]=GC(e,t,n);return zo(function(){const s=e.getHandlerId();if(s!=null)return e.subscribeToStateChange(i,{handlerIds:[s]})},[e,i]),r}function qC(e,t,n){return yF(t,e||(()=>({})),()=>n.reconnect())}function KC(e,t){const n=[...t||[]];return t==null&&typeof e!="function"&&n.push(e),I.useMemo(()=>typeof e=="function"?e():e,n)}function wF(e){return I.useMemo(()=>e.hooks.dragSource(),[e])}function xF(e){return I.useMemo(()=>e.hooks.dragPreview(),[e])}let Bp=!1,Up=!1;class SF{receiveHandlerId(t){this.sourceId=t}getHandlerId(){return this.sourceId}canDrag(){be(!Bp,"You may not call monitor.canDrag() inside your canDrag() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return Bp=!0,this.internalMonitor.canDragSource(this.sourceId)}finally{Bp=!1}}isDragging(){if(!this.sourceId)return!1;be(!Up,"You may not call monitor.isDragging() inside your isDragging() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drag-source-monitor");try{return Up=!0,this.internalMonitor.isDraggingSource(this.sourceId)}finally{Up=!1}}subscribeToStateChange(t,n){return this.internalMonitor.subscribeToStateChange(t,n)}isDraggingSource(t){return this.internalMonitor.isDraggingSource(t)}isOverTarget(t,n){return this.internalMonitor.isOverTarget(t,n)}getTargetIds(){return this.internalMonitor.getTargetIds()}isSourcePublic(){return this.internalMonitor.isSourcePublic()}getSourceId(){return this.internalMonitor.getSourceId()}subscribeToOffsetChange(t){return this.internalMonitor.subscribeToOffsetChange(t)}canDragSource(t){return this.internalMonitor.canDragSource(t)}canDropOnTarget(t){return this.internalMonitor.canDropOnTarget(t)}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(t){this.sourceId=null,this.internalMonitor=t.getMonitor()}}let Hp=!1;class EF{receiveHandlerId(t){this.targetId=t}getHandlerId(){return this.targetId}subscribeToStateChange(t,n){return this.internalMonitor.subscribeToStateChange(t,n)}canDrop(){if(!this.targetId)return!1;be(!Hp,"You may not call monitor.canDrop() inside your canDrop() implementation. Read more: http://react-dnd.github.io/react-dnd/docs/api/drop-target-monitor");try{return Hp=!0,this.internalMonitor.canDropOnTarget(this.targetId)}finally{Hp=!1}}isOver(t){return this.targetId?this.internalMonitor.isOverTarget(this.targetId,t):!1}getItemType(){return this.internalMonitor.getItemType()}getItem(){return this.internalMonitor.getItem()}getDropResult(){return this.internalMonitor.getDropResult()}didDrop(){return this.internalMonitor.didDrop()}getInitialClientOffset(){return this.internalMonitor.getInitialClientOffset()}getInitialSourceClientOffset(){return this.internalMonitor.getInitialSourceClientOffset()}getSourceClientOffset(){return this.internalMonitor.getSourceClientOffset()}getClientOffset(){return this.internalMonitor.getClientOffset()}getDifferenceFromInitialOffset(){return this.internalMonitor.getDifferenceFromInitialOffset()}constructor(t){this.targetId=null,this.internalMonitor=t.getMonitor()}}function CF(e,t,n){const r=n.getRegistry(),i=r.addTarget(e,t);return[i,()=>r.removeTarget(i)]}function kF(e,t,n){const r=n.getRegistry(),i=r.addSource(e,t);return[i,()=>r.removeSource(i)]}function pg(e,t,n,r){let i=n?n.call(r,e,t):void 0;if(i!==void 0)return!!i;if(e===t)return!0;if(typeof e!="object"||!e||typeof t!="object"||!t)return!1;const l=Object.keys(e),s=Object.keys(t);if(l.length!==s.length)return!1;const a=Object.prototype.hasOwnProperty.bind(t);for(let f=0;f<l.length;f++){const d=l[f];if(!a(d))return!1;const p=e[d],g=t[d];if(i=n?n.call(r,p,g,d):void 0,i===!1||i===void 0&&p!==g)return!1}return!0}function hg(e){return e!==null&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function bF(e){if(typeof e.type=="string")return;const t=e.type.displayName||e.type.name||"the component";throw new Error(`Only native element nodes can now be passed to React DnD connectors.You can either wrap ${t} into a <div>, or turn it into a drag source or a drop target itself.`)}function _F(e){return(t=null,n=null)=>{if(!I.isValidElement(t)){const l=t;return e(l,n),l}const r=t;return bF(r),OF(r,n?l=>e(l,n):e)}}function QC(e){const t={};return Object.keys(e).forEach(n=>{const r=e[n];if(n.endsWith("Ref"))t[n]=e[n];else{const i=_F(r);t[n]=()=>i}}),t}function Nw(e,t){typeof e=="function"?e(t):e.current=t}function OF(e,t){const n=e.ref;return be(typeof n!="string","Cannot connect React DnD to an element with an existing string ref. Please convert it to use a callback ref instead, or wrap it into a <span> or <div>. Read more: https://reactjs.org/docs/refs-and-the-dom.html#callback-refs"),n?I.cloneElement(e,{ref:r=>{Nw(n,r),Nw(t,r)}}):I.cloneElement(e,{ref:t})}class TF{receiveHandlerId(t){this.handlerId!==t&&(this.handlerId=t,this.reconnect())}get connectTarget(){return this.dragSource}get dragSourceOptions(){return this.dragSourceOptionsInternal}set dragSourceOptions(t){this.dragSourceOptionsInternal=t}get dragPreviewOptions(){return this.dragPreviewOptionsInternal}set dragPreviewOptions(t){this.dragPreviewOptionsInternal=t}reconnect(){const t=this.reconnectDragSource();this.reconnectDragPreview(t)}reconnectDragSource(){const t=this.dragSource,n=this.didHandlerIdChange()||this.didConnectedDragSourceChange()||this.didDragSourceOptionsChange();return n&&this.disconnectDragSource(),this.handlerId?t?(n&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragSource=t,this.lastConnectedDragSourceOptions=this.dragSourceOptions,this.dragSourceUnsubscribe=this.backend.connectDragSource(this.handlerId,t,this.dragSourceOptions)),n):(this.lastConnectedDragSource=t,n):n}reconnectDragPreview(t=!1){const n=this.dragPreview,r=t||this.didHandlerIdChange()||this.didConnectedDragPreviewChange()||this.didDragPreviewOptionsChange();if(r&&this.disconnectDragPreview(),!!this.handlerId){if(!n){this.lastConnectedDragPreview=n;return}r&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDragPreview=n,this.lastConnectedDragPreviewOptions=this.dragPreviewOptions,this.dragPreviewUnsubscribe=this.backend.connectDragPreview(this.handlerId,n,this.dragPreviewOptions))}}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didConnectedDragSourceChange(){return this.lastConnectedDragSource!==this.dragSource}didConnectedDragPreviewChange(){return this.lastConnectedDragPreview!==this.dragPreview}didDragSourceOptionsChange(){return!pg(this.lastConnectedDragSourceOptions,this.dragSourceOptions)}didDragPreviewOptionsChange(){return!pg(this.lastConnectedDragPreviewOptions,this.dragPreviewOptions)}disconnectDragSource(){this.dragSourceUnsubscribe&&(this.dragSourceUnsubscribe(),this.dragSourceUnsubscribe=void 0)}disconnectDragPreview(){this.dragPreviewUnsubscribe&&(this.dragPreviewUnsubscribe(),this.dragPreviewUnsubscribe=void 0,this.dragPreviewNode=null,this.dragPreviewRef=null)}get dragSource(){return this.dragSourceNode||this.dragSourceRef&&this.dragSourceRef.current}get dragPreview(){return this.dragPreviewNode||this.dragPreviewRef&&this.dragPreviewRef.current}clearDragSource(){this.dragSourceNode=null,this.dragSourceRef=null}clearDragPreview(){this.dragPreviewNode=null,this.dragPreviewRef=null}constructor(t){this.hooks=QC({dragSource:(n,r)=>{this.clearDragSource(),this.dragSourceOptions=r||null,hg(n)?this.dragSourceRef=n:this.dragSourceNode=n,this.reconnectDragSource()},dragPreview:(n,r)=>{this.clearDragPreview(),this.dragPreviewOptions=r||null,hg(n)?this.dragPreviewRef=n:this.dragPreviewNode=n,this.reconnectDragPreview()}}),this.handlerId=null,this.dragSourceRef=null,this.dragSourceOptionsInternal=null,this.dragPreviewRef=null,this.dragPreviewOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDragSource=null,this.lastConnectedDragSourceOptions=null,this.lastConnectedDragPreview=null,this.lastConnectedDragPreviewOptions=null,this.backend=t}}class IF{get connectTarget(){return this.dropTarget}reconnect(){const t=this.didHandlerIdChange()||this.didDropTargetChange()||this.didOptionsChange();t&&this.disconnectDropTarget();const n=this.dropTarget;if(this.handlerId){if(!n){this.lastConnectedDropTarget=n;return}t&&(this.lastConnectedHandlerId=this.handlerId,this.lastConnectedDropTarget=n,this.lastConnectedDropTargetOptions=this.dropTargetOptions,this.unsubscribeDropTarget=this.backend.connectDropTarget(this.handlerId,n,this.dropTargetOptions))}}receiveHandlerId(t){t!==this.handlerId&&(this.handlerId=t,this.reconnect())}get dropTargetOptions(){return this.dropTargetOptionsInternal}set dropTargetOptions(t){this.dropTargetOptionsInternal=t}didHandlerIdChange(){return this.lastConnectedHandlerId!==this.handlerId}didDropTargetChange(){return this.lastConnectedDropTarget!==this.dropTarget}didOptionsChange(){return!pg(this.lastConnectedDropTargetOptions,this.dropTargetOptions)}disconnectDropTarget(){this.unsubscribeDropTarget&&(this.unsubscribeDropTarget(),this.unsubscribeDropTarget=void 0)}get dropTarget(){return this.dropTargetNode||this.dropTargetRef&&this.dropTargetRef.current}clearDropTarget(){this.dropTargetRef=null,this.dropTargetNode=null}constructor(t){this.hooks=QC({dropTarget:(n,r)=>{this.clearDropTarget(),this.dropTargetOptions=r,hg(n)?this.dropTargetRef=n:this.dropTargetNode=n,this.reconnect()}}),this.handlerId=null,this.dropTargetRef=null,this.dropTargetOptionsInternal=null,this.lastConnectedHandlerId=null,this.lastConnectedDropTarget=null,this.lastConnectedDropTargetOptions=null,this.backend=t}}function mi(){const{dragDropManager:e}=I.useContext($C);return be(e!=null,"Expected drag drop context"),e}function RF(e,t){const n=mi(),r=I.useMemo(()=>new TF(n.getBackend()),[n]);return zo(()=>(r.dragSourceOptions=e||null,r.reconnect(),()=>r.disconnectDragSource()),[r,e]),zo(()=>(r.dragPreviewOptions=t||null,r.reconnect(),()=>r.disconnectDragPreview()),[r,t]),r}function PF(){const e=mi();return I.useMemo(()=>new SF(e),[e])}class AF{beginDrag(){const t=this.spec,n=this.monitor;let r=null;return typeof t.item=="object"?r=t.item:typeof t.item=="function"?r=t.item(n):r={},r??null}canDrag(){const t=this.spec,n=this.monitor;return typeof t.canDrag=="boolean"?t.canDrag:typeof t.canDrag=="function"?t.canDrag(n):!0}isDragging(t,n){const r=this.spec,i=this.monitor,{isDragging:l}=r;return l?l(i):n===t.getSourceId()}endDrag(){const t=this.spec,n=this.monitor,r=this.connector,{end:i}=t;i&&i(n.getItem(),n),r.reconnect()}constructor(t,n,r){this.spec=t,this.monitor=n,this.connector=r}}function DF(e,t,n){const r=I.useMemo(()=>new AF(e,t,n),[t,n]);return I.useEffect(()=>{r.spec=e},[e]),r}function NF(e){return I.useMemo(()=>{const t=e.type;return be(t!=null,"spec.type must be defined"),t},[e])}function MF(e,t,n){const r=mi(),i=DF(e,t,n),l=NF(e);zo(function(){if(l!=null){const[a,f]=kF(l,i,r);return t.receiveHandlerId(a),n.receiveHandlerId(a),f}},[r,t,n,i,l])}function LF(e,t){const n=KC(e,t);be(!n.begin,"useDrag::spec.begin was deprecated in v14. Replace spec.begin() with spec.item(). (see more here - https://react-dnd.github.io/react-dnd/docs/api/use-drag)");const r=PF(),i=RF(n.options,n.previewOptions);return MF(n,r,i),[qC(n.collect,r,i),wF(i),xF(i)]}function FF(e){const n=mi().getMonitor(),[r,i]=GC(n,e);return I.useEffect(()=>n.subscribeToOffsetChange(i)),I.useEffect(()=>n.subscribeToStateChange(i)),r}function zF(e){return I.useMemo(()=>e.hooks.dropTarget(),[e])}function $F(e){const t=mi(),n=I.useMemo(()=>new IF(t.getBackend()),[t]);return zo(()=>(n.dropTargetOptions=e||null,n.reconnect(),()=>n.disconnectDropTarget()),[e]),n}function BF(){const e=mi();return I.useMemo(()=>new EF(e),[e])}function UF(e){const{accept:t}=e;return I.useMemo(()=>(be(e.accept!=null,"accept must be defined"),Array.isArray(t)?t:[t]),[t])}class HF{canDrop(){const t=this.spec,n=this.monitor;return t.canDrop?t.canDrop(n.getItem(),n):!0}hover(){const t=this.spec,n=this.monitor;t.hover&&t.hover(n.getItem(),n)}drop(){const t=this.spec,n=this.monitor;if(t.drop)return t.drop(n.getItem(),n)}constructor(t,n){this.spec=t,this.monitor=n}}function jF(e,t){const n=I.useMemo(()=>new HF(e,t),[t]);return I.useEffect(()=>{n.spec=e},[e]),n}function WF(e,t,n){const r=mi(),i=jF(e,t),l=UF(e);zo(function(){const[a,f]=CF(l,i,r);return t.receiveHandlerId(a),n.receiveHandlerId(a),f},[r,t,i,n,l.map(s=>s.toString()).join("|")])}function gg(e,t){const n=KC(e,t),r=BF(),i=$F(n.options);return WF(n,r,i),[qC(n.collect,r,i),zF(i)]}var Ki;(function(e){e.mouse="mouse",e.touch="touch",e.keyboard="keyboard"})(Ki||(Ki={}));class VF{get delay(){var t;return(t=this.args.delay)!==null&&t!==void 0?t:0}get scrollAngleRanges(){return this.args.scrollAngleRanges}get getDropTargetElementsAtPoint(){return this.args.getDropTargetElementsAtPoint}get ignoreContextMenu(){var t;return(t=this.args.ignoreContextMenu)!==null&&t!==void 0?t:!1}get enableHoverOutsideTarget(){var t;return(t=this.args.enableHoverOutsideTarget)!==null&&t!==void 0?t:!1}get enableKeyboardEvents(){var t;return(t=this.args.enableKeyboardEvents)!==null&&t!==void 0?t:!1}get enableMouseEvents(){var t;return(t=this.args.enableMouseEvents)!==null&&t!==void 0?t:!1}get enableTouchEvents(){var t;return(t=this.args.enableTouchEvents)!==null&&t!==void 0?t:!0}get touchSlop(){return this.args.touchSlop||0}get delayTouchStart(){var t,n,r,i;return(i=(r=(t=this.args)===null||t===void 0?void 0:t.delayTouchStart)!==null&&r!==void 0?r:(n=this.args)===null||n===void 0?void 0:n.delay)!==null&&i!==void 0?i:0}get delayMouseStart(){var t,n,r,i;return(i=(r=(t=this.args)===null||t===void 0?void 0:t.delayMouseStart)!==null&&r!==void 0?r:(n=this.args)===null||n===void 0?void 0:n.delay)!==null&&i!==void 0?i:0}get window(){if(this.context&&this.context.window)return this.context.window;if(typeof window<"u")return window}get document(){var t;if(!((t=this.context)===null||t===void 0)&&t.document)return this.context.document;if(this.window)return this.window.document}get rootElement(){var t;return((t=this.args)===null||t===void 0?void 0:t.rootElement)||this.document}constructor(t,n){this.args=t,this.context=n}}function GF(e,t,n,r){return Math.sqrt(Math.pow(Math.abs(n-e),2)+Math.pow(Math.abs(r-t),2))}function qF(e,t,n,r,i){if(!i)return!1;const l=Math.atan2(r-t,n-e)*180/Math.PI+180;for(let s=0;s<i.length;++s){const a=i[s];if(a&&(a.start==null||l>=a.start)&&(a.end==null||l<=a.end))return!0}return!1}const KF={Left:1,Right:2,Center:4},QF={Left:0,Center:1,Right:2};function jp(e){return e.button===void 0||e.button===QF.Left}function YF(e){return e.buttons===void 0||(e.buttons&KF.Left)===0}function YC(e){return!!e.targetTouches}const XF=1;function ZF(e){const t=e.nodeType===XF?e:e.parentElement;if(!t)return;const{top:n,left:r}=t.getBoundingClientRect();return{x:r,y:n}}function JF(e,t){if(e.targetTouches.length===1)return Bc(e.targetTouches[0]);if(t&&e.touches.length===1&&e.touches[0].target===t.target)return Bc(e.touches[0])}function Bc(e,t){return YC(e)?JF(e,t):{x:e.clientX,y:e.clientY}}const Mw=(()=>{let e=!1;try{addEventListener("test",()=>{},Object.defineProperty({},"passive",{get(){return e=!0,!0}}))}catch{}return e})(),Du={[Ki.mouse]:{start:"mousedown",move:"mousemove",end:"mouseup",contextmenu:"contextmenu"},[Ki.touch]:{start:"touchstart",move:"touchmove",end:"touchend"},[Ki.keyboard]:{keydown:"keydown"}};class Zu{profile(){var t;return{sourceNodes:this.sourceNodes.size,sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,targetNodes:this.targetNodes.size,dragOverTargetIds:((t=this.dragOverTargetIds)===null||t===void 0?void 0:t.length)||0}}get document(){return this.options.document}setup(){const t=this.options.rootElement;t&&(be(!Zu.isSetUp,"Cannot have two Touch backends at the same time."),Zu.isSetUp=!0,this.addEventListener(t,"start",this.getTopMoveStartHandler()),this.addEventListener(t,"start",this.handleTopMoveStartCapture,!0),this.addEventListener(t,"move",this.handleTopMove),this.addEventListener(t,"move",this.handleTopMoveCapture,!0),this.addEventListener(t,"end",this.handleTopMoveEndCapture,!0),this.options.enableMouseEvents&&!this.options.ignoreContextMenu&&this.addEventListener(t,"contextmenu",this.handleTopMoveEndCapture),this.options.enableKeyboardEvents&&this.addEventListener(t,"keydown",this.handleCancelOnEscape,!0))}teardown(){const t=this.options.rootElement;t&&(Zu.isSetUp=!1,this._mouseClientOffset={},this.removeEventListener(t,"start",this.handleTopMoveStartCapture,!0),this.removeEventListener(t,"start",this.handleTopMoveStart),this.removeEventListener(t,"move",this.handleTopMoveCapture,!0),this.removeEventListener(t,"move",this.handleTopMove),this.removeEventListener(t,"end",this.handleTopMoveEndCapture,!0),this.options.enableMouseEvents&&!this.options.ignoreContextMenu&&this.removeEventListener(t,"contextmenu",this.handleTopMoveEndCapture),this.options.enableKeyboardEvents&&this.removeEventListener(t,"keydown",this.handleCancelOnEscape,!0),this.uninstallSourceNodeRemovalObserver())}addEventListener(t,n,r,i=!1){const l=Mw?{capture:i,passive:!1}:i;this.listenerTypes.forEach(function(s){const a=Du[s][n];a&&t.addEventListener(a,r,l)})}removeEventListener(t,n,r,i=!1){const l=Mw?{capture:i,passive:!1}:i;this.listenerTypes.forEach(function(s){const a=Du[s][n];a&&t.removeEventListener(a,r,l)})}connectDragSource(t,n){const r=this.handleMoveStart.bind(this,t);return this.sourceNodes.set(t,n),this.addEventListener(n,"start",r),()=>{this.sourceNodes.delete(t),this.removeEventListener(n,"start",r)}}connectDragPreview(t,n,r){return this.sourcePreviewNodeOptions.set(t,r),this.sourcePreviewNodes.set(t,n),()=>{this.sourcePreviewNodes.delete(t),this.sourcePreviewNodeOptions.delete(t)}}connectDropTarget(t,n){const r=this.options.rootElement;if(!this.document||!r)return()=>{};const i=l=>{if(!this.document||!r||!this.monitor.isDragging())return;let s;switch(l.type){case Du.mouse.move:s={x:l.clientX,y:l.clientY};break;case Du.touch.move:var a,f;s={x:((a=l.touches[0])===null||a===void 0?void 0:a.clientX)||0,y:((f=l.touches[0])===null||f===void 0?void 0:f.clientY)||0};break}const d=s!=null?this.document.elementFromPoint(s.x,s.y):void 0,p=d&&n.contains(d);if(d===n||p)return this.handleMove(l,t)};return this.addEventListener(this.document.body,"move",i),this.targetNodes.set(t,n),()=>{this.document&&(this.targetNodes.delete(t),this.removeEventListener(this.document.body,"move",i))}}getTopMoveStartHandler(){return!this.options.delayTouchStart&&!this.options.delayMouseStart?this.handleTopMoveStart:this.handleTopMoveStartDelay}installSourceNodeRemovalObserver(t){this.uninstallSourceNodeRemovalObserver(),this.draggedSourceNode=t,this.draggedSourceNodeRemovalObserver=new MutationObserver(()=>{t&&!t.parentElement&&(this.resurrectSourceNode(),this.uninstallSourceNodeRemovalObserver())}),!(!t||!t.parentElement)&&this.draggedSourceNodeRemovalObserver.observe(t.parentElement,{childList:!0})}resurrectSourceNode(){this.document&&this.draggedSourceNode&&(this.draggedSourceNode.style.display="none",this.draggedSourceNode.removeAttribute("data-reactid"),this.document.body.appendChild(this.draggedSourceNode))}uninstallSourceNodeRemovalObserver(){this.draggedSourceNodeRemovalObserver&&this.draggedSourceNodeRemovalObserver.disconnect(),this.draggedSourceNodeRemovalObserver=void 0,this.draggedSourceNode=void 0}constructor(t,n,r){this.getSourceClientOffset=i=>{const l=this.sourceNodes.get(i);return l&&ZF(l)},this.handleTopMoveStartCapture=i=>{jp(i)&&(this.moveStartSourceIds=[])},this.handleMoveStart=i=>{Array.isArray(this.moveStartSourceIds)&&this.moveStartSourceIds.unshift(i)},this.handleTopMoveStart=i=>{if(!jp(i))return;const l=Bc(i);l&&(YC(i)&&(this.lastTargetTouchFallback=i.targetTouches[0]),this._mouseClientOffset=l),this.waitingForDelay=!1},this.handleTopMoveStartDelay=i=>{if(!jp(i))return;const l=i.type===Du.touch.start?this.options.delayTouchStart:this.options.delayMouseStart;this.timeout=setTimeout(this.handleTopMoveStart.bind(this,i),l),this.waitingForDelay=!0},this.handleTopMoveCapture=()=>{this.dragOverTargetIds=[]},this.handleMove=(i,l)=>{this.dragOverTargetIds&&this.dragOverTargetIds.unshift(l)},this.handleTopMove=i=>{if(this.timeout&&clearTimeout(this.timeout),!this.document||this.waitingForDelay)return;const{moveStartSourceIds:l,dragOverTargetIds:s}=this,a=this.options.enableHoverOutsideTarget,f=Bc(i,this.lastTargetTouchFallback);if(!f)return;if(this._isScrolling||!this.monitor.isDragging()&&qF(this._mouseClientOffset.x||0,this._mouseClientOffset.y||0,f.x,f.y,this.options.scrollAngleRanges)){this._isScrolling=!0;return}if(!this.monitor.isDragging()&&this._mouseClientOffset.hasOwnProperty("x")&&l&&GF(this._mouseClientOffset.x||0,this._mouseClientOffset.y||0,f.x,f.y)>(this.options.touchSlop?this.options.touchSlop:0)&&(this.moveStartSourceIds=void 0,this.actions.beginDrag(l,{clientOffset:this._mouseClientOffset,getSourceClientOffset:this.getSourceClientOffset,publishSource:!1})),!this.monitor.isDragging())return;const d=this.sourceNodes.get(this.monitor.getSourceId());this.installSourceNodeRemovalObserver(d),this.actions.publishDragSource(),i.cancelable&&i.preventDefault();const p=(s||[]).map(E=>this.targetNodes.get(E)).filter(E=>!!E),g=this.options.getDropTargetElementsAtPoint?this.options.getDropTargetElementsAtPoint(f.x,f.y,p):this.document.elementsFromPoint(f.x,f.y),v=[];for(const E in g){if(!g.hasOwnProperty(E))continue;let S=g[E];for(S!=null&&v.push(S);S;)S=S.parentElement,S&&v.indexOf(S)===-1&&v.push(S)}const m=v.filter(E=>p.indexOf(E)>-1).map(E=>this._getDropTargetId(E)).filter(E=>!!E).filter((E,S,A)=>A.indexOf(E)===S);if(a)for(const E in this.targetNodes){const S=this.targetNodes.get(E);if(d&&S&&S.contains(d)&&m.indexOf(E)===-1){m.unshift(E);break}}m.reverse(),this.actions.hover(m,{clientOffset:f})},this._getDropTargetId=i=>{const l=this.targetNodes.keys();let s=l.next();for(;s.done===!1;){const a=s.value;if(i===this.targetNodes.get(a))return a;s=l.next()}},this.handleTopMoveEndCapture=i=>{if(this._isScrolling=!1,this.lastTargetTouchFallback=void 0,!!YF(i)){if(!this.monitor.isDragging()||this.monitor.didDrop()){this.moveStartSourceIds=void 0;return}i.cancelable&&i.preventDefault(),this._mouseClientOffset={},this.uninstallSourceNodeRemovalObserver(),this.actions.drop(),this.actions.endDrag()}},this.handleCancelOnEscape=i=>{i.key==="Escape"&&this.monitor.isDragging()&&(this._mouseClientOffset={},this.uninstallSourceNodeRemovalObserver(),this.actions.endDrag())},this.options=new VF(r,n),this.actions=t.getActions(),this.monitor=t.getMonitor(),this.sourceNodes=new Map,this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.targetNodes=new Map,this.listenerTypes=[],this._mouseClientOffset={},this._isScrolling=!1,this.options.enableMouseEvents&&this.listenerTypes.push(Ki.mouse),this.options.enableTouchEvents&&this.listenerTypes.push(Ki.touch),this.options.enableKeyboardEvents&&this.listenerTypes.push(Ki.keyboard)}}const e3=function(t,n={},r={}){return new Zu(t,n,r)};function Er(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw Error("[Immer] minified error nr: "+e+(n.length?" "+n.map(function(i){return"'"+i+"'"}).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function no(e){return!!e&&!!e[ft]}function hi(e){var t;return!!e&&(function(n){if(!n||typeof n!="object")return!1;var r=Object.getPrototypeOf(n);if(r===null)return!0;var i=Object.hasOwnProperty.call(r,"constructor")&&r.constructor;return i===Object||typeof i=="function"&&Function.toString.call(i)===s3}(e)||Array.isArray(e)||!!e[Hw]||!!(!((t=e.constructor)===null||t===void 0)&&t[Hw])||Ym(e)||Xm(e))}function $o(e,t,n){n===void 0&&(n=!1),ru(e)===0?(n?Object.keys:Al)(e).forEach(function(r){n&&typeof r=="symbol"||t(r,e[r],e)}):e.forEach(function(r,i){return t(i,r,e)})}function ru(e){var t=e[ft];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:Ym(e)?2:Xm(e)?3:0}function Pl(e,t){return ru(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function t3(e,t){return ru(e)===2?e.get(t):e[t]}function XC(e,t,n){var r=ru(e);r===2?e.set(t,n):r===3?e.add(n):e[t]=n}function ZC(e,t){return e===t?e!==0||1/e==1/t:e!=e&&t!=t}function Ym(e){return l3&&e instanceof Map}function Xm(e){return u3&&e instanceof Set}function Eo(e){return e.o||e.t}function Zm(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=ek(e);delete t[ft];for(var n=Al(t),r=0;r<n.length;r++){var i=n[r],l=t[i];l.writable===!1&&(l.writable=!0,l.configurable=!0),(l.get||l.set)&&(t[i]={configurable:!0,writable:!0,enumerable:l.enumerable,value:e[i]})}return Object.create(Object.getPrototypeOf(e),t)}function Jm(e,t){return t===void 0&&(t=!1),ev(e)||no(e)||!hi(e)||(ru(e)>1&&(e.set=e.add=e.clear=e.delete=n3),Object.freeze(e),t&&$o(e,function(n,r){return Jm(r,!0)},!0)),e}function n3(){Er(2)}function ev(e){return e==null||typeof e!="object"||Object.isFrozen(e)}function Ur(e){var t=xg[e];return t||Er(18,e),t}function r3(e,t){xg[e]||(xg[e]=t)}function mg(){return Es}function Wp(e,t){t&&(Ur("Patches"),e.u=[],e.s=[],e.v=t)}function Uc(e){vg(e),e.p.forEach(i3),e.p=null}function vg(e){e===Es&&(Es=e.l)}function Lw(e){return Es={p:[],l:Es,h:e,m:!0,_:0}}function i3(e){var t=e[ft];t.i===0||t.i===1?t.j():t.g=!0}function Vp(e,t){t._=t.p.length;var n=t.p[0],r=e!==void 0&&e!==n;return t.h.O||Ur("ES5").S(t,e,r),r?(n[ft].P&&(Uc(t),Er(4)),hi(e)&&(e=Hc(t,e),t.l||jc(t,e)),t.u&&Ur("Patches").M(n[ft].t,e,t.u,t.s)):e=Hc(t,n,[]),Uc(t),t.u&&t.v(t.u,t.s),e!==JC?e:void 0}function Hc(e,t,n){if(ev(t))return t;var r=t[ft];if(!r)return $o(t,function(a,f){return Fw(e,r,t,a,f,n)},!0),t;if(r.A!==e)return t;if(!r.P)return jc(e,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var i=r.i===4||r.i===5?r.o=Zm(r.k):r.o,l=i,s=!1;r.i===3&&(l=new Set(i),i.clear(),s=!0),$o(l,function(a,f){return Fw(e,r,i,a,f,n,s)}),jc(e,i,!1),n&&e.u&&Ur("Patches").N(r,n,e.u,e.s)}return r.o}function Fw(e,t,n,r,i,l,s){if(no(i)){var a=Hc(e,i,l&&t&&t.i!==3&&!Pl(t.R,r)?l.concat(r):void 0);if(XC(n,r,a),!no(a))return;e.m=!1}else s&&n.add(i);if(hi(i)&&!ev(i)){if(!e.h.D&&e._<1)return;Hc(e,i),t&&t.A.l||jc(e,i)}}function jc(e,t,n){n===void 0&&(n=!1),!e.l&&e.h.D&&e.m&&Jm(t,n)}function Gp(e,t){var n=e[ft];return(n?Eo(n):e)[t]}function zw(e,t){if(t in e)for(var n=Object.getPrototypeOf(e);n;){var r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n)}}function Li(e){e.P||(e.P=!0,e.l&&Li(e.l))}function qp(e){e.o||(e.o=Zm(e.t))}function yg(e,t,n){var r=Ym(t)?Ur("MapSet").F(t,n):Xm(t)?Ur("MapSet").T(t,n):e.O?function(i,l){var s=Array.isArray(i),a={i:s?1:0,A:l?l.A:mg(),P:!1,I:!1,R:{},l,t:i,k:null,o:null,j:null,C:!1},f=a,d=Cs;s&&(f=[a],d=$u);var p=Proxy.revocable(f,d),g=p.revoke,v=p.proxy;return a.k=v,a.j=g,v}(t,n):Ur("ES5").J(t,n);return(n?n.A:mg()).p.push(r),r}function wg(e){return no(e)||Er(22,e),function t(n){if(!hi(n))return n;var r,i=n[ft],l=ru(n);if(i){if(!i.P&&(i.i<4||!Ur("ES5").K(i)))return i.t;i.I=!0,r=$w(n,l),i.I=!1}else r=$w(n,l);return $o(r,function(s,a){i&&t3(i.t,s)===a||XC(r,s,t(a))}),l===3?new Set(r):r}(e)}function $w(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return Zm(e)}function o3(){function e(l,s){var a=i[l];return a?a.enumerable=s:i[l]=a={configurable:!0,enumerable:s,get:function(){var f=this[ft];return Cs.get(f,l)},set:function(f){var d=this[ft];Cs.set(d,l,f)}},a}function t(l){for(var s=l.length-1;s>=0;s--){var a=l[s][ft];if(!a.P)switch(a.i){case 5:r(a)&&Li(a);break;case 4:n(a)&&Li(a)}}}function n(l){for(var s=l.t,a=l.k,f=Al(a),d=f.length-1;d>=0;d--){var p=f[d];if(p!==ft){var g=s[p];if(g===void 0&&!Pl(s,p))return!0;var v=a[p],m=v&&v[ft];if(m?m.t!==g:!ZC(v,g))return!0}}var E=!!s[ft];return f.length!==Al(s).length+(E?0:1)}function r(l){var s=l.k;if(s.length!==l.t.length)return!0;var a=Object.getOwnPropertyDescriptor(s,s.length-1);if(a&&!a.get)return!0;for(var f=0;f<s.length;f++)if(!s.hasOwnProperty(f))return!0;return!1}var i={};r3("ES5",{J:function(l,s){var a=Array.isArray(l),f=function(p,g){if(p){for(var v=Array(g.length),m=0;m<g.length;m++)Object.defineProperty(v,""+m,e(m,!0));return v}var E=ek(g);delete E[ft];for(var S=Al(E),A=0;A<S.length;A++){var y=S[A];E[y]=e(y,p||!!E[y].enumerable)}return Object.create(Object.getPrototypeOf(g),E)}(a,l),d={i:a?5:4,A:s?s.A:mg(),P:!1,I:!1,R:{},l:s,t:l,k:f,o:null,g:!1,C:!1};return Object.defineProperty(f,ft,{value:d,writable:!0}),f},S:function(l,s,a){a?no(s)&&s[ft].A===l&&t(l.p):(l.u&&function f(d){if(d&&typeof d=="object"){var p=d[ft];if(p){var g=p.t,v=p.k,m=p.R,E=p.i;if(E===4)$o(v,function(k){k!==ft&&(g[k]!==void 0||Pl(g,k)?m[k]||f(v[k]):(m[k]=!0,Li(p)))}),$o(g,function(k){v[k]!==void 0||Pl(v,k)||(m[k]=!1,Li(p))});else if(E===5){if(r(p)&&(Li(p),m.length=!0),v.length<g.length)for(var S=v.length;S<g.length;S++)m[S]=!1;else for(var A=g.length;A<v.length;A++)m[A]=!0;for(var y=Math.min(v.length,g.length),x=0;x<y;x++)v.hasOwnProperty(x)||(m[x]=!0),m[x]===void 0&&f(v[x])}}}}(l.p[0]),t(l.p))},K:function(l){return l.i===4?n(l):r(l)}})}var Bw,Es,tv=typeof Symbol<"u"&&typeof Symbol("x")=="symbol",l3=typeof Map<"u",u3=typeof Set<"u",Uw=typeof Proxy<"u"&&Proxy.revocable!==void 0&&typeof Reflect<"u",JC=tv?Symbol.for("immer-nothing"):((Bw={})["immer-nothing"]=!0,Bw),Hw=tv?Symbol.for("immer-draftable"):"__$immer_draftable",ft=tv?Symbol.for("immer-state"):"__$immer_state",s3=""+Object.prototype.constructor,Al=typeof Reflect<"u"&&Reflect.ownKeys?Reflect.ownKeys:Object.getOwnPropertySymbols!==void 0?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,ek=Object.getOwnPropertyDescriptors||function(e){var t={};return Al(e).forEach(function(n){t[n]=Object.getOwnPropertyDescriptor(e,n)}),t},xg={},Cs={get:function(e,t){if(t===ft)return e;var n=Eo(e);if(!Pl(n,t))return function(i,l,s){var a,f=zw(l,s);return f?"value"in f?f.value:(a=f.get)===null||a===void 0?void 0:a.call(i.k):void 0}(e,n,t);var r=n[t];return e.I||!hi(r)?r:r===Gp(e.t,t)?(qp(e),e.o[t]=yg(e.A.h,r,e)):r},has:function(e,t){return t in Eo(e)},ownKeys:function(e){return Reflect.ownKeys(Eo(e))},set:function(e,t,n){var r=zw(Eo(e),t);if(r?.set)return r.set.call(e.k,n),!0;if(!e.P){var i=Gp(Eo(e),t),l=i?.[ft];if(l&&l.t===n)return e.o[t]=n,e.R[t]=!1,!0;if(ZC(n,i)&&(n!==void 0||Pl(e.t,t)))return!0;qp(e),Li(e)}return e.o[t]===n&&(n!==void 0||t in e.o)||Number.isNaN(n)&&Number.isNaN(e.o[t])||(e.o[t]=n,e.R[t]=!0),!0},deleteProperty:function(e,t){return Gp(e.t,t)!==void 0||t in e.t?(e.R[t]=!1,qp(e),Li(e)):delete e.R[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var n=Eo(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r&&{writable:!0,configurable:e.i!==1||t!=="length",enumerable:r.enumerable,value:n[t]}},defineProperty:function(){Er(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){Er(12)}},$u={};$o(Cs,function(e,t){$u[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),$u.deleteProperty=function(e,t){return $u.set.call(this,e,t,void 0)},$u.set=function(e,t,n){return Cs.set.call(this,e[0],t,n,e[0])};var a3=function(){function e(n){var r=this;this.O=Uw,this.D=!0,this.produce=function(i,l,s){if(typeof i=="function"&&typeof l!="function"){var a=l;l=i;var f=r;return function(S){var A=this;S===void 0&&(S=a);for(var y=arguments.length,x=Array(y>1?y-1:0),k=1;k<y;k++)x[k-1]=arguments[k];return f.produce(S,function(P){var D;return(D=l).call.apply(D,[A,P].concat(x))})}}var d;if(typeof l!="function"&&Er(6),s!==void 0&&typeof s!="function"&&Er(7),hi(i)){var p=Lw(r),g=yg(r,i,void 0),v=!0;try{d=l(g),v=!1}finally{v?Uc(p):vg(p)}return typeof Promise<"u"&&d instanceof Promise?d.then(function(S){return Wp(p,s),Vp(S,p)},function(S){throw Uc(p),S}):(Wp(p,s),Vp(d,p))}if(!i||typeof i!="object"){if((d=l(i))===void 0&&(d=i),d===JC&&(d=void 0),r.D&&Jm(d,!0),s){var m=[],E=[];Ur("Patches").M(i,d,m,E),s(m,E)}return d}Er(21,i)},this.produceWithPatches=function(i,l){if(typeof i=="function")return function(d){for(var p=arguments.length,g=Array(p>1?p-1:0),v=1;v<p;v++)g[v-1]=arguments[v];return r.produceWithPatches(d,function(m){return i.apply(void 0,[m].concat(g))})};var s,a,f=r.produce(i,l,function(d,p){s=d,a=p});return typeof Promise<"u"&&f instanceof Promise?f.then(function(d){return[d,s,a]}):[f,s,a]},typeof n?.useProxies=="boolean"&&this.setUseProxies(n.useProxies),typeof n?.autoFreeze=="boolean"&&this.setAutoFreeze(n.autoFreeze)}var t=e.prototype;return t.createDraft=function(n){hi(n)||Er(8),no(n)&&(n=wg(n));var r=Lw(this),i=yg(this,n,void 0);return i[ft].C=!0,vg(r),i},t.finishDraft=function(n,r){var i=n&&n[ft],l=i.A;return Wp(l,r),Vp(void 0,l)},t.setAutoFreeze=function(n){this.D=n},t.setUseProxies=function(n){n&&!Uw&&Er(20),this.O=n},t.applyPatches=function(n,r){var i;for(i=r.length-1;i>=0;i--){var l=r[i];if(l.path.length===0&&l.op==="replace"){n=l.value;break}}i>-1&&(r=r.slice(i+1));var s=Ur("Patches").$;return no(n)?s(n,r):this.produce(n,function(a){return s(a,r)})},e}(),Bn=new a3,tk=Bn.produce;Bn.produceWithPatches.bind(Bn);Bn.setAutoFreeze.bind(Bn);Bn.setUseProxies.bind(Bn);Bn.applyPatches.bind(Bn);Bn.createDraft.bind(Bn);Bn.finishDraft.bind(Bn);function nk(e){var t=function(r){var i=r.dispatch,l=r.getState;return function(s){return function(a){return typeof a=="function"?a(i,l,e):s(a)}}};return t}var rk=nk();rk.withExtraArgument=nk;const jw=rk;var ik=globalThis&&globalThis.__extends||function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,i){r.__proto__=i}||function(r,i){for(var l in i)Object.prototype.hasOwnProperty.call(i,l)&&(r[l]=i[l])},e(t,n)};return function(t,n){if(typeof n!="function"&&n!==null)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");e(t,n);function r(){this.constructor=t}t.prototype=n===null?Object.create(n):(r.prototype=n.prototype,new r)}}(),c3=globalThis&&globalThis.__generator||function(e,t){var n={label:0,sent:function(){if(l[0]&1)throw l[1];return l[1]},trys:[],ops:[]},r,i,l,s;return s={next:a(0),throw:a(1),return:a(2)},typeof Symbol=="function"&&(s[Symbol.iterator]=function(){return this}),s;function a(d){return function(p){return f([d,p])}}function f(d){if(r)throw new TypeError("Generator is already executing.");for(;n;)try{if(r=1,i&&(l=d[0]&2?i.return:d[0]?i.throw||((l=i.return)&&l.call(i),0):i.next)&&!(l=l.call(i,d[1])).done)return l;switch(i=0,l&&(d=[d[0]&2,l.value]),d[0]){case 0:case 1:l=d;break;case 4:return n.label++,{value:d[1],done:!1};case 5:n.label++,i=d[1],d=[0];continue;case 7:d=n.ops.pop(),n.trys.pop();continue;default:if(l=n.trys,!(l=l.length>0&&l[l.length-1])&&(d[0]===6||d[0]===2)){n=0;continue}if(d[0]===3&&(!l||d[1]>l[0]&&d[1]<l[3])){n.label=d[1];break}if(d[0]===6&&n.label<l[1]){n.label=l[1],l=d;break}if(l&&n.label<l[2]){n.label=l[2],n.ops.push(d);break}l[2]&&n.ops.pop(),n.trys.pop();continue}d=t.call(e,n)}catch(p){d=[6,p],i=0}finally{r=l=0}if(d[0]&5)throw d[1];return{value:d[0]?d[1]:void 0,done:!0}}},Gl=globalThis&&globalThis.__spreadArray||function(e,t){for(var n=0,r=t.length,i=e.length;n<r;n++,i++)e[i]=t[n];return e},f3=Object.defineProperty,d3=Object.defineProperties,p3=Object.getOwnPropertyDescriptors,Ww=Object.getOwnPropertySymbols,h3=Object.prototype.hasOwnProperty,g3=Object.prototype.propertyIsEnumerable,Vw=function(e,t,n){return t in e?f3(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},Qi=function(e,t){for(var n in t||(t={}))h3.call(t,n)&&Vw(e,n,t[n]);if(Ww)for(var r=0,i=Ww(t);r<i.length;r++){var n=i[r];g3.call(t,n)&&Vw(e,n,t[n])}return e},Kp=function(e,t){return d3(e,p3(t))},m3=function(e,t,n){return new Promise(function(r,i){var l=function(f){try{a(n.next(f))}catch(d){i(d)}},s=function(f){try{a(n.throw(f))}catch(d){i(d)}},a=function(f){return f.done?r(f.value):Promise.resolve(f.value).then(l,s)};a((n=n.apply(e,t)).next())})},v3=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(arguments.length!==0)return typeof arguments[0]=="object"?$c:$c.apply(null,arguments)};function y3(e){if(typeof e!="object"||e===null)return!1;var t=Object.getPrototypeOf(e);if(t===null)return!0;for(var n=t;Object.getPrototypeOf(n)!==null;)n=Object.getPrototypeOf(n);return t===n}var w3=function(e){return e&&typeof e.match=="function"};function Yi(e,t){function n(){for(var r=[],i=0;i<arguments.length;i++)r[i]=arguments[i];if(t){var l=t.apply(void 0,r);if(!l)throw new Error("prepareAction did not return an object");return Qi(Qi({type:e,payload:l.payload},"meta"in l&&{meta:l.meta}),"error"in l&&{error:l.error})}return{type:e,payload:r[0]}}return n.toString=function(){return""+e},n.type=e,n.match=function(r){return r.type===e},n}var x3=function(e){ik(t,e);function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=e.apply(this,n)||this;return Object.setPrototypeOf(i,t.prototype),i}return Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return e.prototype.concat.apply(this,n)},t.prototype.prepend=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return n.length===1&&Array.isArray(n[0])?new(t.bind.apply(t,Gl([void 0],n[0].concat(this)))):new(t.bind.apply(t,Gl([void 0],n.concat(this))))},t}(Array),S3=function(e){ik(t,e);function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var i=e.apply(this,n)||this;return Object.setPrototypeOf(i,t.prototype),i}return Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return e.prototype.concat.apply(this,n)},t.prototype.prepend=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return n.length===1&&Array.isArray(n[0])?new(t.bind.apply(t,Gl([void 0],n[0].concat(this)))):new(t.bind.apply(t,Gl([void 0],n.concat(this))))},t}(Array);function Sg(e){return hi(e)?tk(e,function(){}):e}function E3(e){return typeof e=="boolean"}function C3(){return function(t){return k3(t)}}function k3(e){e===void 0&&(e={});var t=e.thunk,n=t===void 0?!0:t;e.immutableCheck,e.serializableCheck,e.actionCreatorCheck;var r=new x3;return n&&(E3(n)?r.push(jw):r.push(jw.withExtraArgument(n.extraArgument))),r}var b3=!0;function _3(e){var t=C3(),n=e||{},r=n.reducer,i=r===void 0?void 0:r,l=n.middleware,s=l===void 0?t():l,a=n.devTools,f=a===void 0?!0:a,d=n.preloadedState,p=d===void 0?void 0:d,g=n.enhancers,v=g===void 0?void 0:g,m;if(typeof i=="function")m=i;else if(y3(i))m=YM(i);else throw new Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');var E=s;typeof E=="function"&&(E=E(t));var S=XM.apply(void 0,E),A=$c;f&&(A=v3(Qi({trace:!b3},typeof f=="object"&&f)));var y=new S3(S),x=y;Array.isArray(v)?x=Gl([S],v):typeof v=="function"&&(x=v(y));var k=A.apply(void 0,x);return jm(m,p,k)}function ok(e){var t={},n=[],r,i={addCase:function(l,s){var a=typeof l=="string"?l:l.type;if(!a)throw new Error("`builder.addCase` cannot be called with an empty action type");if(a in t)throw new Error("`builder.addCase` cannot be called with two reducers for the same action type");return t[a]=s,i},addMatcher:function(l,s){return n.push({matcher:l,reducer:s}),i},addDefaultCase:function(l){return r=l,i}};return e(i),[t,n,r]}function O3(e){return typeof e=="function"}function T3(e,t,n,r){n===void 0&&(n=[]);var i=typeof t=="function"?ok(t):[t,n,r],l=i[0],s=i[1],a=i[2],f;if(O3(e))f=function(){return Sg(e())};else{var d=Sg(e);f=function(){return d}}function p(g,v){g===void 0&&(g=f());var m=Gl([l[v.type]],s.filter(function(E){var S=E.matcher;return S(v)}).map(function(E){var S=E.reducer;return S}));return m.filter(function(E){return!!E}).length===0&&(m=[a]),m.reduce(function(E,S){if(S)if(no(E)){var A=E,y=S(A,v);return y===void 0?E:y}else{if(hi(E))return tk(E,function(x){return S(x,v)});var y=S(E,v);if(y===void 0){if(E===null)return E;throw Error("A case reducer on a non-draftable value must not return undefined")}return y}return E},g)}return p.getInitialState=f,p}function I3(e,t){return e+"/"+t}function nv(e){var t=e.name;if(!t)throw new Error("`name` is a required option for createSlice");typeof process<"u";var n=typeof e.initialState=="function"?e.initialState:Sg(e.initialState),r=e.reducers||{},i=Object.keys(r),l={},s={},a={};i.forEach(function(p){var g=r[p],v=I3(t,p),m,E;"reducer"in g?(m=g.reducer,E=g.prepare):m=g,l[p]=m,s[v]=m,a[p]=E?Yi(v,E):Yi(v)});function f(){var p=typeof e.extraReducers=="function"?ok(e.extraReducers):[e.extraReducers],g=p[0],v=g===void 0?{}:g,m=p[1],E=m===void 0?[]:m,S=p[2],A=S===void 0?void 0:S,y=Qi(Qi({},v),s);return T3(n,function(x){for(var k in y)x.addCase(k,y[k]);for(var P=0,D=E;P<D.length;P++){var T=D[P];x.addMatcher(T.matcher,T.reducer)}A&&x.addDefaultCase(A)})}var d;return{name:t,reducer:function(p,g){return d||(d=f()),d(p,g)},actions:a,caseReducers:l,getInitialState:function(){return d||(d=f()),d.getInitialState()}}}var R3="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW",P3=function(e){e===void 0&&(e=21);for(var t="",n=e;n--;)t+=R3[Math.random()*64|0];return t},A3=["name","message","stack","code"],Qp=function(){function e(t,n){this.payload=t,this.meta=n}return e}(),Gw=function(){function e(t,n){this.payload=t,this.meta=n}return e}(),D3=function(e){if(typeof e=="object"&&e!==null){for(var t={},n=0,r=A3;n<r.length;n++){var i=r[n];typeof e[i]=="string"&&(t[i]=e[i])}return t}return{message:String(e)}},rv=function(){function e(t,n,r){var i=Yi(t+"/fulfilled",function(d,p,g,v){return{payload:d,meta:Kp(Qi({},v||{}),{arg:g,requestId:p,requestStatus:"fulfilled"})}}),l=Yi(t+"/pending",function(d,p,g){return{payload:void 0,meta:Kp(Qi({},g||{}),{arg:p,requestId:d,requestStatus:"pending"})}}),s=Yi(t+"/rejected",function(d,p,g,v,m){return{payload:v,error:(r&&r.serializeError||D3)(d||"Rejected"),meta:Kp(Qi({},m||{}),{arg:g,requestId:p,rejectedWithValue:!!v,requestStatus:"rejected",aborted:d?.name==="AbortError",condition:d?.name==="ConditionError"})}}),a=typeof AbortController<"u"?AbortController:function(){function d(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return!1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}}}return d.prototype.abort=function(){},d}();function f(d){return function(p,g,v){var m=r?.idGenerator?r.idGenerator(d):P3(),E=new a,S;function A(x){S=x,E.abort()}var y=function(){return m3(this,null,function(){var x,k,P,D,T,F,z;return c3(this,function(G){switch(G.label){case 0:return G.trys.push([0,4,,5]),D=(x=r?.condition)==null?void 0:x.call(r,d,{getState:g,extra:v}),M3(D)?[4,D]:[3,2];case 1:D=G.sent(),G.label=2;case 2:if(D===!1||E.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};return T=new Promise(function(q,B){return E.signal.addEventListener("abort",function(){return B({name:"AbortError",message:S||"Aborted"})})}),p(l(m,d,(k=r?.getPendingMeta)==null?void 0:k.call(r,{requestId:m,arg:d},{getState:g,extra:v}))),[4,Promise.race([T,Promise.resolve(n(d,{dispatch:p,getState:g,extra:v,requestId:m,signal:E.signal,abort:A,rejectWithValue:function(q,B){return new Qp(q,B)},fulfillWithValue:function(q,B){return new Gw(q,B)}})).then(function(q){if(q instanceof Qp)throw q;return q instanceof Gw?i(q.payload,m,d,q.meta):i(q,m,d)})])];case 3:return P=G.sent(),[3,5];case 4:return F=G.sent(),P=F instanceof Qp?s(null,m,d,F.payload,F.meta):s(F,m,d),[3,5];case 5:return z=r&&!r.dispatchConditionRejection&&s.match(P)&&P.meta.condition,z||p(P),[2,P]}})})}();return Object.assign(y,{abort:A,requestId:m,arg:d,unwrap:function(){return y.then(N3)}})}}return Object.assign(f,{pending:l,rejected:s,fulfilled:i,typePrefix:t})}return e.withTypes=function(){return e},e}();function N3(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}function M3(e){return e!==null&&typeof e=="object"&&typeof e.then=="function"}var L3=function(e,t){return w3(e)?e.match(t):e(t)};function iv(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(n){return e.some(function(r){return L3(r,n)})}}function ov(e,t){if(!e||!e.meta)return!1;var n=typeof e.meta.requestId=="string",r=t.indexOf(e.meta.requestStatus)>-1;return n&&r}function lv(e){return typeof e[0]=="function"&&"pending"in e[0]&&"fulfilled"in e[0]&&"rejected"in e[0]}function lk(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.length===0?function(n){return ov(n,["pending"])}:lv(e)?function(n){var r=e.map(function(l){return l.pending}),i=iv.apply(void 0,r);return i(n)}:lk()(e[0])}function uk(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.length===0?function(n){return ov(n,["rejected"])}:lv(e)?function(n){var r=e.map(function(l){return l.rejected}),i=iv.apply(void 0,r);return i(n)}:uk()(e[0])}function sk(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.length===0?function(n){return ov(n,["fulfilled"])}:lv(e)?function(n){var r=e.map(function(l){return l.fulfilled}),i=iv.apply(void 0,r);return i(n)}:sk()(e[0])}var uv="listenerMiddleware";Yi(uv+"/add");Yi(uv+"/removeAll");Yi(uv+"/remove");var qw;typeof queueMicrotask=="function"&&queueMicrotask.bind(typeof window<"u"?window:typeof global<"u"?global:globalThis);o3();var Yt=(e=>(e.PLAYER="player",e.SHOP="shop",e.CONTAINER="container",e.CRAFTING="crafting",e))(Yt||{}),Wc={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */Wc.exports;(function(e,t){(function(){var n,r="4.17.21",i=200,l="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",s="Expected a function",a="Invalid `variable` option passed into `_.template`",f="__lodash_hash_undefined__",d=500,p="__lodash_placeholder__",g=1,v=2,m=4,E=1,S=2,A=1,y=2,x=4,k=8,P=16,D=32,T=64,F=128,z=256,G=512,q=30,B="...",Q=800,U=16,H=1,ee=2,le=3,oe=1/0,$=9007199254740991,Z=17976931348623157e292,_=0/0,ne=**********,ae=ne-1,O=ne>>>1,de=[["ary",F],["bind",A],["bindKey",y],["curry",k],["curryRight",P],["flip",G],["partial",D],["partialRight",T],["rearg",z]],Re="[object Arguments]",me="[object Array]",Ae="[object AsyncFunction]",Se="[object Boolean]",Le="[object Date]",Te="[object DOMException]",Be="[object Error]",Fe="[object Function]",qe="[object GeneratorFunction]",yt="[object Map]",Wn="[object Number]",he="[object Null]",at="[object Object]",Mt="[object Promise]",Cn="[object Proxy]",kn="[object RegExp]",Ht="[object Set]",Tr="[object String]",Gr="[object Symbol]",Go="[object Undefined]",Ir="[object WeakMap]",Vn="[object WeakSet]",V="[object ArrayBuffer]",re="[object DataView]",pe="[object Float32Array]",Ue="[object Float64Array]",He="[object Int8Array]",Zt="[object Int16Array]",Gn="[object Int32Array]",bn="[object Uint8Array]",qr="[object Uint8ClampedArray]",Kr="[object Uint16Array]",it="[object Uint32Array]",su=/\b__p \+= '';/g,pr=/\b(__p \+=) '' \+/g,Kb=/(__e\(.*?\)|\b__t\)) \+\n'';/g,zv=/&(?:amp|lt|gt|quot|#39);/g,$v=/[&<>"']/g,Qb=RegExp(zv.source),Yb=RegExp($v.source),Xb=/<%-([\s\S]+?)%>/g,Zb=/<%([\s\S]+?)%>/g,Bv=/<%=([\s\S]+?)%>/g,Jb=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,e_=/^\w*$/,t_=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,ed=/[\\^$.*+?()[\]{}|]/g,n_=RegExp(ed.source),td=/^\s+/,r_=/\s/,i_=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,o_=/\{\n\/\* \[wrapped with (.+)\] \*/,l_=/,? & /,u_=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,s_=/[()=,{}\[\]\/\s]/,a_=/\\(\\)?/g,c_=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,Uv=/\w*$/,f_=/^[-+]0x[0-9a-f]+$/i,d_=/^0b[01]+$/i,p_=/^\[object .+?Constructor\]$/,h_=/^0o[0-7]+$/i,g_=/^(?:0|[1-9]\d*)$/,m_=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Fs=/($^)/,v_=/['\n\r\u2028\u2029\\]/g,zs="\\ud800-\\udfff",y_="\\u0300-\\u036f",w_="\\ufe20-\\ufe2f",x_="\\u20d0-\\u20ff",Hv=y_+w_+x_,jv="\\u2700-\\u27bf",Wv="a-z\\xdf-\\xf6\\xf8-\\xff",S_="\\xac\\xb1\\xd7\\xf7",E_="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",C_="\\u2000-\\u206f",k_=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Vv="A-Z\\xc0-\\xd6\\xd8-\\xde",Gv="\\ufe0e\\ufe0f",qv=S_+E_+C_+k_,nd="['’]",b_="["+zs+"]",Kv="["+qv+"]",$s="["+Hv+"]",Qv="\\d+",__="["+jv+"]",Yv="["+Wv+"]",Xv="[^"+zs+qv+Qv+jv+Wv+Vv+"]",rd="\\ud83c[\\udffb-\\udfff]",O_="(?:"+$s+"|"+rd+")",Zv="[^"+zs+"]",id="(?:\\ud83c[\\udde6-\\uddff]){2}",od="[\\ud800-\\udbff][\\udc00-\\udfff]",qo="["+Vv+"]",Jv="\\u200d",ey="(?:"+Yv+"|"+Xv+")",T_="(?:"+qo+"|"+Xv+")",ty="(?:"+nd+"(?:d|ll|m|re|s|t|ve))?",ny="(?:"+nd+"(?:D|LL|M|RE|S|T|VE))?",ry=O_+"?",iy="["+Gv+"]?",I_="(?:"+Jv+"(?:"+[Zv,id,od].join("|")+")"+iy+ry+")*",R_="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",P_="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",oy=iy+ry+I_,A_="(?:"+[__,id,od].join("|")+")"+oy,D_="(?:"+[Zv+$s+"?",$s,id,od,b_].join("|")+")",N_=RegExp(nd,"g"),M_=RegExp($s,"g"),ld=RegExp(rd+"(?="+rd+")|"+D_+oy,"g"),L_=RegExp([qo+"?"+Yv+"+"+ty+"(?="+[Kv,qo,"$"].join("|")+")",T_+"+"+ny+"(?="+[Kv,qo+ey,"$"].join("|")+")",qo+"?"+ey+"+"+ty,qo+"+"+ny,P_,R_,Qv,A_].join("|"),"g"),F_=RegExp("["+Jv+zs+Hv+Gv+"]"),z_=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,$_=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],B_=-1,ot={};ot[pe]=ot[Ue]=ot[He]=ot[Zt]=ot[Gn]=ot[bn]=ot[qr]=ot[Kr]=ot[it]=!0,ot[Re]=ot[me]=ot[V]=ot[Se]=ot[re]=ot[Le]=ot[Be]=ot[Fe]=ot[yt]=ot[Wn]=ot[at]=ot[kn]=ot[Ht]=ot[Tr]=ot[Ir]=!1;var et={};et[Re]=et[me]=et[V]=et[re]=et[Se]=et[Le]=et[pe]=et[Ue]=et[He]=et[Zt]=et[Gn]=et[yt]=et[Wn]=et[at]=et[kn]=et[Ht]=et[Tr]=et[Gr]=et[bn]=et[qr]=et[Kr]=et[it]=!0,et[Be]=et[Fe]=et[Ir]=!1;var U_={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},H_={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},j_={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},W_={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},V_=parseFloat,G_=parseInt,ly=typeof ku=="object"&&ku&&ku.Object===Object&&ku,q_=typeof self=="object"&&self&&self.Object===Object&&self,Lt=ly||q_||Function("return this")(),ud=t&&!t.nodeType&&t,co=ud&&!0&&e&&!e.nodeType&&e,uy=co&&co.exports===ud,sd=uy&&ly.process,qn=function(){try{var M=co&&co.require&&co.require("util").types;return M||sd&&sd.binding&&sd.binding("util")}catch{}}(),sy=qn&&qn.isArrayBuffer,ay=qn&&qn.isDate,cy=qn&&qn.isMap,fy=qn&&qn.isRegExp,dy=qn&&qn.isSet,py=qn&&qn.isTypedArray;function _n(M,K,j){switch(j.length){case 0:return M.call(K);case 1:return M.call(K,j[0]);case 2:return M.call(K,j[0],j[1]);case 3:return M.call(K,j[0],j[1],j[2])}return M.apply(K,j)}function K_(M,K,j,se){for(var Ee=-1,je=M==null?0:M.length;++Ee<je;){var _t=M[Ee];K(se,_t,j(_t),M)}return se}function Kn(M,K){for(var j=-1,se=M==null?0:M.length;++j<se&&K(M[j],j,M)!==!1;);return M}function Q_(M,K){for(var j=M==null?0:M.length;j--&&K(M[j],j,M)!==!1;);return M}function hy(M,K){for(var j=-1,se=M==null?0:M.length;++j<se;)if(!K(M[j],j,M))return!1;return!0}function wi(M,K){for(var j=-1,se=M==null?0:M.length,Ee=0,je=[];++j<se;){var _t=M[j];K(_t,j,M)&&(je[Ee++]=_t)}return je}function Bs(M,K){var j=M==null?0:M.length;return!!j&&Ko(M,K,0)>-1}function ad(M,K,j){for(var se=-1,Ee=M==null?0:M.length;++se<Ee;)if(j(K,M[se]))return!0;return!1}function ct(M,K){for(var j=-1,se=M==null?0:M.length,Ee=Array(se);++j<se;)Ee[j]=K(M[j],j,M);return Ee}function xi(M,K){for(var j=-1,se=K.length,Ee=M.length;++j<se;)M[Ee+j]=K[j];return M}function cd(M,K,j,se){var Ee=-1,je=M==null?0:M.length;for(se&&je&&(j=M[++Ee]);++Ee<je;)j=K(j,M[Ee],Ee,M);return j}function Y_(M,K,j,se){var Ee=M==null?0:M.length;for(se&&Ee&&(j=M[--Ee]);Ee--;)j=K(j,M[Ee],Ee,M);return j}function fd(M,K){for(var j=-1,se=M==null?0:M.length;++j<se;)if(K(M[j],j,M))return!0;return!1}var X_=dd("length");function Z_(M){return M.split("")}function J_(M){return M.match(u_)||[]}function gy(M,K,j){var se;return j(M,function(Ee,je,_t){if(K(Ee,je,_t))return se=je,!1}),se}function Us(M,K,j,se){for(var Ee=M.length,je=j+(se?1:-1);se?je--:++je<Ee;)if(K(M[je],je,M))return je;return-1}function Ko(M,K,j){return K===K?fO(M,K,j):Us(M,my,j)}function eO(M,K,j,se){for(var Ee=j-1,je=M.length;++Ee<je;)if(se(M[Ee],K))return Ee;return-1}function my(M){return M!==M}function vy(M,K){var j=M==null?0:M.length;return j?hd(M,K)/j:_}function dd(M){return function(K){return K==null?n:K[M]}}function pd(M){return function(K){return M==null?n:M[K]}}function yy(M,K,j,se,Ee){return Ee(M,function(je,_t,Je){j=se?(se=!1,je):K(j,je,_t,Je)}),j}function tO(M,K){var j=M.length;for(M.sort(K);j--;)M[j]=M[j].value;return M}function hd(M,K){for(var j,se=-1,Ee=M.length;++se<Ee;){var je=K(M[se]);je!==n&&(j=j===n?je:j+je)}return j}function gd(M,K){for(var j=-1,se=Array(M);++j<M;)se[j]=K(j);return se}function nO(M,K){return ct(K,function(j){return[j,M[j]]})}function wy(M){return M&&M.slice(0,Cy(M)+1).replace(td,"")}function On(M){return function(K){return M(K)}}function md(M,K){return ct(K,function(j){return M[j]})}function au(M,K){return M.has(K)}function xy(M,K){for(var j=-1,se=M.length;++j<se&&Ko(K,M[j],0)>-1;);return j}function Sy(M,K){for(var j=M.length;j--&&Ko(K,M[j],0)>-1;);return j}function rO(M,K){for(var j=M.length,se=0;j--;)M[j]===K&&++se;return se}var iO=pd(U_),oO=pd(H_);function lO(M){return"\\"+W_[M]}function uO(M,K){return M==null?n:M[K]}function Qo(M){return F_.test(M)}function sO(M){return z_.test(M)}function aO(M){for(var K,j=[];!(K=M.next()).done;)j.push(K.value);return j}function vd(M){var K=-1,j=Array(M.size);return M.forEach(function(se,Ee){j[++K]=[Ee,se]}),j}function Ey(M,K){return function(j){return M(K(j))}}function Si(M,K){for(var j=-1,se=M.length,Ee=0,je=[];++j<se;){var _t=M[j];(_t===K||_t===p)&&(M[j]=p,je[Ee++]=j)}return je}function Hs(M){var K=-1,j=Array(M.size);return M.forEach(function(se){j[++K]=se}),j}function cO(M){var K=-1,j=Array(M.size);return M.forEach(function(se){j[++K]=[se,se]}),j}function fO(M,K,j){for(var se=j-1,Ee=M.length;++se<Ee;)if(M[se]===K)return se;return-1}function dO(M,K,j){for(var se=j+1;se--;)if(M[se]===K)return se;return se}function Yo(M){return Qo(M)?hO(M):X_(M)}function hr(M){return Qo(M)?gO(M):Z_(M)}function Cy(M){for(var K=M.length;K--&&r_.test(M.charAt(K)););return K}var pO=pd(j_);function hO(M){for(var K=ld.lastIndex=0;ld.test(M);)++K;return K}function gO(M){return M.match(ld)||[]}function mO(M){return M.match(L_)||[]}var vO=function M(K){K=K==null?Lt:Xo.defaults(Lt.Object(),K,Xo.pick(Lt,$_));var j=K.Array,se=K.Date,Ee=K.Error,je=K.Function,_t=K.Math,Je=K.Object,yd=K.RegExp,yO=K.String,Qn=K.TypeError,js=j.prototype,wO=je.prototype,Zo=Je.prototype,Ws=K["__core-js_shared__"],Vs=wO.toString,Ke=Zo.hasOwnProperty,xO=0,ky=function(){var o=/[^.]+$/.exec(Ws&&Ws.keys&&Ws.keys.IE_PROTO||"");return o?"Symbol(src)_1."+o:""}(),Gs=Zo.toString,SO=Vs.call(Je),EO=Lt._,CO=yd("^"+Vs.call(Ke).replace(ed,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),qs=uy?K.Buffer:n,Ei=K.Symbol,Ks=K.Uint8Array,by=qs?qs.allocUnsafe:n,Qs=Ey(Je.getPrototypeOf,Je),_y=Je.create,Oy=Zo.propertyIsEnumerable,Ys=js.splice,Ty=Ei?Ei.isConcatSpreadable:n,cu=Ei?Ei.iterator:n,fo=Ei?Ei.toStringTag:n,Xs=function(){try{var o=vo(Je,"defineProperty");return o({},"",{}),o}catch{}}(),kO=K.clearTimeout!==Lt.clearTimeout&&K.clearTimeout,bO=se&&se.now!==Lt.Date.now&&se.now,_O=K.setTimeout!==Lt.setTimeout&&K.setTimeout,Zs=_t.ceil,Js=_t.floor,wd=Je.getOwnPropertySymbols,OO=qs?qs.isBuffer:n,Iy=K.isFinite,TO=js.join,IO=Ey(Je.keys,Je),Ot=_t.max,jt=_t.min,RO=se.now,PO=K.parseInt,Ry=_t.random,AO=js.reverse,xd=vo(K,"DataView"),fu=vo(K,"Map"),Sd=vo(K,"Promise"),Jo=vo(K,"Set"),du=vo(K,"WeakMap"),pu=vo(Je,"create"),ea=du&&new du,el={},DO=yo(xd),NO=yo(fu),MO=yo(Sd),LO=yo(Jo),FO=yo(du),ta=Ei?Ei.prototype:n,hu=ta?ta.valueOf:n,Py=ta?ta.toString:n;function C(o){if(wt(o)&&!ke(o)&&!(o instanceof Me)){if(o instanceof Yn)return o;if(Ke.call(o,"__wrapped__"))return A0(o)}return new Yn(o)}var tl=function(){function o(){}return function(u){if(!ht(u))return{};if(_y)return _y(u);o.prototype=u;var c=new o;return o.prototype=n,c}}();function na(){}function Yn(o,u){this.__wrapped__=o,this.__actions__=[],this.__chain__=!!u,this.__index__=0,this.__values__=n}C.templateSettings={escape:Xb,evaluate:Zb,interpolate:Bv,variable:"",imports:{_:C}},C.prototype=na.prototype,C.prototype.constructor=C,Yn.prototype=tl(na.prototype),Yn.prototype.constructor=Yn;function Me(o){this.__wrapped__=o,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=ne,this.__views__=[]}function zO(){var o=new Me(this.__wrapped__);return o.__actions__=sn(this.__actions__),o.__dir__=this.__dir__,o.__filtered__=this.__filtered__,o.__iteratees__=sn(this.__iteratees__),o.__takeCount__=this.__takeCount__,o.__views__=sn(this.__views__),o}function $O(){if(this.__filtered__){var o=new Me(this);o.__dir__=-1,o.__filtered__=!0}else o=this.clone(),o.__dir__*=-1;return o}function BO(){var o=this.__wrapped__.value(),u=this.__dir__,c=ke(o),h=u<0,w=c?o.length:0,b=ZT(0,w,this.__views__),R=b.start,N=b.end,L=N-R,Y=h?N:R-1,X=this.__iteratees__,J=X.length,ue=0,fe=jt(L,this.__takeCount__);if(!c||!h&&w==L&&fe==L)return t0(o,this.__actions__);var ve=[];e:for(;L--&&ue<fe;){Y+=u;for(var Ie=-1,ye=o[Y];++Ie<J;){var De=X[Ie],ze=De.iteratee,Rn=De.type,tn=ze(ye);if(Rn==ee)ye=tn;else if(!tn){if(Rn==H)continue e;break e}}ve[ue++]=ye}return ve}Me.prototype=tl(na.prototype),Me.prototype.constructor=Me;function po(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var h=o[u];this.set(h[0],h[1])}}function UO(){this.__data__=pu?pu(null):{},this.size=0}function HO(o){var u=this.has(o)&&delete this.__data__[o];return this.size-=u?1:0,u}function jO(o){var u=this.__data__;if(pu){var c=u[o];return c===f?n:c}return Ke.call(u,o)?u[o]:n}function WO(o){var u=this.__data__;return pu?u[o]!==n:Ke.call(u,o)}function VO(o,u){var c=this.__data__;return this.size+=this.has(o)?0:1,c[o]=pu&&u===n?f:u,this}po.prototype.clear=UO,po.prototype.delete=HO,po.prototype.get=jO,po.prototype.has=WO,po.prototype.set=VO;function Qr(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var h=o[u];this.set(h[0],h[1])}}function GO(){this.__data__=[],this.size=0}function qO(o){var u=this.__data__,c=ra(u,o);if(c<0)return!1;var h=u.length-1;return c==h?u.pop():Ys.call(u,c,1),--this.size,!0}function KO(o){var u=this.__data__,c=ra(u,o);return c<0?n:u[c][1]}function QO(o){return ra(this.__data__,o)>-1}function YO(o,u){var c=this.__data__,h=ra(c,o);return h<0?(++this.size,c.push([o,u])):c[h][1]=u,this}Qr.prototype.clear=GO,Qr.prototype.delete=qO,Qr.prototype.get=KO,Qr.prototype.has=QO,Qr.prototype.set=YO;function Yr(o){var u=-1,c=o==null?0:o.length;for(this.clear();++u<c;){var h=o[u];this.set(h[0],h[1])}}function XO(){this.size=0,this.__data__={hash:new po,map:new(fu||Qr),string:new po}}function ZO(o){var u=ga(this,o).delete(o);return this.size-=u?1:0,u}function JO(o){return ga(this,o).get(o)}function eT(o){return ga(this,o).has(o)}function tT(o,u){var c=ga(this,o),h=c.size;return c.set(o,u),this.size+=c.size==h?0:1,this}Yr.prototype.clear=XO,Yr.prototype.delete=ZO,Yr.prototype.get=JO,Yr.prototype.has=eT,Yr.prototype.set=tT;function ho(o){var u=-1,c=o==null?0:o.length;for(this.__data__=new Yr;++u<c;)this.add(o[u])}function nT(o){return this.__data__.set(o,f),this}function rT(o){return this.__data__.has(o)}ho.prototype.add=ho.prototype.push=nT,ho.prototype.has=rT;function gr(o){var u=this.__data__=new Qr(o);this.size=u.size}function iT(){this.__data__=new Qr,this.size=0}function oT(o){var u=this.__data__,c=u.delete(o);return this.size=u.size,c}function lT(o){return this.__data__.get(o)}function uT(o){return this.__data__.has(o)}function sT(o,u){var c=this.__data__;if(c instanceof Qr){var h=c.__data__;if(!fu||h.length<i-1)return h.push([o,u]),this.size=++c.size,this;c=this.__data__=new Yr(h)}return c.set(o,u),this.size=c.size,this}gr.prototype.clear=iT,gr.prototype.delete=oT,gr.prototype.get=lT,gr.prototype.has=uT,gr.prototype.set=sT;function Ay(o,u){var c=ke(o),h=!c&&wo(o),w=!c&&!h&&Oi(o),b=!c&&!h&&!w&&ol(o),R=c||h||w||b,N=R?gd(o.length,yO):[],L=N.length;for(var Y in o)(u||Ke.call(o,Y))&&!(R&&(Y=="length"||w&&(Y=="offset"||Y=="parent")||b&&(Y=="buffer"||Y=="byteLength"||Y=="byteOffset")||ei(Y,L)))&&N.push(Y);return N}function Dy(o){var u=o.length;return u?o[Ad(0,u-1)]:n}function aT(o,u){return ma(sn(o),go(u,0,o.length))}function cT(o){return ma(sn(o))}function Ed(o,u,c){(c!==n&&!mr(o[u],c)||c===n&&!(u in o))&&Xr(o,u,c)}function gu(o,u,c){var h=o[u];(!(Ke.call(o,u)&&mr(h,c))||c===n&&!(u in o))&&Xr(o,u,c)}function ra(o,u){for(var c=o.length;c--;)if(mr(o[c][0],u))return c;return-1}function fT(o,u,c,h){return Ci(o,function(w,b,R){u(h,w,c(w),R)}),h}function Ny(o,u){return o&&Pr(u,Rt(u),o)}function dT(o,u){return o&&Pr(u,cn(u),o)}function Xr(o,u,c){u=="__proto__"&&Xs?Xs(o,u,{configurable:!0,enumerable:!0,value:c,writable:!0}):o[u]=c}function Cd(o,u){for(var c=-1,h=u.length,w=j(h),b=o==null;++c<h;)w[c]=b?n:rp(o,u[c]);return w}function go(o,u,c){return o===o&&(c!==n&&(o=o<=c?o:c),u!==n&&(o=o>=u?o:u)),o}function Xn(o,u,c,h,w,b){var R,N=u&g,L=u&v,Y=u&m;if(c&&(R=w?c(o,h,w,b):c(o)),R!==n)return R;if(!ht(o))return o;var X=ke(o);if(X){if(R=eI(o),!N)return sn(o,R)}else{var J=Wt(o),ue=J==Fe||J==qe;if(Oi(o))return i0(o,N);if(J==at||J==Re||ue&&!w){if(R=L||ue?{}:C0(o),!N)return L?jT(o,dT(R,o)):HT(o,Ny(R,o))}else{if(!et[J])return w?o:{};R=tI(o,J,N)}}b||(b=new gr);var fe=b.get(o);if(fe)return fe;b.set(o,R),Z0(o)?o.forEach(function(ye){R.add(Xn(ye,u,c,ye,o,b))}):Y0(o)&&o.forEach(function(ye,De){R.set(De,Xn(ye,u,c,De,o,b))});var ve=Y?L?jd:Hd:L?cn:Rt,Ie=X?n:ve(o);return Kn(Ie||o,function(ye,De){Ie&&(De=ye,ye=o[De]),gu(R,De,Xn(ye,u,c,De,o,b))}),R}function pT(o){var u=Rt(o);return function(c){return My(c,o,u)}}function My(o,u,c){var h=c.length;if(o==null)return!h;for(o=Je(o);h--;){var w=c[h],b=u[w],R=o[w];if(R===n&&!(w in o)||!b(R))return!1}return!0}function Ly(o,u,c){if(typeof o!="function")throw new Qn(s);return Eu(function(){o.apply(n,c)},u)}function mu(o,u,c,h){var w=-1,b=Bs,R=!0,N=o.length,L=[],Y=u.length;if(!N)return L;c&&(u=ct(u,On(c))),h?(b=ad,R=!1):u.length>=i&&(b=au,R=!1,u=new ho(u));e:for(;++w<N;){var X=o[w],J=c==null?X:c(X);if(X=h||X!==0?X:0,R&&J===J){for(var ue=Y;ue--;)if(u[ue]===J)continue e;L.push(X)}else b(u,J,h)||L.push(X)}return L}var Ci=a0(Rr),Fy=a0(bd,!0);function hT(o,u){var c=!0;return Ci(o,function(h,w,b){return c=!!u(h,w,b),c}),c}function ia(o,u,c){for(var h=-1,w=o.length;++h<w;){var b=o[h],R=u(b);if(R!=null&&(N===n?R===R&&!In(R):c(R,N)))var N=R,L=b}return L}function gT(o,u,c,h){var w=o.length;for(c=Oe(c),c<0&&(c=-c>w?0:w+c),h=h===n||h>w?w:Oe(h),h<0&&(h+=w),h=c>h?0:e1(h);c<h;)o[c++]=u;return o}function zy(o,u){var c=[];return Ci(o,function(h,w,b){u(h,w,b)&&c.push(h)}),c}function Ft(o,u,c,h,w){var b=-1,R=o.length;for(c||(c=rI),w||(w=[]);++b<R;){var N=o[b];u>0&&c(N)?u>1?Ft(N,u-1,c,h,w):xi(w,N):h||(w[w.length]=N)}return w}var kd=c0(),$y=c0(!0);function Rr(o,u){return o&&kd(o,u,Rt)}function bd(o,u){return o&&$y(o,u,Rt)}function oa(o,u){return wi(u,function(c){return ti(o[c])})}function mo(o,u){u=bi(u,o);for(var c=0,h=u.length;o!=null&&c<h;)o=o[Ar(u[c++])];return c&&c==h?o:n}function By(o,u,c){var h=u(o);return ke(o)?h:xi(h,c(o))}function Jt(o){return o==null?o===n?Go:he:fo&&fo in Je(o)?XT(o):cI(o)}function _d(o,u){return o>u}function mT(o,u){return o!=null&&Ke.call(o,u)}function vT(o,u){return o!=null&&u in Je(o)}function yT(o,u,c){return o>=jt(u,c)&&o<Ot(u,c)}function Od(o,u,c){for(var h=c?ad:Bs,w=o[0].length,b=o.length,R=b,N=j(b),L=1/0,Y=[];R--;){var X=o[R];R&&u&&(X=ct(X,On(u))),L=jt(X.length,L),N[R]=!c&&(u||w>=120&&X.length>=120)?new ho(R&&X):n}X=o[0];var J=-1,ue=N[0];e:for(;++J<w&&Y.length<L;){var fe=X[J],ve=u?u(fe):fe;if(fe=c||fe!==0?fe:0,!(ue?au(ue,ve):h(Y,ve,c))){for(R=b;--R;){var Ie=N[R];if(!(Ie?au(Ie,ve):h(o[R],ve,c)))continue e}ue&&ue.push(ve),Y.push(fe)}}return Y}function wT(o,u,c,h){return Rr(o,function(w,b,R){u(h,c(w),b,R)}),h}function vu(o,u,c){u=bi(u,o),o=O0(o,u);var h=o==null?o:o[Ar(Jn(u))];return h==null?n:_n(h,o,c)}function Uy(o){return wt(o)&&Jt(o)==Re}function xT(o){return wt(o)&&Jt(o)==V}function ST(o){return wt(o)&&Jt(o)==Le}function yu(o,u,c,h,w){return o===u?!0:o==null||u==null||!wt(o)&&!wt(u)?o!==o&&u!==u:ET(o,u,c,h,yu,w)}function ET(o,u,c,h,w,b){var R=ke(o),N=ke(u),L=R?me:Wt(o),Y=N?me:Wt(u);L=L==Re?at:L,Y=Y==Re?at:Y;var X=L==at,J=Y==at,ue=L==Y;if(ue&&Oi(o)){if(!Oi(u))return!1;R=!0,X=!1}if(ue&&!X)return b||(b=new gr),R||ol(o)?x0(o,u,c,h,w,b):QT(o,u,L,c,h,w,b);if(!(c&E)){var fe=X&&Ke.call(o,"__wrapped__"),ve=J&&Ke.call(u,"__wrapped__");if(fe||ve){var Ie=fe?o.value():o,ye=ve?u.value():u;return b||(b=new gr),w(Ie,ye,c,h,b)}}return ue?(b||(b=new gr),YT(o,u,c,h,w,b)):!1}function CT(o){return wt(o)&&Wt(o)==yt}function Td(o,u,c,h){var w=c.length,b=w,R=!h;if(o==null)return!b;for(o=Je(o);w--;){var N=c[w];if(R&&N[2]?N[1]!==o[N[0]]:!(N[0]in o))return!1}for(;++w<b;){N=c[w];var L=N[0],Y=o[L],X=N[1];if(R&&N[2]){if(Y===n&&!(L in o))return!1}else{var J=new gr;if(h)var ue=h(Y,X,L,o,u,J);if(!(ue===n?yu(X,Y,E|S,h,J):ue))return!1}}return!0}function Hy(o){if(!ht(o)||oI(o))return!1;var u=ti(o)?CO:p_;return u.test(yo(o))}function kT(o){return wt(o)&&Jt(o)==kn}function bT(o){return wt(o)&&Wt(o)==Ht}function _T(o){return wt(o)&&Ea(o.length)&&!!ot[Jt(o)]}function jy(o){return typeof o=="function"?o:o==null?fn:typeof o=="object"?ke(o)?Gy(o[0],o[1]):Vy(o):f1(o)}function Id(o){if(!Su(o))return IO(o);var u=[];for(var c in Je(o))Ke.call(o,c)&&c!="constructor"&&u.push(c);return u}function OT(o){if(!ht(o))return aI(o);var u=Su(o),c=[];for(var h in o)h=="constructor"&&(u||!Ke.call(o,h))||c.push(h);return c}function Rd(o,u){return o<u}function Wy(o,u){var c=-1,h=an(o)?j(o.length):[];return Ci(o,function(w,b,R){h[++c]=u(w,b,R)}),h}function Vy(o){var u=Vd(o);return u.length==1&&u[0][2]?b0(u[0][0],u[0][1]):function(c){return c===o||Td(c,o,u)}}function Gy(o,u){return qd(o)&&k0(u)?b0(Ar(o),u):function(c){var h=rp(c,o);return h===n&&h===u?ip(c,o):yu(u,h,E|S)}}function la(o,u,c,h,w){o!==u&&kd(u,function(b,R){if(w||(w=new gr),ht(b))TT(o,u,R,c,la,h,w);else{var N=h?h(Qd(o,R),b,R+"",o,u,w):n;N===n&&(N=b),Ed(o,R,N)}},cn)}function TT(o,u,c,h,w,b,R){var N=Qd(o,c),L=Qd(u,c),Y=R.get(L);if(Y){Ed(o,c,Y);return}var X=b?b(N,L,c+"",o,u,R):n,J=X===n;if(J){var ue=ke(L),fe=!ue&&Oi(L),ve=!ue&&!fe&&ol(L);X=L,ue||fe||ve?ke(N)?X=N:St(N)?X=sn(N):fe?(J=!1,X=i0(L,!0)):ve?(J=!1,X=o0(L,!0)):X=[]:Cu(L)||wo(L)?(X=N,wo(N)?X=t1(N):(!ht(N)||ti(N))&&(X=C0(L))):J=!1}J&&(R.set(L,X),w(X,L,h,b,R),R.delete(L)),Ed(o,c,X)}function qy(o,u){var c=o.length;if(c)return u+=u<0?c:0,ei(u,c)?o[u]:n}function Ky(o,u,c){u.length?u=ct(u,function(b){return ke(b)?function(R){return mo(R,b.length===1?b[0]:b)}:b}):u=[fn];var h=-1;u=ct(u,On(ge()));var w=Wy(o,function(b,R,N){var L=ct(u,function(Y){return Y(b)});return{criteria:L,index:++h,value:b}});return tO(w,function(b,R){return UT(b,R,c)})}function IT(o,u){return Qy(o,u,function(c,h){return ip(o,h)})}function Qy(o,u,c){for(var h=-1,w=u.length,b={};++h<w;){var R=u[h],N=mo(o,R);c(N,R)&&wu(b,bi(R,o),N)}return b}function RT(o){return function(u){return mo(u,o)}}function Pd(o,u,c,h){var w=h?eO:Ko,b=-1,R=u.length,N=o;for(o===u&&(u=sn(u)),c&&(N=ct(o,On(c)));++b<R;)for(var L=0,Y=u[b],X=c?c(Y):Y;(L=w(N,X,L,h))>-1;)N!==o&&Ys.call(N,L,1),Ys.call(o,L,1);return o}function Yy(o,u){for(var c=o?u.length:0,h=c-1;c--;){var w=u[c];if(c==h||w!==b){var b=w;ei(w)?Ys.call(o,w,1):Md(o,w)}}return o}function Ad(o,u){return o+Js(Ry()*(u-o+1))}function PT(o,u,c,h){for(var w=-1,b=Ot(Zs((u-o)/(c||1)),0),R=j(b);b--;)R[h?b:++w]=o,o+=c;return R}function Dd(o,u){var c="";if(!o||u<1||u>$)return c;do u%2&&(c+=o),u=Js(u/2),u&&(o+=o);while(u);return c}function Pe(o,u){return Yd(_0(o,u,fn),o+"")}function AT(o){return Dy(ll(o))}function DT(o,u){var c=ll(o);return ma(c,go(u,0,c.length))}function wu(o,u,c,h){if(!ht(o))return o;u=bi(u,o);for(var w=-1,b=u.length,R=b-1,N=o;N!=null&&++w<b;){var L=Ar(u[w]),Y=c;if(L==="__proto__"||L==="constructor"||L==="prototype")return o;if(w!=R){var X=N[L];Y=h?h(X,L,N):n,Y===n&&(Y=ht(X)?X:ei(u[w+1])?[]:{})}gu(N,L,Y),N=N[L]}return o}var Xy=ea?function(o,u){return ea.set(o,u),o}:fn,NT=Xs?function(o,u){return Xs(o,"toString",{configurable:!0,enumerable:!1,value:lp(u),writable:!0})}:fn;function MT(o){return ma(ll(o))}function Zn(o,u,c){var h=-1,w=o.length;u<0&&(u=-u>w?0:w+u),c=c>w?w:c,c<0&&(c+=w),w=u>c?0:c-u>>>0,u>>>=0;for(var b=j(w);++h<w;)b[h]=o[h+u];return b}function LT(o,u){var c;return Ci(o,function(h,w,b){return c=u(h,w,b),!c}),!!c}function ua(o,u,c){var h=0,w=o==null?h:o.length;if(typeof u=="number"&&u===u&&w<=O){for(;h<w;){var b=h+w>>>1,R=o[b];R!==null&&!In(R)&&(c?R<=u:R<u)?h=b+1:w=b}return w}return Nd(o,u,fn,c)}function Nd(o,u,c,h){var w=0,b=o==null?0:o.length;if(b===0)return 0;u=c(u);for(var R=u!==u,N=u===null,L=In(u),Y=u===n;w<b;){var X=Js((w+b)/2),J=c(o[X]),ue=J!==n,fe=J===null,ve=J===J,Ie=In(J);if(R)var ye=h||ve;else Y?ye=ve&&(h||ue):N?ye=ve&&ue&&(h||!fe):L?ye=ve&&ue&&!fe&&(h||!Ie):fe||Ie?ye=!1:ye=h?J<=u:J<u;ye?w=X+1:b=X}return jt(b,ae)}function Zy(o,u){for(var c=-1,h=o.length,w=0,b=[];++c<h;){var R=o[c],N=u?u(R):R;if(!c||!mr(N,L)){var L=N;b[w++]=R===0?0:R}}return b}function Jy(o){return typeof o=="number"?o:In(o)?_:+o}function Tn(o){if(typeof o=="string")return o;if(ke(o))return ct(o,Tn)+"";if(In(o))return Py?Py.call(o):"";var u=o+"";return u=="0"&&1/o==-oe?"-0":u}function ki(o,u,c){var h=-1,w=Bs,b=o.length,R=!0,N=[],L=N;if(c)R=!1,w=ad;else if(b>=i){var Y=u?null:qT(o);if(Y)return Hs(Y);R=!1,w=au,L=new ho}else L=u?[]:N;e:for(;++h<b;){var X=o[h],J=u?u(X):X;if(X=c||X!==0?X:0,R&&J===J){for(var ue=L.length;ue--;)if(L[ue]===J)continue e;u&&L.push(J),N.push(X)}else w(L,J,c)||(L!==N&&L.push(J),N.push(X))}return N}function Md(o,u){return u=bi(u,o),o=O0(o,u),o==null||delete o[Ar(Jn(u))]}function e0(o,u,c,h){return wu(o,u,c(mo(o,u)),h)}function sa(o,u,c,h){for(var w=o.length,b=h?w:-1;(h?b--:++b<w)&&u(o[b],b,o););return c?Zn(o,h?0:b,h?b+1:w):Zn(o,h?b+1:0,h?w:b)}function t0(o,u){var c=o;return c instanceof Me&&(c=c.value()),cd(u,function(h,w){return w.func.apply(w.thisArg,xi([h],w.args))},c)}function Ld(o,u,c){var h=o.length;if(h<2)return h?ki(o[0]):[];for(var w=-1,b=j(h);++w<h;)for(var R=o[w],N=-1;++N<h;)N!=w&&(b[w]=mu(b[w]||R,o[N],u,c));return ki(Ft(b,1),u,c)}function n0(o,u,c){for(var h=-1,w=o.length,b=u.length,R={};++h<w;){var N=h<b?u[h]:n;c(R,o[h],N)}return R}function Fd(o){return St(o)?o:[]}function zd(o){return typeof o=="function"?o:fn}function bi(o,u){return ke(o)?o:qd(o,u)?[o]:P0(Ge(o))}var FT=Pe;function _i(o,u,c){var h=o.length;return c=c===n?h:c,!u&&c>=h?o:Zn(o,u,c)}var r0=kO||function(o){return Lt.clearTimeout(o)};function i0(o,u){if(u)return o.slice();var c=o.length,h=by?by(c):new o.constructor(c);return o.copy(h),h}function $d(o){var u=new o.constructor(o.byteLength);return new Ks(u).set(new Ks(o)),u}function zT(o,u){var c=u?$d(o.buffer):o.buffer;return new o.constructor(c,o.byteOffset,o.byteLength)}function $T(o){var u=new o.constructor(o.source,Uv.exec(o));return u.lastIndex=o.lastIndex,u}function BT(o){return hu?Je(hu.call(o)):{}}function o0(o,u){var c=u?$d(o.buffer):o.buffer;return new o.constructor(c,o.byteOffset,o.length)}function l0(o,u){if(o!==u){var c=o!==n,h=o===null,w=o===o,b=In(o),R=u!==n,N=u===null,L=u===u,Y=In(u);if(!N&&!Y&&!b&&o>u||b&&R&&L&&!N&&!Y||h&&R&&L||!c&&L||!w)return 1;if(!h&&!b&&!Y&&o<u||Y&&c&&w&&!h&&!b||N&&c&&w||!R&&w||!L)return-1}return 0}function UT(o,u,c){for(var h=-1,w=o.criteria,b=u.criteria,R=w.length,N=c.length;++h<R;){var L=l0(w[h],b[h]);if(L){if(h>=N)return L;var Y=c[h];return L*(Y=="desc"?-1:1)}}return o.index-u.index}function u0(o,u,c,h){for(var w=-1,b=o.length,R=c.length,N=-1,L=u.length,Y=Ot(b-R,0),X=j(L+Y),J=!h;++N<L;)X[N]=u[N];for(;++w<R;)(J||w<b)&&(X[c[w]]=o[w]);for(;Y--;)X[N++]=o[w++];return X}function s0(o,u,c,h){for(var w=-1,b=o.length,R=-1,N=c.length,L=-1,Y=u.length,X=Ot(b-N,0),J=j(X+Y),ue=!h;++w<X;)J[w]=o[w];for(var fe=w;++L<Y;)J[fe+L]=u[L];for(;++R<N;)(ue||w<b)&&(J[fe+c[R]]=o[w++]);return J}function sn(o,u){var c=-1,h=o.length;for(u||(u=j(h));++c<h;)u[c]=o[c];return u}function Pr(o,u,c,h){var w=!c;c||(c={});for(var b=-1,R=u.length;++b<R;){var N=u[b],L=h?h(c[N],o[N],N,c,o):n;L===n&&(L=o[N]),w?Xr(c,N,L):gu(c,N,L)}return c}function HT(o,u){return Pr(o,Gd(o),u)}function jT(o,u){return Pr(o,S0(o),u)}function aa(o,u){return function(c,h){var w=ke(c)?K_:fT,b=u?u():{};return w(c,o,ge(h,2),b)}}function nl(o){return Pe(function(u,c){var h=-1,w=c.length,b=w>1?c[w-1]:n,R=w>2?c[2]:n;for(b=o.length>3&&typeof b=="function"?(w--,b):n,R&&en(c[0],c[1],R)&&(b=w<3?n:b,w=1),u=Je(u);++h<w;){var N=c[h];N&&o(u,N,h,b)}return u})}function a0(o,u){return function(c,h){if(c==null)return c;if(!an(c))return o(c,h);for(var w=c.length,b=u?w:-1,R=Je(c);(u?b--:++b<w)&&h(R[b],b,R)!==!1;);return c}}function c0(o){return function(u,c,h){for(var w=-1,b=Je(u),R=h(u),N=R.length;N--;){var L=R[o?N:++w];if(c(b[L],L,b)===!1)break}return u}}function WT(o,u,c){var h=u&A,w=xu(o);function b(){var R=this&&this!==Lt&&this instanceof b?w:o;return R.apply(h?c:this,arguments)}return b}function f0(o){return function(u){u=Ge(u);var c=Qo(u)?hr(u):n,h=c?c[0]:u.charAt(0),w=c?_i(c,1).join(""):u.slice(1);return h[o]()+w}}function rl(o){return function(u){return cd(a1(s1(u).replace(N_,"")),o,"")}}function xu(o){return function(){var u=arguments;switch(u.length){case 0:return new o;case 1:return new o(u[0]);case 2:return new o(u[0],u[1]);case 3:return new o(u[0],u[1],u[2]);case 4:return new o(u[0],u[1],u[2],u[3]);case 5:return new o(u[0],u[1],u[2],u[3],u[4]);case 6:return new o(u[0],u[1],u[2],u[3],u[4],u[5]);case 7:return new o(u[0],u[1],u[2],u[3],u[4],u[5],u[6])}var c=tl(o.prototype),h=o.apply(c,u);return ht(h)?h:c}}function VT(o,u,c){var h=xu(o);function w(){for(var b=arguments.length,R=j(b),N=b,L=il(w);N--;)R[N]=arguments[N];var Y=b<3&&R[0]!==L&&R[b-1]!==L?[]:Si(R,L);if(b-=Y.length,b<c)return m0(o,u,ca,w.placeholder,n,R,Y,n,n,c-b);var X=this&&this!==Lt&&this instanceof w?h:o;return _n(X,this,R)}return w}function d0(o){return function(u,c,h){var w=Je(u);if(!an(u)){var b=ge(c,3);u=Rt(u),c=function(N){return b(w[N],N,w)}}var R=o(u,c,h);return R>-1?w[b?u[R]:R]:n}}function p0(o){return Jr(function(u){var c=u.length,h=c,w=Yn.prototype.thru;for(o&&u.reverse();h--;){var b=u[h];if(typeof b!="function")throw new Qn(s);if(w&&!R&&ha(b)=="wrapper")var R=new Yn([],!0)}for(h=R?h:c;++h<c;){b=u[h];var N=ha(b),L=N=="wrapper"?Wd(b):n;L&&Kd(L[0])&&L[1]==(F|k|D|z)&&!L[4].length&&L[9]==1?R=R[ha(L[0])].apply(R,L[3]):R=b.length==1&&Kd(b)?R[N]():R.thru(b)}return function(){var Y=arguments,X=Y[0];if(R&&Y.length==1&&ke(X))return R.plant(X).value();for(var J=0,ue=c?u[J].apply(this,Y):X;++J<c;)ue=u[J].call(this,ue);return ue}})}function ca(o,u,c,h,w,b,R,N,L,Y){var X=u&F,J=u&A,ue=u&y,fe=u&(k|P),ve=u&G,Ie=ue?n:xu(o);function ye(){for(var De=arguments.length,ze=j(De),Rn=De;Rn--;)ze[Rn]=arguments[Rn];if(fe)var tn=il(ye),Pn=rO(ze,tn);if(h&&(ze=u0(ze,h,w,fe)),b&&(ze=s0(ze,b,R,fe)),De-=Pn,fe&&De<Y){var Et=Si(ze,tn);return m0(o,u,ca,ye.placeholder,c,ze,Et,N,L,Y-De)}var vr=J?c:this,ri=ue?vr[o]:o;return De=ze.length,N?ze=fI(ze,N):ve&&De>1&&ze.reverse(),X&&L<De&&(ze.length=L),this&&this!==Lt&&this instanceof ye&&(ri=Ie||xu(ri)),ri.apply(vr,ze)}return ye}function h0(o,u){return function(c,h){return wT(c,o,u(h),{})}}function fa(o,u){return function(c,h){var w;if(c===n&&h===n)return u;if(c!==n&&(w=c),h!==n){if(w===n)return h;typeof c=="string"||typeof h=="string"?(c=Tn(c),h=Tn(h)):(c=Jy(c),h=Jy(h)),w=o(c,h)}return w}}function Bd(o){return Jr(function(u){return u=ct(u,On(ge())),Pe(function(c){var h=this;return o(u,function(w){return _n(w,h,c)})})})}function da(o,u){u=u===n?" ":Tn(u);var c=u.length;if(c<2)return c?Dd(u,o):u;var h=Dd(u,Zs(o/Yo(u)));return Qo(u)?_i(hr(h),0,o).join(""):h.slice(0,o)}function GT(o,u,c,h){var w=u&A,b=xu(o);function R(){for(var N=-1,L=arguments.length,Y=-1,X=h.length,J=j(X+L),ue=this&&this!==Lt&&this instanceof R?b:o;++Y<X;)J[Y]=h[Y];for(;L--;)J[Y++]=arguments[++N];return _n(ue,w?c:this,J)}return R}function g0(o){return function(u,c,h){return h&&typeof h!="number"&&en(u,c,h)&&(c=h=n),u=ni(u),c===n?(c=u,u=0):c=ni(c),h=h===n?u<c?1:-1:ni(h),PT(u,c,h,o)}}function pa(o){return function(u,c){return typeof u=="string"&&typeof c=="string"||(u=er(u),c=er(c)),o(u,c)}}function m0(o,u,c,h,w,b,R,N,L,Y){var X=u&k,J=X?R:n,ue=X?n:R,fe=X?b:n,ve=X?n:b;u|=X?D:T,u&=~(X?T:D),u&x||(u&=~(A|y));var Ie=[o,u,w,fe,J,ve,ue,N,L,Y],ye=c.apply(n,Ie);return Kd(o)&&T0(ye,Ie),ye.placeholder=h,I0(ye,o,u)}function Ud(o){var u=_t[o];return function(c,h){if(c=er(c),h=h==null?0:jt(Oe(h),292),h&&Iy(c)){var w=(Ge(c)+"e").split("e"),b=u(w[0]+"e"+(+w[1]+h));return w=(Ge(b)+"e").split("e"),+(w[0]+"e"+(+w[1]-h))}return u(c)}}var qT=Jo&&1/Hs(new Jo([,-0]))[1]==oe?function(o){return new Jo(o)}:ap;function v0(o){return function(u){var c=Wt(u);return c==yt?vd(u):c==Ht?cO(u):nO(u,o(u))}}function Zr(o,u,c,h,w,b,R,N){var L=u&y;if(!L&&typeof o!="function")throw new Qn(s);var Y=h?h.length:0;if(Y||(u&=~(D|T),h=w=n),R=R===n?R:Ot(Oe(R),0),N=N===n?N:Oe(N),Y-=w?w.length:0,u&T){var X=h,J=w;h=w=n}var ue=L?n:Wd(o),fe=[o,u,c,h,w,X,J,b,R,N];if(ue&&sI(fe,ue),o=fe[0],u=fe[1],c=fe[2],h=fe[3],w=fe[4],N=fe[9]=fe[9]===n?L?0:o.length:Ot(fe[9]-Y,0),!N&&u&(k|P)&&(u&=~(k|P)),!u||u==A)var ve=WT(o,u,c);else u==k||u==P?ve=VT(o,u,N):(u==D||u==(A|D))&&!w.length?ve=GT(o,u,c,h):ve=ca.apply(n,fe);var Ie=ue?Xy:T0;return I0(Ie(ve,fe),o,u)}function y0(o,u,c,h){return o===n||mr(o,Zo[c])&&!Ke.call(h,c)?u:o}function w0(o,u,c,h,w,b){return ht(o)&&ht(u)&&(b.set(u,o),la(o,u,n,w0,b),b.delete(u)),o}function KT(o){return Cu(o)?n:o}function x0(o,u,c,h,w,b){var R=c&E,N=o.length,L=u.length;if(N!=L&&!(R&&L>N))return!1;var Y=b.get(o),X=b.get(u);if(Y&&X)return Y==u&&X==o;var J=-1,ue=!0,fe=c&S?new ho:n;for(b.set(o,u),b.set(u,o);++J<N;){var ve=o[J],Ie=u[J];if(h)var ye=R?h(Ie,ve,J,u,o,b):h(ve,Ie,J,o,u,b);if(ye!==n){if(ye)continue;ue=!1;break}if(fe){if(!fd(u,function(De,ze){if(!au(fe,ze)&&(ve===De||w(ve,De,c,h,b)))return fe.push(ze)})){ue=!1;break}}else if(!(ve===Ie||w(ve,Ie,c,h,b))){ue=!1;break}}return b.delete(o),b.delete(u),ue}function QT(o,u,c,h,w,b,R){switch(c){case re:if(o.byteLength!=u.byteLength||o.byteOffset!=u.byteOffset)return!1;o=o.buffer,u=u.buffer;case V:return!(o.byteLength!=u.byteLength||!b(new Ks(o),new Ks(u)));case Se:case Le:case Wn:return mr(+o,+u);case Be:return o.name==u.name&&o.message==u.message;case kn:case Tr:return o==u+"";case yt:var N=vd;case Ht:var L=h&E;if(N||(N=Hs),o.size!=u.size&&!L)return!1;var Y=R.get(o);if(Y)return Y==u;h|=S,R.set(o,u);var X=x0(N(o),N(u),h,w,b,R);return R.delete(o),X;case Gr:if(hu)return hu.call(o)==hu.call(u)}return!1}function YT(o,u,c,h,w,b){var R=c&E,N=Hd(o),L=N.length,Y=Hd(u),X=Y.length;if(L!=X&&!R)return!1;for(var J=L;J--;){var ue=N[J];if(!(R?ue in u:Ke.call(u,ue)))return!1}var fe=b.get(o),ve=b.get(u);if(fe&&ve)return fe==u&&ve==o;var Ie=!0;b.set(o,u),b.set(u,o);for(var ye=R;++J<L;){ue=N[J];var De=o[ue],ze=u[ue];if(h)var Rn=R?h(ze,De,ue,u,o,b):h(De,ze,ue,o,u,b);if(!(Rn===n?De===ze||w(De,ze,c,h,b):Rn)){Ie=!1;break}ye||(ye=ue=="constructor")}if(Ie&&!ye){var tn=o.constructor,Pn=u.constructor;tn!=Pn&&"constructor"in o&&"constructor"in u&&!(typeof tn=="function"&&tn instanceof tn&&typeof Pn=="function"&&Pn instanceof Pn)&&(Ie=!1)}return b.delete(o),b.delete(u),Ie}function Jr(o){return Yd(_0(o,n,M0),o+"")}function Hd(o){return By(o,Rt,Gd)}function jd(o){return By(o,cn,S0)}var Wd=ea?function(o){return ea.get(o)}:ap;function ha(o){for(var u=o.name+"",c=el[u],h=Ke.call(el,u)?c.length:0;h--;){var w=c[h],b=w.func;if(b==null||b==o)return w.name}return u}function il(o){var u=Ke.call(C,"placeholder")?C:o;return u.placeholder}function ge(){var o=C.iteratee||up;return o=o===up?jy:o,arguments.length?o(arguments[0],arguments[1]):o}function ga(o,u){var c=o.__data__;return iI(u)?c[typeof u=="string"?"string":"hash"]:c.map}function Vd(o){for(var u=Rt(o),c=u.length;c--;){var h=u[c],w=o[h];u[c]=[h,w,k0(w)]}return u}function vo(o,u){var c=uO(o,u);return Hy(c)?c:n}function XT(o){var u=Ke.call(o,fo),c=o[fo];try{o[fo]=n;var h=!0}catch{}var w=Gs.call(o);return h&&(u?o[fo]=c:delete o[fo]),w}var Gd=wd?function(o){return o==null?[]:(o=Je(o),wi(wd(o),function(u){return Oy.call(o,u)}))}:cp,S0=wd?function(o){for(var u=[];o;)xi(u,Gd(o)),o=Qs(o);return u}:cp,Wt=Jt;(xd&&Wt(new xd(new ArrayBuffer(1)))!=re||fu&&Wt(new fu)!=yt||Sd&&Wt(Sd.resolve())!=Mt||Jo&&Wt(new Jo)!=Ht||du&&Wt(new du)!=Ir)&&(Wt=function(o){var u=Jt(o),c=u==at?o.constructor:n,h=c?yo(c):"";if(h)switch(h){case DO:return re;case NO:return yt;case MO:return Mt;case LO:return Ht;case FO:return Ir}return u});function ZT(o,u,c){for(var h=-1,w=c.length;++h<w;){var b=c[h],R=b.size;switch(b.type){case"drop":o+=R;break;case"dropRight":u-=R;break;case"take":u=jt(u,o+R);break;case"takeRight":o=Ot(o,u-R);break}}return{start:o,end:u}}function JT(o){var u=o.match(o_);return u?u[1].split(l_):[]}function E0(o,u,c){u=bi(u,o);for(var h=-1,w=u.length,b=!1;++h<w;){var R=Ar(u[h]);if(!(b=o!=null&&c(o,R)))break;o=o[R]}return b||++h!=w?b:(w=o==null?0:o.length,!!w&&Ea(w)&&ei(R,w)&&(ke(o)||wo(o)))}function eI(o){var u=o.length,c=new o.constructor(u);return u&&typeof o[0]=="string"&&Ke.call(o,"index")&&(c.index=o.index,c.input=o.input),c}function C0(o){return typeof o.constructor=="function"&&!Su(o)?tl(Qs(o)):{}}function tI(o,u,c){var h=o.constructor;switch(u){case V:return $d(o);case Se:case Le:return new h(+o);case re:return zT(o,c);case pe:case Ue:case He:case Zt:case Gn:case bn:case qr:case Kr:case it:return o0(o,c);case yt:return new h;case Wn:case Tr:return new h(o);case kn:return $T(o);case Ht:return new h;case Gr:return BT(o)}}function nI(o,u){var c=u.length;if(!c)return o;var h=c-1;return u[h]=(c>1?"& ":"")+u[h],u=u.join(c>2?", ":" "),o.replace(i_,`{
/* [wrapped with `+u+`] */
`)}function rI(o){return ke(o)||wo(o)||!!(Ty&&o&&o[Ty])}function ei(o,u){var c=typeof o;return u=u??$,!!u&&(c=="number"||c!="symbol"&&g_.test(o))&&o>-1&&o%1==0&&o<u}function en(o,u,c){if(!ht(c))return!1;var h=typeof u;return(h=="number"?an(c)&&ei(u,c.length):h=="string"&&u in c)?mr(c[u],o):!1}function qd(o,u){if(ke(o))return!1;var c=typeof o;return c=="number"||c=="symbol"||c=="boolean"||o==null||In(o)?!0:e_.test(o)||!Jb.test(o)||u!=null&&o in Je(u)}function iI(o){var u=typeof o;return u=="string"||u=="number"||u=="symbol"||u=="boolean"?o!=="__proto__":o===null}function Kd(o){var u=ha(o),c=C[u];if(typeof c!="function"||!(u in Me.prototype))return!1;if(o===c)return!0;var h=Wd(c);return!!h&&o===h[0]}function oI(o){return!!ky&&ky in o}var lI=Ws?ti:fp;function Su(o){var u=o&&o.constructor,c=typeof u=="function"&&u.prototype||Zo;return o===c}function k0(o){return o===o&&!ht(o)}function b0(o,u){return function(c){return c==null?!1:c[o]===u&&(u!==n||o in Je(c))}}function uI(o){var u=xa(o,function(h){return c.size===d&&c.clear(),h}),c=u.cache;return u}function sI(o,u){var c=o[1],h=u[1],w=c|h,b=w<(A|y|F),R=h==F&&c==k||h==F&&c==z&&o[7].length<=u[8]||h==(F|z)&&u[7].length<=u[8]&&c==k;if(!(b||R))return o;h&A&&(o[2]=u[2],w|=c&A?0:x);var N=u[3];if(N){var L=o[3];o[3]=L?u0(L,N,u[4]):N,o[4]=L?Si(o[3],p):u[4]}return N=u[5],N&&(L=o[5],o[5]=L?s0(L,N,u[6]):N,o[6]=L?Si(o[5],p):u[6]),N=u[7],N&&(o[7]=N),h&F&&(o[8]=o[8]==null?u[8]:jt(o[8],u[8])),o[9]==null&&(o[9]=u[9]),o[0]=u[0],o[1]=w,o}function aI(o){var u=[];if(o!=null)for(var c in Je(o))u.push(c);return u}function cI(o){return Gs.call(o)}function _0(o,u,c){return u=Ot(u===n?o.length-1:u,0),function(){for(var h=arguments,w=-1,b=Ot(h.length-u,0),R=j(b);++w<b;)R[w]=h[u+w];w=-1;for(var N=j(u+1);++w<u;)N[w]=h[w];return N[u]=c(R),_n(o,this,N)}}function O0(o,u){return u.length<2?o:mo(o,Zn(u,0,-1))}function fI(o,u){for(var c=o.length,h=jt(u.length,c),w=sn(o);h--;){var b=u[h];o[h]=ei(b,c)?w[b]:n}return o}function Qd(o,u){if(!(u==="constructor"&&typeof o[u]=="function")&&u!="__proto__")return o[u]}var T0=R0(Xy),Eu=_O||function(o,u){return Lt.setTimeout(o,u)},Yd=R0(NT);function I0(o,u,c){var h=u+"";return Yd(o,nI(h,dI(JT(h),c)))}function R0(o){var u=0,c=0;return function(){var h=RO(),w=U-(h-c);if(c=h,w>0){if(++u>=Q)return arguments[0]}else u=0;return o.apply(n,arguments)}}function ma(o,u){var c=-1,h=o.length,w=h-1;for(u=u===n?h:u;++c<u;){var b=Ad(c,w),R=o[b];o[b]=o[c],o[c]=R}return o.length=u,o}var P0=uI(function(o){var u=[];return o.charCodeAt(0)===46&&u.push(""),o.replace(t_,function(c,h,w,b){u.push(w?b.replace(a_,"$1"):h||c)}),u});function Ar(o){if(typeof o=="string"||In(o))return o;var u=o+"";return u=="0"&&1/o==-oe?"-0":u}function yo(o){if(o!=null){try{return Vs.call(o)}catch{}try{return o+""}catch{}}return""}function dI(o,u){return Kn(de,function(c){var h="_."+c[0];u&c[1]&&!Bs(o,h)&&o.push(h)}),o.sort()}function A0(o){if(o instanceof Me)return o.clone();var u=new Yn(o.__wrapped__,o.__chain__);return u.__actions__=sn(o.__actions__),u.__index__=o.__index__,u.__values__=o.__values__,u}function pI(o,u,c){(c?en(o,u,c):u===n)?u=1:u=Ot(Oe(u),0);var h=o==null?0:o.length;if(!h||u<1)return[];for(var w=0,b=0,R=j(Zs(h/u));w<h;)R[b++]=Zn(o,w,w+=u);return R}function hI(o){for(var u=-1,c=o==null?0:o.length,h=0,w=[];++u<c;){var b=o[u];b&&(w[h++]=b)}return w}function gI(){var o=arguments.length;if(!o)return[];for(var u=j(o-1),c=arguments[0],h=o;h--;)u[h-1]=arguments[h];return xi(ke(c)?sn(c):[c],Ft(u,1))}var mI=Pe(function(o,u){return St(o)?mu(o,Ft(u,1,St,!0)):[]}),vI=Pe(function(o,u){var c=Jn(u);return St(c)&&(c=n),St(o)?mu(o,Ft(u,1,St,!0),ge(c,2)):[]}),yI=Pe(function(o,u){var c=Jn(u);return St(c)&&(c=n),St(o)?mu(o,Ft(u,1,St,!0),n,c):[]});function wI(o,u,c){var h=o==null?0:o.length;return h?(u=c||u===n?1:Oe(u),Zn(o,u<0?0:u,h)):[]}function xI(o,u,c){var h=o==null?0:o.length;return h?(u=c||u===n?1:Oe(u),u=h-u,Zn(o,0,u<0?0:u)):[]}function SI(o,u){return o&&o.length?sa(o,ge(u,3),!0,!0):[]}function EI(o,u){return o&&o.length?sa(o,ge(u,3),!0):[]}function CI(o,u,c,h){var w=o==null?0:o.length;return w?(c&&typeof c!="number"&&en(o,u,c)&&(c=0,h=w),gT(o,u,c,h)):[]}function D0(o,u,c){var h=o==null?0:o.length;if(!h)return-1;var w=c==null?0:Oe(c);return w<0&&(w=Ot(h+w,0)),Us(o,ge(u,3),w)}function N0(o,u,c){var h=o==null?0:o.length;if(!h)return-1;var w=h-1;return c!==n&&(w=Oe(c),w=c<0?Ot(h+w,0):jt(w,h-1)),Us(o,ge(u,3),w,!0)}function M0(o){var u=o==null?0:o.length;return u?Ft(o,1):[]}function kI(o){var u=o==null?0:o.length;return u?Ft(o,oe):[]}function bI(o,u){var c=o==null?0:o.length;return c?(u=u===n?1:Oe(u),Ft(o,u)):[]}function _I(o){for(var u=-1,c=o==null?0:o.length,h={};++u<c;){var w=o[u];h[w[0]]=w[1]}return h}function L0(o){return o&&o.length?o[0]:n}function OI(o,u,c){var h=o==null?0:o.length;if(!h)return-1;var w=c==null?0:Oe(c);return w<0&&(w=Ot(h+w,0)),Ko(o,u,w)}function TI(o){var u=o==null?0:o.length;return u?Zn(o,0,-1):[]}var II=Pe(function(o){var u=ct(o,Fd);return u.length&&u[0]===o[0]?Od(u):[]}),RI=Pe(function(o){var u=Jn(o),c=ct(o,Fd);return u===Jn(c)?u=n:c.pop(),c.length&&c[0]===o[0]?Od(c,ge(u,2)):[]}),PI=Pe(function(o){var u=Jn(o),c=ct(o,Fd);return u=typeof u=="function"?u:n,u&&c.pop(),c.length&&c[0]===o[0]?Od(c,n,u):[]});function AI(o,u){return o==null?"":TO.call(o,u)}function Jn(o){var u=o==null?0:o.length;return u?o[u-1]:n}function DI(o,u,c){var h=o==null?0:o.length;if(!h)return-1;var w=h;return c!==n&&(w=Oe(c),w=w<0?Ot(h+w,0):jt(w,h-1)),u===u?dO(o,u,w):Us(o,my,w,!0)}function NI(o,u){return o&&o.length?qy(o,Oe(u)):n}var MI=Pe(F0);function F0(o,u){return o&&o.length&&u&&u.length?Pd(o,u):o}function LI(o,u,c){return o&&o.length&&u&&u.length?Pd(o,u,ge(c,2)):o}function FI(o,u,c){return o&&o.length&&u&&u.length?Pd(o,u,n,c):o}var zI=Jr(function(o,u){var c=o==null?0:o.length,h=Cd(o,u);return Yy(o,ct(u,function(w){return ei(w,c)?+w:w}).sort(l0)),h});function $I(o,u){var c=[];if(!(o&&o.length))return c;var h=-1,w=[],b=o.length;for(u=ge(u,3);++h<b;){var R=o[h];u(R,h,o)&&(c.push(R),w.push(h))}return Yy(o,w),c}function Xd(o){return o==null?o:AO.call(o)}function BI(o,u,c){var h=o==null?0:o.length;return h?(c&&typeof c!="number"&&en(o,u,c)?(u=0,c=h):(u=u==null?0:Oe(u),c=c===n?h:Oe(c)),Zn(o,u,c)):[]}function UI(o,u){return ua(o,u)}function HI(o,u,c){return Nd(o,u,ge(c,2))}function jI(o,u){var c=o==null?0:o.length;if(c){var h=ua(o,u);if(h<c&&mr(o[h],u))return h}return-1}function WI(o,u){return ua(o,u,!0)}function VI(o,u,c){return Nd(o,u,ge(c,2),!0)}function GI(o,u){var c=o==null?0:o.length;if(c){var h=ua(o,u,!0)-1;if(mr(o[h],u))return h}return-1}function qI(o){return o&&o.length?Zy(o):[]}function KI(o,u){return o&&o.length?Zy(o,ge(u,2)):[]}function QI(o){var u=o==null?0:o.length;return u?Zn(o,1,u):[]}function YI(o,u,c){return o&&o.length?(u=c||u===n?1:Oe(u),Zn(o,0,u<0?0:u)):[]}function XI(o,u,c){var h=o==null?0:o.length;return h?(u=c||u===n?1:Oe(u),u=h-u,Zn(o,u<0?0:u,h)):[]}function ZI(o,u){return o&&o.length?sa(o,ge(u,3),!1,!0):[]}function JI(o,u){return o&&o.length?sa(o,ge(u,3)):[]}var eR=Pe(function(o){return ki(Ft(o,1,St,!0))}),tR=Pe(function(o){var u=Jn(o);return St(u)&&(u=n),ki(Ft(o,1,St,!0),ge(u,2))}),nR=Pe(function(o){var u=Jn(o);return u=typeof u=="function"?u:n,ki(Ft(o,1,St,!0),n,u)});function rR(o){return o&&o.length?ki(o):[]}function iR(o,u){return o&&o.length?ki(o,ge(u,2)):[]}function oR(o,u){return u=typeof u=="function"?u:n,o&&o.length?ki(o,n,u):[]}function Zd(o){if(!(o&&o.length))return[];var u=0;return o=wi(o,function(c){if(St(c))return u=Ot(c.length,u),!0}),gd(u,function(c){return ct(o,dd(c))})}function z0(o,u){if(!(o&&o.length))return[];var c=Zd(o);return u==null?c:ct(c,function(h){return _n(u,n,h)})}var lR=Pe(function(o,u){return St(o)?mu(o,u):[]}),uR=Pe(function(o){return Ld(wi(o,St))}),sR=Pe(function(o){var u=Jn(o);return St(u)&&(u=n),Ld(wi(o,St),ge(u,2))}),aR=Pe(function(o){var u=Jn(o);return u=typeof u=="function"?u:n,Ld(wi(o,St),n,u)}),cR=Pe(Zd);function fR(o,u){return n0(o||[],u||[],gu)}function dR(o,u){return n0(o||[],u||[],wu)}var pR=Pe(function(o){var u=o.length,c=u>1?o[u-1]:n;return c=typeof c=="function"?(o.pop(),c):n,z0(o,c)});function $0(o){var u=C(o);return u.__chain__=!0,u}function hR(o,u){return u(o),o}function va(o,u){return u(o)}var gR=Jr(function(o){var u=o.length,c=u?o[0]:0,h=this.__wrapped__,w=function(b){return Cd(b,o)};return u>1||this.__actions__.length||!(h instanceof Me)||!ei(c)?this.thru(w):(h=h.slice(c,+c+(u?1:0)),h.__actions__.push({func:va,args:[w],thisArg:n}),new Yn(h,this.__chain__).thru(function(b){return u&&!b.length&&b.push(n),b}))});function mR(){return $0(this)}function vR(){return new Yn(this.value(),this.__chain__)}function yR(){this.__values__===n&&(this.__values__=J0(this.value()));var o=this.__index__>=this.__values__.length,u=o?n:this.__values__[this.__index__++];return{done:o,value:u}}function wR(){return this}function xR(o){for(var u,c=this;c instanceof na;){var h=A0(c);h.__index__=0,h.__values__=n,u?w.__wrapped__=h:u=h;var w=h;c=c.__wrapped__}return w.__wrapped__=o,u}function SR(){var o=this.__wrapped__;if(o instanceof Me){var u=o;return this.__actions__.length&&(u=new Me(this)),u=u.reverse(),u.__actions__.push({func:va,args:[Xd],thisArg:n}),new Yn(u,this.__chain__)}return this.thru(Xd)}function ER(){return t0(this.__wrapped__,this.__actions__)}var CR=aa(function(o,u,c){Ke.call(o,c)?++o[c]:Xr(o,c,1)});function kR(o,u,c){var h=ke(o)?hy:hT;return c&&en(o,u,c)&&(u=n),h(o,ge(u,3))}function bR(o,u){var c=ke(o)?wi:zy;return c(o,ge(u,3))}var _R=d0(D0),OR=d0(N0);function TR(o,u){return Ft(ya(o,u),1)}function IR(o,u){return Ft(ya(o,u),oe)}function RR(o,u,c){return c=c===n?1:Oe(c),Ft(ya(o,u),c)}function B0(o,u){var c=ke(o)?Kn:Ci;return c(o,ge(u,3))}function U0(o,u){var c=ke(o)?Q_:Fy;return c(o,ge(u,3))}var PR=aa(function(o,u,c){Ke.call(o,c)?o[c].push(u):Xr(o,c,[u])});function AR(o,u,c,h){o=an(o)?o:ll(o),c=c&&!h?Oe(c):0;var w=o.length;return c<0&&(c=Ot(w+c,0)),Ca(o)?c<=w&&o.indexOf(u,c)>-1:!!w&&Ko(o,u,c)>-1}var DR=Pe(function(o,u,c){var h=-1,w=typeof u=="function",b=an(o)?j(o.length):[];return Ci(o,function(R){b[++h]=w?_n(u,R,c):vu(R,u,c)}),b}),NR=aa(function(o,u,c){Xr(o,c,u)});function ya(o,u){var c=ke(o)?ct:Wy;return c(o,ge(u,3))}function MR(o,u,c,h){return o==null?[]:(ke(u)||(u=u==null?[]:[u]),c=h?n:c,ke(c)||(c=c==null?[]:[c]),Ky(o,u,c))}var LR=aa(function(o,u,c){o[c?0:1].push(u)},function(){return[[],[]]});function FR(o,u,c){var h=ke(o)?cd:yy,w=arguments.length<3;return h(o,ge(u,4),c,w,Ci)}function zR(o,u,c){var h=ke(o)?Y_:yy,w=arguments.length<3;return h(o,ge(u,4),c,w,Fy)}function $R(o,u){var c=ke(o)?wi:zy;return c(o,Sa(ge(u,3)))}function BR(o){var u=ke(o)?Dy:AT;return u(o)}function UR(o,u,c){(c?en(o,u,c):u===n)?u=1:u=Oe(u);var h=ke(o)?aT:DT;return h(o,u)}function HR(o){var u=ke(o)?cT:MT;return u(o)}function jR(o){if(o==null)return 0;if(an(o))return Ca(o)?Yo(o):o.length;var u=Wt(o);return u==yt||u==Ht?o.size:Id(o).length}function WR(o,u,c){var h=ke(o)?fd:LT;return c&&en(o,u,c)&&(u=n),h(o,ge(u,3))}var VR=Pe(function(o,u){if(o==null)return[];var c=u.length;return c>1&&en(o,u[0],u[1])?u=[]:c>2&&en(u[0],u[1],u[2])&&(u=[u[0]]),Ky(o,Ft(u,1),[])}),wa=bO||function(){return Lt.Date.now()};function GR(o,u){if(typeof u!="function")throw new Qn(s);return o=Oe(o),function(){if(--o<1)return u.apply(this,arguments)}}function H0(o,u,c){return u=c?n:u,u=o&&u==null?o.length:u,Zr(o,F,n,n,n,n,u)}function j0(o,u){var c;if(typeof u!="function")throw new Qn(s);return o=Oe(o),function(){return--o>0&&(c=u.apply(this,arguments)),o<=1&&(u=n),c}}var Jd=Pe(function(o,u,c){var h=A;if(c.length){var w=Si(c,il(Jd));h|=D}return Zr(o,h,u,c,w)}),W0=Pe(function(o,u,c){var h=A|y;if(c.length){var w=Si(c,il(W0));h|=D}return Zr(u,h,o,c,w)});function V0(o,u,c){u=c?n:u;var h=Zr(o,k,n,n,n,n,n,u);return h.placeholder=V0.placeholder,h}function G0(o,u,c){u=c?n:u;var h=Zr(o,P,n,n,n,n,n,u);return h.placeholder=G0.placeholder,h}function q0(o,u,c){var h,w,b,R,N,L,Y=0,X=!1,J=!1,ue=!0;if(typeof o!="function")throw new Qn(s);u=er(u)||0,ht(c)&&(X=!!c.leading,J="maxWait"in c,b=J?Ot(er(c.maxWait)||0,u):b,ue="trailing"in c?!!c.trailing:ue);function fe(Et){var vr=h,ri=w;return h=w=n,Y=Et,R=o.apply(ri,vr),R}function ve(Et){return Y=Et,N=Eu(De,u),X?fe(Et):R}function Ie(Et){var vr=Et-L,ri=Et-Y,d1=u-vr;return J?jt(d1,b-ri):d1}function ye(Et){var vr=Et-L,ri=Et-Y;return L===n||vr>=u||vr<0||J&&ri>=b}function De(){var Et=wa();if(ye(Et))return ze(Et);N=Eu(De,Ie(Et))}function ze(Et){return N=n,ue&&h?fe(Et):(h=w=n,R)}function Rn(){N!==n&&r0(N),Y=0,h=L=w=N=n}function tn(){return N===n?R:ze(wa())}function Pn(){var Et=wa(),vr=ye(Et);if(h=arguments,w=this,L=Et,vr){if(N===n)return ve(L);if(J)return r0(N),N=Eu(De,u),fe(L)}return N===n&&(N=Eu(De,u)),R}return Pn.cancel=Rn,Pn.flush=tn,Pn}var qR=Pe(function(o,u){return Ly(o,1,u)}),KR=Pe(function(o,u,c){return Ly(o,er(u)||0,c)});function QR(o){return Zr(o,G)}function xa(o,u){if(typeof o!="function"||u!=null&&typeof u!="function")throw new Qn(s);var c=function(){var h=arguments,w=u?u.apply(this,h):h[0],b=c.cache;if(b.has(w))return b.get(w);var R=o.apply(this,h);return c.cache=b.set(w,R)||b,R};return c.cache=new(xa.Cache||Yr),c}xa.Cache=Yr;function Sa(o){if(typeof o!="function")throw new Qn(s);return function(){var u=arguments;switch(u.length){case 0:return!o.call(this);case 1:return!o.call(this,u[0]);case 2:return!o.call(this,u[0],u[1]);case 3:return!o.call(this,u[0],u[1],u[2])}return!o.apply(this,u)}}function YR(o){return j0(2,o)}var XR=FT(function(o,u){u=u.length==1&&ke(u[0])?ct(u[0],On(ge())):ct(Ft(u,1),On(ge()));var c=u.length;return Pe(function(h){for(var w=-1,b=jt(h.length,c);++w<b;)h[w]=u[w].call(this,h[w]);return _n(o,this,h)})}),ep=Pe(function(o,u){var c=Si(u,il(ep));return Zr(o,D,n,u,c)}),K0=Pe(function(o,u){var c=Si(u,il(K0));return Zr(o,T,n,u,c)}),ZR=Jr(function(o,u){return Zr(o,z,n,n,n,u)});function JR(o,u){if(typeof o!="function")throw new Qn(s);return u=u===n?u:Oe(u),Pe(o,u)}function eP(o,u){if(typeof o!="function")throw new Qn(s);return u=u==null?0:Ot(Oe(u),0),Pe(function(c){var h=c[u],w=_i(c,0,u);return h&&xi(w,h),_n(o,this,w)})}function tP(o,u,c){var h=!0,w=!0;if(typeof o!="function")throw new Qn(s);return ht(c)&&(h="leading"in c?!!c.leading:h,w="trailing"in c?!!c.trailing:w),q0(o,u,{leading:h,maxWait:u,trailing:w})}function nP(o){return H0(o,1)}function rP(o,u){return ep(zd(u),o)}function iP(){if(!arguments.length)return[];var o=arguments[0];return ke(o)?o:[o]}function oP(o){return Xn(o,m)}function lP(o,u){return u=typeof u=="function"?u:n,Xn(o,m,u)}function uP(o){return Xn(o,g|m)}function sP(o,u){return u=typeof u=="function"?u:n,Xn(o,g|m,u)}function aP(o,u){return u==null||My(o,u,Rt(u))}function mr(o,u){return o===u||o!==o&&u!==u}var cP=pa(_d),fP=pa(function(o,u){return o>=u}),wo=Uy(function(){return arguments}())?Uy:function(o){return wt(o)&&Ke.call(o,"callee")&&!Oy.call(o,"callee")},ke=j.isArray,dP=sy?On(sy):xT;function an(o){return o!=null&&Ea(o.length)&&!ti(o)}function St(o){return wt(o)&&an(o)}function pP(o){return o===!0||o===!1||wt(o)&&Jt(o)==Se}var Oi=OO||fp,hP=ay?On(ay):ST;function gP(o){return wt(o)&&o.nodeType===1&&!Cu(o)}function mP(o){if(o==null)return!0;if(an(o)&&(ke(o)||typeof o=="string"||typeof o.splice=="function"||Oi(o)||ol(o)||wo(o)))return!o.length;var u=Wt(o);if(u==yt||u==Ht)return!o.size;if(Su(o))return!Id(o).length;for(var c in o)if(Ke.call(o,c))return!1;return!0}function vP(o,u){return yu(o,u)}function yP(o,u,c){c=typeof c=="function"?c:n;var h=c?c(o,u):n;return h===n?yu(o,u,n,c):!!h}function tp(o){if(!wt(o))return!1;var u=Jt(o);return u==Be||u==Te||typeof o.message=="string"&&typeof o.name=="string"&&!Cu(o)}function wP(o){return typeof o=="number"&&Iy(o)}function ti(o){if(!ht(o))return!1;var u=Jt(o);return u==Fe||u==qe||u==Ae||u==Cn}function Q0(o){return typeof o=="number"&&o==Oe(o)}function Ea(o){return typeof o=="number"&&o>-1&&o%1==0&&o<=$}function ht(o){var u=typeof o;return o!=null&&(u=="object"||u=="function")}function wt(o){return o!=null&&typeof o=="object"}var Y0=cy?On(cy):CT;function xP(o,u){return o===u||Td(o,u,Vd(u))}function SP(o,u,c){return c=typeof c=="function"?c:n,Td(o,u,Vd(u),c)}function EP(o){return X0(o)&&o!=+o}function CP(o){if(lI(o))throw new Ee(l);return Hy(o)}function kP(o){return o===null}function bP(o){return o==null}function X0(o){return typeof o=="number"||wt(o)&&Jt(o)==Wn}function Cu(o){if(!wt(o)||Jt(o)!=at)return!1;var u=Qs(o);if(u===null)return!0;var c=Ke.call(u,"constructor")&&u.constructor;return typeof c=="function"&&c instanceof c&&Vs.call(c)==SO}var np=fy?On(fy):kT;function _P(o){return Q0(o)&&o>=-$&&o<=$}var Z0=dy?On(dy):bT;function Ca(o){return typeof o=="string"||!ke(o)&&wt(o)&&Jt(o)==Tr}function In(o){return typeof o=="symbol"||wt(o)&&Jt(o)==Gr}var ol=py?On(py):_T;function OP(o){return o===n}function TP(o){return wt(o)&&Wt(o)==Ir}function IP(o){return wt(o)&&Jt(o)==Vn}var RP=pa(Rd),PP=pa(function(o,u){return o<=u});function J0(o){if(!o)return[];if(an(o))return Ca(o)?hr(o):sn(o);if(cu&&o[cu])return aO(o[cu]());var u=Wt(o),c=u==yt?vd:u==Ht?Hs:ll;return c(o)}function ni(o){if(!o)return o===0?o:0;if(o=er(o),o===oe||o===-oe){var u=o<0?-1:1;return u*Z}return o===o?o:0}function Oe(o){var u=ni(o),c=u%1;return u===u?c?u-c:u:0}function e1(o){return o?go(Oe(o),0,ne):0}function er(o){if(typeof o=="number")return o;if(In(o))return _;if(ht(o)){var u=typeof o.valueOf=="function"?o.valueOf():o;o=ht(u)?u+"":u}if(typeof o!="string")return o===0?o:+o;o=wy(o);var c=d_.test(o);return c||h_.test(o)?G_(o.slice(2),c?2:8):f_.test(o)?_:+o}function t1(o){return Pr(o,cn(o))}function AP(o){return o?go(Oe(o),-$,$):o===0?o:0}function Ge(o){return o==null?"":Tn(o)}var DP=nl(function(o,u){if(Su(u)||an(u)){Pr(u,Rt(u),o);return}for(var c in u)Ke.call(u,c)&&gu(o,c,u[c])}),n1=nl(function(o,u){Pr(u,cn(u),o)}),ka=nl(function(o,u,c,h){Pr(u,cn(u),o,h)}),NP=nl(function(o,u,c,h){Pr(u,Rt(u),o,h)}),MP=Jr(Cd);function LP(o,u){var c=tl(o);return u==null?c:Ny(c,u)}var FP=Pe(function(o,u){o=Je(o);var c=-1,h=u.length,w=h>2?u[2]:n;for(w&&en(u[0],u[1],w)&&(h=1);++c<h;)for(var b=u[c],R=cn(b),N=-1,L=R.length;++N<L;){var Y=R[N],X=o[Y];(X===n||mr(X,Zo[Y])&&!Ke.call(o,Y))&&(o[Y]=b[Y])}return o}),zP=Pe(function(o){return o.push(n,w0),_n(r1,n,o)});function $P(o,u){return gy(o,ge(u,3),Rr)}function BP(o,u){return gy(o,ge(u,3),bd)}function UP(o,u){return o==null?o:kd(o,ge(u,3),cn)}function HP(o,u){return o==null?o:$y(o,ge(u,3),cn)}function jP(o,u){return o&&Rr(o,ge(u,3))}function WP(o,u){return o&&bd(o,ge(u,3))}function VP(o){return o==null?[]:oa(o,Rt(o))}function GP(o){return o==null?[]:oa(o,cn(o))}function rp(o,u,c){var h=o==null?n:mo(o,u);return h===n?c:h}function qP(o,u){return o!=null&&E0(o,u,mT)}function ip(o,u){return o!=null&&E0(o,u,vT)}var KP=h0(function(o,u,c){u!=null&&typeof u.toString!="function"&&(u=Gs.call(u)),o[u]=c},lp(fn)),QP=h0(function(o,u,c){u!=null&&typeof u.toString!="function"&&(u=Gs.call(u)),Ke.call(o,u)?o[u].push(c):o[u]=[c]},ge),YP=Pe(vu);function Rt(o){return an(o)?Ay(o):Id(o)}function cn(o){return an(o)?Ay(o,!0):OT(o)}function XP(o,u){var c={};return u=ge(u,3),Rr(o,function(h,w,b){Xr(c,u(h,w,b),h)}),c}function ZP(o,u){var c={};return u=ge(u,3),Rr(o,function(h,w,b){Xr(c,w,u(h,w,b))}),c}var JP=nl(function(o,u,c){la(o,u,c)}),r1=nl(function(o,u,c,h){la(o,u,c,h)}),e2=Jr(function(o,u){var c={};if(o==null)return c;var h=!1;u=ct(u,function(b){return b=bi(b,o),h||(h=b.length>1),b}),Pr(o,jd(o),c),h&&(c=Xn(c,g|v|m,KT));for(var w=u.length;w--;)Md(c,u[w]);return c});function t2(o,u){return i1(o,Sa(ge(u)))}var n2=Jr(function(o,u){return o==null?{}:IT(o,u)});function i1(o,u){if(o==null)return{};var c=ct(jd(o),function(h){return[h]});return u=ge(u),Qy(o,c,function(h,w){return u(h,w[0])})}function r2(o,u,c){u=bi(u,o);var h=-1,w=u.length;for(w||(w=1,o=n);++h<w;){var b=o==null?n:o[Ar(u[h])];b===n&&(h=w,b=c),o=ti(b)?b.call(o):b}return o}function i2(o,u,c){return o==null?o:wu(o,u,c)}function o2(o,u,c,h){return h=typeof h=="function"?h:n,o==null?o:wu(o,u,c,h)}var o1=v0(Rt),l1=v0(cn);function l2(o,u,c){var h=ke(o),w=h||Oi(o)||ol(o);if(u=ge(u,4),c==null){var b=o&&o.constructor;w?c=h?new b:[]:ht(o)?c=ti(b)?tl(Qs(o)):{}:c={}}return(w?Kn:Rr)(o,function(R,N,L){return u(c,R,N,L)}),c}function u2(o,u){return o==null?!0:Md(o,u)}function s2(o,u,c){return o==null?o:e0(o,u,zd(c))}function a2(o,u,c,h){return h=typeof h=="function"?h:n,o==null?o:e0(o,u,zd(c),h)}function ll(o){return o==null?[]:md(o,Rt(o))}function c2(o){return o==null?[]:md(o,cn(o))}function f2(o,u,c){return c===n&&(c=u,u=n),c!==n&&(c=er(c),c=c===c?c:0),u!==n&&(u=er(u),u=u===u?u:0),go(er(o),u,c)}function d2(o,u,c){return u=ni(u),c===n?(c=u,u=0):c=ni(c),o=er(o),yT(o,u,c)}function p2(o,u,c){if(c&&typeof c!="boolean"&&en(o,u,c)&&(u=c=n),c===n&&(typeof u=="boolean"?(c=u,u=n):typeof o=="boolean"&&(c=o,o=n)),o===n&&u===n?(o=0,u=1):(o=ni(o),u===n?(u=o,o=0):u=ni(u)),o>u){var h=o;o=u,u=h}if(c||o%1||u%1){var w=Ry();return jt(o+w*(u-o+V_("1e-"+((w+"").length-1))),u)}return Ad(o,u)}var h2=rl(function(o,u,c){return u=u.toLowerCase(),o+(c?u1(u):u)});function u1(o){return op(Ge(o).toLowerCase())}function s1(o){return o=Ge(o),o&&o.replace(m_,iO).replace(M_,"")}function g2(o,u,c){o=Ge(o),u=Tn(u);var h=o.length;c=c===n?h:go(Oe(c),0,h);var w=c;return c-=u.length,c>=0&&o.slice(c,w)==u}function m2(o){return o=Ge(o),o&&Yb.test(o)?o.replace($v,oO):o}function v2(o){return o=Ge(o),o&&n_.test(o)?o.replace(ed,"\\$&"):o}var y2=rl(function(o,u,c){return o+(c?"-":"")+u.toLowerCase()}),w2=rl(function(o,u,c){return o+(c?" ":"")+u.toLowerCase()}),x2=f0("toLowerCase");function S2(o,u,c){o=Ge(o),u=Oe(u);var h=u?Yo(o):0;if(!u||h>=u)return o;var w=(u-h)/2;return da(Js(w),c)+o+da(Zs(w),c)}function E2(o,u,c){o=Ge(o),u=Oe(u);var h=u?Yo(o):0;return u&&h<u?o+da(u-h,c):o}function C2(o,u,c){o=Ge(o),u=Oe(u);var h=u?Yo(o):0;return u&&h<u?da(u-h,c)+o:o}function k2(o,u,c){return c||u==null?u=0:u&&(u=+u),PO(Ge(o).replace(td,""),u||0)}function b2(o,u,c){return(c?en(o,u,c):u===n)?u=1:u=Oe(u),Dd(Ge(o),u)}function _2(){var o=arguments,u=Ge(o[0]);return o.length<3?u:u.replace(o[1],o[2])}var O2=rl(function(o,u,c){return o+(c?"_":"")+u.toLowerCase()});function T2(o,u,c){return c&&typeof c!="number"&&en(o,u,c)&&(u=c=n),c=c===n?ne:c>>>0,c?(o=Ge(o),o&&(typeof u=="string"||u!=null&&!np(u))&&(u=Tn(u),!u&&Qo(o))?_i(hr(o),0,c):o.split(u,c)):[]}var I2=rl(function(o,u,c){return o+(c?" ":"")+op(u)});function R2(o,u,c){return o=Ge(o),c=c==null?0:go(Oe(c),0,o.length),u=Tn(u),o.slice(c,c+u.length)==u}function P2(o,u,c){var h=C.templateSettings;c&&en(o,u,c)&&(u=n),o=Ge(o),u=ka({},u,h,y0);var w=ka({},u.imports,h.imports,y0),b=Rt(w),R=md(w,b),N,L,Y=0,X=u.interpolate||Fs,J="__p += '",ue=yd((u.escape||Fs).source+"|"+X.source+"|"+(X===Bv?c_:Fs).source+"|"+(u.evaluate||Fs).source+"|$","g"),fe="//# sourceURL="+(Ke.call(u,"sourceURL")?(u.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++B_+"]")+`
`;o.replace(ue,function(ye,De,ze,Rn,tn,Pn){return ze||(ze=Rn),J+=o.slice(Y,Pn).replace(v_,lO),De&&(N=!0,J+=`' +
__e(`+De+`) +
'`),tn&&(L=!0,J+=`';
`+tn+`;
__p += '`),ze&&(J+=`' +
((__t = (`+ze+`)) == null ? '' : __t) +
'`),Y=Pn+ye.length,ye}),J+=`';
`;var ve=Ke.call(u,"variable")&&u.variable;if(!ve)J=`with (obj) {
`+J+`
}
`;else if(s_.test(ve))throw new Ee(a);J=(L?J.replace(su,""):J).replace(pr,"$1").replace(Kb,"$1;"),J="function("+(ve||"obj")+`) {
`+(ve?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(N?", __e = _.escape":"")+(L?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+J+`return __p
}`;var Ie=c1(function(){return je(b,fe+"return "+J).apply(n,R)});if(Ie.source=J,tp(Ie))throw Ie;return Ie}function A2(o){return Ge(o).toLowerCase()}function D2(o){return Ge(o).toUpperCase()}function N2(o,u,c){if(o=Ge(o),o&&(c||u===n))return wy(o);if(!o||!(u=Tn(u)))return o;var h=hr(o),w=hr(u),b=xy(h,w),R=Sy(h,w)+1;return _i(h,b,R).join("")}function M2(o,u,c){if(o=Ge(o),o&&(c||u===n))return o.slice(0,Cy(o)+1);if(!o||!(u=Tn(u)))return o;var h=hr(o),w=Sy(h,hr(u))+1;return _i(h,0,w).join("")}function L2(o,u,c){if(o=Ge(o),o&&(c||u===n))return o.replace(td,"");if(!o||!(u=Tn(u)))return o;var h=hr(o),w=xy(h,hr(u));return _i(h,w).join("")}function F2(o,u){var c=q,h=B;if(ht(u)){var w="separator"in u?u.separator:w;c="length"in u?Oe(u.length):c,h="omission"in u?Tn(u.omission):h}o=Ge(o);var b=o.length;if(Qo(o)){var R=hr(o);b=R.length}if(c>=b)return o;var N=c-Yo(h);if(N<1)return h;var L=R?_i(R,0,N).join(""):o.slice(0,N);if(w===n)return L+h;if(R&&(N+=L.length-N),np(w)){if(o.slice(N).search(w)){var Y,X=L;for(w.global||(w=yd(w.source,Ge(Uv.exec(w))+"g")),w.lastIndex=0;Y=w.exec(X);)var J=Y.index;L=L.slice(0,J===n?N:J)}}else if(o.indexOf(Tn(w),N)!=N){var ue=L.lastIndexOf(w);ue>-1&&(L=L.slice(0,ue))}return L+h}function z2(o){return o=Ge(o),o&&Qb.test(o)?o.replace(zv,pO):o}var $2=rl(function(o,u,c){return o+(c?" ":"")+u.toUpperCase()}),op=f0("toUpperCase");function a1(o,u,c){return o=Ge(o),u=c?n:u,u===n?sO(o)?mO(o):J_(o):o.match(u)||[]}var c1=Pe(function(o,u){try{return _n(o,n,u)}catch(c){return tp(c)?c:new Ee(c)}}),B2=Jr(function(o,u){return Kn(u,function(c){c=Ar(c),Xr(o,c,Jd(o[c],o))}),o});function U2(o){var u=o==null?0:o.length,c=ge();return o=u?ct(o,function(h){if(typeof h[1]!="function")throw new Qn(s);return[c(h[0]),h[1]]}):[],Pe(function(h){for(var w=-1;++w<u;){var b=o[w];if(_n(b[0],this,h))return _n(b[1],this,h)}})}function H2(o){return pT(Xn(o,g))}function lp(o){return function(){return o}}function j2(o,u){return o==null||o!==o?u:o}var W2=p0(),V2=p0(!0);function fn(o){return o}function up(o){return jy(typeof o=="function"?o:Xn(o,g))}function G2(o){return Vy(Xn(o,g))}function q2(o,u){return Gy(o,Xn(u,g))}var K2=Pe(function(o,u){return function(c){return vu(c,o,u)}}),Q2=Pe(function(o,u){return function(c){return vu(o,c,u)}});function sp(o,u,c){var h=Rt(u),w=oa(u,h);c==null&&!(ht(u)&&(w.length||!h.length))&&(c=u,u=o,o=this,w=oa(u,Rt(u)));var b=!(ht(c)&&"chain"in c)||!!c.chain,R=ti(o);return Kn(w,function(N){var L=u[N];o[N]=L,R&&(o.prototype[N]=function(){var Y=this.__chain__;if(b||Y){var X=o(this.__wrapped__),J=X.__actions__=sn(this.__actions__);return J.push({func:L,args:arguments,thisArg:o}),X.__chain__=Y,X}return L.apply(o,xi([this.value()],arguments))})}),o}function Y2(){return Lt._===this&&(Lt._=EO),this}function ap(){}function X2(o){return o=Oe(o),Pe(function(u){return qy(u,o)})}var Z2=Bd(ct),J2=Bd(hy),eA=Bd(fd);function f1(o){return qd(o)?dd(Ar(o)):RT(o)}function tA(o){return function(u){return o==null?n:mo(o,u)}}var nA=g0(),rA=g0(!0);function cp(){return[]}function fp(){return!1}function iA(){return{}}function oA(){return""}function lA(){return!0}function uA(o,u){if(o=Oe(o),o<1||o>$)return[];var c=ne,h=jt(o,ne);u=ge(u),o-=ne;for(var w=gd(h,u);++c<o;)u(c);return w}function sA(o){return ke(o)?ct(o,Ar):In(o)?[o]:sn(P0(Ge(o)))}function aA(o){var u=++xO;return Ge(o)+u}var cA=fa(function(o,u){return o+u},0),fA=Ud("ceil"),dA=fa(function(o,u){return o/u},1),pA=Ud("floor");function hA(o){return o&&o.length?ia(o,fn,_d):n}function gA(o,u){return o&&o.length?ia(o,ge(u,2),_d):n}function mA(o){return vy(o,fn)}function vA(o,u){return vy(o,ge(u,2))}function yA(o){return o&&o.length?ia(o,fn,Rd):n}function wA(o,u){return o&&o.length?ia(o,ge(u,2),Rd):n}var xA=fa(function(o,u){return o*u},1),SA=Ud("round"),EA=fa(function(o,u){return o-u},0);function CA(o){return o&&o.length?hd(o,fn):0}function kA(o,u){return o&&o.length?hd(o,ge(u,2)):0}return C.after=GR,C.ary=H0,C.assign=DP,C.assignIn=n1,C.assignInWith=ka,C.assignWith=NP,C.at=MP,C.before=j0,C.bind=Jd,C.bindAll=B2,C.bindKey=W0,C.castArray=iP,C.chain=$0,C.chunk=pI,C.compact=hI,C.concat=gI,C.cond=U2,C.conforms=H2,C.constant=lp,C.countBy=CR,C.create=LP,C.curry=V0,C.curryRight=G0,C.debounce=q0,C.defaults=FP,C.defaultsDeep=zP,C.defer=qR,C.delay=KR,C.difference=mI,C.differenceBy=vI,C.differenceWith=yI,C.drop=wI,C.dropRight=xI,C.dropRightWhile=SI,C.dropWhile=EI,C.fill=CI,C.filter=bR,C.flatMap=TR,C.flatMapDeep=IR,C.flatMapDepth=RR,C.flatten=M0,C.flattenDeep=kI,C.flattenDepth=bI,C.flip=QR,C.flow=W2,C.flowRight=V2,C.fromPairs=_I,C.functions=VP,C.functionsIn=GP,C.groupBy=PR,C.initial=TI,C.intersection=II,C.intersectionBy=RI,C.intersectionWith=PI,C.invert=KP,C.invertBy=QP,C.invokeMap=DR,C.iteratee=up,C.keyBy=NR,C.keys=Rt,C.keysIn=cn,C.map=ya,C.mapKeys=XP,C.mapValues=ZP,C.matches=G2,C.matchesProperty=q2,C.memoize=xa,C.merge=JP,C.mergeWith=r1,C.method=K2,C.methodOf=Q2,C.mixin=sp,C.negate=Sa,C.nthArg=X2,C.omit=e2,C.omitBy=t2,C.once=YR,C.orderBy=MR,C.over=Z2,C.overArgs=XR,C.overEvery=J2,C.overSome=eA,C.partial=ep,C.partialRight=K0,C.partition=LR,C.pick=n2,C.pickBy=i1,C.property=f1,C.propertyOf=tA,C.pull=MI,C.pullAll=F0,C.pullAllBy=LI,C.pullAllWith=FI,C.pullAt=zI,C.range=nA,C.rangeRight=rA,C.rearg=ZR,C.reject=$R,C.remove=$I,C.rest=JR,C.reverse=Xd,C.sampleSize=UR,C.set=i2,C.setWith=o2,C.shuffle=HR,C.slice=BI,C.sortBy=VR,C.sortedUniq=qI,C.sortedUniqBy=KI,C.split=T2,C.spread=eP,C.tail=QI,C.take=YI,C.takeRight=XI,C.takeRightWhile=ZI,C.takeWhile=JI,C.tap=hR,C.throttle=tP,C.thru=va,C.toArray=J0,C.toPairs=o1,C.toPairsIn=l1,C.toPath=sA,C.toPlainObject=t1,C.transform=l2,C.unary=nP,C.union=eR,C.unionBy=tR,C.unionWith=nR,C.uniq=rR,C.uniqBy=iR,C.uniqWith=oR,C.unset=u2,C.unzip=Zd,C.unzipWith=z0,C.update=s2,C.updateWith=a2,C.values=ll,C.valuesIn=c2,C.without=lR,C.words=a1,C.wrap=rP,C.xor=uR,C.xorBy=sR,C.xorWith=aR,C.zip=cR,C.zipObject=fR,C.zipObjectDeep=dR,C.zipWith=pR,C.entries=o1,C.entriesIn=l1,C.extend=n1,C.extendWith=ka,sp(C,C),C.add=cA,C.attempt=c1,C.camelCase=h2,C.capitalize=u1,C.ceil=fA,C.clamp=f2,C.clone=oP,C.cloneDeep=uP,C.cloneDeepWith=sP,C.cloneWith=lP,C.conformsTo=aP,C.deburr=s1,C.defaultTo=j2,C.divide=dA,C.endsWith=g2,C.eq=mr,C.escape=m2,C.escapeRegExp=v2,C.every=kR,C.find=_R,C.findIndex=D0,C.findKey=$P,C.findLast=OR,C.findLastIndex=N0,C.findLastKey=BP,C.floor=pA,C.forEach=B0,C.forEachRight=U0,C.forIn=UP,C.forInRight=HP,C.forOwn=jP,C.forOwnRight=WP,C.get=rp,C.gt=cP,C.gte=fP,C.has=qP,C.hasIn=ip,C.head=L0,C.identity=fn,C.includes=AR,C.indexOf=OI,C.inRange=d2,C.invoke=YP,C.isArguments=wo,C.isArray=ke,C.isArrayBuffer=dP,C.isArrayLike=an,C.isArrayLikeObject=St,C.isBoolean=pP,C.isBuffer=Oi,C.isDate=hP,C.isElement=gP,C.isEmpty=mP,C.isEqual=vP,C.isEqualWith=yP,C.isError=tp,C.isFinite=wP,C.isFunction=ti,C.isInteger=Q0,C.isLength=Ea,C.isMap=Y0,C.isMatch=xP,C.isMatchWith=SP,C.isNaN=EP,C.isNative=CP,C.isNil=bP,C.isNull=kP,C.isNumber=X0,C.isObject=ht,C.isObjectLike=wt,C.isPlainObject=Cu,C.isRegExp=np,C.isSafeInteger=_P,C.isSet=Z0,C.isString=Ca,C.isSymbol=In,C.isTypedArray=ol,C.isUndefined=OP,C.isWeakMap=TP,C.isWeakSet=IP,C.join=AI,C.kebabCase=y2,C.last=Jn,C.lastIndexOf=DI,C.lowerCase=w2,C.lowerFirst=x2,C.lt=RP,C.lte=PP,C.max=hA,C.maxBy=gA,C.mean=mA,C.meanBy=vA,C.min=yA,C.minBy=wA,C.stubArray=cp,C.stubFalse=fp,C.stubObject=iA,C.stubString=oA,C.stubTrue=lA,C.multiply=xA,C.nth=NI,C.noConflict=Y2,C.noop=ap,C.now=wa,C.pad=S2,C.padEnd=E2,C.padStart=C2,C.parseInt=k2,C.random=p2,C.reduce=FR,C.reduceRight=zR,C.repeat=b2,C.replace=_2,C.result=r2,C.round=SA,C.runInContext=M,C.sample=BR,C.size=jR,C.snakeCase=O2,C.some=WR,C.sortedIndex=UI,C.sortedIndexBy=HI,C.sortedIndexOf=jI,C.sortedLastIndex=WI,C.sortedLastIndexBy=VI,C.sortedLastIndexOf=GI,C.startCase=I2,C.startsWith=R2,C.subtract=EA,C.sum=CA,C.sumBy=kA,C.template=P2,C.times=uA,C.toFinite=ni,C.toInteger=Oe,C.toLength=e1,C.toLower=A2,C.toNumber=er,C.toSafeInteger=AP,C.toString=Ge,C.toUpper=D2,C.trim=N2,C.trimEnd=M2,C.trimStart=L2,C.truncate=F2,C.unescape=z2,C.uniqueId=aA,C.upperCase=$2,C.upperFirst=op,C.each=B0,C.eachRight=U0,C.first=L0,sp(C,function(){var o={};return Rr(C,function(u,c){Ke.call(C.prototype,c)||(o[c]=u)}),o}(),{chain:!1}),C.VERSION=r,Kn(["bind","bindKey","curry","curryRight","partial","partialRight"],function(o){C[o].placeholder=C}),Kn(["drop","take"],function(o,u){Me.prototype[o]=function(c){c=c===n?1:Ot(Oe(c),0);var h=this.__filtered__&&!u?new Me(this):this.clone();return h.__filtered__?h.__takeCount__=jt(c,h.__takeCount__):h.__views__.push({size:jt(c,ne),type:o+(h.__dir__<0?"Right":"")}),h},Me.prototype[o+"Right"]=function(c){return this.reverse()[o](c).reverse()}}),Kn(["filter","map","takeWhile"],function(o,u){var c=u+1,h=c==H||c==le;Me.prototype[o]=function(w){var b=this.clone();return b.__iteratees__.push({iteratee:ge(w,3),type:c}),b.__filtered__=b.__filtered__||h,b}}),Kn(["head","last"],function(o,u){var c="take"+(u?"Right":"");Me.prototype[o]=function(){return this[c](1).value()[0]}}),Kn(["initial","tail"],function(o,u){var c="drop"+(u?"":"Right");Me.prototype[o]=function(){return this.__filtered__?new Me(this):this[c](1)}}),Me.prototype.compact=function(){return this.filter(fn)},Me.prototype.find=function(o){return this.filter(o).head()},Me.prototype.findLast=function(o){return this.reverse().find(o)},Me.prototype.invokeMap=Pe(function(o,u){return typeof o=="function"?new Me(this):this.map(function(c){return vu(c,o,u)})}),Me.prototype.reject=function(o){return this.filter(Sa(ge(o)))},Me.prototype.slice=function(o,u){o=Oe(o);var c=this;return c.__filtered__&&(o>0||u<0)?new Me(c):(o<0?c=c.takeRight(-o):o&&(c=c.drop(o)),u!==n&&(u=Oe(u),c=u<0?c.dropRight(-u):c.take(u-o)),c)},Me.prototype.takeRightWhile=function(o){return this.reverse().takeWhile(o).reverse()},Me.prototype.toArray=function(){return this.take(ne)},Rr(Me.prototype,function(o,u){var c=/^(?:filter|find|map|reject)|While$/.test(u),h=/^(?:head|last)$/.test(u),w=C[h?"take"+(u=="last"?"Right":""):u],b=h||/^find/.test(u);w&&(C.prototype[u]=function(){var R=this.__wrapped__,N=h?[1]:arguments,L=R instanceof Me,Y=N[0],X=L||ke(R),J=function(De){var ze=w.apply(C,xi([De],N));return h&&ue?ze[0]:ze};X&&c&&typeof Y=="function"&&Y.length!=1&&(L=X=!1);var ue=this.__chain__,fe=!!this.__actions__.length,ve=b&&!ue,Ie=L&&!fe;if(!b&&X){R=Ie?R:new Me(this);var ye=o.apply(R,N);return ye.__actions__.push({func:va,args:[J],thisArg:n}),new Yn(ye,ue)}return ve&&Ie?o.apply(this,N):(ye=this.thru(J),ve?h?ye.value()[0]:ye.value():ye)})}),Kn(["pop","push","shift","sort","splice","unshift"],function(o){var u=js[o],c=/^(?:push|sort|unshift)$/.test(o)?"tap":"thru",h=/^(?:pop|shift)$/.test(o);C.prototype[o]=function(){var w=arguments;if(h&&!this.__chain__){var b=this.value();return u.apply(ke(b)?b:[],w)}return this[c](function(R){return u.apply(ke(R)?R:[],w)})}}),Rr(Me.prototype,function(o,u){var c=C[u];if(c){var h=c.name+"";Ke.call(el,h)||(el[h]=[]),el[h].push({name:u,func:c})}}),el[ca(n,y).name]=[{name:"wrapper",func:n}],Me.prototype.clone=zO,Me.prototype.reverse=$O,Me.prototype.value=BO,C.prototype.at=gR,C.prototype.chain=mR,C.prototype.commit=vR,C.prototype.next=yR,C.prototype.plant=xR,C.prototype.reverse=SR,C.prototype.toJSON=C.prototype.valueOf=C.prototype.value=ER,C.prototype.first=C.prototype.head,cu&&(C.prototype[cu]=wR),C},Xo=vO();co?((co.exports=Xo)._=Xo,ud._=Xo):Lt._=Xo}).call(ku)})(Wc,Wc.exports);var ak=Wc.exports;const dt={water:{name:"water",close:!1,label:"VODA",stack:!0,usable:!0,count:0},burger:{name:"burger",close:!1,label:"BURGR",stack:!1,usable:!1,count:0}};let ac="images";function F3(e){e&&e!==""&&(ac=e)}const ck=()=>!window.invokeNative,fk=()=>{},z3=window.GetParentResourceName?window.GetParentResourceName():"ox_inventory";async function ur(e,t){if(!ck())try{return await(await fetch(`https://${z3}/${e}`,{method:"post",headers:{"Content-Type":"application/json; charset=UTF-8"},body:JSON.stringify(t)})).json()}catch(n){throw Error(`Failed to fetch NUI callback ${e}! (${n})`)}}const Kw=(e,t)=>{if(t.type!=="shop"||!gn(e))return!0;if(e.count!==void 0&&e.count===0)return!1;if(e.grade===void 0||!t.groups)return!0;const n=Nn.getState().inventory.leftInventory;if(!n.groups)return!1;const r=Object.keys(t.groups);if(Array.isArray(e.grade)){for(let i=0;i<r.length;i++){const l=r[i];if(n.groups[l]!==void 0){const s=n.groups[l];for(let a=0;a<e.grade.length;a++){const f=e.grade[a];if(s===f)return!0}}}return!1}else{for(let i=0;i<r.length;i++){const l=r[i];if(n.groups[l]!==void 0&&n.groups[l]>=e.grade)return!0}return!1}},Qw=(e,t)=>{if(!gn(e)||t!=="crafting"||!e.ingredients)return!0;const n=Nn.getState().inventory.leftInventory;return Object.entries(e.ingredients).filter(l=>{const[s,a]=[l[0],l[1]],f=dt[s];return a>=1&&f&&f.count>=a?!1:!n.items.find(p=>{if(gn(p)&&p.name===s&&a<1)return p.metadata?.durability>=a*100})}).length===0},gn=(e,t=!1)=>e.name!==void 0&&e.weight!==void 0||t&&e.name!==void 0&&e.count!==void 0&&e.weight!==void 0,$3=(e,t)=>e.name===t.name&&ak.isEqual(e.metadata,t.metadata),B3=(e,t,n)=>t.stack?n.find(i=>i.name===e.name&&ak.isEqual(i.metadata,e.metadata))||n.find(i=>i.name===void 0):n.find(i=>i.name===void 0),Wf=(e,t,n)=>({sourceInventory:t===Yt.PLAYER?e.leftInventory:e.rightInventory,targetInventory:n?n===Yt.PLAYER?e.leftInventory:e.rightInventory:t===Yt.PLAYER?e.rightInventory:e.leftInventory}),ql=(e,t)=>{if(e?.durability===void 0)return;let n=e.durability;return n>100&&e.degrade&&(n=(e.durability-t)/(60*e.degrade)*100),n<0&&(n=0),n},U3=e=>e.reduce((t,n)=>gn(n)?t+n.weight:t,0),Yw=async e=>{const t=await ur("getItemData",e);if(t?.name)return dt[e]=t,t},Dl=e=>{const t=typeof e=="object";if(t){if(!e.name)return;const i=e.metadata;if(i?.imageurl)return`${i.imageurl}`;if(i?.image)return`${ac}/${i.image}.png`}const n=t?e.name:e,r=dt[n];return r?(r.image||(r.image=`${ac}/${n}.png`),r.image):`${ac}/${n}.png`},H3=(e,t)=>{const{leftInventory:n,rightInventory:r}=t.payload,i=Math.floor(Date.now()/1e3);n&&(e.leftInventory={...n,items:Array.from(Array(n.slots),(l,s)=>{const a=Object.values(n.items).find(f=>f?.slot===s+1)||{slot:s+1};return a.name&&(typeof dt[a.name]>"u"&&Yw(a.name),a.durability=ql(a.metadata,i)),a})}),r&&(e.rightInventory={...r,items:Array.from(Array(r.slots),(l,s)=>{const a=Object.values(r.items).find(f=>f?.slot===s+1)||{slot:s+1};return a.name&&(typeof dt[a.name]>"u"&&Yw(a.name),a.durability=ql(a.metadata,i)),a})}),e.shiftPressed=!1,e.isBusy=!1},j3=(e,t)=>{if(t.payload.items){Array.isArray(t.payload.items)||(t.payload.items=[t.payload.items]);const n=Math.floor(Date.now()/1e3);Object.values(t.payload.items).filter(r=>!!r).forEach(r=>{const i=r.inventory&&r.inventory!==Yt.PLAYER?e.rightInventory:e.leftInventory;r.item.durability=ql(r.item.metadata,n),i.items[r.item.slot-1]=r.item}),e.rightInventory.type===Yt.CRAFTING&&(e.rightInventory={...e.rightInventory})}if(t.payload.itemCount){const n=Object.entries(t.payload.itemCount);for(let r=0;r<n.length;r++){const i=n[r][0],l=n[r][1];dt[i]?dt[i].count+=l:console.log(`Item data for ${i} is undefined`)}}if(t.payload.weightData){const n=t.payload.weightData.inventoryId,r=t.payload.weightData.maxWeight,i=n===e.leftInventory.id?"leftInventory":n===e.rightInventory.id?"rightInventory":null;if(!i)return;e[i].maxWeight=r}if(t.payload.slotsData){const{inventoryId:n}=t.payload.slotsData,{slots:r}=t.payload.slotsData,i=n===e.leftInventory.id?"leftInventory":n===e.rightInventory.id?"rightInventory":null;if(!i)return;e[i].slots=r,sv.caseReducers.setupInventory(e,{type:"setupInventory",payload:{leftInventory:i==="leftInventory"?e[i]:void 0,rightInventory:i==="rightInventory"?e[i]:void 0}})}},W3=(e,t)=>{const{fromSlot:n,fromType:r,toSlot:i,toType:l}=t.payload,{sourceInventory:s,targetInventory:a}=Wf(e,r,l),f=Math.floor(Date.now()/1e3);[s.items[n.slot-1],a.items[i.slot-1]]=[{...a.items[i.slot-1],slot:n.slot,durability:ql(i.metadata,f)},{...s.items[n.slot-1],slot:i.slot,durability:ql(n.metadata,f)}]},V3=(e,t)=>{const{fromSlot:n,fromType:r,toSlot:i,toType:l,count:s}=t.payload,{sourceInventory:a,targetInventory:f}=Wf(e,r,l),d=n.weight/n.count;f.items[i.slot-1]={...f.items[i.slot-1],count:i.count+s,weight:d*(i.count+s)},!(r===Yt.SHOP||r===Yt.CRAFTING)&&(a.items[n.slot-1]=n.count-s>0?{...a.items[n.slot-1],count:n.count-s,weight:d*(n.count-s)}:{slot:n.slot})},G3=(e,t)=>{const{fromSlot:n,fromType:r,toSlot:i,toType:l,count:s}=t.payload,{sourceInventory:a,targetInventory:f}=Wf(e,r,l),d=n.weight/n.count,p=Math.floor(Date.now()/1e3),g=a.items[n.slot-1];f.items[i.slot-1]={...g,count:s,weight:d*s,slot:i.slot,durability:ql(g.metadata,p)},!(r===Yt.SHOP||r===Yt.CRAFTING)&&(a.items[n.slot-1]=n.count-s>0?{...a.items[n.slot-1],count:n.count-s,weight:d*(n.count-s)}:{slot:n.slot})},q3={leftInventory:{id:"",type:"",slots:0,maxWeight:0,items:[]},rightInventory:{id:"",type:"",slots:0,maxWeight:0,items:[]},additionalMetadata:new Array,itemAmount:0,shiftPressed:!1,isBusy:!1},sv=nv({name:"inventory",initialState:q3,reducers:{stackSlots:V3,swapSlots:W3,setupInventory:H3,moveSlots:G3,refreshSlots:j3,setAdditionalMetadata:(e,t)=>{const n=[];for(let r=0;r<t.payload.length;r++){const i=t.payload[r];e.additionalMetadata.find(l=>l.value===i.value)||n.push(i)}e.additionalMetadata=[...e.additionalMetadata,...n]},setItemAmount:(e,t)=>{e.itemAmount=t.payload},setShiftPressed:(e,t)=>{e.shiftPressed=t.payload},setContainerWeight:(e,t)=>{const n=e.leftInventory.items.find(r=>r.metadata?.container===e.rightInventory.id);n&&(n.weight=t.payload)}},extraReducers:e=>{e.addMatcher(lk,t=>{t.isBusy=!0,t.history={leftInventory:wg(t.leftInventory),rightInventory:wg(t.rightInventory)}}),e.addMatcher(sk,t=>{t.isBusy=!1}),e.addMatcher(uk,t=>{t.history&&t.history.leftInventory&&t.history.rightInventory&&(t.leftInventory=t.history.leftInventory,t.rightInventory=t.history.rightInventory),t.isBusy=!1})}}),{setAdditionalMetadata:K3,setItemAmount:Q3,setShiftPressed:Y3,setupInventory:dk,swapSlots:X3,moveSlots:Z3,stackSlots:J3,refreshSlots:e4,setContainerWeight:t4}=sv.actions,pk=e=>e.inventory.leftInventory,n4=e=>e.inventory.rightInventory,r4=e=>e.inventory.itemAmount,i4=sv.reducer,o4={open:!1,item:null,inventoryType:null},hk=nv({name:"tooltip",initialState:o4,reducers:{openTooltip(e,t){e.open=!0,e.item=t.payload.item,e.inventoryType=t.payload.inventoryType},closeTooltip(e){e.open=!1}}}),{openTooltip:l4,closeTooltip:Ju}=hk.actions,u4=hk.reducer,s4={coords:null,item:null},gk=nv({name:"contextMenu",initialState:s4,reducers:{openContextMenu(e,t){e.coords=t.payload.coords,e.item=t.payload.item},closeContextMenu(e){e.coords=null}}}),{openContextMenu:a4,closeContextMenu:mk}=gk.actions,c4=gk.reducer,Nn=_3({reducer:{inventory:i4,tooltip:u4,contextMenu:c4}}),iu=()=>WM(),vi=kM,Fr=(e,t)=>{const n=I.useRef(fk);I.useEffect(()=>{n.current=t},[t]),I.useEffect(()=>{const r=i=>{const{action:l,data:s}=i.data;n.current&&l===e&&n.current(s)};return window.addEventListener("message",r),()=>window.removeEventListener("message",r)},[e])},av=e=>{ur("useItem",e.slot)},vk=e=>{const{inventory:{itemAmount:t}}=Nn.getState();ur("giveItem",{slot:e.slot,count:t})},ut={},Xw=Math.floor;function yk(e){return wk(e)?(e.nodeName||"").toLowerCase():"#document"}function Ps(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function f4(e){var t;return(t=(wk(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function wk(e){return e instanceof Node||e instanceof Ps(e).Node}function hn(e){return e instanceof Element||e instanceof Ps(e).Element}function Kl(e){return e instanceof HTMLElement||e instanceof Ps(e).HTMLElement}function Eg(e){return typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof Ps(e).ShadowRoot}function d4(e){return["html","body","#document"].includes(yk(e))}function p4(e){return Ps(e).getComputedStyle(e)}function h4(e){if(yk(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Eg(e)&&e.host||f4(e);return Eg(t)?t.host:t}function Fi(e){let t=e.activeElement;for(;((n=t)==null||(r=n.shadowRoot)==null?void 0:r.activeElement)!=null;){var n,r;t=t.shadowRoot.activeElement}return t}function Pt(e,t){if(!e||!t)return!1;const n=t.getRootNode&&t.getRootNode();if(e.contains(t))return!0;if(n&&Eg(n)){let r=t;for(;r;){if(e===r)return!0;r=r.parentNode||r.host}}return!1}function cv(){const e=navigator.userAgentData;return e!=null&&e.platform?e.platform:navigator.platform}function g4(){const e=navigator.userAgentData;return e&&Array.isArray(e.brands)?e.brands.map(t=>{let{brand:n,version:r}=t;return n+"/"+r}).join(" "):navigator.userAgent}function xk(e){if(e.mozInputSource===0&&e.isTrusted)return!0;const t=/Android/i;return(t.test(cv())||t.test(g4()))&&e.pointerType?e.type==="click"&&e.buttons===1:e.detail===0&&!e.pointerType}function Sk(e){return e.width===0&&e.height===0||e.width===1&&e.height===1&&e.pressure===0&&e.detail===0&&e.pointerType!=="mouse"||e.width<1&&e.height<1&&e.pressure===0&&e.detail===0}function Ek(){return/apple/i.test(navigator.vendor)}function m4(){return cv().toLowerCase().startsWith("mac")&&!navigator.maxTouchPoints}function Vc(e,t){const n=["mouse","pen"];return t||n.push("",void 0),n.includes(e)}function v4(e){return"nativeEvent"in e}function y4(e){return e.matches("html,body")}function rn(e){return e?.ownerDocument||document}function Yp(e,t){if(t==null)return!1;if("composedPath"in e)return e.composedPath().includes(t);const n=e;return n.target!=null&&t.contains(n.target)}function fv(e){return"composedPath"in e?e.composedPath()[0]:e.target}const w4="input:not([type='hidden']):not([disabled]),[contenteditable]:not([contenteditable='false']),textarea:not([disabled])";function Ck(e){return Kl(e)&&e.matches(w4)}function $t(e){e.preventDefault(),e.stopPropagation()}const x4=Math.min,S4=Math.max,E4={left:"right",right:"left",bottom:"top",top:"bottom"},C4={start:"end",end:"start"};function Zw(e,t,n){return S4(e,x4(t,n))}function Vf(e,t){return typeof e=="function"?e(t):e}function Bo(e){return e.split("-")[0]}function Gf(e){return e.split("-")[1]}function kk(e){return e==="x"?"y":"x"}function bk(e){return e==="y"?"height":"width"}function Ql(e){return["top","bottom"].includes(Bo(e))?"y":"x"}function _k(e){return kk(Ql(e))}function k4(e,t,n){n===void 0&&(n=!1);const r=Gf(e),i=_k(e),l=bk(i);let s=i==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[l]>t.floating[l]&&(s=Gc(s)),[s,Gc(s)]}function b4(e){const t=Gc(e);return[Cg(e),t,Cg(t)]}function Cg(e){return e.replace(/start|end/g,t=>C4[t])}function _4(e,t,n){const r=["left","right"],i=["right","left"],l=["top","bottom"],s=["bottom","top"];switch(e){case"top":case"bottom":return n?t?i:r:t?r:i;case"left":case"right":return t?l:s;default:return[]}}function O4(e,t,n,r){const i=Gf(e);let l=_4(Bo(e),n==="start",r);return i&&(l=l.map(s=>s+"-"+i),t&&(l=l.concat(l.map(Cg)))),l}function Gc(e){return e.replace(/left|right|bottom|top/g,t=>E4[t])}function T4(e){return{top:0,right:0,bottom:0,left:0,...e}}function I4(e){return typeof e!="number"?T4(e):{top:e,right:e,bottom:e,left:e}}function qc(e){const{x:t,y:n,width:r,height:i}=e;return{width:r,height:i,top:n,left:t,right:t+r,bottom:n+i,x:t,y:n}}function Jw(e,t,n){let{reference:r,floating:i}=e;const l=Ql(t),s=_k(t),a=bk(s),f=Bo(t),d=l==="y",p=r.x+r.width/2-i.width/2,g=r.y+r.height/2-i.height/2,v=r[a]/2-i[a]/2;let m;switch(f){case"top":m={x:p,y:r.y-i.height};break;case"bottom":m={x:p,y:r.y+r.height};break;case"right":m={x:r.x+r.width,y:g};break;case"left":m={x:r.x-i.width,y:g};break;default:m={x:r.x,y:r.y}}switch(Gf(t)){case"start":m[s]-=v*(n&&d?-1:1);break;case"end":m[s]+=v*(n&&d?-1:1);break}return m}const R4=async(e,t,n)=>{const{placement:r="bottom",strategy:i="absolute",middleware:l=[],platform:s}=n,a=l.filter(Boolean),f=await(s.isRTL==null?void 0:s.isRTL(t));let d=await s.getElementRects({reference:e,floating:t,strategy:i}),{x:p,y:g}=Jw(d,r,f),v=r,m={},E=0;for(let S=0;S<a.length;S++){const{name:A,fn:y}=a[S],{x,y:k,data:P,reset:D}=await y({x:p,y:g,initialPlacement:r,placement:v,strategy:i,middlewareData:m,rects:d,platform:s,elements:{reference:e,floating:t}});p=x??p,g=k??g,m={...m,[A]:{...m[A],...P}},D&&E<=50&&(E++,typeof D=="object"&&(D.placement&&(v=D.placement),D.rects&&(d=D.rects===!0?await s.getElementRects({reference:e,floating:t,strategy:i}):D.rects),{x:p,y:g}=Jw(d,v,f)),S=-1)}return{x:p,y:g,placement:v,strategy:i,middlewareData:m}};async function Ok(e,t){var n;t===void 0&&(t={});const{x:r,y:i,platform:l,rects:s,elements:a,strategy:f}=e,{boundary:d="clippingAncestors",rootBoundary:p="viewport",elementContext:g="floating",altBoundary:v=!1,padding:m=0}=Vf(t,e),E=I4(m),A=a[v?g==="floating"?"reference":"floating":g],y=qc(await l.getClippingRect({element:(n=await(l.isElement==null?void 0:l.isElement(A)))==null||n?A:A.contextElement||await(l.getDocumentElement==null?void 0:l.getDocumentElement(a.floating)),boundary:d,rootBoundary:p,strategy:f})),x=g==="floating"?{x:r,y:i,width:s.floating.width,height:s.floating.height}:s.reference,k=await(l.getOffsetParent==null?void 0:l.getOffsetParent(a.floating)),P=await(l.isElement==null?void 0:l.isElement(k))?await(l.getScale==null?void 0:l.getScale(k))||{x:1,y:1}:{x:1,y:1},D=qc(l.convertOffsetParentRelativeRectToViewportRelativeRect?await l.convertOffsetParentRelativeRectToViewportRelativeRect({elements:a,rect:x,offsetParent:k,strategy:f}):x);return{top:(y.top-D.top+E.top)/P.y,bottom:(D.bottom-y.bottom+E.bottom)/P.y,left:(y.left-D.left+E.left)/P.x,right:(D.right-y.right+E.right)/P.x}}const P4=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:i,middlewareData:l,rects:s,initialPlacement:a,platform:f,elements:d}=t,{mainAxis:p=!0,crossAxis:g=!0,fallbackPlacements:v,fallbackStrategy:m="bestFit",fallbackAxisSideDirection:E="none",flipAlignment:S=!0,...A}=Vf(e,t);if((n=l.arrow)!=null&&n.alignmentOffset)return{};const y=Bo(i),x=Ql(a),k=Bo(a)===a,P=await(f.isRTL==null?void 0:f.isRTL(d.floating)),D=v||(k||!S?[Gc(a)]:b4(a)),T=E!=="none";!v&&T&&D.push(...O4(a,S,E,P));const F=[a,...D],z=await Ok(t,A),G=[];let q=((r=l.flip)==null?void 0:r.overflows)||[];if(p&&G.push(z[y]),g){const H=k4(i,s,P);G.push(z[H[0]],z[H[1]])}if(q=[...q,{placement:i,overflows:G}],!G.every(H=>H<=0)){var B,Q;const H=(((B=l.flip)==null?void 0:B.index)||0)+1,ee=F[H];if(ee)return{data:{index:H,overflows:q},reset:{placement:ee}};let le=(Q=q.filter(oe=>oe.overflows[0]<=0).sort((oe,$)=>oe.overflows[1]-$.overflows[1])[0])==null?void 0:Q.placement;if(!le)switch(m){case"bestFit":{var U;const oe=(U=q.filter($=>{if(T){const Z=Ql($.placement);return Z===x||Z==="y"}return!0}).map($=>[$.placement,$.overflows.filter(Z=>Z>0).reduce((Z,_)=>Z+_,0)]).sort(($,Z)=>$[1]-Z[1])[0])==null?void 0:U[0];oe&&(le=oe);break}case"initialPlacement":le=a;break}if(i!==le)return{reset:{placement:le}}}return{}}}};async function A4(e,t){const{placement:n,platform:r,elements:i}=e,l=await(r.isRTL==null?void 0:r.isRTL(i.floating)),s=Bo(n),a=Gf(n),f=Ql(n)==="y",d=["left","top"].includes(s)?-1:1,p=l&&f?-1:1,g=Vf(t,e);let{mainAxis:v,crossAxis:m,alignmentAxis:E}=typeof g=="number"?{mainAxis:g,crossAxis:0,alignmentAxis:null}:{mainAxis:g.mainAxis||0,crossAxis:g.crossAxis||0,alignmentAxis:g.alignmentAxis};return a&&typeof E=="number"&&(m=a==="end"?E*-1:E),f?{x:m*p,y:v*d}:{x:v*d,y:m*p}}const D4=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:i,y:l,placement:s,middlewareData:a}=t,f=await A4(t,e);return s===((n=a.offset)==null?void 0:n.placement)&&(r=a.arrow)!=null&&r.alignmentOffset?{}:{x:i+f.x,y:l+f.y,data:{...f,placement:s}}}}},N4=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:i}=t,{mainAxis:l=!0,crossAxis:s=!1,limiter:a={fn:A=>{let{x:y,y:x}=A;return{x:y,y:x}}},...f}=Vf(e,t),d={x:n,y:r},p=await Ok(t,f),g=Ql(Bo(i)),v=kk(g);let m=d[v],E=d[g];if(l){const A=v==="y"?"top":"left",y=v==="y"?"bottom":"right",x=m+p[A],k=m-p[y];m=Zw(x,m,k)}if(s){const A=g==="y"?"top":"left",y=g==="y"?"bottom":"right",x=E+p[A],k=E-p[y];E=Zw(x,E,k)}const S=a.fn({...t,[v]:m,[g]:E});return{...S,data:{x:S.x-n,y:S.y-r,enabled:{[v]:l,[g]:s}}}}}},kg=Math.min,Nl=Math.max,Kc=Math.round,ja=Math.floor,Hr=e=>({x:e,y:e});function qf(){return typeof window<"u"}function ou(e){return Tk(e)?(e.nodeName||"").toLowerCase():"#document"}function zn(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Vr(e){var t;return(t=(Tk(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Tk(e){return qf()?e instanceof Node||e instanceof zn(e).Node:!1}function _r(e){return qf()?e instanceof Element||e instanceof zn(e).Element:!1}function jr(e){return qf()?e instanceof HTMLElement||e instanceof zn(e).HTMLElement:!1}function ex(e){return!qf()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof zn(e).ShadowRoot}function As(e){const{overflow:t,overflowX:n,overflowY:r,display:i}=Or(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(i)}function M4(e){return["table","td","th"].includes(ou(e))}function Kf(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function dv(e){const t=pv(),n=_r(e)?Or(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function L4(e){let t=ro(e);for(;jr(t)&&!Yl(t);){if(dv(t))return t;if(Kf(t))return null;t=ro(t)}return null}function pv(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Yl(e){return["html","body","#document"].includes(ou(e))}function Or(e){return zn(e).getComputedStyle(e)}function Qf(e){return _r(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ro(e){if(ou(e)==="html")return e;const t=e.assignedSlot||e.parentNode||ex(e)&&e.host||Vr(e);return ex(t)?t.host:t}function Ik(e){const t=ro(e);return Yl(t)?e.ownerDocument?e.ownerDocument.body:e.body:jr(t)&&As(t)?t:Ik(t)}function Xi(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const i=Ik(e),l=i===((r=e.ownerDocument)==null?void 0:r.body),s=zn(i);if(l){const a=bg(s);return t.concat(s,s.visualViewport||[],As(i)?i:[],a&&n?Xi(a):[])}return t.concat(i,Xi(i,[],n))}function bg(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Rk(e){const t=Or(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const i=jr(e),l=i?e.offsetWidth:n,s=i?e.offsetHeight:r,a=Kc(n)!==l||Kc(r)!==s;return a&&(n=l,r=s),{width:n,height:r,$:a}}function hv(e){return _r(e)?e:e.contextElement}function Ml(e){const t=hv(e);if(!jr(t))return Hr(1);const n=t.getBoundingClientRect(),{width:r,height:i,$:l}=Rk(t);let s=(l?Kc(n.width):n.width)/r,a=(l?Kc(n.height):n.height)/i;return(!s||!Number.isFinite(s))&&(s=1),(!a||!Number.isFinite(a))&&(a=1),{x:s,y:a}}const F4=Hr(0);function Pk(e){const t=zn(e);return!pv()||!t.visualViewport?F4:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function z4(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==zn(e)?!1:t}function Uo(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const i=e.getBoundingClientRect(),l=hv(e);let s=Hr(1);t&&(r?_r(r)&&(s=Ml(r)):s=Ml(e));const a=z4(l,n,r)?Pk(l):Hr(0);let f=(i.left+a.x)/s.x,d=(i.top+a.y)/s.y,p=i.width/s.x,g=i.height/s.y;if(l){const v=zn(l),m=r&&_r(r)?zn(r):r;let E=v,S=bg(E);for(;S&&r&&m!==E;){const A=Ml(S),y=S.getBoundingClientRect(),x=Or(S),k=y.left+(S.clientLeft+parseFloat(x.paddingLeft))*A.x,P=y.top+(S.clientTop+parseFloat(x.paddingTop))*A.y;f*=A.x,d*=A.y,p*=A.x,g*=A.y,f+=k,d+=P,E=zn(S),S=bg(E)}}return qc({width:p,height:g,x:f,y:d})}function gv(e,t){const n=Qf(e).scrollLeft;return t?t.left+n:Uo(Vr(e)).left+n}function Ak(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),i=r.left+t.scrollLeft-(n?0:gv(e,r)),l=r.top+t.scrollTop;return{x:i,y:l}}function $4(e){let{elements:t,rect:n,offsetParent:r,strategy:i}=e;const l=i==="fixed",s=Vr(r),a=t?Kf(t.floating):!1;if(r===s||a&&l)return n;let f={scrollLeft:0,scrollTop:0},d=Hr(1);const p=Hr(0),g=jr(r);if((g||!g&&!l)&&((ou(r)!=="body"||As(s))&&(f=Qf(r)),jr(r))){const m=Uo(r);d=Ml(r),p.x=m.x+r.clientLeft,p.y=m.y+r.clientTop}const v=s&&!g&&!l?Ak(s,f,!0):Hr(0);return{width:n.width*d.x,height:n.height*d.y,x:n.x*d.x-f.scrollLeft*d.x+p.x+v.x,y:n.y*d.y-f.scrollTop*d.y+p.y+v.y}}function B4(e){return Array.from(e.getClientRects())}function U4(e){const t=Vr(e),n=Qf(e),r=e.ownerDocument.body,i=Nl(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),l=Nl(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let s=-n.scrollLeft+gv(e);const a=-n.scrollTop;return Or(r).direction==="rtl"&&(s+=Nl(t.clientWidth,r.clientWidth)-i),{width:i,height:l,x:s,y:a}}function H4(e,t){const n=zn(e),r=Vr(e),i=n.visualViewport;let l=r.clientWidth,s=r.clientHeight,a=0,f=0;if(i){l=i.width,s=i.height;const d=pv();(!d||d&&t==="fixed")&&(a=i.offsetLeft,f=i.offsetTop)}return{width:l,height:s,x:a,y:f}}function j4(e,t){const n=Uo(e,!0,t==="fixed"),r=n.top+e.clientTop,i=n.left+e.clientLeft,l=jr(e)?Ml(e):Hr(1),s=e.clientWidth*l.x,a=e.clientHeight*l.y,f=i*l.x,d=r*l.y;return{width:s,height:a,x:f,y:d}}function tx(e,t,n){let r;if(t==="viewport")r=H4(e,n);else if(t==="document")r=U4(Vr(e));else if(_r(t))r=j4(t,n);else{const i=Pk(e);r={x:t.x-i.x,y:t.y-i.y,width:t.width,height:t.height}}return qc(r)}function Dk(e,t){const n=ro(e);return n===t||!_r(n)||Yl(n)?!1:Or(n).position==="fixed"||Dk(n,t)}function W4(e,t){const n=t.get(e);if(n)return n;let r=Xi(e,[],!1).filter(a=>_r(a)&&ou(a)!=="body"),i=null;const l=Or(e).position==="fixed";let s=l?ro(e):e;for(;_r(s)&&!Yl(s);){const a=Or(s),f=dv(s);!f&&a.position==="fixed"&&(i=null),(l?!f&&!i:!f&&a.position==="static"&&!!i&&["absolute","fixed"].includes(i.position)||As(s)&&!f&&Dk(e,s))?r=r.filter(p=>p!==s):i=a,s=ro(s)}return t.set(e,r),r}function V4(e){let{element:t,boundary:n,rootBoundary:r,strategy:i}=e;const s=[...n==="clippingAncestors"?Kf(t)?[]:W4(t,this._c):[].concat(n),r],a=s[0],f=s.reduce((d,p)=>{const g=tx(t,p,i);return d.top=Nl(g.top,d.top),d.right=kg(g.right,d.right),d.bottom=kg(g.bottom,d.bottom),d.left=Nl(g.left,d.left),d},tx(t,a,i));return{width:f.right-f.left,height:f.bottom-f.top,x:f.left,y:f.top}}function G4(e){const{width:t,height:n}=Rk(e);return{width:t,height:n}}function q4(e,t,n){const r=jr(t),i=Vr(t),l=n==="fixed",s=Uo(e,!0,l,t);let a={scrollLeft:0,scrollTop:0};const f=Hr(0);if(r||!r&&!l)if((ou(t)!=="body"||As(i))&&(a=Qf(t)),r){const v=Uo(t,!0,l,t);f.x=v.x+t.clientLeft,f.y=v.y+t.clientTop}else i&&(f.x=gv(i));const d=i&&!r&&!l?Ak(i,a):Hr(0),p=s.left+a.scrollLeft-f.x-d.x,g=s.top+a.scrollTop-f.y-d.y;return{x:p,y:g,width:s.width,height:s.height}}function Xp(e){return Or(e).position==="static"}function nx(e,t){if(!jr(e)||Or(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Vr(e)===n&&(n=n.ownerDocument.body),n}function Nk(e,t){const n=zn(e);if(Kf(e))return n;if(!jr(e)){let i=ro(e);for(;i&&!Yl(i);){if(_r(i)&&!Xp(i))return i;i=ro(i)}return n}let r=nx(e,t);for(;r&&M4(r)&&Xp(r);)r=nx(r,t);return r&&Yl(r)&&Xp(r)&&!dv(r)?n:r||L4(e)||n}const K4=async function(e){const t=this.getOffsetParent||Nk,n=this.getDimensions,r=await n(e.floating);return{reference:q4(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function Q4(e){return Or(e).direction==="rtl"}const Y4={convertOffsetParentRelativeRectToViewportRelativeRect:$4,getDocumentElement:Vr,getClippingRect:V4,getOffsetParent:Nk,getElementRects:K4,getClientRects:B4,getDimensions:G4,getScale:Ml,isElement:_r,isRTL:Q4};function Mk(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function X4(e,t){let n=null,r;const i=Vr(e);function l(){var a;clearTimeout(r),(a=n)==null||a.disconnect(),n=null}function s(a,f){a===void 0&&(a=!1),f===void 0&&(f=1),l();const d=e.getBoundingClientRect(),{left:p,top:g,width:v,height:m}=d;if(a||t(),!v||!m)return;const E=ja(g),S=ja(i.clientWidth-(p+v)),A=ja(i.clientHeight-(g+m)),y=ja(p),k={rootMargin:-E+"px "+-S+"px "+-A+"px "+-y+"px",threshold:Nl(0,kg(1,f))||1};let P=!0;function D(T){const F=T[0].intersectionRatio;if(F!==f){if(!P)return s();F?s(!1,F):r=setTimeout(()=>{s(!1,1e-7)},1e3)}F===1&&!Mk(d,e.getBoundingClientRect())&&s(),P=!1}try{n=new IntersectionObserver(D,{...k,root:i.ownerDocument})}catch{n=new IntersectionObserver(D,k)}n.observe(e)}return s(!0),l}function Z4(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:i=!0,ancestorResize:l=!0,elementResize:s=typeof ResizeObserver=="function",layoutShift:a=typeof IntersectionObserver=="function",animationFrame:f=!1}=r,d=hv(e),p=i||l?[...d?Xi(d):[],...Xi(t)]:[];p.forEach(y=>{i&&y.addEventListener("scroll",n,{passive:!0}),l&&y.addEventListener("resize",n)});const g=d&&a?X4(d,n):null;let v=-1,m=null;s&&(m=new ResizeObserver(y=>{let[x]=y;x&&x.target===d&&m&&(m.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var k;(k=m)==null||k.observe(t)})),n()}),d&&!f&&m.observe(d),m.observe(t));let E,S=f?Uo(e):null;f&&A();function A(){const y=Uo(e);S&&!Mk(S,y)&&n(),S=y,E=requestAnimationFrame(A)}return n(),()=>{var y;p.forEach(x=>{i&&x.removeEventListener("scroll",n),l&&x.removeEventListener("resize",n)}),g?.(),(y=m)==null||y.disconnect(),m=null,f&&cancelAnimationFrame(E)}}const J4=D4,ez=N4,tz=P4,nz=(e,t,n)=>{const r=new Map,i={platform:Y4,...n},l={...i.platform,_c:r};return R4(e,t,{...i,platform:l})};var cc=typeof document<"u"?I.useLayoutEffect:I.useEffect;function Qc(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,i;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!Qc(e[r],t[r]))return!1;return!0}if(i=Object.keys(e),n=i.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,i[r]))return!1;for(r=n;r--!==0;){const l=i[r];if(!(l==="_owner"&&e.$$typeof)&&!Qc(e[l],t[l]))return!1}return!0}return e!==e&&t!==t}function Lk(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function rx(e,t){const n=Lk(e);return Math.round(t*n)/n}function Zp(e){const t=I.useRef(e);return cc(()=>{t.current=e}),t}function rz(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:i,elements:{reference:l,floating:s}={},transform:a=!0,whileElementsMounted:f,open:d}=e,[p,g]=I.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[v,m]=I.useState(r);Qc(v,r)||m(r);const[E,S]=I.useState(null),[A,y]=I.useState(null),x=I.useCallback($=>{$!==T.current&&(T.current=$,S($))},[]),k=I.useCallback($=>{$!==F.current&&(F.current=$,y($))},[]),P=l||E,D=s||A,T=I.useRef(null),F=I.useRef(null),z=I.useRef(p),G=f!=null,q=Zp(f),B=Zp(i),Q=Zp(d),U=I.useCallback(()=>{if(!T.current||!F.current)return;const $={placement:t,strategy:n,middleware:v};B.current&&($.platform=B.current),nz(T.current,F.current,$).then(Z=>{const _={...Z,isPositioned:Q.current!==!1};H.current&&!Qc(z.current,_)&&(z.current=_,nu.flushSync(()=>{g(_)}))})},[v,t,n,B,Q]);cc(()=>{d===!1&&z.current.isPositioned&&(z.current.isPositioned=!1,g($=>({...$,isPositioned:!1})))},[d]);const H=I.useRef(!1);cc(()=>(H.current=!0,()=>{H.current=!1}),[]),cc(()=>{if(P&&(T.current=P),D&&(F.current=D),P&&D){if(q.current)return q.current(P,D,U);U()}},[P,D,U,q,G]);const ee=I.useMemo(()=>({reference:T,floating:F,setReference:x,setFloating:k}),[x,k]),le=I.useMemo(()=>({reference:P,floating:D}),[P,D]),oe=I.useMemo(()=>{const $={position:n,left:0,top:0};if(!le.floating)return $;const Z=rx(le.floating,p.x),_=rx(le.floating,p.y);return a?{...$,transform:"translate("+Z+"px, "+_+"px)",...Lk(le.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:Z,top:_}},[n,a,le.floating,p.x,p.y]);return I.useMemo(()=>({...p,update:U,refs:ee,elements:le,floatingStyles:oe}),[p,U,ee,le,oe])}const Fk=(e,t)=>({...J4(e),options:[e,t]}),zk=(e,t)=>({...ez(e),options:[e,t]}),$k=(e,t)=>({...tz(e),options:[e,t]});/*!
* tabbable 6.2.0
* @license MIT, https://github.com/focus-trap/tabbable/blob/master/LICENSE
*/var iz=["input:not([inert])","select:not([inert])","textarea:not([inert])","a[href]:not([inert])","button:not([inert])","[tabindex]:not(slot):not([inert])","audio[controls]:not([inert])","video[controls]:not([inert])",'[contenteditable]:not([contenteditable="false"]):not([inert])',"details>summary:first-of-type:not([inert])","details:not([inert])"],_g=iz.join(","),Bk=typeof Element>"u",ks=Bk?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector,Yc=!Bk&&Element.prototype.getRootNode?function(e){var t;return e==null||(t=e.getRootNode)===null||t===void 0?void 0:t.call(e)}:function(e){return e?.ownerDocument},Xc=function e(t,n){var r;n===void 0&&(n=!0);var i=t==null||(r=t.getAttribute)===null||r===void 0?void 0:r.call(t,"inert"),l=i===""||i==="true",s=l||n&&t&&e(t.parentNode);return s},oz=function(t){var n,r=t==null||(n=t.getAttribute)===null||n===void 0?void 0:n.call(t,"contenteditable");return r===""||r==="true"},lz=function(t,n,r){if(Xc(t))return[];var i=Array.prototype.slice.apply(t.querySelectorAll(_g));return n&&ks.call(t,_g)&&i.unshift(t),i=i.filter(r),i},uz=function e(t,n,r){for(var i=[],l=Array.from(t);l.length;){var s=l.shift();if(!Xc(s,!1))if(s.tagName==="SLOT"){var a=s.assignedElements(),f=a.length?a:s.children,d=e(f,!0,r);r.flatten?i.push.apply(i,d):i.push({scopeParent:s,candidates:d})}else{var p=ks.call(s,_g);p&&r.filter(s)&&(n||!t.includes(s))&&i.push(s);var g=s.shadowRoot||typeof r.getShadowRoot=="function"&&r.getShadowRoot(s),v=!Xc(g,!1)&&(!r.shadowRootFilter||r.shadowRootFilter(s));if(g&&v){var m=e(g===!0?s.children:g.children,!0,r);r.flatten?i.push.apply(i,m):i.push({scopeParent:s,candidates:m})}else l.unshift.apply(l,s.children)}}return i},Uk=function(t){return!isNaN(parseInt(t.getAttribute("tabindex"),10))},Hk=function(t){if(!t)throw new Error("No node provided");return t.tabIndex<0&&(/^(AUDIO|VIDEO|DETAILS)$/.test(t.tagName)||oz(t))&&!Uk(t)?0:t.tabIndex},sz=function(t,n){var r=Hk(t);return r<0&&n&&!Uk(t)?0:r},az=function(t,n){return t.tabIndex===n.tabIndex?t.documentOrder-n.documentOrder:t.tabIndex-n.tabIndex},jk=function(t){return t.tagName==="INPUT"},cz=function(t){return jk(t)&&t.type==="hidden"},fz=function(t){var n=t.tagName==="DETAILS"&&Array.prototype.slice.apply(t.children).some(function(r){return r.tagName==="SUMMARY"});return n},dz=function(t,n){for(var r=0;r<t.length;r++)if(t[r].checked&&t[r].form===n)return t[r]},pz=function(t){if(!t.name)return!0;var n=t.form||Yc(t),r=function(a){return n.querySelectorAll('input[type="radio"][name="'+a+'"]')},i;if(typeof window<"u"&&typeof window.CSS<"u"&&typeof window.CSS.escape=="function")i=r(window.CSS.escape(t.name));else try{i=r(t.name)}catch(s){return console.error("Looks like you have a radio button with a name attribute containing invalid CSS selector characters and need the CSS.escape polyfill: %s",s.message),!1}var l=dz(i,t.form);return!l||l===t},hz=function(t){return jk(t)&&t.type==="radio"},gz=function(t){return hz(t)&&!pz(t)},mz=function(t){var n,r=t&&Yc(t),i=(n=r)===null||n===void 0?void 0:n.host,l=!1;if(r&&r!==t){var s,a,f;for(l=!!((s=i)!==null&&s!==void 0&&(a=s.ownerDocument)!==null&&a!==void 0&&a.contains(i)||t!=null&&(f=t.ownerDocument)!==null&&f!==void 0&&f.contains(t));!l&&i;){var d,p,g;r=Yc(i),i=(d=r)===null||d===void 0?void 0:d.host,l=!!((p=i)!==null&&p!==void 0&&(g=p.ownerDocument)!==null&&g!==void 0&&g.contains(i))}}return l},ix=function(t){var n=t.getBoundingClientRect(),r=n.width,i=n.height;return r===0&&i===0},vz=function(t,n){var r=n.displayCheck,i=n.getShadowRoot;if(getComputedStyle(t).visibility==="hidden")return!0;var l=ks.call(t,"details>summary:first-of-type"),s=l?t.parentElement:t;if(ks.call(s,"details:not([open]) *"))return!0;if(!r||r==="full"||r==="legacy-full"){if(typeof i=="function"){for(var a=t;t;){var f=t.parentElement,d=Yc(t);if(f&&!f.shadowRoot&&i(f)===!0)return ix(t);t.assignedSlot?t=t.assignedSlot:!f&&d!==t.ownerDocument?t=d.host:t=f}t=a}if(mz(t))return!t.getClientRects().length;if(r!=="legacy-full")return!0}else if(r==="non-zero-area")return ix(t);return!1},yz=function(t){if(/^(INPUT|BUTTON|SELECT|TEXTAREA)$/.test(t.tagName))for(var n=t.parentElement;n;){if(n.tagName==="FIELDSET"&&n.disabled){for(var r=0;r<n.children.length;r++){var i=n.children.item(r);if(i.tagName==="LEGEND")return ks.call(n,"fieldset[disabled] *")?!0:!i.contains(t)}return!0}n=n.parentElement}return!1},wz=function(t,n){return!(n.disabled||Xc(n)||cz(n)||vz(n,t)||fz(n)||yz(n))},ox=function(t,n){return!(gz(n)||Hk(n)<0||!wz(t,n))},xz=function(t){var n=parseInt(t.getAttribute("tabindex"),10);return!!(isNaN(n)||n>=0)},Sz=function e(t){var n=[],r=[];return t.forEach(function(i,l){var s=!!i.scopeParent,a=s?i.scopeParent:i,f=sz(a,s),d=s?e(i.candidates):a;f===0?s?n.push.apply(n,d):n.push(a):r.push({documentOrder:l,tabIndex:f,item:i,isScope:s,content:d})}),r.sort(az).reduce(function(i,l){return l.isScope?i.push.apply(i,l.content):i.push(l.content),i},[]).concat(n)},mv=function(t,n){n=n||{};var r;return n.getShadowRoot?r=uz([t],n.includeContainer,{filter:ox.bind(null,n),flatten:!1,getShadowRoot:n.getShadowRoot,shadowRootFilter:xz}):r=lz(t,n.includeContainer,ox.bind(null,n)),Sz(r)};function vv(e){return I.useMemo(()=>e.every(t=>t==null)?null:t=>{e.forEach(n=>{typeof n=="function"?n(t):n!=null&&(n.current=t)})},e)}const yv="ArrowUp",Yf="ArrowDown",Xl="ArrowLeft",Ds="ArrowRight";function Wa(e,t,n){return Math.floor(e/t)!==n}function es(e,t){return t<0||t>=e.current.length}function Jp(e,t){return Kt(e,{disabledIndices:t})}function lx(e,t){return Kt(e,{decrement:!0,startingIndex:e.current.length,disabledIndices:t})}function Kt(e,t){let{startingIndex:n=-1,decrement:r=!1,disabledIndices:i,amount:l=1}=t===void 0?{}:t;const s=e.current;let a=n;do{var f,d;a=a+(r?-l:l)}while(a>=0&&a<=s.length-1&&(i?i.includes(a):s[a]==null||(f=s[a])!=null&&f.hasAttribute("disabled")||((d=s[a])==null?void 0:d.getAttribute("aria-disabled"))==="true"));return a}function Ez(e,t){let{event:n,orientation:r,loop:i,cols:l,disabledIndices:s,minIndex:a,maxIndex:f,prevIndex:d,stopEvent:p=!1}=t,g=d;if(n.key===yv){if(p&&$t(n),d===-1)g=f;else if(g=Kt(e,{startingIndex:g,amount:l,decrement:!0,disabledIndices:s}),i&&(d-l<a||g<0)){const v=d%l,m=f%l,E=f-(m-v);m===v?g=f:g=m>v?E:E-l}es(e,g)&&(g=d)}if(n.key===Yf&&(p&&$t(n),d===-1?g=a:(g=Kt(e,{startingIndex:d,amount:l,disabledIndices:s}),i&&d+l>f&&(g=Kt(e,{startingIndex:d%l-l,amount:l,disabledIndices:s}))),es(e,g)&&(g=d)),r==="both"){const v=Xw(d/l);n.key===Ds&&(p&&$t(n),d%l!==l-1?(g=Kt(e,{startingIndex:d,disabledIndices:s}),i&&Wa(g,l,v)&&(g=Kt(e,{startingIndex:d-d%l-1,disabledIndices:s}))):i&&(g=Kt(e,{startingIndex:d-d%l-1,disabledIndices:s})),Wa(g,l,v)&&(g=d)),n.key===Xl&&(p&&$t(n),d%l!==0?(g=Kt(e,{startingIndex:d,disabledIndices:s,decrement:!0}),i&&Wa(g,l,v)&&(g=Kt(e,{startingIndex:d+(l-d%l),decrement:!0,disabledIndices:s}))):i&&(g=Kt(e,{startingIndex:d+(l-d%l),decrement:!0,disabledIndices:s})),Wa(g,l,v)&&(g=d));const m=Xw(f/l)===v;es(e,g)&&(i&&m?g=n.key===Xl?f:Kt(e,{startingIndex:d-d%l-1,disabledIndices:s}):g=d)}return g}let ux=0;function oi(e,t){t===void 0&&(t={});const{preventScroll:n=!1,cancelPrevious:r=!0,sync:i=!1}=t;r&&cancelAnimationFrame(ux);const l=()=>e?.focus({preventScroll:n});i?l():ux=requestAnimationFrame(l)}var Qe=typeof document<"u"?I.useLayoutEffect:I.useEffect;function Cz(e,t){const n=e.compareDocumentPosition(t);return n&Node.DOCUMENT_POSITION_FOLLOWING||n&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:n&Node.DOCUMENT_POSITION_PRECEDING||n&Node.DOCUMENT_POSITION_CONTAINS?1:0}function kz(e,t){if(e.size!==t.size)return!1;for(const[n,r]of e.entries())if(r!==t.get(n))return!1;return!0}const Wk=I.createContext({register:()=>{},unregister:()=>{},map:new Map,elementsRef:{current:[]}});function bz(e){let{children:t,elementsRef:n,labelsRef:r}=e;const[i,l]=I.useState(()=>new Map),s=I.useCallback(f=>{l(d=>new Map(d).set(f,null))},[]),a=I.useCallback(f=>{l(d=>{const p=new Map(d);return p.delete(f),p})},[]);return Qe(()=>{const f=new Map(i);Array.from(f.keys()).sort(Cz).forEach((p,g)=>{f.set(p,g)}),kz(i,f)||l(f)},[i]),I.createElement(Wk.Provider,{value:I.useMemo(()=>({register:s,unregister:a,map:i,elementsRef:n,labelsRef:r}),[s,a,i,n,r])},t)}function Vk(e){let{label:t}=e===void 0?{}:e;const[n,r]=I.useState(null),i=I.useRef(null),{register:l,unregister:s,map:a,elementsRef:f,labelsRef:d}=I.useContext(Wk),p=I.useCallback(g=>{if(i.current=g,n!==null&&(f.current[n]=g,d)){var v;const m=t!==void 0;d.current[n]=m?t:(v=g?.textContent)!=null?v:null}},[n,f,d,t]);return Qe(()=>{const g=i.current;if(g)return l(g),()=>{s(g)}},[l,s]),Qe(()=>{const g=i.current?a.get(i.current):null;g!=null&&r(g)},[a]),I.useMemo(()=>({ref:p,index:n??-1}),[n,p])}function bs(){return bs=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},bs.apply(this,arguments)}let eh=!1,_z=0;const sx=()=>"floating-ui-"+_z++;function Oz(){const[e,t]=I.useState(()=>eh?sx():void 0);return Qe(()=>{e==null&&t(sx())},[]),I.useEffect(()=>{eh||(eh=!0)},[]),e}const Tz=SS["useId".toString()],Ns=Tz||Oz;function Gk(){const e=new Map;return{emit(t,n){var r;(r=e.get(t))==null||r.forEach(i=>i(n))},on(t,n){e.set(t,[...e.get(t)||[],n])},off(t,n){var r;e.set(t,((r=e.get(t))==null?void 0:r.filter(i=>i!==n))||[])}}}const qk=I.createContext(null),Kk=I.createContext(null),Wo=()=>{var e;return((e=I.useContext(qk))==null?void 0:e.id)||null},so=()=>I.useContext(Kk);function Iz(e){const t=Ns(),n=so(),r=Wo(),i=e||r;return Qe(()=>{const l={id:t,parentId:i};return n?.addNode(l),()=>{n?.removeNode(l)}},[n,t,i]),t}function Rz(e){let{children:t,id:n}=e;const r=Wo();return I.createElement(qk.Provider,{value:I.useMemo(()=>({id:n,parentId:r}),[n,r])},t)}function Pz(e){let{children:t}=e;const n=I.useRef([]),r=I.useCallback(s=>{n.current=[...n.current,s]},[]),i=I.useCallback(s=>{n.current=n.current.filter(a=>a!==s)},[]),l=I.useState(()=>Gk())[0];return I.createElement(Kk.Provider,{value:I.useMemo(()=>({nodesRef:n,addNode:r,removeNode:i,events:l}),[n,r,i,l])},t)}function Zl(e){return"data-floating-ui-"+e}function vn(e){const t=I.useRef(e);return Qe(()=>{t.current=e}),t}const ax=Zl("safe-polygon");function th(e,t,n){return n&&!Vc(n)?0:typeof e=="number"?e:e?.[t]}function Az(e,t){t===void 0&&(t={});const{open:n,onOpenChange:r,dataRef:i,events:l,elements:{domReference:s,floating:a},refs:f}=e,{enabled:d=!0,delay:p=0,handleClose:g=null,mouseOnly:v=!1,restMs:m=0,move:E=!0}=t,S=so(),A=Wo(),y=vn(g),x=vn(p),k=I.useRef(),P=I.useRef(),D=I.useRef(),T=I.useRef(),F=I.useRef(!0),z=I.useRef(!1),G=I.useRef(()=>{}),q=I.useCallback(()=>{var H;const ee=(H=i.current.openEvent)==null?void 0:H.type;return ee?.includes("mouse")&&ee!=="mousedown"},[i]);I.useEffect(()=>{if(!d)return;function H(){clearTimeout(P.current),clearTimeout(T.current),F.current=!0}return l.on("dismiss",H),()=>{l.off("dismiss",H)}},[d,l]),I.useEffect(()=>{if(!d||!y.current||!n)return;function H(le){q()&&r(!1,le)}const ee=rn(a).documentElement;return ee.addEventListener("mouseleave",H),()=>{ee.removeEventListener("mouseleave",H)}},[a,n,r,d,y,i,q]);const B=I.useCallback(function(H,ee){ee===void 0&&(ee=!0);const le=th(x.current,"close",k.current);le&&!D.current?(clearTimeout(P.current),P.current=setTimeout(()=>r(!1,H),le)):ee&&(clearTimeout(P.current),r(!1,H))},[x,r]),Q=I.useCallback(()=>{G.current(),D.current=void 0},[]),U=I.useCallback(()=>{if(z.current){const H=rn(f.floating.current).body;H.style.pointerEvents="",H.removeAttribute(ax),z.current=!1}},[f]);return I.useEffect(()=>{if(!d)return;function H(){return i.current.openEvent?["click","mousedown"].includes(i.current.openEvent.type):!1}function ee($){if(clearTimeout(P.current),F.current=!1,v&&!Vc(k.current)||m>0&&th(x.current,"open")===0)return;const Z=th(x.current,"open",k.current);Z?P.current=setTimeout(()=>{r(!0,$)},Z):r(!0,$)}function le($){if(H())return;G.current();const Z=rn(a);if(clearTimeout(T.current),y.current){n||clearTimeout(P.current),D.current=y.current({...e,tree:S,x:$.clientX,y:$.clientY,onClose(){U(),Q(),B($)}});const ne=D.current;Z.addEventListener("mousemove",ne),G.current=()=>{Z.removeEventListener("mousemove",ne)};return}(k.current==="touch"?!Pt(a,$.relatedTarget):!0)&&B($)}function oe($){H()||y.current==null||y.current({...e,tree:S,x:$.clientX,y:$.clientY,onClose(){U(),Q(),B($)}})($)}if(hn(s)){const $=s;return n&&$.addEventListener("mouseleave",oe),a?.addEventListener("mouseleave",oe),E&&$.addEventListener("mousemove",ee,{once:!0}),$.addEventListener("mouseenter",ee),$.addEventListener("mouseleave",le),()=>{n&&$.removeEventListener("mouseleave",oe),a?.removeEventListener("mouseleave",oe),E&&$.removeEventListener("mousemove",ee),$.removeEventListener("mouseenter",ee),$.removeEventListener("mouseleave",le)}}},[s,a,d,e,v,m,E,B,Q,U,r,n,S,x,y,i]),Qe(()=>{var H;if(d&&n&&(H=y.current)!=null&&H.__options.blockPointerEvents&&q()){const oe=rn(a).body;if(oe.setAttribute(ax,""),oe.style.pointerEvents="none",z.current=!0,hn(s)&&a){var ee,le;const $=s,Z=S==null||(ee=S.nodesRef.current.find(_=>_.id===A))==null||(le=ee.context)==null?void 0:le.elements.floating;return Z&&(Z.style.pointerEvents=""),$.style.pointerEvents="auto",a.style.pointerEvents="auto",()=>{$.style.pointerEvents="",a.style.pointerEvents=""}}}},[d,n,A,a,s,S,y,i,q]),Qe(()=>{n||(k.current=void 0,Q(),U())},[n,Q,U]),I.useEffect(()=>()=>{Q(),clearTimeout(P.current),clearTimeout(T.current),U()},[d,s,Q,U]),I.useMemo(()=>{if(!d)return{};function H(ee){k.current=ee.pointerType}return{reference:{onPointerDown:H,onPointerEnter:H,onMouseMove(ee){n||m===0||(clearTimeout(T.current),T.current=setTimeout(()=>{F.current||r(!0,ee.nativeEvent)},m))}},floating:{onMouseEnter(){clearTimeout(P.current)},onMouseLeave(ee){l.emit("dismiss",{type:"mouseLeave",data:{returnFocus:!1}}),B(ee.nativeEvent,!1)}}}},[l,d,m,n,r,B])}function Dz(e,t){var n;let r=[],i=(n=e.find(l=>l.id===t))==null?void 0:n.parentId;for(;i;){const l=e.find(s=>s.id===i);i=l?.parentId,l&&(r=r.concat(l))}return r}function Ao(e,t){let n=e.filter(i=>{var l;return i.parentId===t&&((l=i.context)==null?void 0:l.open)}),r=n;for(;r.length;)r=e.filter(i=>{var l;return(l=r)==null?void 0:l.some(s=>{var a;return i.parentId===s.id&&((a=i.context)==null?void 0:a.open)})}),n=n.concat(r);return n}function Nz(e,t){let n,r=-1;function i(l,s){s>r&&(n=l,r=s),Ao(e,l).forEach(f=>{i(f.id,s+1)})}return i(t,0),e.find(l=>l.id===n)}let al=new WeakMap,Va=new WeakSet,Ga={},nh=0;const Mz=()=>typeof HTMLElement<"u"&&"inert"in HTMLElement.prototype,Qk=e=>e&&(e.host||Qk(e.parentNode)),Lz=(e,t)=>t.map(n=>{if(e.contains(n))return n;const r=Qk(n);return e.contains(r)?r:null}).filter(n=>n!=null);function Fz(e,t,n,r){const i="data-floating-ui-inert",l=r?"inert":n?"aria-hidden":null,s=Lz(t,e),a=new Set,f=new Set(s),d=[];Ga[i]||(Ga[i]=new WeakMap);const p=Ga[i];s.forEach(g),v(t),a.clear();function g(m){!m||a.has(m)||(a.add(m),m.parentNode&&g(m.parentNode))}function v(m){!m||f.has(m)||Array.prototype.forEach.call(m.children,E=>{if(a.has(E))v(E);else{const S=l?E.getAttribute(l):null,A=S!==null&&S!=="false",y=(al.get(E)||0)+1,x=(p.get(E)||0)+1;al.set(E,y),p.set(E,x),d.push(E),y===1&&A&&Va.add(E),x===1&&E.setAttribute(i,""),!A&&l&&E.setAttribute(l,"true")}})}return nh++,()=>{d.forEach(m=>{const E=(al.get(m)||0)-1,S=(p.get(m)||0)-1;al.set(m,E),p.set(m,S),E||(!Va.has(m)&&l&&m.removeAttribute(l),Va.delete(m)),S||m.removeAttribute(i)}),nh--,nh||(al=new WeakMap,al=new WeakMap,Va=new WeakSet,Ga={})}}function cx(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!1);const r=rn(e[0]).body;return Fz(e.concat(Array.from(r.querySelectorAll("[aria-live]"))),r,t,n)}const wv=()=>({getShadowRoot:!0,displayCheck:typeof ResizeObserver=="function"&&ResizeObserver.toString().includes("[native code]")?"full":"none"});function Yk(e,t){const n=mv(e,wv());t==="prev"&&n.reverse();const r=n.indexOf(Fi(rn(e)));return n.slice(r+1)[0]}function Xk(){return Yk(document.body,"next")}function Zk(){return Yk(document.body,"prev")}function ts(e,t){const n=t||e.currentTarget,r=e.relatedTarget;return!r||!Pt(n,r)}function zz(e){mv(e,wv()).forEach(n=>{n.dataset.tabindex=n.getAttribute("tabindex")||"",n.setAttribute("tabindex","-1")})}function $z(e){e.querySelectorAll("[data-tabindex]").forEach(n=>{const r=n.dataset.tabindex;delete n.dataset.tabindex,r?n.setAttribute("tabindex",r):n.removeAttribute("tabindex")})}const xv={border:0,clip:"rect(0 0 0 0)",height:"1px",margin:"-1px",overflow:"hidden",padding:0,position:"fixed",whiteSpace:"nowrap",width:"1px",top:0,left:0};let Bz;function fx(e){e.key==="Tab"&&(e.target,clearTimeout(Bz))}const Zc=I.forwardRef(function(t,n){const[r,i]=I.useState();Qe(()=>(Ek()&&i("button"),document.addEventListener("keydown",fx),()=>{document.removeEventListener("keydown",fx)}),[]);const l={ref:n,tabIndex:0,role:r,"aria-hidden":r?void 0:!0,[Zl("focus-guard")]:"",style:xv};return I.createElement("span",bs({},t,l))}),Jk=I.createContext(null);function Uz(e){let{id:t,root:n}=e===void 0?{}:e;const[r,i]=I.useState(null),l=Ns(),s=eb(),a=I.useMemo(()=>({id:t,root:n,portalContext:s,uniqueId:l}),[t,n,s,l]),f=I.useRef();return Qe(()=>()=>{r?.remove()},[r,a]),Qe(()=>{if(f.current===a)return;f.current=a;const{id:d,root:p,portalContext:g,uniqueId:v}=a,m=d?document.getElementById(d):null,E=Zl("portal");if(m){const S=document.createElement("div");S.id=v,S.setAttribute(E,""),m.appendChild(S),i(S)}else{let S=p||g?.portalNode;S&&!hn(S)&&(S=S.current),S=S||document.body;let A=null;d&&(A=document.createElement("div"),A.id=d,S.appendChild(A));const y=document.createElement("div");y.id=v,y.setAttribute(E,""),S=A||S,S.appendChild(y),i(y)}},[a]),r}function Sv(e){let{children:t,id:n,root:r=null,preserveTabOrder:i=!0}=e;const l=Uz({id:n,root:r}),[s,a]=I.useState(null),f=I.useRef(null),d=I.useRef(null),p=I.useRef(null),g=I.useRef(null),v=!!s&&!s.modal&&s.open&&i&&!!(r||l);return I.useEffect(()=>{if(!l||!i||s!=null&&s.modal)return;function m(E){l&&ts(E)&&(E.type==="focusin"?$z:zz)(l)}return l.addEventListener("focusin",m,!0),l.addEventListener("focusout",m,!0),()=>{l.removeEventListener("focusin",m,!0),l.removeEventListener("focusout",m,!0)}},[l,i,s?.modal]),I.createElement(Jk.Provider,{value:I.useMemo(()=>({preserveTabOrder:i,beforeOutsideRef:f,afterOutsideRef:d,beforeInsideRef:p,afterInsideRef:g,portalNode:l,setFocusManagerState:a}),[i,l])},v&&l&&I.createElement(Zc,{"data-type":"outside",ref:f,onFocus:m=>{if(ts(m,l)){var E;(E=p.current)==null||E.focus()}else{const S=Zk()||s?.refs.domReference.current;S?.focus()}}}),v&&l&&I.createElement("span",{"aria-owns":l.id,style:xv}),l&&nu.createPortal(t,l),v&&l&&I.createElement(Zc,{"data-type":"outside",ref:d,onFocus:m=>{if(ts(m,l)){var E;(E=g.current)==null||E.focus()}else{const S=Xk()||s?.refs.domReference.current;S?.focus(),s?.closeOnFocusOut&&s?.onOpenChange(!1,m.nativeEvent)}}}))}const eb=()=>I.useContext(Jk),Hz=I.forwardRef(function(t,n){return I.createElement("button",bs({},t,{type:"button",ref:n,tabIndex:-1,style:xv}))});function tb(e){const{context:t,children:n,disabled:r=!1,order:i=["content"],guards:l=!0,initialFocus:s=0,returnFocus:a=!0,modal:f=!0,visuallyHiddenDismiss:d=!1,closeOnFocusOut:p=!0}=e,{open:g,refs:v,nodeId:m,onOpenChange:E,events:S,dataRef:A,elements:{domReference:y,floating:x}}=t,k=Mz()?l:!0,P=vn(i),D=vn(s),T=vn(a),F=so(),z=eb(),G=typeof s=="number"&&s<0,q=I.useRef(null),B=I.useRef(null),Q=I.useRef(!1),U=I.useRef(null),H=I.useRef(!1),ee=z!=null,le=y&&y.getAttribute("role")==="combobox"&&Ck(y)&&G,oe=I.useCallback(function(ne){return ne===void 0&&(ne=x),ne?mv(ne,wv()):[]},[x]),$=I.useCallback(ne=>{const ae=oe(ne);return P.current.map(O=>y&&O==="reference"?y:x&&O==="floating"?x:ae).filter(Boolean).flat()},[y,x,P,oe]);I.useEffect(()=>{if(r||!f)return;function ne(O){if(O.key==="Tab"){Pt(x,Fi(rn(x)))&&oe().length===0&&!le&&$t(O);const de=$(),Re=fv(O);P.current[0]==="reference"&&Re===y&&($t(O),O.shiftKey?oi(de[de.length-1]):oi(de[1])),P.current[1]==="floating"&&Re===x&&O.shiftKey&&($t(O),oi(de[0]))}}const ae=rn(x);return ae.addEventListener("keydown",ne),()=>{ae.removeEventListener("keydown",ne)}},[r,y,x,f,P,v,le,oe,$]),I.useEffect(()=>{if(r||!p)return;function ne(){H.current=!0,setTimeout(()=>{H.current=!1})}function ae(O){const de=O.relatedTarget;queueMicrotask(()=>{const Re=!(Pt(y,de)||Pt(x,de)||Pt(de,x)||Pt(z?.portalNode,de)||de!=null&&de.hasAttribute(Zl("focus-guard"))||F&&(Ao(F.nodesRef.current,m).find(me=>{var Ae,Se;return Pt((Ae=me.context)==null?void 0:Ae.elements.floating,de)||Pt((Se=me.context)==null?void 0:Se.elements.domReference,de)})||Dz(F.nodesRef.current,m).find(me=>{var Ae,Se;return((Ae=me.context)==null?void 0:Ae.elements.floating)===de||((Se=me.context)==null?void 0:Se.elements.domReference)===de})));de&&Re&&!H.current&&de!==U.current&&(Q.current=!0,E(!1,O))})}if(x&&Kl(y))return y.addEventListener("focusout",ae),y.addEventListener("pointerdown",ne),!f&&x.addEventListener("focusout",ae),()=>{y.removeEventListener("focusout",ae),y.removeEventListener("pointerdown",ne),!f&&x.removeEventListener("focusout",ae)}},[r,y,x,f,m,F,z,E,p]),I.useEffect(()=>{var ne;if(r)return;const ae=Array.from((z==null||(ne=z.portalNode)==null?void 0:ne.querySelectorAll("["+Zl("portal")+"]"))||[]);if(x){const O=[x,...ae,q.current,B.current,P.current.includes("reference")||le?y:null].filter(Re=>Re!=null),de=f?cx(O,k,!k):cx(O);return()=>{de()}}},[r,y,x,f,P,z,le,k]),Qe(()=>{if(r||!x)return;const ne=rn(x),ae=Fi(ne);queueMicrotask(()=>{const O=$(x),de=D.current,Re=(typeof de=="number"?O[de]:de.current)||x,me=Pt(x,ae);!G&&!me&&g&&oi(Re,{preventScroll:Re===x})})},[r,g,x,G,$,D]),Qe(()=>{if(r||!x)return;let ne=!1;const ae=rn(x),O=Fi(ae),de=A.current;U.current=O;function Re(me){if(me.type==="escapeKey"&&v.domReference.current&&(U.current=v.domReference.current),["referencePress","escapeKey"].includes(me.type))return;const Ae=me.data.returnFocus;typeof Ae=="object"?(Q.current=!1,ne=Ae.preventScroll):Q.current=!Ae}return S.on("dismiss",Re),()=>{S.off("dismiss",Re);const me=Fi(ae);(Pt(x,me)||F&&Ao(F.nodesRef.current,m).some(Se=>{var Le;return Pt((Le=Se.context)==null?void 0:Le.elements.floating,me)})||de.openEvent&&["click","mousedown"].includes(de.openEvent.type))&&v.domReference.current&&(U.current=v.domReference.current),T.current&&Kl(U.current)&&!Q.current&&oi(U.current,{cancelPrevious:!1,preventScroll:ne})}},[r,x,T,A,v,S,F,m]),Qe(()=>{if(!(r||!z))return z.setFocusManagerState({modal:f,closeOnFocusOut:p,open:g,onOpenChange:E,refs:v}),()=>{z.setFocusManagerState(null)}},[r,z,f,g,E,v,p]),Qe(()=>{if(!r&&x&&typeof MutationObserver=="function"&&!G){const ne=()=>{const O=x.getAttribute("tabindex");P.current.includes("floating")||Fi(rn(x))!==v.domReference.current&&oe().length===0?O!=="0"&&x.setAttribute("tabindex","0"):O!=="-1"&&x.setAttribute("tabindex","-1")};ne();const ae=new MutationObserver(ne);return ae.observe(x,{childList:!0,subtree:!0,attributes:!0}),()=>{ae.disconnect()}}},[r,x,v,P,oe,G]);function Z(ne){return r||!d||!f?null:I.createElement(Hz,{ref:ne==="start"?q:B,onClick:ae=>E(!1,ae.nativeEvent)},typeof d=="string"?d:"Dismiss")}const _=!r&&k&&!le&&(ee||f);return I.createElement(I.Fragment,null,_&&I.createElement(Zc,{"data-type":"inside",ref:z?.beforeInsideRef,onFocus:ne=>{if(f){const O=$();oi(i[0]==="reference"?O[0]:O[O.length-1])}else if(z!=null&&z.preserveTabOrder&&z.portalNode)if(Q.current=!1,ts(ne,z.portalNode)){const O=Xk()||y;O?.focus()}else{var ae;(ae=z.beforeOutsideRef.current)==null||ae.focus()}}}),!le&&Z("start"),n,Z("end"),_&&I.createElement(Zc,{"data-type":"inside",ref:z?.afterInsideRef,onFocus:ne=>{if(f)oi($()[0]);else if(z!=null&&z.preserveTabOrder&&z.portalNode)if(p&&(Q.current=!0),ts(ne,z.portalNode)){const O=Zk()||y;O?.focus()}else{var ae;(ae=z.afterOutsideRef.current)==null||ae.focus()}}}))}const rh=new Set,nb=I.forwardRef(function(t,n){let{lockScroll:r=!1,...i}=t;const l=Ns();return Qe(()=>{if(!r)return;rh.add(l);const s=/iP(hone|ad|od)|iOS/.test(cv()),a=document.body.style,d=Math.round(document.documentElement.getBoundingClientRect().left)+document.documentElement.scrollLeft?"paddingLeft":"paddingRight",p=window.innerWidth-document.documentElement.clientWidth,g=a.left?parseFloat(a.left):window.pageXOffset,v=a.top?parseFloat(a.top):window.pageYOffset;if(a.overflow="hidden",p&&(a[d]=p+"px"),s){var m,E;const S=((m=window.visualViewport)==null?void 0:m.offsetLeft)||0,A=((E=window.visualViewport)==null?void 0:E.offsetTop)||0;Object.assign(a,{position:"fixed",top:-(v-Math.floor(A))+"px",left:-(g-Math.floor(S))+"px",right:"0"})}return()=>{rh.delete(l),rh.size===0&&(Object.assign(a,{overflow:"",[d]:""}),s&&(Object.assign(a,{position:"",top:"",left:"",right:""}),window.scrollTo(g,v)))}},[l,r]),I.createElement("div",bs({ref:n},i,{style:{position:"fixed",overflow:"auto",top:0,right:0,bottom:0,left:0,...i.style}}))});function dx(e){return Kl(e.target)&&e.target.tagName==="BUTTON"}function px(e){return Ck(e)}function jz(e,t){t===void 0&&(t={});const{open:n,onOpenChange:r,dataRef:i,elements:{domReference:l}}=e,{enabled:s=!0,event:a="click",toggle:f=!0,ignoreMouse:d=!1,keyboardHandlers:p=!0}=t,g=I.useRef(),v=I.useRef(!1);return I.useMemo(()=>s?{reference:{onPointerDown(m){g.current=m.pointerType},onMouseDown(m){m.button===0&&(Vc(g.current,!0)&&d||a!=="click"&&(n&&f&&(!i.current.openEvent||i.current.openEvent.type==="mousedown")?r(!1,m.nativeEvent):(m.preventDefault(),r(!0,m.nativeEvent))))},onClick(m){if(a==="mousedown"&&g.current){g.current=void 0;return}Vc(g.current,!0)&&d||(n&&f&&(!i.current.openEvent||i.current.openEvent.type==="click")?r(!1,m.nativeEvent):r(!0,m.nativeEvent))},onKeyDown(m){g.current=void 0,!(m.defaultPrevented||!p||dx(m))&&(m.key===" "&&!px(l)&&(m.preventDefault(),v.current=!0),m.key==="Enter"&&r(!(n&&f),m.nativeEvent))},onKeyUp(m){m.defaultPrevented||!p||dx(m)||px(l)||m.key===" "&&v.current&&(v.current=!1,r(!(n&&f),m.nativeEvent))}}}:{},[s,i,a,d,p,l,f,n,r])}const Wz=SS["useInsertionEffect".toString()],Vz=Wz||(e=>e());function Zi(e){const t=I.useRef(()=>{});return Vz(()=>{t.current=e}),I.useCallback(function(){for(var n=arguments.length,r=new Array(n),i=0;i<n;i++)r[i]=arguments[i];return t.current==null?void 0:t.current(...r)},[])}const Gz={pointerdown:"onPointerDown",mousedown:"onMouseDown",click:"onClick"},qz={pointerdown:"onPointerDownCapture",mousedown:"onMouseDownCapture",click:"onClickCapture"},Kz=e=>{var t,n;return{escapeKeyBubbles:typeof e=="boolean"?e:(t=e?.escapeKey)!=null?t:!1,outsidePressBubbles:typeof e=="boolean"?e:(n=e?.outsidePress)!=null?n:!0}};function rb(e,t){t===void 0&&(t={});const{open:n,onOpenChange:r,events:i,nodeId:l,elements:{reference:s,domReference:a,floating:f},dataRef:d}=e,{enabled:p=!0,escapeKey:g=!0,outsidePress:v=!0,outsidePressEvent:m="pointerdown",referencePress:E=!1,referencePressEvent:S="pointerdown",ancestorScroll:A=!1,bubbles:y}=t,x=so(),k=Wo()!=null,P=Zi(typeof v=="function"?v:()=>!1),D=typeof v=="function"?P:v,T=I.useRef(!1),{escapeKeyBubbles:F,outsidePressBubbles:z}=Kz(y),G=Zi(B=>{if(!n||!p||!g||B.key!=="Escape")return;const Q=x?Ao(x.nodesRef.current,l):[];if(!F&&(B.stopPropagation(),Q.length>0)){let U=!0;if(Q.forEach(H=>{var ee;if((ee=H.context)!=null&&ee.open&&!H.context.dataRef.current.__escapeKeyBubbles){U=!1;return}}),!U)return}i.emit("dismiss",{type:"escapeKey",data:{returnFocus:{preventScroll:!1}}}),r(!1,v4(B)?B.nativeEvent:B)}),q=Zi(B=>{const Q=T.current;if(T.current=!1,Q||typeof D=="function"&&!D(B))return;const U=fv(B),H="["+Zl("inert")+"]",ee=rn(f).querySelectorAll(H);let le=hn(U)?U:null;for(;le&&!d4(le);){const Z=h4(le);if(Z===rn(f).body||!hn(Z))break;le=Z}if(ee.length&&hn(U)&&!y4(U)&&!Pt(U,f)&&Array.from(ee).every(Z=>!Pt(le,Z)))return;if(Kl(U)&&f){const Z=U.clientWidth>0&&U.scrollWidth>U.clientWidth,_=U.clientHeight>0&&U.scrollHeight>U.clientHeight;let ne=_&&B.offsetX>U.clientWidth;if(_&&p4(U).direction==="rtl"&&(ne=B.offsetX<=U.offsetWidth-U.clientWidth),ne||Z&&B.offsetY>U.clientHeight)return}const oe=x&&Ao(x.nodesRef.current,l).some(Z=>{var _;return Yp(B,(_=Z.context)==null?void 0:_.elements.floating)});if(Yp(B,f)||Yp(B,a)||oe)return;const $=x?Ao(x.nodesRef.current,l):[];if($.length>0){let Z=!0;if($.forEach(_=>{var ne;if((ne=_.context)!=null&&ne.open&&!_.context.dataRef.current.__outsidePressBubbles){Z=!1;return}}),!Z)return}i.emit("dismiss",{type:"outsidePress",data:{returnFocus:k?{preventScroll:!0}:xk(B)||Sk(B)}}),r(!1,B)});return I.useEffect(()=>{if(!n||!p)return;d.current.__escapeKeyBubbles=F,d.current.__outsidePressBubbles=z;function B(H){r(!1,H)}const Q=rn(f);g&&Q.addEventListener("keydown",G),D&&Q.addEventListener(m,q);let U=[];return A&&(hn(a)&&(U=Xi(a)),hn(f)&&(U=U.concat(Xi(f))),!hn(s)&&s&&s.contextElement&&(U=U.concat(Xi(s.contextElement)))),U=U.filter(H=>{var ee;return H!==((ee=Q.defaultView)==null?void 0:ee.visualViewport)}),U.forEach(H=>{H.addEventListener("scroll",B,{passive:!0})}),()=>{g&&Q.removeEventListener("keydown",G),D&&Q.removeEventListener(m,q),U.forEach(H=>{H.removeEventListener("scroll",B)})}},[d,f,a,s,g,D,m,n,r,A,p,F,z,G,q]),I.useEffect(()=>{T.current=!1},[D,m]),I.useMemo(()=>p?{reference:{onKeyDown:G,[Gz[S]]:B=>{E&&(i.emit("dismiss",{type:"referencePress",data:{returnFocus:!1}}),r(!1,B.nativeEvent))}},floating:{onKeyDown:G,[qz[m]]:()=>{T.current=!0}}}:{},[p,i,E,m,S,r,G])}function Ev(e){var t;e===void 0&&(e={});const{open:n=!1,onOpenChange:r,nodeId:i}=e,[l,s]=I.useState(null),a=((t=e.elements)==null?void 0:t.reference)||l,f=rz(e),d=so(),p=Zi((P,D)=>{P&&(v.current.openEvent=D),r?.(P,D)}),g=I.useRef(null),v=I.useRef({}),m=I.useState(()=>Gk())[0],E=Ns(),S=I.useCallback(P=>{const D=hn(P)?{getBoundingClientRect:()=>P.getBoundingClientRect(),contextElement:P}:P;f.refs.setReference(D)},[f.refs]),A=I.useCallback(P=>{(hn(P)||P===null)&&(g.current=P,s(P)),(hn(f.refs.reference.current)||f.refs.reference.current===null||P!==null&&!hn(P))&&f.refs.setReference(P)},[f.refs]),y=I.useMemo(()=>({...f.refs,setReference:A,setPositionReference:S,domReference:g}),[f.refs,A,S]),x=I.useMemo(()=>({...f.elements,domReference:a}),[f.elements,a]),k=I.useMemo(()=>({...f,refs:y,elements:x,dataRef:v,nodeId:i,floatingId:E,events:m,open:n,onOpenChange:p}),[f,i,E,m,n,p,y,x]);return Qe(()=>{const P=d?.nodesRef.current.find(D=>D.id===i);P&&(P.context=k)}),I.useMemo(()=>({...f,context:k,refs:y,elements:x}),[f,y,x,k])}function ih(e,t,n){const r=new Map;return{...n==="floating"&&{tabIndex:-1},...e,...t.map(i=>i?i[n]:null).concat(e).reduce((i,l)=>(l&&Object.entries(l).forEach(s=>{let[a,f]=s;if(a.indexOf("on")===0){if(r.has(a)||r.set(a,[]),typeof f=="function"){var d;(d=r.get(a))==null||d.push(f),i[a]=function(){for(var p,g=arguments.length,v=new Array(g),m=0;m<g;m++)v[m]=arguments[m];return(p=r.get(a))==null?void 0:p.map(E=>E(...v)).find(E=>E!==void 0)}}}else i[a]=f}),i),{})}}function ib(e){e===void 0&&(e=[]);const t=e,n=I.useCallback(l=>ih(l,e,"reference"),t),r=I.useCallback(l=>ih(l,e,"floating"),t),i=I.useCallback(l=>ih(l,e,"item"),e.map(l=>l?.item));return I.useMemo(()=>({getReferenceProps:n,getFloatingProps:r,getItemProps:i}),[n,r,i])}let hx=!1;function Xf(e,t,n){switch(e){case"vertical":return t;case"horizontal":return n;default:return t||n}}function gx(e,t){return Xf(t,e===yv||e===Yf,e===Xl||e===Ds)}function oh(e,t,n){return Xf(t,e===Yf,n?e===Xl:e===Ds)||e==="Enter"||e==" "||e===""}function Qz(e,t,n){return Xf(t,n?e===Xl:e===Ds,e===Yf)}function mx(e,t,n){return Xf(t,n?e===Ds:e===Xl,e===yv)}function Yz(e,t){const{open:n,onOpenChange:r,refs:i,elements:{domReference:l,floating:s}}=e,{listRef:a,activeIndex:f,onNavigate:d=()=>{},enabled:p=!0,selectedIndex:g=null,allowEscape:v=!1,loop:m=!1,nested:E=!1,rtl:S=!1,virtual:A=!1,focusItemOnOpen:y="auto",focusItemOnHover:x=!0,openOnArrowKeyDown:k=!0,disabledIndices:P=void 0,orientation:D="vertical",cols:T=1,scrollItemIntoView:F=!0,virtualItemRef:z}=t,G=Wo(),q=so(),B=Zi(d),Q=I.useRef(y),U=I.useRef(g??-1),H=I.useRef(null),ee=I.useRef(!0),le=I.useRef(B),oe=I.useRef(!!s),$=I.useRef(!1),Z=I.useRef(!1),_=vn(P),ne=vn(n),ae=vn(F),[O,de]=I.useState(),[Re,me]=I.useState(),Ae=Zi(function(Te,Be,Fe){Fe===void 0&&(Fe=!1);const qe=Te.current[Be.current];qe&&(A?(de(qe.id),q?.events.emit("virtualfocus",qe),z&&(z.current=qe)):oi(qe,{preventScroll:!0,sync:m4()&&Ek()?hx||$.current:!1}),requestAnimationFrame(()=>{const yt=ae.current;yt&&qe&&(Fe||!ee.current)&&(qe.scrollIntoView==null||qe.scrollIntoView(typeof yt=="boolean"?{block:"nearest",inline:"nearest"}:yt))}))});Qe(()=>{document.createElement("div").focus({get preventScroll(){return hx=!0,!1}})},[]),Qe(()=>{p&&(n&&s?Q.current&&g!=null&&(Z.current=!0,B(g)):oe.current&&(U.current=-1,le.current(null)))},[p,n,s,g,B]),Qe(()=>{if(p&&n&&s)if(f==null){if($.current=!1,g!=null)return;if(oe.current&&(U.current=-1,Ae(a,U)),!oe.current&&Q.current&&(H.current!=null||Q.current===!0&&H.current==null)){let Te=0;const Be=()=>{a.current[0]==null?(Te<2&&(Te?requestAnimationFrame:queueMicrotask)(Be),Te++):(U.current=H.current==null||oh(H.current,D,S)||E?Jp(a,_.current):lx(a,_.current),H.current=null,B(U.current))};Be()}}else es(a,f)||(U.current=f,Ae(a,U,Z.current),Z.current=!1)},[p,n,s,f,g,E,a,D,S,B,Ae,_]),Qe(()=>{var Te,Be;if(!p||s||!q||A||!oe.current)return;const Fe=q.nodesRef.current,qe=(Te=Fe.find(he=>he.id===G))==null||(Be=Te.context)==null?void 0:Be.elements.floating,yt=Fi(rn(s)),Wn=Fe.some(he=>he.context&&Pt(he.context.elements.floating,yt));qe&&!Wn&&ee.current&&qe.focus({preventScroll:!0})},[p,s,q,G,A]),Qe(()=>{if(!p||!q||!A||G)return;function Te(Be){me(Be.id),z&&(z.current=Be)}return q.events.on("virtualfocus",Te),()=>{q.events.off("virtualfocus",Te)}},[p,q,A,G,z]),Qe(()=>{le.current=B,oe.current=!!s}),Qe(()=>{n||(H.current=null)},[n]);const Se=f!=null,Le=I.useMemo(()=>{function Te(Fe){if(!n)return;const qe=a.current.indexOf(Fe);qe!==-1&&B(qe)}return{onFocus(Fe){let{currentTarget:qe}=Fe;Te(qe)},onClick:Fe=>{let{currentTarget:qe}=Fe;return qe.focus({preventScroll:!0})},...x&&{onMouseMove(Fe){let{currentTarget:qe}=Fe;Te(qe)},onPointerLeave(Fe){let{pointerType:qe}=Fe;!ee.current||qe==="touch"||(U.current=-1,Ae(a,U),B(null),A||oi(i.floating.current,{preventScroll:!0}))}}}},[n,i,Ae,x,a,B,A]);return I.useMemo(()=>{if(!p)return{};const Te=_.current;function Be(he){if(ee.current=!1,$.current=!0,!ne.current&&he.currentTarget===i.floating.current)return;if(E&&mx(he.key,D,S)){$t(he),r(!1,he.nativeEvent),Kl(l)&&!A&&l.focus();return}const at=U.current,Mt=Jp(a,Te),Cn=lx(a,Te);if(he.key==="Home"&&($t(he),U.current=Mt,B(U.current)),he.key==="End"&&($t(he),U.current=Cn,B(U.current)),!(T>1&&(U.current=Ez(a,{event:he,orientation:D,loop:m,cols:T,disabledIndices:Te,minIndex:Mt,maxIndex:Cn,prevIndex:U.current,stopEvent:!0}),B(U.current),D==="both"))&&gx(he.key,D)){if($t(he),n&&!A&&Fi(he.currentTarget.ownerDocument)===he.currentTarget){U.current=oh(he.key,D,S)?Mt:Cn,B(U.current);return}oh(he.key,D,S)?m?U.current=at>=Cn?v&&at!==a.current.length?-1:Mt:Kt(a,{startingIndex:at,disabledIndices:Te}):U.current=Math.min(Cn,Kt(a,{startingIndex:at,disabledIndices:Te})):m?U.current=at<=Mt?v&&at!==-1?a.current.length:Cn:Kt(a,{startingIndex:at,decrement:!0,disabledIndices:Te}):U.current=Math.max(Mt,Kt(a,{startingIndex:at,decrement:!0,disabledIndices:Te})),es(a,U.current)?B(null):B(U.current)}}function Fe(he){y==="auto"&&xk(he.nativeEvent)&&(Q.current=!0)}function qe(he){Q.current=y,y==="auto"&&Sk(he.nativeEvent)&&(Q.current=!0)}const yt=A&&n&&Se&&{"aria-activedescendant":Re||O},Wn=a.current.find(he=>he?.id===O);return{reference:{...yt,onKeyDown(he){ee.current=!1;const at=he.key.indexOf("Arrow")===0,Mt=Qz(he.key,D,S),Cn=mx(he.key,D,S),kn=gx(he.key,D),Ht=(E?Mt:kn)||he.key==="Enter"||he.key.trim()==="";if(A&&n){const Ir=q?.nodesRef.current.find(V=>V.parentId==null),Vn=q&&Ir?Nz(q.nodesRef.current,Ir.id):null;if(at&&Vn&&z){const V=new KeyboardEvent("keydown",{key:he.key,bubbles:!0});if(Mt||Cn){var Tr,Gr;const re=((Tr=Vn.context)==null?void 0:Tr.elements.domReference)===he.currentTarget,pe=Cn&&!re?(Gr=Vn.context)==null?void 0:Gr.elements.domReference:Mt?Wn:null;pe&&($t(he),pe.dispatchEvent(V),me(void 0))}if(kn&&Vn.context&&Vn.context.open&&Vn.parentId&&he.currentTarget!==Vn.context.elements.domReference){var Go;$t(he),(Go=Vn.context.elements.domReference)==null||Go.dispatchEvent(V);return}}return Be(he)}if(!(!n&&!k&&at)){if(Ht&&(H.current=E&&kn?null:he.key),E){Mt&&($t(he),n?(U.current=Jp(a,Te),B(U.current)):r(!0,he.nativeEvent));return}kn&&(g!=null&&(U.current=g),$t(he),!n&&k?r(!0,he.nativeEvent):Be(he),n&&B(U.current))}},onFocus(){n&&B(null)},onPointerDown:qe,onMouseDown:Fe,onClick:Fe},floating:{"aria-orientation":D==="both"?void 0:D,...yt,onKeyDown:Be,onPointerMove(){ee.current=!0}},item:Le}},[l,i,O,Re,_,ne,a,p,D,S,A,n,Se,E,g,k,v,T,m,y,B,r,Le,q,z])}function Xz(e,t){t===void 0&&(t={});const{open:n,floatingId:r}=e,{enabled:i=!0,role:l="dialog"}=t,s=Ns();return I.useMemo(()=>{const a={id:r,role:l};return i?l==="tooltip"?{reference:{"aria-describedby":n?r:void 0},floating:a}:{reference:{"aria-expanded":n?"true":"false","aria-haspopup":l==="alertdialog"?"dialog":l,"aria-controls":n?r:void 0,...l==="listbox"&&{role:"combobox"},...l==="menu"&&{id:s}},floating:{...a,...l==="menu"&&{"aria-labelledby":s}}}:{}},[i,l,n,r,s])}const vx=e=>e.replace(/[A-Z]+(?![a-z])|[A-Z]/g,(t,n)=>(n?"-":"")+t.toLowerCase());function cl(e,t){return typeof e=="function"?e(t):e}function Zz(e,t){const[n,r]=I.useState(e);return e&&!n&&r(!0),I.useEffect(()=>{if(!e){const i=setTimeout(()=>r(!1),t);return()=>clearTimeout(i)}},[e,t]),n}function Jz(e,t){t===void 0&&(t={});const{open:n,elements:{floating:r}}=e,{duration:i=250}=t,s=(typeof i=="number"?i:i.close)||0,[a,f]=I.useState(!1),[d,p]=I.useState("unmounted"),g=Zz(n,s);return Qe(()=>{a&&!g&&p("unmounted")},[a,g]),Qe(()=>{if(r)if(n){p("initial");const v=requestAnimationFrame(()=>{p("open")});return()=>{cancelAnimationFrame(v)}}else f(!0),p("close")},[n,r]),{isMounted:g,status:d}}function Cv(e,t){t===void 0&&(t={});const{initial:n={opacity:0},open:r,close:i,common:l,duration:s=250}=t,a=e.placement,f=a.split("-")[0],d=I.useMemo(()=>({side:f,placement:a}),[f,a]),p=typeof s=="number",g=(p?s:s.open)||0,v=(p?s:s.close)||0,[m,E]=I.useState(()=>({...cl(l,d),...cl(n,d)})),{isMounted:S,status:A}=Jz(e,{duration:s}),y=vn(n),x=vn(r),k=vn(i),P=vn(l);return Qe(()=>{const D=cl(y.current,d),T=cl(k.current,d),F=cl(P.current,d),z=cl(x.current,d)||Object.keys(D).reduce((G,q)=>(G[q]="",G),{});if(A==="initial"&&E(G=>({transitionProperty:G.transitionProperty,...F,...D})),A==="open"&&E({transitionProperty:Object.keys(z).map(vx).join(","),transitionDuration:g+"ms",...F,...z}),A==="close"){const G=T||D;E({transitionProperty:Object.keys(G).map(vx).join(","),transitionDuration:v+"ms",...F,...G})}},[v,k,y,x,P,g,A,d]),{isMounted:S,styles:m}}function e6(e,t){var n;const{open:r,dataRef:i}=e,{listRef:l,activeIndex:s,onMatch:a,onTypingChange:f,enabled:d=!0,findMatch:p=null,resetMs:g=750,ignoreKeys:v=[],selectedIndex:m=null}=t,E=I.useRef(),S=I.useRef(""),A=I.useRef((n=m??s)!=null?n:-1),y=I.useRef(null),x=Zi(a),k=Zi(f),P=vn(p),D=vn(v);return Qe(()=>{r&&(clearTimeout(E.current),y.current=null,S.current="")},[r]),Qe(()=>{if(r&&S.current===""){var T;A.current=(T=m??s)!=null?T:-1}},[r,m,s]),I.useMemo(()=>{if(!d)return{};function T(G){G?i.current.typing||(i.current.typing=G,k(G)):i.current.typing&&(i.current.typing=G,k(G))}function F(G,q,B){const Q=P.current?P.current(q,B):q.find(U=>U?.toLocaleLowerCase().indexOf(B.toLocaleLowerCase())===0);return Q?G.indexOf(Q):-1}function z(G){const q=l.current;if(S.current.length>0&&S.current[0]!==" "&&(F(q,q,S.current)===-1?T(!1):G.key===" "&&$t(G)),q==null||D.current.includes(G.key)||G.key.length!==1||G.ctrlKey||G.metaKey||G.altKey)return;r&&G.key!==" "&&($t(G),T(!0)),q.every(H=>{var ee,le;return H?((ee=H[0])==null?void 0:ee.toLocaleLowerCase())!==((le=H[1])==null?void 0:le.toLocaleLowerCase()):!0})&&S.current===G.key&&(S.current="",A.current=y.current),S.current+=G.key,clearTimeout(E.current),E.current=setTimeout(()=>{S.current="",A.current=y.current,T(!1)},g);const Q=A.current,U=F(q,[...q.slice((Q||0)+1),...q.slice(0,(Q||0)+1)],S.current);U!==-1?(x(U),y.current=U):G.key!==" "&&(S.current="",T(!1))}return{reference:{onKeyDown:z},floating:{onKeyDown:z,onKeyUp(G){G.key===" "&&T(!1)}}}},[d,r,i,l,g,D,P,x,k])}function yx(e,t){const[n,r]=e;let i=!1;const l=t.length;for(let s=0,a=l-1;s<l;a=s++){const[f,d]=t[s]||[0,0],[p,g]=t[a]||[0,0];d>=r!=g>=r&&n<=(p-f)*(r-d)/(g-d)+f&&(i=!i)}return i}function t6(e,t){return e[0]>=t.x&&e[0]<=t.x+t.width&&e[1]>=t.y&&e[1]<=t.y+t.height}function n6(e){e===void 0&&(e={});const{buffer:t=.5,blockPointerEvents:n=!1,requireIntent:r=!0}=e;let i,l=!1,s=null,a=null,f=performance.now();function d(g,v){const m=performance.now(),E=m-f;if(s===null||a===null||E===0)return s=g,a=v,f=m,null;const S=g-s,A=v-a,x=Math.sqrt(S*S+A*A)/E;return s=g,a=v,f=m,x}const p=g=>{let{x:v,y:m,placement:E,elements:S,onClose:A,nodeId:y,tree:x}=g;return function(P){function D(){clearTimeout(i),A()}if(clearTimeout(i),!S.domReference||!S.floating||E==null||v==null||m==null)return;const{clientX:T,clientY:F}=P,z=[T,F],G=fv(P),q=P.type==="mouseleave",B=Pt(S.floating,G),Q=Pt(S.domReference,G),U=S.domReference.getBoundingClientRect(),H=S.floating.getBoundingClientRect(),ee=E.split("-")[0],le=v>H.right-H.width/2,oe=m>H.bottom-H.height/2,$=t6(z,U),Z=H.width>U.width,_=H.height>U.height,ne=(Z?U:H).left,ae=(Z?U:H).right,O=(_?U:H).top,de=(_?U:H).bottom;if(B&&(l=!0,!q))return;if(Q&&(l=!1),Q&&!q){l=!0;return}if(q&&hn(P.relatedTarget)&&Pt(S.floating,P.relatedTarget)||x&&Ao(x.nodesRef.current,y).some(Ae=>{let{context:Se}=Ae;return Se?.open}))return;if(ee==="top"&&m>=U.bottom-1||ee==="bottom"&&m<=U.top+1||ee==="left"&&v>=U.right-1||ee==="right"&&v<=U.left+1)return D();let Re=[];switch(ee){case"top":Re=[[ne,U.top+1],[ne,H.bottom-1],[ae,H.bottom-1],[ae,U.top+1]];break;case"bottom":Re=[[ne,H.top+1],[ne,U.bottom-1],[ae,U.bottom-1],[ae,H.top+1]];break;case"left":Re=[[H.right-1,de],[H.right-1,O],[U.left+1,O],[U.left+1,de]];break;case"right":Re=[[U.right-1,de],[U.right-1,O],[H.left+1,O],[H.left+1,de]];break}function me(Ae){let[Se,Le]=Ae;switch(ee){case"top":{const Te=[Z?Se+t/2:le?Se+t*4:Se-t*4,Le+t+1],Be=[Z?Se-t/2:le?Se+t*4:Se-t*4,Le+t+1],Fe=[[H.left,le||Z?H.bottom-t:H.top],[H.right,le?Z?H.bottom-t:H.top:H.bottom-t]];return[Te,Be,...Fe]}case"bottom":{const Te=[Z?Se+t/2:le?Se+t*4:Se-t*4,Le-t],Be=[Z?Se-t/2:le?Se+t*4:Se-t*4,Le-t],Fe=[[H.left,le||Z?H.top+t:H.bottom],[H.right,le?Z?H.top+t:H.bottom:H.top+t]];return[Te,Be,...Fe]}case"left":{const Te=[Se+t+1,_?Le+t/2:oe?Le+t*4:Le-t*4],Be=[Se+t+1,_?Le-t/2:oe?Le+t*4:Le-t*4];return[...[[oe||_?H.right-t:H.left,H.top],[oe?_?H.right-t:H.left:H.right-t,H.bottom]],Te,Be]}case"right":{const Te=[Se-t,_?Le+t/2:oe?Le+t*4:Le-t*4],Be=[Se-t,_?Le-t/2:oe?Le+t*4:Le-t*4],Fe=[[oe||_?H.left+t:H.right,H.top],[oe?_?H.left+t:H.right:H.left+t,H.bottom]];return[Te,Be,...Fe]}}}if(!yx([T,F],Re)){if(l&&!$)return D();if(!q&&r){const Ae=d(P.clientX,P.clientY);if(Ae!==null&&Ae<.1)return D()}yx([T,F],me([v,m]))?!l&&r&&(i=window.setTimeout(D,40)):D()}}};return p.__options={blockPointerEvents:n},p}const r6=({infoVisible:e,setInfoVisible:t})=>{const{refs:n,context:r}=Ev({open:e,onOpenChange:t}),i=rb(r,{outsidePressEvent:"mousedown"}),{isMounted:l,styles:s}=Cv(r),{getFloatingProps:a}=ib([i]);return W(En,{children:l&&W(Sv,{children:W(nb,{lockScroll:!0,className:"useful-controls-dialog-overlay","data-open":e,style:s,children:W(tb,{context:r,children:we("div",{ref:n.setFloating,...a(),className:"useful-controls-dialog",style:s,children:[we("div",{className:"useful-controls-dialog-title",children:[W("p",{children:ut.ui_usefulcontrols||"Useful controls"}),W("div",{className:"useful-controls-dialog-close",onClick:()=>t(!1),children:W("svg",{xmlns:"http://www.w3.org/2000/svg",height:"1em",viewBox:"0 0 400 528",children:W("path",{d:"M376.6 84.5c11.3-13.6 9.5-33.8-4.1-45.1s-33.8-9.5-45.1 4.1L192 206 56.6 43.5C45.3 29.9 25.1 28.1 11.5 39.4S-3.9 70.9 7.4 84.5L150.3 256 7.4 427.5c-11.3 13.6-9.5 33.8 4.1 45.1s33.8 9.5 45.1-4.1L192 306 327.4 468.5c11.3 13.6 31.5 15.4 45.1 4.1s15.4-31.5 4.1-45.1L233.7 256 376.6 84.5z"})})})]}),we("div",{className:"useful-controls-content-wrapper",children:[we("p",{children:[W("kbd",{children:"RMB"}),W("br",{}),ut.ui_rmb]}),we("p",{children:[W("kbd",{children:"ALT + LMB"}),W("br",{}),ut.ui_alt_lmb]}),we("p",{children:[W("kbd",{children:"CTRL + LMB"}),W("br",{}),ut.ui_ctrl_lmb]}),we("p",{children:[W("kbd",{children:"SHIFT + Drag"}),W("br",{}),ut.ui_shift_drag]}),we("p",{children:[W("kbd",{children:"CTRL + SHIFT + LMB"}),W("br",{}),ut.ui_ctrl_shift_lmb]}),W("div",{style:{textAlign:"right"},children:"🐂"})]})]})})})})})},i6=()=>{const e=vi(r4),t=iu(),[n,r]=I.useState(!1),[,i]=gg(()=>({accept:"SLOT",drop:a=>{a.inventory==="player"&&av(a.item)}})),[,l]=gg(()=>({accept:"SLOT",drop:a=>{a.inventory==="player"&&vk(a.item)}}));return we(En,{children:[W(r6,{infoVisible:n,setInfoVisible:r}),W("div",{className:"inventory-control",children:we("div",{className:"inventory-control-wrapper",children:[W("input",{className:"inventory-control-input",type:"number",defaultValue:e,onChange:a=>{a.target.valueAsNumber=isNaN(a.target.valueAsNumber)||a.target.valueAsNumber<0?0:Math.floor(a.target.valueAsNumber),t(Q3(a.target.valueAsNumber))},min:0}),W("button",{className:"inventory-control-button",ref:i,children:ut.ui_use||"Use"}),W("button",{className:"inventory-control-button",ref:l,children:ut.ui_give||"Give"}),W("button",{className:"inventory-control-button",onClick:()=>ur("exit"),children:ut.ui_close||"Close"})]})}),W("button",{className:"useful-controls-button",onClick:()=>r(!0),children:W("svg",{xmlns:"http://www.w3.org/2000/svg",height:"2em",viewBox:"0 0 524 524",children:W("path",{d:"M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zM216 336h24V272H216c-13.3 0-24-10.7-24-24s10.7-24 24-24h48c13.3 0 24 10.7 24 24v88h8c13.3 0 24 10.7 24 24s-10.7 24-24 24H216c-13.3 0-24-10.7-24-24s10.7-24 24-24zm40-208a32 32 0 1 1 0 64 32 32 0 1 1 0-64z"})})})]})},lh=(e,t,n)=>{let r=e*n,i=t*(1-n);return r+i},qa=(e,t,n)=>{let r=lh(e[0],t[0],n),i=lh(e[1],t[1],n),l=lh(e[2],t[2],n);return`rgb(${r}, ${i}, ${l})`},Ii={primaryColor:[231,76,60],secondColor:[39,174,96],accentColor:[211,84,0]},kv=({percent:e,durability:t})=>{const n=I.useMemo(()=>t?e<50?qa(Ii.accentColor,Ii.primaryColor,e/100):qa(Ii.secondColor,Ii.accentColor,e/100):e>50?qa(Ii.primaryColor,Ii.accentColor,e/100):qa(Ii.accentColor,Ii.secondColor,e/50),[t,e]);return W("div",{className:t?"durability-bar":"weight-bar",children:W("div",{style:{visibility:e>0?"visible":"hidden",height:"100%",width:`${e}%`,backgroundColor:n,transition:`background ${.3}s ease, width ${.3}s ease`}})})};function Og(e,t){return Og=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,r){return n.__proto__=r,n},Og(e,t)}function bv(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,Og(e,t)}var ob={exports:{}},o6="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",l6=o6,u6=l6;function lb(){}function ub(){}ub.resetWarningCache=lb;var s6=function(){function e(r,i,l,s,a,f){if(f!==u6){var d=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw d.name="Invariant Violation",d}}e.isRequired=e;function t(){return e}var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:ub,resetWarningCache:lb};return n.PropTypes=n,n};ob.exports=s6();var a6=ob.exports;const Ce=io(a6);function c6(e,t){return e.classList?!!t&&e.classList.contains(t):(" "+(e.className.baseVal||e.className)+" ").indexOf(" "+t+" ")!==-1}function f6(e,t){e.classList?e.classList.add(t):c6(e,t)||(typeof e.className=="string"?e.className=e.className+" "+t:e.setAttribute("class",(e.className&&e.className.baseVal||"")+" "+t))}function wx(e,t){return e.replace(new RegExp("(^|\\s)"+t+"(?:\\s|$)","g"),"$1").replace(/\s+/g," ").replace(/^\s*|\s*$/g,"")}function d6(e,t){e.classList?e.classList.remove(t):typeof e.className=="string"?e.className=wx(e.className,t):e.setAttribute("class",wx(e.className&&e.className.baseVal||"",t))}const xx={disabled:!1},Jc=Ve.createContext(null);var sb=function(t){return t.scrollTop},Bu="unmounted",Co="exited",ko="entering",dl="entered",Tg="exiting",yi=function(e){bv(t,e);function t(r,i){var l;l=e.call(this,r,i)||this;var s=i,a=s&&!s.isMounting?r.enter:r.appear,f;return l.appearStatus=null,r.in?a?(f=Co,l.appearStatus=ko):f=dl:r.unmountOnExit||r.mountOnEnter?f=Bu:f=Co,l.state={status:f},l.nextCallback=null,l}t.getDerivedStateFromProps=function(i,l){var s=i.in;return s&&l.status===Bu?{status:Co}:null};var n=t.prototype;return n.componentDidMount=function(){this.updateStatus(!0,this.appearStatus)},n.componentDidUpdate=function(i){var l=null;if(i!==this.props){var s=this.state.status;this.props.in?s!==ko&&s!==dl&&(l=ko):(s===ko||s===dl)&&(l=Tg)}this.updateStatus(!1,l)},n.componentWillUnmount=function(){this.cancelNextCallback()},n.getTimeouts=function(){var i=this.props.timeout,l,s,a;return l=s=a=i,i!=null&&typeof i!="number"&&(l=i.exit,s=i.enter,a=i.appear!==void 0?i.appear:s),{exit:l,enter:s,appear:a}},n.updateStatus=function(i,l){if(i===void 0&&(i=!1),l!==null)if(this.cancelNextCallback(),l===ko){if(this.props.unmountOnExit||this.props.mountOnEnter){var s=this.props.nodeRef?this.props.nodeRef.current:Ha.findDOMNode(this);s&&sb(s)}this.performEnter(i)}else this.performExit();else this.props.unmountOnExit&&this.state.status===Co&&this.setState({status:Bu})},n.performEnter=function(i){var l=this,s=this.props.enter,a=this.context?this.context.isMounting:i,f=this.props.nodeRef?[a]:[Ha.findDOMNode(this),a],d=f[0],p=f[1],g=this.getTimeouts(),v=a?g.appear:g.enter;if(!i&&!s||xx.disabled){this.safeSetState({status:dl},function(){l.props.onEntered(d)});return}this.props.onEnter(d,p),this.safeSetState({status:ko},function(){l.props.onEntering(d,p),l.onTransitionEnd(v,function(){l.safeSetState({status:dl},function(){l.props.onEntered(d,p)})})})},n.performExit=function(){var i=this,l=this.props.exit,s=this.getTimeouts(),a=this.props.nodeRef?void 0:Ha.findDOMNode(this);if(!l||xx.disabled){this.safeSetState({status:Co},function(){i.props.onExited(a)});return}this.props.onExit(a),this.safeSetState({status:Tg},function(){i.props.onExiting(a),i.onTransitionEnd(s.exit,function(){i.safeSetState({status:Co},function(){i.props.onExited(a)})})})},n.cancelNextCallback=function(){this.nextCallback!==null&&(this.nextCallback.cancel(),this.nextCallback=null)},n.safeSetState=function(i,l){l=this.setNextCallback(l),this.setState(i,l)},n.setNextCallback=function(i){var l=this,s=!0;return this.nextCallback=function(a){s&&(s=!1,l.nextCallback=null,i(a))},this.nextCallback.cancel=function(){s=!1},this.nextCallback},n.onTransitionEnd=function(i,l){this.setNextCallback(l);var s=this.props.nodeRef?this.props.nodeRef.current:Ha.findDOMNode(this),a=i==null&&!this.props.addEndListener;if(!s||a){setTimeout(this.nextCallback,0);return}if(this.props.addEndListener){var f=this.props.nodeRef?[this.nextCallback]:[s,this.nextCallback],d=f[0],p=f[1];this.props.addEndListener(d,p)}i!=null&&setTimeout(this.nextCallback,i)},n.render=function(){var i=this.state.status;if(i===Bu)return null;var l=this.props,s=l.children;l.in,l.mountOnEnter,l.unmountOnExit,l.appear,l.enter,l.exit,l.timeout,l.addEndListener,l.onEnter,l.onEntering,l.onEntered,l.onExit,l.onExiting,l.onExited,l.nodeRef;var a=Fm(l,["children","in","mountOnEnter","unmountOnExit","appear","enter","exit","timeout","addEndListener","onEnter","onEntering","onEntered","onExit","onExiting","onExited","nodeRef"]);return Ve.createElement(Jc.Provider,{value:null},typeof s=="function"?s(i,a):Ve.cloneElement(Ve.Children.only(s),a))},t}(Ve.Component);yi.contextType=Jc;yi.propTypes={};function fl(){}yi.defaultProps={in:!1,mountOnEnter:!1,unmountOnExit:!1,appear:!1,enter:!0,exit:!0,onEnter:fl,onEntering:fl,onEntered:fl,onExit:fl,onExiting:fl,onExited:fl};yi.UNMOUNTED=Bu;yi.EXITED=Co;yi.ENTERING=ko;yi.ENTERED=dl;yi.EXITING=Tg;const p6=yi;var h6=function(t,n){return t&&n&&n.split(" ").forEach(function(r){return f6(t,r)})},uh=function(t,n){return t&&n&&n.split(" ").forEach(function(r){return d6(t,r)})},_v=function(e){bv(t,e);function t(){for(var r,i=arguments.length,l=new Array(i),s=0;s<i;s++)l[s]=arguments[s];return r=e.call.apply(e,[this].concat(l))||this,r.appliedClasses={appear:{},enter:{},exit:{}},r.onEnter=function(a,f){var d=r.resolveArguments(a,f),p=d[0],g=d[1];r.removeClasses(p,"exit"),r.addClass(p,g?"appear":"enter","base"),r.props.onEnter&&r.props.onEnter(a,f)},r.onEntering=function(a,f){var d=r.resolveArguments(a,f),p=d[0],g=d[1],v=g?"appear":"enter";r.addClass(p,v,"active"),r.props.onEntering&&r.props.onEntering(a,f)},r.onEntered=function(a,f){var d=r.resolveArguments(a,f),p=d[0],g=d[1],v=g?"appear":"enter";r.removeClasses(p,v),r.addClass(p,v,"done"),r.props.onEntered&&r.props.onEntered(a,f)},r.onExit=function(a){var f=r.resolveArguments(a),d=f[0];r.removeClasses(d,"appear"),r.removeClasses(d,"enter"),r.addClass(d,"exit","base"),r.props.onExit&&r.props.onExit(a)},r.onExiting=function(a){var f=r.resolveArguments(a),d=f[0];r.addClass(d,"exit","active"),r.props.onExiting&&r.props.onExiting(a)},r.onExited=function(a){var f=r.resolveArguments(a),d=f[0];r.removeClasses(d,"exit"),r.addClass(d,"exit","done"),r.props.onExited&&r.props.onExited(a)},r.resolveArguments=function(a,f){return r.props.nodeRef?[r.props.nodeRef.current,a]:[a,f]},r.getClassNames=function(a){var f=r.props.classNames,d=typeof f=="string",p=d&&f?f+"-":"",g=d?""+p+a:f[a],v=d?g+"-active":f[a+"Active"],m=d?g+"-done":f[a+"Done"];return{baseClassName:g,activeClassName:v,doneClassName:m}},r}var n=t.prototype;return n.addClass=function(i,l,s){var a=this.getClassNames(l)[s+"ClassName"],f=this.getClassNames("enter"),d=f.doneClassName;l==="appear"&&s==="done"&&d&&(a+=" "+d),s==="active"&&i&&sb(i),a&&(this.appliedClasses[l][s]=a,h6(i,a))},n.removeClasses=function(i,l){var s=this.appliedClasses[l],a=s.base,f=s.active,d=s.done;this.appliedClasses[l]={},a&&uh(i,a),f&&uh(i,f),d&&uh(i,d)},n.render=function(){var i=this.props;i.classNames;var l=Fm(i,["classNames"]);return Ve.createElement(p6,Fc({},l,{onEnter:this.onEnter,onEntered:this.onEntered,onEntering:this.onEntering,onExit:this.onExit,onExiting:this.onExiting,onExited:this.onExited}))},t}(Ve.Component);_v.defaultProps={classNames:""};_v.propTypes={};const ab=_v;function g6(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ov(e,t){var n=function(l){return t&&I.isValidElement(l)?t(l):l},r=Object.create(null);return e&&I.Children.map(e,function(i){return i}).forEach(function(i){r[i.key]=n(i)}),r}function m6(e,t){e=e||{},t=t||{};function n(p){return p in t?t[p]:e[p]}var r=Object.create(null),i=[];for(var l in e)l in t?i.length&&(r[l]=i,i=[]):i.push(l);var s,a={};for(var f in t){if(r[f])for(s=0;s<r[f].length;s++){var d=r[f][s];a[r[f][s]]=n(d)}a[f]=n(f)}for(s=0;s<i.length;s++)a[i[s]]=n(i[s]);return a}function Io(e,t,n){return n[t]!=null?n[t]:e.props[t]}function v6(e,t){return Ov(e.children,function(n){return I.cloneElement(n,{onExited:t.bind(null,n),in:!0,appear:Io(n,"appear",e),enter:Io(n,"enter",e),exit:Io(n,"exit",e)})})}function y6(e,t,n){var r=Ov(e.children),i=m6(t,r);return Object.keys(i).forEach(function(l){var s=i[l];if(I.isValidElement(s)){var a=l in t,f=l in r,d=t[l],p=I.isValidElement(d)&&!d.props.in;f&&(!a||p)?i[l]=I.cloneElement(s,{onExited:n.bind(null,s),in:!0,exit:Io(s,"exit",e),enter:Io(s,"enter",e)}):!f&&a&&!p?i[l]=I.cloneElement(s,{in:!1}):f&&a&&I.isValidElement(d)&&(i[l]=I.cloneElement(s,{onExited:n.bind(null,s),in:d.props.in,exit:Io(s,"exit",e),enter:Io(s,"enter",e)}))}}),i}var w6=Object.values||function(e){return Object.keys(e).map(function(t){return e[t]})},x6={component:"div",childFactory:function(t){return t}},Tv=function(e){bv(t,e);function t(r,i){var l;l=e.call(this,r,i)||this;var s=l.handleExited.bind(g6(l));return l.state={contextValue:{isMounting:!0},handleExited:s,firstRender:!0},l}var n=t.prototype;return n.componentDidMount=function(){this.mounted=!0,this.setState({contextValue:{isMounting:!1}})},n.componentWillUnmount=function(){this.mounted=!1},t.getDerivedStateFromProps=function(i,l){var s=l.children,a=l.handleExited,f=l.firstRender;return{children:f?v6(i,a):y6(i,s,a),firstRender:!1}},n.handleExited=function(i,l){var s=Ov(this.props.children);i.key in s||(i.props.onExited&&i.props.onExited(l),this.mounted&&this.setState(function(a){var f=Fc({},a.children);return delete f[i.key],{children:f}}))},n.render=function(){var i=this.props,l=i.component,s=i.childFactory,a=Fm(i,["component","childFactory"]),f=this.state.contextValue,d=w6(this.state.children).map(s);return delete a.appear,delete a.enter,delete a.exit,l===null?Ve.createElement(Jc.Provider,{value:f},d):Ve.createElement(Jc.Provider,{value:f},Ve.createElement(l,a,d))},t}(Ve.Component);Tv.propTypes={};Tv.defaultProps=x6;const S6=Tv,E6=e=>{const t=I.useRef(null);return W(ab,{nodeRef:t,in:e.in,timeout:200,classNames:"transition-slide-up",unmountOnExit:!0,children:Ve.cloneElement(e.children,{ref:t})})},C6=()=>{const[e,t]=I.useState(!1),n=vi(pk).items.slice(0,5),[r,i]=I.useState();return Fr("toggleHotbar",()=>{e?t(!1):(r&&clearTimeout(r),t(!0),i(setTimeout(()=>t(!1),3e3)))}),W(E6,{in:e,children:W("div",{className:"hotbar-container",children:n.map(l=>W("div",{className:"hotbar-item-slot",style:{backgroundImage:`url(${l?.name?Dl(l):"none"}`},children:gn(l)&&we("div",{className:"item-slot-wrapper",children:[we("div",{className:"hotbar-slot-header-wrapper",children:[W("div",{className:"inventory-slot-number",children:l.slot}),we("div",{className:"item-slot-info-wrapper",children:[W("p",{children:l.weight>0?l.weight>=1e3?`${(l.weight/1e3).toLocaleString("en-us",{minimumFractionDigits:2})}kg `:`${l.weight.toLocaleString("en-us",{minimumFractionDigits:0})}g `:""}),W("p",{children:l.count?l.count.toLocaleString("en-us")+"x":""})]})]}),we("div",{children:[l?.durability!==void 0&&W(kv,{percent:l.durability,durability:!0}),W("div",{className:"inventory-slot-label-box",children:W("div",{className:"inventory-slot-label-text",children:l.metadata?.label?l.metadata.label:dt[l.name]?.label||l.name})})]})]})},`hotbar-${l.slot}`))})})},k6=["Escape"],b6=e=>{const t=I.useRef(fk),n=iu();I.useEffect(()=>{t.current=e},[e]),I.useEffect(()=>{const r=i=>{k6.includes(i.code)&&(t.current(!1),n(Ju()),n(mk()),ur("exit"))};return window.addEventListener("keyup",r),()=>window.removeEventListener("keyup",r)},[])},_6=rv("inventory/validateMove",async(e,{rejectWithValue:t,dispatch:n})=>{try{const r=await ur("swapItems",e);if(r===!1)return t(r);typeof r=="number"&&n(t4(r))}catch{return t(!1)}}),Ig=(e,t)=>{const{inventory:n}=Nn.getState(),{sourceInventory:r,targetInventory:i}=Wf(n,e.inventory,t?.inventory),l=r.items[e.item.slot-1],s=dt[l.name];if(s===void 0)return console.error(`${l.name} item data undefined!`);if(l.metadata?.container!==void 0){if(i.type===Yt.CONTAINER)return console.log(`Cannot store container ${l.name} inside another container`);if(n.rightInventory.id===l.metadata.container)return console.log(`Cannot move container ${l.name} when opened`)}const a=t?i.items[t.item.slot-1]:B3(l,s,i.items);if(a===void 0)return console.error("Target slot undefined!");if(a.metadata?.container!==void 0&&n.rightInventory.id===a.metadata.container)return console.log(`Cannot swap item ${l.name} with container ${a.name} when opened`);const f=n.shiftPressed&&l.count>1&&r.type!=="shop"?Math.floor(l.count/2):n.itemAmount===0||n.itemAmount>l.count?l.count:n.itemAmount,d={fromSlot:l,toSlot:a,fromType:r.type,toType:i.type,count:f};Nn.dispatch(_6({...d,fromSlot:l.slot,toSlot:a.slot})),gn(a,!0)?s.stack&&$3(l,a)?Nn.dispatch(J3({...d,toSlot:a})):Nn.dispatch(X3({...d,toSlot:a})):Nn.dispatch(Z3(d))},O6=rv("inventory/buyItem",async(e,{rejectWithValue:t})=>{try{const n=await ur("buyItem",e);if(n===!1)return t(n)}catch{return t(!1)}}),T6=(e,t)=>{const{inventory:n}=Nn.getState(),r=n.rightInventory,i=n.leftInventory,l=r.items[e.item.slot-1];if(!gn(l))throw new Error(`Item ${l.slot} name === undefined`);if(l.count===0)return;if(dt[l.name]===void 0)return console.error(`Item ${l.name} data undefined!`);const a=i.items[t.item.slot-1];if(a===void 0)return console.error("Target slot undefined");const f=n.itemAmount!==0?l.count&&n.itemAmount>l.count?l.count:n.itemAmount:1,d={fromSlot:l,toSlot:a,fromType:r.type,toType:i.type,count:f};Nn.dispatch(O6({...d,fromSlot:l.slot,toSlot:a.slot}))},I6=rv("inventory/craftItem",async(e,{rejectWithValue:t})=>{try{const n=await ur("craftItem",e);if(n===!1)return t(n)}catch{return t(!1)}}),R6=(e,t)=>{const{inventory:n}=Nn.getState(),r=n.rightInventory,i=n.leftInventory,l=r.items[e.item.slot-1];if(!gn(l))throw new Error(`Item ${l.slot} name === undefined`);if(l.count===0)return;if(dt[l.name]===void 0)return console.error(`Item ${l.name} data undefined!`);const a=i.items[t.item.slot-1];if(a===void 0)return console.error("Target slot undefined");const f=n.itemAmount===0?1:n.itemAmount,d={fromSlot:l,toSlot:a,fromType:r.type,toType:i.type,count:f};Nn.dispatch(I6({...d,fromSlot:l.slot,toSlot:a.slot}))},P6=({item:e,inventoryId:t,inventoryType:n,inventoryGroups:r},i)=>{const l=mi(),s=iu(),a=I.useRef(null),f=I.useCallback(()=>Kw(e,{type:n,groups:r})&&Qw(e,n),[e,n,r]),[{isDragging:d},p]=LF(()=>({type:"SLOT",collect:y=>({isDragging:y.isDragging()}),item:()=>gn(e,n!==Yt.SHOP)?{inventory:n,item:{name:e.name,slot:e.slot},image:e?.name&&`url(${Dl(e)||"none"}`}:null,canDrag:f}),[n,e]),[{isOver:g},v]=gg(()=>({accept:"SLOT",collect:y=>({isOver:y.isOver()}),drop:y=>{switch(s(Ju()),y.inventory){case Yt.SHOP:T6(y,{inventory:n,item:{slot:e.slot}});break;case Yt.CRAFTING:R6(y,{inventory:n,item:{slot:e.slot}});break;default:Ig(y,{inventory:n,item:{slot:e.slot}});break}},canDrop:y=>(y.item.slot!==e.slot||y.inventory!==n)&&n!==Yt.SHOP&&n!==Yt.CRAFTING}),[n,e]);Fr("refreshSlots",y=>{!d&&!y.items||!Array.isArray(y.items)||!y.items.find(k=>k.item.slot===e.slot&&k.inventory===t)||l.dispatch({type:"dnd-core/END_DRAG"})});const m=y=>p(v(y)),E=y=>{y.preventDefault(),!(n!=="player"||!gn(e))&&s(a4({item:e,coords:{x:y.clientX,y:y.clientY}}))},S=y=>{s(Ju()),a.current&&clearTimeout(a.current),y.ctrlKey&&gn(e)&&n!=="shop"&&n!=="crafting"?Ig({item:e,inventory:n}):y.altKey&&gn(e)&&n==="player"&&av(e)},A=vv([m,i]);return W("div",{ref:A,onContextMenu:E,onClick:S,className:"inventory-slot",style:{filter:!Kw(e,{type:n,groups:r})||!Qw(e,n)?"brightness(80%) grayscale(100%)":void 0,opacity:d?.4:1,backgroundImage:`url(${e?.name?Dl(e):"none"}`,border:g?"1px dashed rgba(255,255,255,0.4)":""},children:gn(e)&&we("div",{className:"item-slot-wrapper",onMouseEnter:()=>{a.current=window.setTimeout(()=>{s(l4({item:e,inventoryType:n}))},500)},onMouseLeave:()=>{s(Ju()),a.current&&(clearTimeout(a.current),a.current=null)},children:[we("div",{className:n==="player"&&e.slot<=5?"item-hotslot-header-wrapper":"item-slot-header-wrapper",children:[n==="player"&&e.slot<=5&&W("div",{className:"inventory-slot-number",children:e.slot}),we("div",{className:"item-slot-info-wrapper",children:[W("p",{children:e.weight>0?e.weight>=1e3?`${(e.weight/1e3).toLocaleString("en-us",{minimumFractionDigits:2})}kg `:`${e.weight.toLocaleString("en-us",{minimumFractionDigits:0})}g `:""}),W("p",{children:e.count?e.count.toLocaleString("en-us")+"x":""})]})]}),we("div",{children:[n!=="shop"&&e?.durability!==void 0&&W(kv,{percent:e.durability,durability:!0}),n==="shop"&&e?.price!==void 0&&W(En,{children:e?.currency!=="money"&&e.currency!=="black_money"&&e.price>0&&e.currency?we("div",{className:"item-slot-currency-wrapper",children:[W("img",{src:e.currency?Dl(e.currency):"none",alt:"item-image",style:{imageRendering:"-webkit-optimize-contrast",height:"auto",width:"2vh",backfaceVisibility:"hidden",transform:"translateZ(0)"}}),W("p",{children:e.price.toLocaleString("en-us")})]}):W(En,{children:e.price>0&&W("div",{className:"item-slot-price-wrapper",style:{color:e.currency==="money"||!e.currency?"#2ECC71":"#E74C3C"},children:we("p",{children:[ut.$||"$",e.price.toLocaleString("en-us")]})})})}),W("div",{className:"inventory-slot-label-box",children:W("div",{className:"inventory-slot-label-text",children:e.metadata?.label?e.metadata.label:dt[e.name]?.label||e.name})})]})]})})},A6=Ve.memo(Ve.forwardRef(P6));function D6(e){const[t,n]=I.useState(null),r=I.useRef(null);return{ref:I.useCallback(l=>{if(r.current&&(r.current.disconnect(),r.current=null),l===null){n(null);return}r.current=new IntersectionObserver(([s])=>{n(s)},e),r.current.observe(l)},[e?.rootMargin,e?.root,e?.threshold]),entry:t}}const Sx=30,cb=({inventory:e})=>{const t=I.useMemo(()=>e.maxWeight!==void 0?Math.floor(U3(e.items)*1e3)/1e3:0,[e.maxWeight,e.items]),[n,r]=I.useState(0),i=I.useRef(null),{ref:l,entry:s}=D6({threshold:.5}),a=vi(f=>f.inventory.isBusy);return I.useEffect(()=>{s&&s.isIntersecting&&r(f=>++f)},[s]),W(En,{children:we("div",{className:"inventory-grid-wrapper",style:{pointerEvents:a?"none":"auto"},children:[we("div",{children:[we("div",{className:"inventory-grid-header-wrapper",children:[W("p",{children:e.label}),e.maxWeight&&we("p",{children:[t/1e3,"/",e.maxWeight/1e3,"kg"]})]}),W(kv,{percent:e.maxWeight?t/e.maxWeight*100:0})]}),W("div",{className:"inventory-grid-container",ref:i,children:W(En,{children:e.items.slice(0,(n+1)*Sx).map((f,d)=>W(A6,{item:f,ref:d===(n+1)*Sx-1?l:null,inventoryType:e.type,inventoryGroups:e.groups,inventoryId:e.id},`${e.type}-${e.id}-${f.slot}`))})})]})})},N6=()=>{const e=vi(n4);return W(cb,{inventory:e})},M6=()=>{const e=vi(pk);return W(cb,{inventory:e})},Ex=["http","https","mailto","tel"];function L6(e){const t=(e||"").trim(),n=t.charAt(0);if(n==="#"||n==="/")return t;const r=t.indexOf(":");if(r===-1)return t;let i=-1;for(;++i<Ex.length;){const l=Ex[i];if(r===l.length&&t.slice(0,l.length).toLowerCase()===l)return t}return i=t.indexOf("?"),i!==-1&&r>i||(i=t.indexOf("#"),i!==-1&&r>i)?t:"javascript:void(0)"}/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */var F6=function(t){return t!=null&&t.constructor!=null&&typeof t.constructor.isBuffer=="function"&&t.constructor.isBuffer(t)};const fb=io(F6);function ns(e){return!e||typeof e!="object"?"":"position"in e||"type"in e?Cx(e.position):"start"in e||"end"in e?Cx(e):"line"in e||"column"in e?Rg(e):""}function Rg(e){return kx(e&&e.line)+":"+kx(e&&e.column)}function Cx(e){return Rg(e&&e.start)+"-"+Rg(e&&e.end)}function kx(e){return e&&typeof e=="number"?e:1}class fr extends Error{constructor(t,n,r){const i=[null,null];let l={start:{line:null,column:null},end:{line:null,column:null}};if(super(),typeof n=="string"&&(r=n,n=void 0),typeof r=="string"){const s=r.indexOf(":");s===-1?i[1]=r:(i[0]=r.slice(0,s),i[1]=r.slice(s+1))}n&&("type"in n||"position"in n?n.position&&(l=n.position):"start"in n||"end"in n?l=n:("line"in n||"column"in n)&&(l.start=n)),this.name=ns(n)||"1:1",this.message=typeof t=="object"?t.message:t,this.stack="",typeof t=="object"&&t.stack&&(this.stack=t.stack),this.reason=this.message,this.fatal,this.line=l.start.line,this.column=l.start.column,this.position=l,this.source=i[0],this.ruleId=i[1],this.file,this.actual,this.expected,this.url,this.note}}fr.prototype.file="";fr.prototype.name="";fr.prototype.reason="";fr.prototype.message="";fr.prototype.stack="";fr.prototype.fatal=null;fr.prototype.column=null;fr.prototype.line=null;fr.prototype.source=null;fr.prototype.ruleId=null;fr.prototype.position=null;const Mr={basename:z6,dirname:$6,extname:B6,join:U6,sep:"/"};function z6(e,t){if(t!==void 0&&typeof t!="string")throw new TypeError('"ext" argument must be a string');Ms(e);let n=0,r=-1,i=e.length,l;if(t===void 0||t.length===0||t.length>e.length){for(;i--;)if(e.charCodeAt(i)===47){if(l){n=i+1;break}}else r<0&&(l=!0,r=i+1);return r<0?"":e.slice(n,r)}if(t===e)return"";let s=-1,a=t.length-1;for(;i--;)if(e.charCodeAt(i)===47){if(l){n=i+1;break}}else s<0&&(l=!0,s=i+1),a>-1&&(e.charCodeAt(i)===t.charCodeAt(a--)?a<0&&(r=i):(a=-1,r=s));return n===r?r=s:r<0&&(r=e.length),e.slice(n,r)}function $6(e){if(Ms(e),e.length===0)return".";let t=-1,n=e.length,r;for(;--n;)if(e.charCodeAt(n)===47){if(r){t=n;break}}else r||(r=!0);return t<0?e.charCodeAt(0)===47?"/":".":t===1&&e.charCodeAt(0)===47?"//":e.slice(0,t)}function B6(e){Ms(e);let t=e.length,n=-1,r=0,i=-1,l=0,s;for(;t--;){const a=e.charCodeAt(t);if(a===47){if(s){r=t+1;break}continue}n<0&&(s=!0,n=t+1),a===46?i<0?i=t:l!==1&&(l=1):i>-1&&(l=-1)}return i<0||n<0||l===0||l===1&&i===n-1&&i===r+1?"":e.slice(i,n)}function U6(...e){let t=-1,n;for(;++t<e.length;)Ms(e[t]),e[t]&&(n=n===void 0?e[t]:n+"/"+e[t]);return n===void 0?".":H6(n)}function H6(e){Ms(e);const t=e.charCodeAt(0)===47;let n=j6(e,!t);return n.length===0&&!t&&(n="."),n.length>0&&e.charCodeAt(e.length-1)===47&&(n+="/"),t?"/"+n:n}function j6(e,t){let n="",r=0,i=-1,l=0,s=-1,a,f;for(;++s<=e.length;){if(s<e.length)a=e.charCodeAt(s);else{if(a===47)break;a=47}if(a===47){if(!(i===s-1||l===1))if(i!==s-1&&l===2){if(n.length<2||r!==2||n.charCodeAt(n.length-1)!==46||n.charCodeAt(n.length-2)!==46){if(n.length>2){if(f=n.lastIndexOf("/"),f!==n.length-1){f<0?(n="",r=0):(n=n.slice(0,f),r=n.length-1-n.lastIndexOf("/")),i=s,l=0;continue}}else if(n.length>0){n="",r=0,i=s,l=0;continue}}t&&(n=n.length>0?n+"/..":"..",r=2)}else n.length>0?n+="/"+e.slice(i+1,s):n=e.slice(i+1,s),r=s-i-1;i=s,l=0}else a===46&&l>-1?l++:l=-1}return n}function Ms(e){if(typeof e!="string")throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}const W6={cwd:V6};function V6(){return"/"}function Pg(e){return e!==null&&typeof e=="object"&&e.href&&e.origin}function G6(e){if(typeof e=="string")e=new URL(e);else if(!Pg(e)){const t=new TypeError('The "path" argument must be of type string or an instance of URL. Received `'+e+"`");throw t.code="ERR_INVALID_ARG_TYPE",t}if(e.protocol!=="file:"){const t=new TypeError("The URL must be of scheme file");throw t.code="ERR_INVALID_URL_SCHEME",t}return q6(e)}function q6(e){if(e.hostname!==""){const r=new TypeError('File URL host must be "localhost" or empty on darwin');throw r.code="ERR_INVALID_FILE_URL_HOST",r}const t=e.pathname;let n=-1;for(;++n<t.length;)if(t.charCodeAt(n)===37&&t.charCodeAt(n+1)===50){const r=t.charCodeAt(n+2);if(r===70||r===102){const i=new TypeError("File URL path must not include encoded / characters");throw i.code="ERR_INVALID_FILE_URL_PATH",i}}return decodeURIComponent(t)}const sh=["history","path","basename","stem","extname","dirname"];class db{constructor(t){let n;t?typeof t=="string"||K6(t)?n={value:t}:Pg(t)?n={path:t}:n=t:n={},this.data={},this.messages=[],this.history=[],this.cwd=W6.cwd(),this.value,this.stored,this.result,this.map;let r=-1;for(;++r<sh.length;){const l=sh[r];l in n&&n[l]!==void 0&&n[l]!==null&&(this[l]=l==="history"?[...n[l]]:n[l])}let i;for(i in n)sh.includes(i)||(this[i]=n[i])}get path(){return this.history[this.history.length-1]}set path(t){Pg(t)&&(t=G6(t)),ch(t,"path"),this.path!==t&&this.history.push(t)}get dirname(){return typeof this.path=="string"?Mr.dirname(this.path):void 0}set dirname(t){bx(this.basename,"dirname"),this.path=Mr.join(t||"",this.basename)}get basename(){return typeof this.path=="string"?Mr.basename(this.path):void 0}set basename(t){ch(t,"basename"),ah(t,"basename"),this.path=Mr.join(this.dirname||"",t)}get extname(){return typeof this.path=="string"?Mr.extname(this.path):void 0}set extname(t){if(ah(t,"extname"),bx(this.dirname,"extname"),t){if(t.charCodeAt(0)!==46)throw new Error("`extname` must start with `.`");if(t.includes(".",1))throw new Error("`extname` cannot contain multiple dots")}this.path=Mr.join(this.dirname,this.stem+(t||""))}get stem(){return typeof this.path=="string"?Mr.basename(this.path,this.extname):void 0}set stem(t){ch(t,"stem"),ah(t,"stem"),this.path=Mr.join(this.dirname||"",t+(this.extname||""))}toString(t){return(this.value||"").toString(t||void 0)}message(t,n,r){const i=new fr(t,n,r);return this.path&&(i.name=this.path+":"+i.name,i.file=this.path),i.fatal=!1,this.messages.push(i),i}info(t,n,r){const i=this.message(t,n,r);return i.fatal=null,i}fail(t,n,r){const i=this.message(t,n,r);throw i.fatal=!0,i}}function ah(e,t){if(e&&e.includes(Mr.sep))throw new Error("`"+t+"` cannot be a path: did not expect `"+Mr.sep+"`")}function ch(e,t){if(!e)throw new Error("`"+t+"` cannot be empty")}function bx(e,t){if(!e)throw new Error("Setting `"+t+"` requires `path` to be set too")}function K6(e){return fb(e)}function _x(e){if(e)throw e}var fc=Object.prototype.hasOwnProperty,pb=Object.prototype.toString,Ox=Object.defineProperty,Tx=Object.getOwnPropertyDescriptor,Ix=function(t){return typeof Array.isArray=="function"?Array.isArray(t):pb.call(t)==="[object Array]"},Rx=function(t){if(!t||pb.call(t)!=="[object Object]")return!1;var n=fc.call(t,"constructor"),r=t.constructor&&t.constructor.prototype&&fc.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!n&&!r)return!1;var i;for(i in t);return typeof i>"u"||fc.call(t,i)},Px=function(t,n){Ox&&n.name==="__proto__"?Ox(t,n.name,{enumerable:!0,configurable:!0,value:n.newValue,writable:!0}):t[n.name]=n.newValue},Ax=function(t,n){if(n==="__proto__")if(fc.call(t,n)){if(Tx)return Tx(t,n).value}else return;return t[n]},Q6=function e(){var t,n,r,i,l,s,a=arguments[0],f=1,d=arguments.length,p=!1;for(typeof a=="boolean"&&(p=a,a=arguments[1]||{},f=2),(a==null||typeof a!="object"&&typeof a!="function")&&(a={});f<d;++f)if(t=arguments[f],t!=null)for(n in t)r=Ax(a,n),i=Ax(t,n),a!==i&&(p&&i&&(Rx(i)||(l=Ix(i)))?(l?(l=!1,s=r&&Ix(r)?r:[]):s=r&&Rx(r)?r:{},Px(a,{name:n,newValue:e(p,s,i)})):typeof i<"u"&&Px(a,{name:n,newValue:i}));return a};const Dx=io(Q6);function Ag(e){if(typeof e!="object"||e===null)return!1;const t=Object.getPrototypeOf(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function Y6(){const e=[],t={run:n,use:r};return t;function n(...i){let l=-1;const s=i.pop();if(typeof s!="function")throw new TypeError("Expected function as last argument, not "+s);a(null,...i);function a(f,...d){const p=e[++l];let g=-1;if(f){s(f);return}for(;++g<i.length;)(d[g]===null||d[g]===void 0)&&(d[g]=i[g]);i=d,p?X6(p,a)(...d):s(null,...d)}}function r(i){if(typeof i!="function")throw new TypeError("Expected `middelware` to be a function, not "+i);return e.push(i),t}}function X6(e,t){let n;return r;function r(...s){const a=e.length>s.length;let f;a&&s.push(i);try{f=e.apply(this,s)}catch(d){const p=d;if(a&&n)throw p;return i(p)}a||(f&&f.then&&typeof f.then=="function"?f.then(l,i):f instanceof Error?i(f):l(f))}function i(s,...a){n||(n=!0,t(s,...a))}function l(s){i(null,s)}}const Z6=gb().freeze(),hb={}.hasOwnProperty;function gb(){const e=Y6(),t=[];let n={},r,i=-1;return l.data=s,l.Parser=void 0,l.Compiler=void 0,l.freeze=a,l.attachers=t,l.use=f,l.parse=d,l.stringify=p,l.run=g,l.runSync=v,l.process=m,l.processSync=E,l;function l(){const S=gb();let A=-1;for(;++A<t.length;)S.use(...t[A]);return S.data(Dx(!0,{},n)),S}function s(S,A){return typeof S=="string"?arguments.length===2?(ph("data",r),n[S]=A,l):hb.call(n,S)&&n[S]||null:S?(ph("data",r),n=S,l):n}function a(){if(r)return l;for(;++i<t.length;){const[S,...A]=t[i];if(A[0]===!1)continue;A[0]===!0&&(A[0]=void 0);const y=S.call(l,...A);typeof y=="function"&&e.use(y)}return r=!0,i=Number.POSITIVE_INFINITY,l}function f(S,...A){let y;if(ph("use",r),S!=null)if(typeof S=="function")D(S,...A);else if(typeof S=="object")Array.isArray(S)?P(S):k(S);else throw new TypeError("Expected usable value, not `"+S+"`");return y&&(n.settings=Object.assign(n.settings||{},y)),l;function x(T){if(typeof T=="function")D(T);else if(typeof T=="object")if(Array.isArray(T)){const[F,...z]=T;D(F,...z)}else k(T);else throw new TypeError("Expected usable value, not `"+T+"`")}function k(T){P(T.plugins),T.settings&&(y=Object.assign(y||{},T.settings))}function P(T){let F=-1;if(T!=null)if(Array.isArray(T))for(;++F<T.length;){const z=T[F];x(z)}else throw new TypeError("Expected a list of plugins, not `"+T+"`")}function D(T,F){let z=-1,G;for(;++z<t.length;)if(t[z][0]===T){G=t[z];break}G?(Ag(G[1])&&Ag(F)&&(F=Dx(!0,G[1],F)),G[1]=F):t.push([...arguments])}}function d(S){l.freeze();const A=Nu(S),y=l.Parser;return fh("parse",y),Nx(y,"parse")?new y(String(A),A).parse():y(String(A),A)}function p(S,A){l.freeze();const y=Nu(A),x=l.Compiler;return dh("stringify",x),Mx(S),Nx(x,"compile")?new x(S,y).compile():x(S,y)}function g(S,A,y){if(Mx(S),l.freeze(),!y&&typeof A=="function"&&(y=A,A=void 0),!y)return new Promise(x);x(null,y);function x(k,P){e.run(S,Nu(A),D);function D(T,F,z){F=F||S,T?P(T):k?k(F):y(null,F,z)}}}function v(S,A){let y,x;return l.run(S,A,k),Lx("runSync","run",x),y;function k(P,D){_x(P),y=D,x=!0}}function m(S,A){if(l.freeze(),fh("process",l.Parser),dh("process",l.Compiler),!A)return new Promise(y);y(null,A);function y(x,k){const P=Nu(S);l.run(l.parse(P),P,(T,F,z)=>{if(T||!F||!z)D(T);else{const G=l.stringify(F,z);G==null||(t$(G)?z.value=G:z.result=G),D(T,z)}});function D(T,F){T||!F?k(T):x?x(F):A(null,F)}}}function E(S){let A;l.freeze(),fh("processSync",l.Parser),dh("processSync",l.Compiler);const y=Nu(S);return l.process(y,x),Lx("processSync","process",A),y;function x(k){A=!0,_x(k)}}}function Nx(e,t){return typeof e=="function"&&e.prototype&&(J6(e.prototype)||t in e.prototype)}function J6(e){let t;for(t in e)if(hb.call(e,t))return!0;return!1}function fh(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `Parser`")}function dh(e,t){if(typeof t!="function")throw new TypeError("Cannot `"+e+"` without `Compiler`")}function ph(e,t){if(t)throw new Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function Mx(e){if(!Ag(e)||typeof e.type!="string")throw new TypeError("Expected node, got `"+e+"`")}function Lx(e,t,n){if(!n)throw new Error("`"+e+"` finished async. Use `"+t+"` instead")}function Nu(e){return e$(e)?e:new db(e)}function e$(e){return!!(e&&typeof e=="object"&&"message"in e&&"messages"in e)}function t$(e){return typeof e=="string"||fb(e)}const n$={};function r$(e,t){const n=t||n$,r=typeof n.includeImageAlt=="boolean"?n.includeImageAlt:!0,i=typeof n.includeHtml=="boolean"?n.includeHtml:!0;return mb(e,r,i)}function mb(e,t,n){if(i$(e)){if("value"in e)return e.type==="html"&&!n?"":e.value;if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return Fx(e.children,t,n)}return Array.isArray(e)?Fx(e,t,n):""}function Fx(e,t,n){const r=[];let i=-1;for(;++i<e.length;)r[i]=mb(e[i],t,n);return r.join("")}function i$(e){return!!(e&&typeof e=="object")}function Wr(e,t,n,r){const i=e.length;let l=0,s;if(t<0?t=-t>i?0:i+t:t=t>i?i:t,n=n>0?n:0,r.length<1e4)s=Array.from(r),s.unshift(t,n),e.splice(...s);else for(n&&e.splice(t,n);l<r.length;)s=r.slice(l,l+1e4),s.unshift(t,0),e.splice(...s),l+=1e4,t+=1e4}function ir(e,t){return e.length>0?(Wr(e,e.length,0,t),e):t}const zx={}.hasOwnProperty;function o$(e){const t={};let n=-1;for(;++n<e.length;)l$(t,e[n]);return t}function l$(e,t){let n;for(n in t){const i=(zx.call(e,n)?e[n]:void 0)||(e[n]={}),l=t[n];let s;if(l)for(s in l){zx.call(i,s)||(i[s]=[]);const a=l[s];u$(i[s],Array.isArray(a)?a:a?[a]:[])}}}function u$(e,t){let n=-1;const r=[];for(;++n<t.length;)(t[n].add==="after"?e:r).push(t[n]);Wr(e,0,0,r)}const s$=/[!-\/:-@\[-`\{-~\xA1\xA7\xAB\xB6\xB7\xBB\xBF\u037E\u0387\u055A-\u055F\u0589\u058A\u05BE\u05C0\u05C3\u05C6\u05F3\u05F4\u0609\u060A\u060C\u060D\u061B\u061D-\u061F\u066A-\u066D\u06D4\u0700-\u070D\u07F7-\u07F9\u0830-\u083E\u085E\u0964\u0965\u0970\u09FD\u0A76\u0AF0\u0C77\u0C84\u0DF4\u0E4F\u0E5A\u0E5B\u0F04-\u0F12\u0F14\u0F3A-\u0F3D\u0F85\u0FD0-\u0FD4\u0FD9\u0FDA\u104A-\u104F\u10FB\u1360-\u1368\u1400\u166E\u169B\u169C\u16EB-\u16ED\u1735\u1736\u17D4-\u17D6\u17D8-\u17DA\u1800-\u180A\u1944\u1945\u1A1E\u1A1F\u1AA0-\u1AA6\u1AA8-\u1AAD\u1B5A-\u1B60\u1B7D\u1B7E\u1BFC-\u1BFF\u1C3B-\u1C3F\u1C7E\u1C7F\u1CC0-\u1CC7\u1CD3\u2010-\u2027\u2030-\u2043\u2045-\u2051\u2053-\u205E\u207D\u207E\u208D\u208E\u2308-\u230B\u2329\u232A\u2768-\u2775\u27C5\u27C6\u27E6-\u27EF\u2983-\u2998\u29D8-\u29DB\u29FC\u29FD\u2CF9-\u2CFC\u2CFE\u2CFF\u2D70\u2E00-\u2E2E\u2E30-\u2E4F\u2E52-\u2E5D\u3001-\u3003\u3008-\u3011\u3014-\u301F\u3030\u303D\u30A0\u30FB\uA4FE\uA4FF\uA60D-\uA60F\uA673\uA67E\uA6F2-\uA6F7\uA874-\uA877\uA8CE\uA8CF\uA8F8-\uA8FA\uA8FC\uA92E\uA92F\uA95F\uA9C1-\uA9CD\uA9DE\uA9DF\uAA5C-\uAA5F\uAADE\uAADF\uAAF0\uAAF1\uABEB\uFD3E\uFD3F\uFE10-\uFE19\uFE30-\uFE52\uFE54-\uFE61\uFE63\uFE68\uFE6A\uFE6B\uFF01-\uFF03\uFF05-\uFF0A\uFF0C-\uFF0F\uFF1A\uFF1B\uFF1F\uFF20\uFF3B-\uFF3D\uFF3F\uFF5B\uFF5D\uFF5F-\uFF65]/,zr=ao(/[A-Za-z]/),Ln=ao(/[\dA-Za-z]/),a$=ao(/[#-'*+\--9=?A-Z^-~]/);function Dg(e){return e!==null&&(e<32||e===127)}const Ng=ao(/\d/),c$=ao(/[\dA-Fa-f]/),f$=ao(/[!-/:-@[-`{-~]/);function xe(e){return e!==null&&e<-2}function Sn(e){return e!==null&&(e<0||e===32)}function We(e){return e===-2||e===-1||e===32}const d$=ao(s$),p$=ao(/\s/);function ao(e){return t;function t(n){return n!==null&&e.test(String.fromCharCode(n))}}function rt(e,t,n,r){const i=r?r-1:Number.POSITIVE_INFINITY;let l=0;return s;function s(f){return We(f)?(e.enter(n),a(f)):t(f)}function a(f){return We(f)&&l++<i?(e.consume(f),a):(e.exit(n),t(f))}}const h$={tokenize:g$};function g$(e){const t=e.attempt(this.parser.constructs.contentInitial,r,i);let n;return t;function r(a){if(a===null){e.consume(a);return}return e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),rt(e,t,"linePrefix")}function i(a){return e.enter("paragraph"),l(a)}function l(a){const f=e.enter("chunkText",{contentType:"text",previous:n});return n&&(n.next=f),n=f,s(a)}function s(a){if(a===null){e.exit("chunkText"),e.exit("paragraph"),e.consume(a);return}return xe(a)?(e.consume(a),e.exit("chunkText"),l):(e.consume(a),s)}}const m$={tokenize:v$},$x={tokenize:y$};function v$(e){const t=this,n=[];let r=0,i,l,s;return a;function a(k){if(r<n.length){const P=n[r];return t.containerState=P[1],e.attempt(P[0].continuation,f,d)(k)}return d(k)}function f(k){if(r++,t.containerState._closeFlow){t.containerState._closeFlow=void 0,i&&x();const P=t.events.length;let D=P,T;for(;D--;)if(t.events[D][0]==="exit"&&t.events[D][1].type==="chunkFlow"){T=t.events[D][1].end;break}y(r);let F=P;for(;F<t.events.length;)t.events[F][1].end=Object.assign({},T),F++;return Wr(t.events,D+1,0,t.events.slice(P)),t.events.length=F,d(k)}return a(k)}function d(k){if(r===n.length){if(!i)return v(k);if(i.currentConstruct&&i.currentConstruct.concrete)return E(k);t.interrupt=!!(i.currentConstruct&&!i._gfmTableDynamicInterruptHack)}return t.containerState={},e.check($x,p,g)(k)}function p(k){return i&&x(),y(r),v(k)}function g(k){return t.parser.lazy[t.now().line]=r!==n.length,s=t.now().offset,E(k)}function v(k){return t.containerState={},e.attempt($x,m,E)(k)}function m(k){return r++,n.push([t.currentConstruct,t.containerState]),v(k)}function E(k){if(k===null){i&&x(),y(0),e.consume(k);return}return i=i||t.parser.flow(t.now()),e.enter("chunkFlow",{contentType:"flow",previous:l,_tokenizer:i}),S(k)}function S(k){if(k===null){A(e.exit("chunkFlow"),!0),y(0),e.consume(k);return}return xe(k)?(e.consume(k),A(e.exit("chunkFlow")),r=0,t.interrupt=void 0,a):(e.consume(k),S)}function A(k,P){const D=t.sliceStream(k);if(P&&D.push(null),k.previous=l,l&&(l.next=k),l=k,i.defineSkip(k.start),i.write(D),t.parser.lazy[k.start.line]){let T=i.events.length;for(;T--;)if(i.events[T][1].start.offset<s&&(!i.events[T][1].end||i.events[T][1].end.offset>s))return;const F=t.events.length;let z=F,G,q;for(;z--;)if(t.events[z][0]==="exit"&&t.events[z][1].type==="chunkFlow"){if(G){q=t.events[z][1].end;break}G=!0}for(y(r),T=F;T<t.events.length;)t.events[T][1].end=Object.assign({},q),T++;Wr(t.events,z+1,0,t.events.slice(F)),t.events.length=T}}function y(k){let P=n.length;for(;P-- >k;){const D=n[P];t.containerState=D[1],D[0].exit.call(t,e)}n.length=k}function x(){i.write([null]),l=void 0,i=void 0,t.containerState._closeFlow=void 0}}function y$(e,t,n){return rt(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}function Bx(e){if(e===null||Sn(e)||p$(e))return 1;if(d$(e))return 2}function Iv(e,t,n){const r=[];let i=-1;for(;++i<e.length;){const l=e[i].resolveAll;l&&!r.includes(l)&&(t=l(t,n),r.push(l))}return t}const Mg={name:"attention",tokenize:x$,resolveAll:w$};function w$(e,t){let n=-1,r,i,l,s,a,f,d,p;for(;++n<e.length;)if(e[n][0]==="enter"&&e[n][1].type==="attentionSequence"&&e[n][1]._close){for(r=n;r--;)if(e[r][0]==="exit"&&e[r][1].type==="attentionSequence"&&e[r][1]._open&&t.sliceSerialize(e[r][1]).charCodeAt(0)===t.sliceSerialize(e[n][1]).charCodeAt(0)){if((e[r][1]._close||e[n][1]._open)&&(e[n][1].end.offset-e[n][1].start.offset)%3&&!((e[r][1].end.offset-e[r][1].start.offset+e[n][1].end.offset-e[n][1].start.offset)%3))continue;f=e[r][1].end.offset-e[r][1].start.offset>1&&e[n][1].end.offset-e[n][1].start.offset>1?2:1;const g=Object.assign({},e[r][1].end),v=Object.assign({},e[n][1].start);Ux(g,-f),Ux(v,f),s={type:f>1?"strongSequence":"emphasisSequence",start:g,end:Object.assign({},e[r][1].end)},a={type:f>1?"strongSequence":"emphasisSequence",start:Object.assign({},e[n][1].start),end:v},l={type:f>1?"strongText":"emphasisText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},i={type:f>1?"strong":"emphasis",start:Object.assign({},s.start),end:Object.assign({},a.end)},e[r][1].end=Object.assign({},s.start),e[n][1].start=Object.assign({},a.end),d=[],e[r][1].end.offset-e[r][1].start.offset&&(d=ir(d,[["enter",e[r][1],t],["exit",e[r][1],t]])),d=ir(d,[["enter",i,t],["enter",s,t],["exit",s,t],["enter",l,t]]),d=ir(d,Iv(t.parser.constructs.insideSpan.null,e.slice(r+1,n),t)),d=ir(d,[["exit",l,t],["enter",a,t],["exit",a,t],["exit",i,t]]),e[n][1].end.offset-e[n][1].start.offset?(p=2,d=ir(d,[["enter",e[n][1],t],["exit",e[n][1],t]])):p=0,Wr(e,r-1,n-r+3,d),n=r+d.length-p-2;break}}for(n=-1;++n<e.length;)e[n][1].type==="attentionSequence"&&(e[n][1].type="data");return e}function x$(e,t){const n=this.parser.constructs.attentionMarkers.null,r=this.previous,i=Bx(r);let l;return s;function s(f){return l=f,e.enter("attentionSequence"),a(f)}function a(f){if(f===l)return e.consume(f),a;const d=e.exit("attentionSequence"),p=Bx(f),g=!p||p===2&&i||n.includes(f),v=!i||i===2&&p||n.includes(r);return d._open=!!(l===42?g:g&&(i||!v)),d._close=!!(l===42?v:v&&(p||!g)),t(f)}}function Ux(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}const S$={name:"autolink",tokenize:E$};function E$(e,t,n){let r=0;return i;function i(m){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(m),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),l}function l(m){return zr(m)?(e.consume(m),s):d(m)}function s(m){return m===43||m===45||m===46||Ln(m)?(r=1,a(m)):d(m)}function a(m){return m===58?(e.consume(m),r=0,f):(m===43||m===45||m===46||Ln(m))&&r++<32?(e.consume(m),a):(r=0,d(m))}function f(m){return m===62?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(m),e.exit("autolinkMarker"),e.exit("autolink"),t):m===null||m===32||m===60||Dg(m)?n(m):(e.consume(m),f)}function d(m){return m===64?(e.consume(m),p):a$(m)?(e.consume(m),d):n(m)}function p(m){return Ln(m)?g(m):n(m)}function g(m){return m===46?(e.consume(m),r=0,p):m===62?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(m),e.exit("autolinkMarker"),e.exit("autolink"),t):v(m)}function v(m){if((m===45||Ln(m))&&r++<63){const E=m===45?v:g;return e.consume(m),E}return n(m)}}const Zf={tokenize:C$,partial:!0};function C$(e,t,n){return r;function r(l){return We(l)?rt(e,i,"linePrefix")(l):i(l)}function i(l){return l===null||xe(l)?t(l):n(l)}}const vb={name:"blockQuote",tokenize:k$,continuation:{tokenize:b$},exit:_$};function k$(e,t,n){const r=this;return i;function i(s){if(s===62){const a=r.containerState;return a.open||(e.enter("blockQuote",{_container:!0}),a.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(s),e.exit("blockQuoteMarker"),l}return n(s)}function l(s){return We(s)?(e.enter("blockQuotePrefixWhitespace"),e.consume(s),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(s))}}function b$(e,t,n){const r=this;return i;function i(s){return We(s)?rt(e,l,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(s):l(s)}function l(s){return e.attempt(vb,t,n)(s)}}function _$(e){e.exit("blockQuote")}const yb={name:"characterEscape",tokenize:O$};function O$(e,t,n){return r;function r(l){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(l),e.exit("escapeMarker"),i}function i(l){return f$(l)?(e.enter("characterEscapeValue"),e.consume(l),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(l)}}const Hx=document.createElement("i");function Rv(e){const t="&"+e+";";Hx.innerHTML=t;const n=Hx.textContent;return n.charCodeAt(n.length-1)===59&&e!=="semi"||n===t?!1:n}const wb={name:"characterReference",tokenize:T$};function T$(e,t,n){const r=this;let i=0,l,s;return a;function a(g){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(g),e.exit("characterReferenceMarker"),f}function f(g){return g===35?(e.enter("characterReferenceMarkerNumeric"),e.consume(g),e.exit("characterReferenceMarkerNumeric"),d):(e.enter("characterReferenceValue"),l=31,s=Ln,p(g))}function d(g){return g===88||g===120?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(g),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),l=6,s=c$,p):(e.enter("characterReferenceValue"),l=7,s=Ng,p(g))}function p(g){if(g===59&&i){const v=e.exit("characterReferenceValue");return s===Ln&&!Rv(r.sliceSerialize(v))?n(g):(e.enter("characterReferenceMarker"),e.consume(g),e.exit("characterReferenceMarker"),e.exit("characterReference"),t)}return s(g)&&i++<l?(e.consume(g),p):n(g)}}const jx={tokenize:R$,partial:!0},Wx={name:"codeFenced",tokenize:I$,concrete:!0};function I$(e,t,n){const r=this,i={tokenize:D,partial:!0};let l=0,s=0,a;return f;function f(T){return d(T)}function d(T){const F=r.events[r.events.length-1];return l=F&&F[1].type==="linePrefix"?F[2].sliceSerialize(F[1],!0).length:0,a=T,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),p(T)}function p(T){return T===a?(s++,e.consume(T),p):s<3?n(T):(e.exit("codeFencedFenceSequence"),We(T)?rt(e,g,"whitespace")(T):g(T))}function g(T){return T===null||xe(T)?(e.exit("codeFencedFence"),r.interrupt?t(T):e.check(jx,S,P)(T)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),v(T))}function v(T){return T===null||xe(T)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),g(T)):We(T)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),rt(e,m,"whitespace")(T)):T===96&&T===a?n(T):(e.consume(T),v)}function m(T){return T===null||xe(T)?g(T):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),E(T))}function E(T){return T===null||xe(T)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),g(T)):T===96&&T===a?n(T):(e.consume(T),E)}function S(T){return e.attempt(i,P,A)(T)}function A(T){return e.enter("lineEnding"),e.consume(T),e.exit("lineEnding"),y}function y(T){return l>0&&We(T)?rt(e,x,"linePrefix",l+1)(T):x(T)}function x(T){return T===null||xe(T)?e.check(jx,S,P)(T):(e.enter("codeFlowValue"),k(T))}function k(T){return T===null||xe(T)?(e.exit("codeFlowValue"),x(T)):(e.consume(T),k)}function P(T){return e.exit("codeFenced"),t(T)}function D(T,F,z){let G=0;return q;function q(ee){return T.enter("lineEnding"),T.consume(ee),T.exit("lineEnding"),B}function B(ee){return T.enter("codeFencedFence"),We(ee)?rt(T,Q,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(ee):Q(ee)}function Q(ee){return ee===a?(T.enter("codeFencedFenceSequence"),U(ee)):z(ee)}function U(ee){return ee===a?(G++,T.consume(ee),U):G>=s?(T.exit("codeFencedFenceSequence"),We(ee)?rt(T,H,"whitespace")(ee):H(ee)):z(ee)}function H(ee){return ee===null||xe(ee)?(T.exit("codeFencedFence"),F(ee)):z(ee)}}}function R$(e,t,n){const r=this;return i;function i(s){return s===null?n(s):(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),l)}function l(s){return r.parser.lazy[r.now().line]?n(s):t(s)}}const hh={name:"codeIndented",tokenize:A$},P$={tokenize:D$,partial:!0};function A$(e,t,n){const r=this;return i;function i(d){return e.enter("codeIndented"),rt(e,l,"linePrefix",4+1)(d)}function l(d){const p=r.events[r.events.length-1];return p&&p[1].type==="linePrefix"&&p[2].sliceSerialize(p[1],!0).length>=4?s(d):n(d)}function s(d){return d===null?f(d):xe(d)?e.attempt(P$,s,f)(d):(e.enter("codeFlowValue"),a(d))}function a(d){return d===null||xe(d)?(e.exit("codeFlowValue"),s(d)):(e.consume(d),a)}function f(d){return e.exit("codeIndented"),t(d)}}function D$(e,t,n){const r=this;return i;function i(s){return r.parser.lazy[r.now().line]?n(s):xe(s)?(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),i):rt(e,l,"linePrefix",4+1)(s)}function l(s){const a=r.events[r.events.length-1];return a&&a[1].type==="linePrefix"&&a[2].sliceSerialize(a[1],!0).length>=4?t(s):xe(s)?i(s):n(s)}}const N$={name:"codeText",tokenize:F$,resolve:M$,previous:L$};function M$(e){let t=e.length-4,n=3,r,i;if((e[n][1].type==="lineEnding"||e[n][1].type==="space")&&(e[t][1].type==="lineEnding"||e[t][1].type==="space")){for(r=n;++r<t;)if(e[r][1].type==="codeTextData"){e[n][1].type="codeTextPadding",e[t][1].type="codeTextPadding",n+=2,t-=2;break}}for(r=n-1,t++;++r<=t;)i===void 0?r!==t&&e[r][1].type!=="lineEnding"&&(i=r):(r===t||e[r][1].type==="lineEnding")&&(e[i][1].type="codeTextData",r!==i+2&&(e[i][1].end=e[r-1][1].end,e.splice(i+2,r-i-2),t-=r-i-2,r=i+2),i=void 0);return e}function L$(e){return e!==96||this.events[this.events.length-1][1].type==="characterEscape"}function F$(e,t,n){let r=0,i,l;return s;function s(g){return e.enter("codeText"),e.enter("codeTextSequence"),a(g)}function a(g){return g===96?(e.consume(g),r++,a):(e.exit("codeTextSequence"),f(g))}function f(g){return g===null?n(g):g===32?(e.enter("space"),e.consume(g),e.exit("space"),f):g===96?(l=e.enter("codeTextSequence"),i=0,p(g)):xe(g)?(e.enter("lineEnding"),e.consume(g),e.exit("lineEnding"),f):(e.enter("codeTextData"),d(g))}function d(g){return g===null||g===32||g===96||xe(g)?(e.exit("codeTextData"),f(g)):(e.consume(g),d)}function p(g){return g===96?(e.consume(g),i++,p):i===r?(e.exit("codeTextSequence"),e.exit("codeText"),t(g)):(l.type="codeTextData",d(g))}}function xb(e){const t={};let n=-1,r,i,l,s,a,f,d;for(;++n<e.length;){for(;n in t;)n=t[n];if(r=e[n],n&&r[1].type==="chunkFlow"&&e[n-1][1].type==="listItemPrefix"&&(f=r[1]._tokenizer.events,l=0,l<f.length&&f[l][1].type==="lineEndingBlank"&&(l+=2),l<f.length&&f[l][1].type==="content"))for(;++l<f.length&&f[l][1].type!=="content";)f[l][1].type==="chunkText"&&(f[l][1]._isInFirstContentOfListItem=!0,l++);if(r[0]==="enter")r[1].contentType&&(Object.assign(t,z$(e,n)),n=t[n],d=!0);else if(r[1]._container){for(l=n,i=void 0;l--&&(s=e[l],s[1].type==="lineEnding"||s[1].type==="lineEndingBlank");)s[0]==="enter"&&(i&&(e[i][1].type="lineEndingBlank"),s[1].type="lineEnding",i=l);i&&(r[1].end=Object.assign({},e[i][1].start),a=e.slice(i,n),a.unshift(r),Wr(e,i,n-i+1,a))}}return!d}function z$(e,t){const n=e[t][1],r=e[t][2];let i=t-1;const l=[],s=n._tokenizer||r.parser[n.contentType](n.start),a=s.events,f=[],d={};let p,g,v=-1,m=n,E=0,S=0;const A=[S];for(;m;){for(;e[++i][1]!==m;);l.push(i),m._tokenizer||(p=r.sliceStream(m),m.next||p.push(null),g&&s.defineSkip(m.start),m._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=!0),s.write(p),m._isInFirstContentOfListItem&&(s._gfmTasklistFirstContentOfListItem=void 0)),g=m,m=m.next}for(m=n;++v<a.length;)a[v][0]==="exit"&&a[v-1][0]==="enter"&&a[v][1].type===a[v-1][1].type&&a[v][1].start.line!==a[v][1].end.line&&(S=v+1,A.push(S),m._tokenizer=void 0,m.previous=void 0,m=m.next);for(s.events=[],m?(m._tokenizer=void 0,m.previous=void 0):A.pop(),v=A.length;v--;){const y=a.slice(A[v],A[v+1]),x=l.pop();f.unshift([x,x+y.length-1]),Wr(e,x,2,y)}for(v=-1;++v<f.length;)d[E+f[v][0]]=E+f[v][1],E+=f[v][1]-f[v][0]-1;return d}const $$={tokenize:H$,resolve:U$},B$={tokenize:j$,partial:!0};function U$(e){return xb(e),e}function H$(e,t){let n;return r;function r(a){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),i(a)}function i(a){return a===null?l(a):xe(a)?e.check(B$,s,l)(a):(e.consume(a),i)}function l(a){return e.exit("chunkContent"),e.exit("content"),t(a)}function s(a){return e.consume(a),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,i}}function j$(e,t,n){const r=this;return i;function i(s){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),rt(e,l,"linePrefix")}function l(s){if(s===null||xe(s))return n(s);const a=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&a&&a[1].type==="linePrefix"&&a[2].sliceSerialize(a[1],!0).length>=4?t(s):e.interrupt(r.parser.constructs.flow,n,t)(s)}}function Sb(e,t,n,r,i,l,s,a,f){const d=f||Number.POSITIVE_INFINITY;let p=0;return g;function g(y){return y===60?(e.enter(r),e.enter(i),e.enter(l),e.consume(y),e.exit(l),v):y===null||y===32||y===41||Dg(y)?n(y):(e.enter(r),e.enter(s),e.enter(a),e.enter("chunkString",{contentType:"string"}),S(y))}function v(y){return y===62?(e.enter(l),e.consume(y),e.exit(l),e.exit(i),e.exit(r),t):(e.enter(a),e.enter("chunkString",{contentType:"string"}),m(y))}function m(y){return y===62?(e.exit("chunkString"),e.exit(a),v(y)):y===null||y===60||xe(y)?n(y):(e.consume(y),y===92?E:m)}function E(y){return y===60||y===62||y===92?(e.consume(y),m):m(y)}function S(y){return!p&&(y===null||y===41||Sn(y))?(e.exit("chunkString"),e.exit(a),e.exit(s),e.exit(r),t(y)):p<d&&y===40?(e.consume(y),p++,S):y===41?(e.consume(y),p--,S):y===null||y===32||y===40||Dg(y)?n(y):(e.consume(y),y===92?A:S)}function A(y){return y===40||y===41||y===92?(e.consume(y),S):S(y)}}function Eb(e,t,n,r,i,l){const s=this;let a=0,f;return d;function d(m){return e.enter(r),e.enter(i),e.consume(m),e.exit(i),e.enter(l),p}function p(m){return a>999||m===null||m===91||m===93&&!f||m===94&&!a&&"_hiddenFootnoteSupport"in s.parser.constructs?n(m):m===93?(e.exit(l),e.enter(i),e.consume(m),e.exit(i),e.exit(r),t):xe(m)?(e.enter("lineEnding"),e.consume(m),e.exit("lineEnding"),p):(e.enter("chunkString",{contentType:"string"}),g(m))}function g(m){return m===null||m===91||m===93||xe(m)||a++>999?(e.exit("chunkString"),p(m)):(e.consume(m),f||(f=!We(m)),m===92?v:g)}function v(m){return m===91||m===92||m===93?(e.consume(m),a++,g):g(m)}}function Cb(e,t,n,r,i,l){let s;return a;function a(v){return v===34||v===39||v===40?(e.enter(r),e.enter(i),e.consume(v),e.exit(i),s=v===40?41:v,f):n(v)}function f(v){return v===s?(e.enter(i),e.consume(v),e.exit(i),e.exit(r),t):(e.enter(l),d(v))}function d(v){return v===s?(e.exit(l),f(s)):v===null?n(v):xe(v)?(e.enter("lineEnding"),e.consume(v),e.exit("lineEnding"),rt(e,d,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),p(v))}function p(v){return v===s||v===null||xe(v)?(e.exit("chunkString"),d(v)):(e.consume(v),v===92?g:p)}function g(v){return v===s||v===92?(e.consume(v),p):p(v)}}function rs(e,t){let n;return r;function r(i){return xe(i)?(e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),n=!0,r):We(i)?rt(e,r,n?"linePrefix":"lineSuffix")(i):t(i)}}function Ll(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}const W$={name:"definition",tokenize:G$},V$={tokenize:q$,partial:!0};function G$(e,t,n){const r=this;let i;return l;function l(m){return e.enter("definition"),s(m)}function s(m){return Eb.call(r,e,a,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(m)}function a(m){return i=Ll(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)),m===58?(e.enter("definitionMarker"),e.consume(m),e.exit("definitionMarker"),f):n(m)}function f(m){return Sn(m)?rs(e,d)(m):d(m)}function d(m){return Sb(e,p,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(m)}function p(m){return e.attempt(V$,g,g)(m)}function g(m){return We(m)?rt(e,v,"whitespace")(m):v(m)}function v(m){return m===null||xe(m)?(e.exit("definition"),r.parser.defined.push(i),t(m)):n(m)}}function q$(e,t,n){return r;function r(a){return Sn(a)?rs(e,i)(a):n(a)}function i(a){return Cb(e,l,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(a)}function l(a){return We(a)?rt(e,s,"whitespace")(a):s(a)}function s(a){return a===null||xe(a)?t(a):n(a)}}const K$={name:"hardBreakEscape",tokenize:Q$};function Q$(e,t,n){return r;function r(l){return e.enter("hardBreakEscape"),e.consume(l),i}function i(l){return xe(l)?(e.exit("hardBreakEscape"),t(l)):n(l)}}const Y$={name:"headingAtx",tokenize:Z$,resolve:X$};function X$(e,t){let n=e.length-2,r=3,i,l;return e[r][1].type==="whitespace"&&(r+=2),n-2>r&&e[n][1].type==="whitespace"&&(n-=2),e[n][1].type==="atxHeadingSequence"&&(r===n-1||n-4>r&&e[n-2][1].type==="whitespace")&&(n-=r+1===n?2:4),n>r&&(i={type:"atxHeadingText",start:e[r][1].start,end:e[n][1].end},l={type:"chunkText",start:e[r][1].start,end:e[n][1].end,contentType:"text"},Wr(e,r,n-r+1,[["enter",i,t],["enter",l,t],["exit",l,t],["exit",i,t]])),e}function Z$(e,t,n){let r=0;return i;function i(p){return e.enter("atxHeading"),l(p)}function l(p){return e.enter("atxHeadingSequence"),s(p)}function s(p){return p===35&&r++<6?(e.consume(p),s):p===null||Sn(p)?(e.exit("atxHeadingSequence"),a(p)):n(p)}function a(p){return p===35?(e.enter("atxHeadingSequence"),f(p)):p===null||xe(p)?(e.exit("atxHeading"),t(p)):We(p)?rt(e,a,"whitespace")(p):(e.enter("atxHeadingText"),d(p))}function f(p){return p===35?(e.consume(p),f):(e.exit("atxHeadingSequence"),a(p))}function d(p){return p===null||p===35||Sn(p)?(e.exit("atxHeadingText"),a(p)):(e.consume(p),d)}}const J$=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],Vx=["pre","script","style","textarea"],e5={name:"htmlFlow",tokenize:i5,resolveTo:r5,concrete:!0},t5={tokenize:l5,partial:!0},n5={tokenize:o5,partial:!0};function r5(e){let t=e.length;for(;t--&&!(e[t][0]==="enter"&&e[t][1].type==="htmlFlow"););return t>1&&e[t-2][1].type==="linePrefix"&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e}function i5(e,t,n){const r=this;let i,l,s,a,f;return d;function d(O){return p(O)}function p(O){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(O),g}function g(O){return O===33?(e.consume(O),v):O===47?(e.consume(O),l=!0,S):O===63?(e.consume(O),i=3,r.interrupt?t:_):zr(O)?(e.consume(O),s=String.fromCharCode(O),A):n(O)}function v(O){return O===45?(e.consume(O),i=2,m):O===91?(e.consume(O),i=5,a=0,E):zr(O)?(e.consume(O),i=4,r.interrupt?t:_):n(O)}function m(O){return O===45?(e.consume(O),r.interrupt?t:_):n(O)}function E(O){const de="CDATA[";return O===de.charCodeAt(a++)?(e.consume(O),a===de.length?r.interrupt?t:Q:E):n(O)}function S(O){return zr(O)?(e.consume(O),s=String.fromCharCode(O),A):n(O)}function A(O){if(O===null||O===47||O===62||Sn(O)){const de=O===47,Re=s.toLowerCase();return!de&&!l&&Vx.includes(Re)?(i=1,r.interrupt?t(O):Q(O)):J$.includes(s.toLowerCase())?(i=6,de?(e.consume(O),y):r.interrupt?t(O):Q(O)):(i=7,r.interrupt&&!r.parser.lazy[r.now().line]?n(O):l?x(O):k(O))}return O===45||Ln(O)?(e.consume(O),s+=String.fromCharCode(O),A):n(O)}function y(O){return O===62?(e.consume(O),r.interrupt?t:Q):n(O)}function x(O){return We(O)?(e.consume(O),x):q(O)}function k(O){return O===47?(e.consume(O),q):O===58||O===95||zr(O)?(e.consume(O),P):We(O)?(e.consume(O),k):q(O)}function P(O){return O===45||O===46||O===58||O===95||Ln(O)?(e.consume(O),P):D(O)}function D(O){return O===61?(e.consume(O),T):We(O)?(e.consume(O),D):k(O)}function T(O){return O===null||O===60||O===61||O===62||O===96?n(O):O===34||O===39?(e.consume(O),f=O,F):We(O)?(e.consume(O),T):z(O)}function F(O){return O===f?(e.consume(O),f=null,G):O===null||xe(O)?n(O):(e.consume(O),F)}function z(O){return O===null||O===34||O===39||O===47||O===60||O===61||O===62||O===96||Sn(O)?D(O):(e.consume(O),z)}function G(O){return O===47||O===62||We(O)?k(O):n(O)}function q(O){return O===62?(e.consume(O),B):n(O)}function B(O){return O===null||xe(O)?Q(O):We(O)?(e.consume(O),B):n(O)}function Q(O){return O===45&&i===2?(e.consume(O),le):O===60&&i===1?(e.consume(O),oe):O===62&&i===4?(e.consume(O),ne):O===63&&i===3?(e.consume(O),_):O===93&&i===5?(e.consume(O),Z):xe(O)&&(i===6||i===7)?(e.exit("htmlFlowData"),e.check(t5,ae,U)(O)):O===null||xe(O)?(e.exit("htmlFlowData"),U(O)):(e.consume(O),Q)}function U(O){return e.check(n5,H,ae)(O)}function H(O){return e.enter("lineEnding"),e.consume(O),e.exit("lineEnding"),ee}function ee(O){return O===null||xe(O)?U(O):(e.enter("htmlFlowData"),Q(O))}function le(O){return O===45?(e.consume(O),_):Q(O)}function oe(O){return O===47?(e.consume(O),s="",$):Q(O)}function $(O){if(O===62){const de=s.toLowerCase();return Vx.includes(de)?(e.consume(O),ne):Q(O)}return zr(O)&&s.length<8?(e.consume(O),s+=String.fromCharCode(O),$):Q(O)}function Z(O){return O===93?(e.consume(O),_):Q(O)}function _(O){return O===62?(e.consume(O),ne):O===45&&i===2?(e.consume(O),_):Q(O)}function ne(O){return O===null||xe(O)?(e.exit("htmlFlowData"),ae(O)):(e.consume(O),ne)}function ae(O){return e.exit("htmlFlow"),t(O)}}function o5(e,t,n){const r=this;return i;function i(s){return xe(s)?(e.enter("lineEnding"),e.consume(s),e.exit("lineEnding"),l):n(s)}function l(s){return r.parser.lazy[r.now().line]?n(s):t(s)}}function l5(e,t,n){return r;function r(i){return e.enter("lineEnding"),e.consume(i),e.exit("lineEnding"),e.attempt(Zf,t,n)}}const u5={name:"htmlText",tokenize:s5};function s5(e,t,n){const r=this;let i,l,s;return a;function a(_){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(_),f}function f(_){return _===33?(e.consume(_),d):_===47?(e.consume(_),D):_===63?(e.consume(_),k):zr(_)?(e.consume(_),z):n(_)}function d(_){return _===45?(e.consume(_),p):_===91?(e.consume(_),l=0,E):zr(_)?(e.consume(_),x):n(_)}function p(_){return _===45?(e.consume(_),m):n(_)}function g(_){return _===null?n(_):_===45?(e.consume(_),v):xe(_)?(s=g,oe(_)):(e.consume(_),g)}function v(_){return _===45?(e.consume(_),m):g(_)}function m(_){return _===62?le(_):_===45?v(_):g(_)}function E(_){const ne="CDATA[";return _===ne.charCodeAt(l++)?(e.consume(_),l===ne.length?S:E):n(_)}function S(_){return _===null?n(_):_===93?(e.consume(_),A):xe(_)?(s=S,oe(_)):(e.consume(_),S)}function A(_){return _===93?(e.consume(_),y):S(_)}function y(_){return _===62?le(_):_===93?(e.consume(_),y):S(_)}function x(_){return _===null||_===62?le(_):xe(_)?(s=x,oe(_)):(e.consume(_),x)}function k(_){return _===null?n(_):_===63?(e.consume(_),P):xe(_)?(s=k,oe(_)):(e.consume(_),k)}function P(_){return _===62?le(_):k(_)}function D(_){return zr(_)?(e.consume(_),T):n(_)}function T(_){return _===45||Ln(_)?(e.consume(_),T):F(_)}function F(_){return xe(_)?(s=F,oe(_)):We(_)?(e.consume(_),F):le(_)}function z(_){return _===45||Ln(_)?(e.consume(_),z):_===47||_===62||Sn(_)?G(_):n(_)}function G(_){return _===47?(e.consume(_),le):_===58||_===95||zr(_)?(e.consume(_),q):xe(_)?(s=G,oe(_)):We(_)?(e.consume(_),G):le(_)}function q(_){return _===45||_===46||_===58||_===95||Ln(_)?(e.consume(_),q):B(_)}function B(_){return _===61?(e.consume(_),Q):xe(_)?(s=B,oe(_)):We(_)?(e.consume(_),B):G(_)}function Q(_){return _===null||_===60||_===61||_===62||_===96?n(_):_===34||_===39?(e.consume(_),i=_,U):xe(_)?(s=Q,oe(_)):We(_)?(e.consume(_),Q):(e.consume(_),H)}function U(_){return _===i?(e.consume(_),i=void 0,ee):_===null?n(_):xe(_)?(s=U,oe(_)):(e.consume(_),U)}function H(_){return _===null||_===34||_===39||_===60||_===61||_===96?n(_):_===47||_===62||Sn(_)?G(_):(e.consume(_),H)}function ee(_){return _===47||_===62||Sn(_)?G(_):n(_)}function le(_){return _===62?(e.consume(_),e.exit("htmlTextData"),e.exit("htmlText"),t):n(_)}function oe(_){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(_),e.exit("lineEnding"),$}function $(_){return We(_)?rt(e,Z,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(_):Z(_)}function Z(_){return e.enter("htmlTextData"),s(_)}}const Pv={name:"labelEnd",tokenize:h5,resolveTo:p5,resolveAll:d5},a5={tokenize:g5},c5={tokenize:m5},f5={tokenize:v5};function d5(e){let t=-1;for(;++t<e.length;){const n=e[t][1];(n.type==="labelImage"||n.type==="labelLink"||n.type==="labelEnd")&&(e.splice(t+1,n.type==="labelImage"?4:2),n.type="data",t++)}return e}function p5(e,t){let n=e.length,r=0,i,l,s,a;for(;n--;)if(i=e[n][1],l){if(i.type==="link"||i.type==="labelLink"&&i._inactive)break;e[n][0]==="enter"&&i.type==="labelLink"&&(i._inactive=!0)}else if(s){if(e[n][0]==="enter"&&(i.type==="labelImage"||i.type==="labelLink")&&!i._balanced&&(l=n,i.type!=="labelLink")){r=2;break}}else i.type==="labelEnd"&&(s=n);const f={type:e[l][1].type==="labelLink"?"link":"image",start:Object.assign({},e[l][1].start),end:Object.assign({},e[e.length-1][1].end)},d={type:"label",start:Object.assign({},e[l][1].start),end:Object.assign({},e[s][1].end)},p={type:"labelText",start:Object.assign({},e[l+r+2][1].end),end:Object.assign({},e[s-2][1].start)};return a=[["enter",f,t],["enter",d,t]],a=ir(a,e.slice(l+1,l+r+3)),a=ir(a,[["enter",p,t]]),a=ir(a,Iv(t.parser.constructs.insideSpan.null,e.slice(l+r+4,s-3),t)),a=ir(a,[["exit",p,t],e[s-2],e[s-1],["exit",d,t]]),a=ir(a,e.slice(s+1)),a=ir(a,[["exit",f,t]]),Wr(e,l,e.length,a),e}function h5(e,t,n){const r=this;let i=r.events.length,l,s;for(;i--;)if((r.events[i][1].type==="labelImage"||r.events[i][1].type==="labelLink")&&!r.events[i][1]._balanced){l=r.events[i][1];break}return a;function a(v){return l?l._inactive?g(v):(s=r.parser.defined.includes(Ll(r.sliceSerialize({start:l.end,end:r.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(v),e.exit("labelMarker"),e.exit("labelEnd"),f):n(v)}function f(v){return v===40?e.attempt(a5,p,s?p:g)(v):v===91?e.attempt(c5,p,s?d:g)(v):s?p(v):g(v)}function d(v){return e.attempt(f5,p,g)(v)}function p(v){return t(v)}function g(v){return l._balanced=!0,n(v)}}function g5(e,t,n){return r;function r(g){return e.enter("resource"),e.enter("resourceMarker"),e.consume(g),e.exit("resourceMarker"),i}function i(g){return Sn(g)?rs(e,l)(g):l(g)}function l(g){return g===41?p(g):Sb(e,s,a,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(g)}function s(g){return Sn(g)?rs(e,f)(g):p(g)}function a(g){return n(g)}function f(g){return g===34||g===39||g===40?Cb(e,d,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(g):p(g)}function d(g){return Sn(g)?rs(e,p)(g):p(g)}function p(g){return g===41?(e.enter("resourceMarker"),e.consume(g),e.exit("resourceMarker"),e.exit("resource"),t):n(g)}}function m5(e,t,n){const r=this;return i;function i(a){return Eb.call(r,e,l,s,"reference","referenceMarker","referenceString")(a)}function l(a){return r.parser.defined.includes(Ll(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(a):n(a)}function s(a){return n(a)}}function v5(e,t,n){return r;function r(l){return e.enter("reference"),e.enter("referenceMarker"),e.consume(l),e.exit("referenceMarker"),i}function i(l){return l===93?(e.enter("referenceMarker"),e.consume(l),e.exit("referenceMarker"),e.exit("reference"),t):n(l)}}const y5={name:"labelStartImage",tokenize:w5,resolveAll:Pv.resolveAll};function w5(e,t,n){const r=this;return i;function i(a){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(a),e.exit("labelImageMarker"),l}function l(a){return a===91?(e.enter("labelMarker"),e.consume(a),e.exit("labelMarker"),e.exit("labelImage"),s):n(a)}function s(a){return a===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(a):t(a)}}const x5={name:"labelStartLink",tokenize:S5,resolveAll:Pv.resolveAll};function S5(e,t,n){const r=this;return i;function i(s){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(s),e.exit("labelMarker"),e.exit("labelLink"),l}function l(s){return s===94&&"_hiddenFootnoteSupport"in r.parser.constructs?n(s):t(s)}}const gh={name:"lineEnding",tokenize:E5};function E5(e,t){return n;function n(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),rt(e,t,"linePrefix")}}const dc={name:"thematicBreak",tokenize:C5};function C5(e,t,n){let r=0,i;return l;function l(d){return e.enter("thematicBreak"),s(d)}function s(d){return i=d,a(d)}function a(d){return d===i?(e.enter("thematicBreakSequence"),f(d)):r>=3&&(d===null||xe(d))?(e.exit("thematicBreak"),t(d)):n(d)}function f(d){return d===i?(e.consume(d),r++,f):(e.exit("thematicBreakSequence"),We(d)?rt(e,a,"whitespace")(d):a(d))}}const dn={name:"list",tokenize:_5,continuation:{tokenize:O5},exit:I5},k5={tokenize:R5,partial:!0},b5={tokenize:T5,partial:!0};function _5(e,t,n){const r=this,i=r.events[r.events.length-1];let l=i&&i[1].type==="linePrefix"?i[2].sliceSerialize(i[1],!0).length:0,s=0;return a;function a(m){const E=r.containerState.type||(m===42||m===43||m===45?"listUnordered":"listOrdered");if(E==="listUnordered"?!r.containerState.marker||m===r.containerState.marker:Ng(m)){if(r.containerState.type||(r.containerState.type=E,e.enter(E,{_container:!0})),E==="listUnordered")return e.enter("listItemPrefix"),m===42||m===45?e.check(dc,n,d)(m):d(m);if(!r.interrupt||m===49)return e.enter("listItemPrefix"),e.enter("listItemValue"),f(m)}return n(m)}function f(m){return Ng(m)&&++s<10?(e.consume(m),f):(!r.interrupt||s<2)&&(r.containerState.marker?m===r.containerState.marker:m===41||m===46)?(e.exit("listItemValue"),d(m)):n(m)}function d(m){return e.enter("listItemMarker"),e.consume(m),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||m,e.check(Zf,r.interrupt?n:p,e.attempt(k5,v,g))}function p(m){return r.containerState.initialBlankLine=!0,l++,v(m)}function g(m){return We(m)?(e.enter("listItemPrefixWhitespace"),e.consume(m),e.exit("listItemPrefixWhitespace"),v):n(m)}function v(m){return r.containerState.size=l+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(m)}}function O5(e,t,n){const r=this;return r.containerState._closeFlow=void 0,e.check(Zf,i,l);function i(a){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,rt(e,t,"listItemIndent",r.containerState.size+1)(a)}function l(a){return r.containerState.furtherBlankLines||!We(a)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,s(a)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(b5,t,s)(a))}function s(a){return r.containerState._closeFlow=!0,r.interrupt=void 0,rt(e,e.attempt(dn,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(a)}}function T5(e,t,n){const r=this;return rt(e,i,"listItemIndent",r.containerState.size+1);function i(l){const s=r.events[r.events.length-1];return s&&s[1].type==="listItemIndent"&&s[2].sliceSerialize(s[1],!0).length===r.containerState.size?t(l):n(l)}}function I5(e){e.exit(this.containerState.type)}function R5(e,t,n){const r=this;return rt(e,i,"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4+1);function i(l){const s=r.events[r.events.length-1];return!We(l)&&s&&s[1].type==="listItemPrefixWhitespace"?t(l):n(l)}}const Gx={name:"setextUnderline",tokenize:A5,resolveTo:P5};function P5(e,t){let n=e.length,r,i,l;for(;n--;)if(e[n][0]==="enter"){if(e[n][1].type==="content"){r=n;break}e[n][1].type==="paragraph"&&(i=n)}else e[n][1].type==="content"&&e.splice(n,1),!l&&e[n][1].type==="definition"&&(l=n);const s={type:"setextHeading",start:Object.assign({},e[i][1].start),end:Object.assign({},e[e.length-1][1].end)};return e[i][1].type="setextHeadingText",l?(e.splice(i,0,["enter",s,t]),e.splice(l+1,0,["exit",e[r][1],t]),e[r][1].end=Object.assign({},e[l][1].end)):e[r][1]=s,e.push(["exit",s,t]),e}function A5(e,t,n){const r=this;let i;return l;function l(d){let p=r.events.length,g;for(;p--;)if(r.events[p][1].type!=="lineEnding"&&r.events[p][1].type!=="linePrefix"&&r.events[p][1].type!=="content"){g=r.events[p][1].type==="paragraph";break}return!r.parser.lazy[r.now().line]&&(r.interrupt||g)?(e.enter("setextHeadingLine"),i=d,s(d)):n(d)}function s(d){return e.enter("setextHeadingLineSequence"),a(d)}function a(d){return d===i?(e.consume(d),a):(e.exit("setextHeadingLineSequence"),We(d)?rt(e,f,"lineSuffix")(d):f(d))}function f(d){return d===null||xe(d)?(e.exit("setextHeadingLine"),t(d)):n(d)}}const D5={tokenize:N5};function N5(e){const t=this,n=e.attempt(Zf,r,e.attempt(this.parser.constructs.flowInitial,i,rt(e,e.attempt(this.parser.constructs.flow,i,e.attempt($$,i)),"linePrefix")));return n;function r(l){if(l===null){e.consume(l);return}return e.enter("lineEndingBlank"),e.consume(l),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n}function i(l){if(l===null){e.consume(l);return}return e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),t.currentConstruct=void 0,n}}const M5={resolveAll:bb()},L5=kb("string"),F5=kb("text");function kb(e){return{tokenize:t,resolveAll:bb(e==="text"?z5:void 0)};function t(n){const r=this,i=this.parser.constructs[e],l=n.attempt(i,s,a);return s;function s(p){return d(p)?l(p):a(p)}function a(p){if(p===null){n.consume(p);return}return n.enter("data"),n.consume(p),f}function f(p){return d(p)?(n.exit("data"),l(p)):(n.consume(p),f)}function d(p){if(p===null)return!0;const g=i[p];let v=-1;if(g)for(;++v<g.length;){const m=g[v];if(!m.previous||m.previous.call(r,r.previous))return!0}return!1}}}function bb(e){return t;function t(n,r){let i=-1,l;for(;++i<=n.length;)l===void 0?n[i]&&n[i][1].type==="data"&&(l=i,i++):(!n[i]||n[i][1].type!=="data")&&(i!==l+2&&(n[l][1].end=n[i-1][1].end,n.splice(l+2,i-l-2),i=l+2),l=void 0);return e?e(n,r):n}}function z5(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||e[n][1].type==="lineEnding")&&e[n-1][1].type==="data"){const r=e[n-1][1],i=t.sliceStream(r);let l=i.length,s=-1,a=0,f;for(;l--;){const d=i[l];if(typeof d=="string"){for(s=d.length;d.charCodeAt(s-1)===32;)a++,s--;if(s)break;s=-1}else if(d===-2)f=!0,a++;else if(d!==-1){l++;break}}if(a){const d={type:n===e.length||f||a<2?"lineSuffix":"hardBreakTrailing",start:{line:r.end.line,column:r.end.column-a,offset:r.end.offset-a,_index:r.start._index+l,_bufferIndex:l?s:r.start._bufferIndex+s},end:Object.assign({},r.end)};r.end=Object.assign({},d.start),r.start.offset===r.end.offset?Object.assign(r,d):(e.splice(n,0,["enter",d,t],["exit",d,t]),n+=2)}n++}return e}function $5(e,t,n){let r=Object.assign(n?Object.assign({},n):{line:1,column:1,offset:0},{_index:0,_bufferIndex:-1});const i={},l=[];let s=[],a=[];const f={consume:x,enter:k,exit:P,attempt:F(D),check:F(T),interrupt:F(T,{interrupt:!0})},d={previous:null,code:null,containerState:{},events:[],parser:e,sliceStream:m,sliceSerialize:v,now:E,defineSkip:S,write:g};let p=t.tokenize.call(d,f);return t.resolveAll&&l.push(t),d;function g(B){return s=ir(s,B),A(),s[s.length-1]!==null?[]:(z(t,0),d.events=Iv(l,d.events,d),d.events)}function v(B,Q){return U5(m(B),Q)}function m(B){return B5(s,B)}function E(){const{line:B,column:Q,offset:U,_index:H,_bufferIndex:ee}=r;return{line:B,column:Q,offset:U,_index:H,_bufferIndex:ee}}function S(B){i[B.line]=B.column,q()}function A(){let B;for(;r._index<s.length;){const Q=s[r._index];if(typeof Q=="string")for(B=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===B&&r._bufferIndex<Q.length;)y(Q.charCodeAt(r._bufferIndex));else y(Q)}}function y(B){p=p(B)}function x(B){xe(B)?(r.line++,r.column=1,r.offset+=B===-3?2:1,q()):B!==-1&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===s[r._index].length&&(r._bufferIndex=-1,r._index++)),d.previous=B}function k(B,Q){const U=Q||{};return U.type=B,U.start=E(),d.events.push(["enter",U,d]),a.push(U),U}function P(B){const Q=a.pop();return Q.end=E(),d.events.push(["exit",Q,d]),Q}function D(B,Q){z(B,Q.from)}function T(B,Q){Q.restore()}function F(B,Q){return U;function U(H,ee,le){let oe,$,Z,_;return Array.isArray(H)?ae(H):"tokenize"in H?ae([H]):ne(H);function ne(me){return Ae;function Ae(Se){const Le=Se!==null&&me[Se],Te=Se!==null&&me.null,Be=[...Array.isArray(Le)?Le:Le?[Le]:[],...Array.isArray(Te)?Te:Te?[Te]:[]];return ae(Be)(Se)}}function ae(me){return oe=me,$=0,me.length===0?le:O(me[$])}function O(me){return Ae;function Ae(Se){return _=G(),Z=me,me.partial||(d.currentConstruct=me),me.name&&d.parser.constructs.disable.null.includes(me.name)?Re():me.tokenize.call(Q?Object.assign(Object.create(d),Q):d,f,de,Re)(Se)}}function de(me){return B(Z,_),ee}function Re(me){return _.restore(),++$<oe.length?O(oe[$]):le}}}function z(B,Q){B.resolveAll&&!l.includes(B)&&l.push(B),B.resolve&&Wr(d.events,Q,d.events.length-Q,B.resolve(d.events.slice(Q),d)),B.resolveTo&&(d.events=B.resolveTo(d.events,d))}function G(){const B=E(),Q=d.previous,U=d.currentConstruct,H=d.events.length,ee=Array.from(a);return{restore:le,from:H};function le(){r=B,d.previous=Q,d.currentConstruct=U,d.events.length=H,a=ee,q()}}function q(){r.line in i&&r.column<2&&(r.column=i[r.line],r.offset+=i[r.line]-1)}}function B5(e,t){const n=t.start._index,r=t.start._bufferIndex,i=t.end._index,l=t.end._bufferIndex;let s;if(n===i)s=[e[n].slice(r,l)];else{if(s=e.slice(n,i),r>-1){const a=s[0];typeof a=="string"?s[0]=a.slice(r):s.shift()}l>0&&s.push(e[i].slice(0,l))}return s}function U5(e,t){let n=-1;const r=[];let i;for(;++n<e.length;){const l=e[n];let s;if(typeof l=="string")s=l;else switch(l){case-5:{s="\r";break}case-4:{s=`
`;break}case-3:{s=`\r
`;break}case-2:{s=t?" ":"	";break}case-1:{if(!t&&i)continue;s=" ";break}default:s=String.fromCharCode(l)}i=l===-2,r.push(s)}return r.join("")}const H5={42:dn,43:dn,45:dn,48:dn,49:dn,50:dn,51:dn,52:dn,53:dn,54:dn,55:dn,56:dn,57:dn,62:vb},j5={91:W$},W5={[-2]:hh,[-1]:hh,32:hh},V5={35:Y$,42:dc,45:[Gx,dc],60:e5,61:Gx,95:dc,96:Wx,126:Wx},G5={38:wb,92:yb},q5={[-5]:gh,[-4]:gh,[-3]:gh,33:y5,38:wb,42:Mg,60:[S$,u5],91:x5,92:[K$,yb],93:Pv,95:Mg,96:N$},K5={null:[Mg,M5]},Q5={null:[42,95]},Y5={null:[]},X5=Object.freeze(Object.defineProperty({__proto__:null,attentionMarkers:Q5,contentInitial:j5,disable:Y5,document:H5,flow:V5,flowInitial:W5,insideSpan:K5,string:G5,text:q5},Symbol.toStringTag,{value:"Module"}));function Z5(e){const n=o$([X5,...(e||{}).extensions||[]]),r={defined:[],lazy:{},constructs:n,content:i(h$),document:i(m$),flow:i(D5),string:i(L5),text:i(F5)};return r;function i(l){return s;function s(a){return $5(r,l,a)}}}const qx=/[\0\t\n\r]/g;function J5(){let e=1,t="",n=!0,r;return i;function i(l,s,a){const f=[];let d,p,g,v,m;for(l=t+l.toString(s),g=0,t="",n&&(l.charCodeAt(0)===65279&&g++,n=void 0);g<l.length;){if(qx.lastIndex=g,d=qx.exec(l),v=d&&d.index!==void 0?d.index:l.length,m=l.charCodeAt(v),!d){t=l.slice(g);break}if(m===10&&g===v&&r)f.push(-3),r=void 0;else switch(r&&(f.push(-5),r=void 0),g<v&&(f.push(l.slice(g,v)),e+=v-g),m){case 0:{f.push(65533),e++;break}case 9:{for(p=Math.ceil(e/4)*4,f.push(-2);e++<p;)f.push(-1);break}case 10:{f.push(-4),e=1;break}default:r=!0,e=1}g=v+1}return a&&(r&&f.push(-5),t&&f.push(t),f.push(null)),f}}function eB(e){for(;!xb(e););return e}function _b(e,t){const n=Number.parseInt(e,t);return n<9||n===11||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(n&65535)===65535||(n&65535)===65534||n>1114111?"�":String.fromCharCode(n)}const tB=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function nB(e){return e.replace(tB,rB)}function rB(e,t,n){if(t)return t;if(n.charCodeAt(0)===35){const i=n.charCodeAt(1),l=i===120||i===88;return _b(n.slice(l?2:1),l?16:10)}return Rv(n)||e}const Ob={}.hasOwnProperty,iB=function(e,t,n){return typeof t!="string"&&(n=t,t=void 0),oB(n)(eB(Z5(n).document().write(J5()(e,t,!0))))};function oB(e){const t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:a(kn),autolinkProtocol:B,autolinkEmail:B,atxHeading:a(he),blockQuote:a(Be),characterEscape:B,characterReference:B,codeFenced:a(Fe),codeFencedFenceInfo:f,codeFencedFenceMeta:f,codeIndented:a(Fe,f),codeText:a(qe,f),codeTextData:B,data:B,codeFlowValue:B,definition:a(yt),definitionDestinationString:f,definitionLabelString:f,definitionTitleString:f,emphasis:a(Wn),hardBreakEscape:a(at),hardBreakTrailing:a(at),htmlFlow:a(Mt,f),htmlFlowData:B,htmlText:a(Mt,f),htmlTextData:B,image:a(Cn),label:f,link:a(kn),listItem:a(Tr),listItemValue:E,listOrdered:a(Ht,m),listUnordered:a(Ht),paragraph:a(Gr),reference:Re,referenceString:f,resourceDestinationString:f,resourceTitleString:f,setextHeading:a(he),strong:a(Go),thematicBreak:a(Vn)},exit:{atxHeading:p(),atxHeadingSequence:F,autolink:p(),autolinkEmail:Te,autolinkProtocol:Le,blockQuote:p(),characterEscapeValue:Q,characterReferenceMarkerHexadecimal:Ae,characterReferenceMarkerNumeric:Ae,characterReferenceValue:Se,codeFenced:p(x),codeFencedFence:y,codeFencedFenceInfo:S,codeFencedFenceMeta:A,codeFlowValue:Q,codeIndented:p(k),codeText:p(oe),codeTextData:Q,data:Q,definition:p(),definitionDestinationString:T,definitionLabelString:P,definitionTitleString:D,emphasis:p(),hardBreakEscape:p(H),hardBreakTrailing:p(H),htmlFlow:p(ee),htmlFlowData:Q,htmlText:p(le),htmlTextData:Q,image:p(Z),label:ne,labelText:_,lineEnding:U,link:p($),listItem:p(),listOrdered:p(),listUnordered:p(),paragraph:p(),referenceString:me,resourceDestinationString:ae,resourceTitleString:O,resource:de,setextHeading:p(q),setextHeadingLineSequence:G,setextHeadingText:z,strong:p(),thematicBreak:p()}};Tb(t,(e||{}).mdastExtensions||[]);const n={};return r;function r(V){let re={type:"root",children:[]};const pe={stack:[re],tokenStack:[],config:t,enter:d,exit:g,buffer:f,resume:v,setData:l,getData:s},Ue=[];let He=-1;for(;++He<V.length;)if(V[He][1].type==="listOrdered"||V[He][1].type==="listUnordered")if(V[He][0]==="enter")Ue.push(He);else{const Zt=Ue.pop();He=i(V,Zt,He)}for(He=-1;++He<V.length;){const Zt=t[V[He][0]];Ob.call(Zt,V[He][1].type)&&Zt[V[He][1].type].call(Object.assign({sliceSerialize:V[He][2].sliceSerialize},pe),V[He][1])}if(pe.tokenStack.length>0){const Zt=pe.tokenStack[pe.tokenStack.length-1];(Zt[1]||Kx).call(pe,void 0,Zt[0])}for(re.position={start:Ri(V.length>0?V[0][1].start:{line:1,column:1,offset:0}),end:Ri(V.length>0?V[V.length-2][1].end:{line:1,column:1,offset:0})},He=-1;++He<t.transforms.length;)re=t.transforms[He](re)||re;return re}function i(V,re,pe){let Ue=re-1,He=-1,Zt=!1,Gn,bn,qr,Kr;for(;++Ue<=pe;){const it=V[Ue];if(it[1].type==="listUnordered"||it[1].type==="listOrdered"||it[1].type==="blockQuote"?(it[0]==="enter"?He++:He--,Kr=void 0):it[1].type==="lineEndingBlank"?it[0]==="enter"&&(Gn&&!Kr&&!He&&!qr&&(qr=Ue),Kr=void 0):it[1].type==="linePrefix"||it[1].type==="listItemValue"||it[1].type==="listItemMarker"||it[1].type==="listItemPrefix"||it[1].type==="listItemPrefixWhitespace"||(Kr=void 0),!He&&it[0]==="enter"&&it[1].type==="listItemPrefix"||He===-1&&it[0]==="exit"&&(it[1].type==="listUnordered"||it[1].type==="listOrdered")){if(Gn){let su=Ue;for(bn=void 0;su--;){const pr=V[su];if(pr[1].type==="lineEnding"||pr[1].type==="lineEndingBlank"){if(pr[0]==="exit")continue;bn&&(V[bn][1].type="lineEndingBlank",Zt=!0),pr[1].type="lineEnding",bn=su}else if(!(pr[1].type==="linePrefix"||pr[1].type==="blockQuotePrefix"||pr[1].type==="blockQuotePrefixWhitespace"||pr[1].type==="blockQuoteMarker"||pr[1].type==="listItemIndent"))break}qr&&(!bn||qr<bn)&&(Gn._spread=!0),Gn.end=Object.assign({},bn?V[bn][1].start:it[1].end),V.splice(bn||Ue,0,["exit",Gn,it[2]]),Ue++,pe++}it[1].type==="listItemPrefix"&&(Gn={type:"listItem",_spread:!1,start:Object.assign({},it[1].start),end:void 0},V.splice(Ue,0,["enter",Gn,it[2]]),Ue++,pe++,qr=void 0,Kr=!0)}}return V[re][1]._spread=Zt,pe}function l(V,re){n[V]=re}function s(V){return n[V]}function a(V,re){return pe;function pe(Ue){d.call(this,V(Ue),Ue),re&&re.call(this,Ue)}}function f(){this.stack.push({type:"fragment",children:[]})}function d(V,re,pe){return this.stack[this.stack.length-1].children.push(V),this.stack.push(V),this.tokenStack.push([re,pe]),V.position={start:Ri(re.start)},V}function p(V){return re;function re(pe){V&&V.call(this,pe),g.call(this,pe)}}function g(V,re){const pe=this.stack.pop(),Ue=this.tokenStack.pop();if(Ue)Ue[0].type!==V.type&&(re?re.call(this,V,Ue[0]):(Ue[1]||Kx).call(this,V,Ue[0]));else throw new Error("Cannot close `"+V.type+"` ("+ns({start:V.start,end:V.end})+"): it’s not open");return pe.position.end=Ri(V.end),pe}function v(){return r$(this.stack.pop())}function m(){l("expectingFirstListItemValue",!0)}function E(V){if(s("expectingFirstListItemValue")){const re=this.stack[this.stack.length-2];re.start=Number.parseInt(this.sliceSerialize(V),10),l("expectingFirstListItemValue")}}function S(){const V=this.resume(),re=this.stack[this.stack.length-1];re.lang=V}function A(){const V=this.resume(),re=this.stack[this.stack.length-1];re.meta=V}function y(){s("flowCodeInside")||(this.buffer(),l("flowCodeInside",!0))}function x(){const V=this.resume(),re=this.stack[this.stack.length-1];re.value=V.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),l("flowCodeInside")}function k(){const V=this.resume(),re=this.stack[this.stack.length-1];re.value=V.replace(/(\r?\n|\r)$/g,"")}function P(V){const re=this.resume(),pe=this.stack[this.stack.length-1];pe.label=re,pe.identifier=Ll(this.sliceSerialize(V)).toLowerCase()}function D(){const V=this.resume(),re=this.stack[this.stack.length-1];re.title=V}function T(){const V=this.resume(),re=this.stack[this.stack.length-1];re.url=V}function F(V){const re=this.stack[this.stack.length-1];if(!re.depth){const pe=this.sliceSerialize(V).length;re.depth=pe}}function z(){l("setextHeadingSlurpLineEnding",!0)}function G(V){const re=this.stack[this.stack.length-1];re.depth=this.sliceSerialize(V).charCodeAt(0)===61?1:2}function q(){l("setextHeadingSlurpLineEnding")}function B(V){const re=this.stack[this.stack.length-1];let pe=re.children[re.children.length-1];(!pe||pe.type!=="text")&&(pe=Ir(),pe.position={start:Ri(V.start)},re.children.push(pe)),this.stack.push(pe)}function Q(V){const re=this.stack.pop();re.value+=this.sliceSerialize(V),re.position.end=Ri(V.end)}function U(V){const re=this.stack[this.stack.length-1];if(s("atHardBreak")){const pe=re.children[re.children.length-1];pe.position.end=Ri(V.end),l("atHardBreak");return}!s("setextHeadingSlurpLineEnding")&&t.canContainEols.includes(re.type)&&(B.call(this,V),Q.call(this,V))}function H(){l("atHardBreak",!0)}function ee(){const V=this.resume(),re=this.stack[this.stack.length-1];re.value=V}function le(){const V=this.resume(),re=this.stack[this.stack.length-1];re.value=V}function oe(){const V=this.resume(),re=this.stack[this.stack.length-1];re.value=V}function $(){const V=this.stack[this.stack.length-1];if(s("inReference")){const re=s("referenceType")||"shortcut";V.type+="Reference",V.referenceType=re,delete V.url,delete V.title}else delete V.identifier,delete V.label;l("referenceType")}function Z(){const V=this.stack[this.stack.length-1];if(s("inReference")){const re=s("referenceType")||"shortcut";V.type+="Reference",V.referenceType=re,delete V.url,delete V.title}else delete V.identifier,delete V.label;l("referenceType")}function _(V){const re=this.sliceSerialize(V),pe=this.stack[this.stack.length-2];pe.label=nB(re),pe.identifier=Ll(re).toLowerCase()}function ne(){const V=this.stack[this.stack.length-1],re=this.resume(),pe=this.stack[this.stack.length-1];if(l("inReference",!0),pe.type==="link"){const Ue=V.children;pe.children=Ue}else pe.alt=re}function ae(){const V=this.resume(),re=this.stack[this.stack.length-1];re.url=V}function O(){const V=this.resume(),re=this.stack[this.stack.length-1];re.title=V}function de(){l("inReference")}function Re(){l("referenceType","collapsed")}function me(V){const re=this.resume(),pe=this.stack[this.stack.length-1];pe.label=re,pe.identifier=Ll(this.sliceSerialize(V)).toLowerCase(),l("referenceType","full")}function Ae(V){l("characterReferenceType",V.type)}function Se(V){const re=this.sliceSerialize(V),pe=s("characterReferenceType");let Ue;pe?(Ue=_b(re,pe==="characterReferenceMarkerNumeric"?10:16),l("characterReferenceType")):Ue=Rv(re);const He=this.stack.pop();He.value+=Ue,He.position.end=Ri(V.end)}function Le(V){Q.call(this,V);const re=this.stack[this.stack.length-1];re.url=this.sliceSerialize(V)}function Te(V){Q.call(this,V);const re=this.stack[this.stack.length-1];re.url="mailto:"+this.sliceSerialize(V)}function Be(){return{type:"blockquote",children:[]}}function Fe(){return{type:"code",lang:null,meta:null,value:""}}function qe(){return{type:"inlineCode",value:""}}function yt(){return{type:"definition",identifier:"",label:null,title:null,url:""}}function Wn(){return{type:"emphasis",children:[]}}function he(){return{type:"heading",depth:void 0,children:[]}}function at(){return{type:"break"}}function Mt(){return{type:"html",value:""}}function Cn(){return{type:"image",title:null,url:"",alt:null}}function kn(){return{type:"link",title:null,url:"",children:[]}}function Ht(V){return{type:"list",ordered:V.type==="listOrdered",start:null,spread:V._spread,children:[]}}function Tr(V){return{type:"listItem",spread:V._spread,checked:null,children:[]}}function Gr(){return{type:"paragraph",children:[]}}function Go(){return{type:"strong",children:[]}}function Ir(){return{type:"text",value:""}}function Vn(){return{type:"thematicBreak"}}}function Ri(e){return{line:e.line,column:e.column,offset:e.offset}}function Tb(e,t){let n=-1;for(;++n<t.length;){const r=t[n];Array.isArray(r)?Tb(e,r):lB(e,r)}}function lB(e,t){let n;for(n in t)if(Ob.call(t,n)){if(n==="canContainEols"){const r=t[n];r&&e[n].push(...r)}else if(n==="transforms"){const r=t[n];r&&e[n].push(...r)}else if(n==="enter"||n==="exit"){const r=t[n];r&&Object.assign(e[n],r)}}}function Kx(e,t){throw e?new Error("Cannot close `"+e.type+"` ("+ns({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+ns({start:t.start,end:t.end})+") is open"):new Error("Cannot close document, a token (`"+t.type+"`, "+ns({start:t.start,end:t.end})+") is still open")}function uB(e){Object.assign(this,{Parser:n=>{const r=this.data("settings");return iB(n,Object.assign({},r,e,{extensions:this.data("micromarkExtensions")||[],mdastExtensions:this.data("fromMarkdownExtensions")||[]}))}})}function sB(e,t){const n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)}function aB(e,t){const n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:`
`}]}function cB(e,t){const n=t.value?t.value+`
`:"",r=t.lang?t.lang.match(/^[^ \t]+(?=[ \t]|$)/):null,i={};r&&(i.className=["language-"+r]);let l={type:"element",tagName:"code",properties:i,children:[{type:"text",value:n}]};return t.meta&&(l.data={meta:t.meta}),e.patch(t,l),l=e.applyData(t,l),l={type:"element",tagName:"pre",properties:{},children:[l]},e.patch(t,l),l}function fB(e,t){const n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function dB(e,t){const n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function lu(e){const t=[];let n=-1,r=0,i=0;for(;++n<e.length;){const l=e.charCodeAt(n);let s="";if(l===37&&Ln(e.charCodeAt(n+1))&&Ln(e.charCodeAt(n+2)))i=2;else if(l<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(l))||(s=String.fromCharCode(l));else if(l>55295&&l<57344){const a=e.charCodeAt(n+1);l<56320&&a>56319&&a<57344?(s=String.fromCharCode(l,a),i=1):s="�"}else s=String.fromCharCode(l);s&&(t.push(e.slice(r,n),encodeURIComponent(s)),r=n+i+1,s=""),i&&(n+=i,i=0)}return t.join("")+e.slice(r)}function Ib(e,t){const n=String(t.identifier).toUpperCase(),r=lu(n.toLowerCase()),i=e.footnoteOrder.indexOf(n);let l;i===-1?(e.footnoteOrder.push(n),e.footnoteCounts[n]=1,l=e.footnoteOrder.length):(e.footnoteCounts[n]++,l=i+1);const s=e.footnoteCounts[n],a={type:"element",tagName:"a",properties:{href:"#"+e.clobberPrefix+"fn-"+r,id:e.clobberPrefix+"fnref-"+r+(s>1?"-"+s:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(l)}]};e.patch(t,a);const f={type:"element",tagName:"sup",properties:{},children:[a]};return e.patch(t,f),e.applyData(t,f)}function pB(e,t){const n=e.footnoteById;let r=1;for(;r in n;)r++;const i=String(r);return n[i]={type:"footnoteDefinition",identifier:i,children:[{type:"paragraph",children:t.children}],position:t.position},Ib(e,{type:"footnoteReference",identifier:i,position:t.position})}function hB(e,t){const n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function gB(e,t){if(e.dangerous){const n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}return null}function Rb(e,t){const n=t.referenceType;let r="]";if(n==="collapsed"?r+="[]":n==="full"&&(r+="["+(t.label||t.identifier)+"]"),t.type==="imageReference")return{type:"text",value:"!["+t.alt+r};const i=e.all(t),l=i[0];l&&l.type==="text"?l.value="["+l.value:i.unshift({type:"text",value:"["});const s=i[i.length-1];return s&&s.type==="text"?s.value+=r:i.push({type:"text",value:r}),i}function mB(e,t){const n=e.definition(t.identifier);if(!n)return Rb(e,t);const r={src:lu(n.url||""),alt:t.alt};n.title!==null&&n.title!==void 0&&(r.title=n.title);const i={type:"element",tagName:"img",properties:r,children:[]};return e.patch(t,i),e.applyData(t,i)}function vB(e,t){const n={src:lu(t.url)};t.alt!==null&&t.alt!==void 0&&(n.alt=t.alt),t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)}function yB(e,t){const n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);const r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)}function wB(e,t){const n=e.definition(t.identifier);if(!n)return Rb(e,t);const r={href:lu(n.url||"")};n.title!==null&&n.title!==void 0&&(r.title=n.title);const i={type:"element",tagName:"a",properties:r,children:e.all(t)};return e.patch(t,i),e.applyData(t,i)}function xB(e,t){const n={href:lu(t.url)};t.title!==null&&t.title!==void 0&&(n.title=t.title);const r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)}function SB(e,t,n){const r=e.all(t),i=n?EB(n):Pb(t),l={},s=[];if(typeof t.checked=="boolean"){const p=r[0];let g;p&&p.type==="element"&&p.tagName==="p"?g=p:(g={type:"element",tagName:"p",properties:{},children:[]},r.unshift(g)),g.children.length>0&&g.children.unshift({type:"text",value:" "}),g.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),l.className=["task-list-item"]}let a=-1;for(;++a<r.length;){const p=r[a];(i||a!==0||p.type!=="element"||p.tagName!=="p")&&s.push({type:"text",value:`
`}),p.type==="element"&&p.tagName==="p"&&!i?s.push(...p.children):s.push(p)}const f=r[r.length-1];f&&(i||f.type!=="element"||f.tagName!=="p")&&s.push({type:"text",value:`
`});const d={type:"element",tagName:"li",properties:l,children:s};return e.patch(t,d),e.applyData(t,d)}function EB(e){let t=!1;if(e.type==="list"){t=e.spread||!1;const n=e.children;let r=-1;for(;!t&&++r<n.length;)t=Pb(n[r])}return t}function Pb(e){const t=e.spread;return t??e.children.length>1}function CB(e,t){const n={},r=e.all(t);let i=-1;for(typeof t.start=="number"&&t.start!==1&&(n.start=t.start);++i<r.length;){const s=r[i];if(s.type==="element"&&s.tagName==="li"&&s.properties&&Array.isArray(s.properties.className)&&s.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}const l={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,l),e.applyData(t,l)}function kB(e,t){const n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}function bB(e,t){const n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)}function _B(e,t){const n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}const Av=Ab("start"),Dv=Ab("end");function OB(e){return{start:Av(e),end:Dv(e)}}function Ab(e){return t;function t(n){const r=n&&n.position&&n.position[e]||{};return{line:r.line||null,column:r.column||null,offset:r.offset>-1?r.offset:null}}}function TB(e,t){const n=e.all(t),r=n.shift(),i=[];if(r){const s={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],s),i.push(s)}if(n.length>0){const s={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},a=Av(t.children[1]),f=Dv(t.children[t.children.length-1]);a.line&&f.line&&(s.position={start:a,end:f}),i.push(s)}const l={type:"element",tagName:"table",properties:{},children:e.wrap(i,!0)};return e.patch(t,l),e.applyData(t,l)}function IB(e,t,n){const r=n?n.children:void 0,l=(r?r.indexOf(t):1)===0?"th":"td",s=n&&n.type==="table"?n.align:void 0,a=s?s.length:t.children.length;let f=-1;const d=[];for(;++f<a;){const g=t.children[f],v={},m=s?s[f]:void 0;m&&(v.align=m);let E={type:"element",tagName:l,properties:v,children:[]};g&&(E.children=e.all(g),e.patch(g,E),E=e.applyData(t,E)),d.push(E)}const p={type:"element",tagName:"tr",properties:{},children:e.wrap(d,!0)};return e.patch(t,p),e.applyData(t,p)}function RB(e,t){const n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)}const Qx=9,Yx=32;function PB(e){const t=String(e),n=/\r?\n|\r/g;let r=n.exec(t),i=0;const l=[];for(;r;)l.push(Xx(t.slice(i,r.index),i>0,!0),r[0]),i=r.index+r[0].length,r=n.exec(t);return l.push(Xx(t.slice(i),i>0,!1)),l.join("")}function Xx(e,t,n){let r=0,i=e.length;if(t){let l=e.codePointAt(r);for(;l===Qx||l===Yx;)r++,l=e.codePointAt(r)}if(n){let l=e.codePointAt(i-1);for(;l===Qx||l===Yx;)i--,l=e.codePointAt(i-1)}return i>r?e.slice(r,i):""}function AB(e,t){const n={type:"text",value:PB(String(t.value))};return e.patch(t,n),e.applyData(t,n)}function DB(e,t){const n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)}const NB={blockquote:sB,break:aB,code:cB,delete:fB,emphasis:dB,footnoteReference:Ib,footnote:pB,heading:hB,html:gB,imageReference:mB,image:vB,inlineCode:yB,linkReference:wB,link:xB,listItem:SB,list:CB,paragraph:kB,root:bB,strong:_B,table:TB,tableCell:RB,tableRow:IB,text:AB,thematicBreak:DB,toml:Ka,yaml:Ka,definition:Ka,footnoteDefinition:Ka};function Ka(){return null}const Db=function(e){if(e==null)return zB;if(typeof e=="string")return FB(e);if(typeof e=="object")return Array.isArray(e)?MB(e):LB(e);if(typeof e=="function")return Jf(e);throw new Error("Expected function, string, or object as test")};function MB(e){const t=[];let n=-1;for(;++n<e.length;)t[n]=Db(e[n]);return Jf(r);function r(...i){let l=-1;for(;++l<t.length;)if(t[l].call(this,...i))return!0;return!1}}function LB(e){return Jf(t);function t(n){let r;for(r in e)if(n[r]!==e[r])return!1;return!0}}function FB(e){return Jf(t);function t(n){return n&&n.type===e}}function Jf(e){return t;function t(n,...r){return!!(n&&typeof n=="object"&&"type"in n&&e.call(this,n,...r))}}function zB(){return!0}const $B=!0,Zx=!1,BB="skip",UB=function(e,t,n,r){typeof t=="function"&&typeof n!="function"&&(r=n,n=t,t=null);const i=Db(t),l=r?-1:1;s(e,void 0,[])();function s(a,f,d){const p=a&&typeof a=="object"?a:{};if(typeof p.type=="string"){const v=typeof p.tagName=="string"?p.tagName:typeof p.name=="string"?p.name:void 0;Object.defineProperty(g,"name",{value:"node ("+(a.type+(v?"<"+v+">":""))+")"})}return g;function g(){let v=[],m,E,S;if((!t||i(a,f,d[d.length-1]||null))&&(v=HB(n(a,d)),v[0]===Zx))return v;if(a.children&&v[0]!==BB)for(E=(r?a.children.length:-1)+l,S=d.concat(a);E>-1&&E<a.children.length;){if(m=s(a.children[E],E,S)(),m[0]===Zx)return m;E=typeof m[1]=="number"?m[1]:E+l}return v}}};function HB(e){return Array.isArray(e)?e:typeof e=="number"?[$B,e]:[e]}const Nv=function(e,t,n,r){typeof t=="function"&&typeof n!="function"&&(r=n,n=t,t=null),UB(e,t,i,r);function i(l,s){const a=s[s.length-1];return n(l,a?a.children.indexOf(l):null,a)}};function jB(e){return!e||!e.position||!e.position.start||!e.position.start.line||!e.position.start.column||!e.position.end||!e.position.end.line||!e.position.end.column}const Jx={}.hasOwnProperty;function WB(e){const t=Object.create(null);if(!e||!e.type)throw new Error("mdast-util-definitions expected node");return Nv(e,"definition",r=>{const i=eS(r.identifier);i&&!Jx.call(t,i)&&(t[i]=r)}),n;function n(r){const i=eS(r);return i&&Jx.call(t,i)?t[i]:null}}function eS(e){return String(e||"").toUpperCase()}const ef={}.hasOwnProperty;function VB(e,t){const n=t||{},r=n.allowDangerousHtml||!1,i={};return s.dangerous=r,s.clobberPrefix=n.clobberPrefix===void 0||n.clobberPrefix===null?"user-content-":n.clobberPrefix,s.footnoteLabel=n.footnoteLabel||"Footnotes",s.footnoteLabelTagName=n.footnoteLabelTagName||"h2",s.footnoteLabelProperties=n.footnoteLabelProperties||{className:["sr-only"]},s.footnoteBackLabel=n.footnoteBackLabel||"Back to content",s.unknownHandler=n.unknownHandler,s.passThrough=n.passThrough,s.handlers={...NB,...n.handlers},s.definition=WB(e),s.footnoteById=i,s.footnoteOrder=[],s.footnoteCounts={},s.patch=GB,s.applyData=qB,s.one=a,s.all=f,s.wrap=QB,s.augment=l,Nv(e,"footnoteDefinition",d=>{const p=String(d.identifier).toUpperCase();ef.call(i,p)||(i[p]=d)}),s;function l(d,p){if(d&&"data"in d&&d.data){const g=d.data;g.hName&&(p.type!=="element"&&(p={type:"element",tagName:"",properties:{},children:[]}),p.tagName=g.hName),p.type==="element"&&g.hProperties&&(p.properties={...p.properties,...g.hProperties}),"children"in p&&p.children&&g.hChildren&&(p.children=g.hChildren)}if(d){const g="type"in d?d:{position:d};jB(g)||(p.position={start:Av(g),end:Dv(g)})}return p}function s(d,p,g,v){return Array.isArray(g)&&(v=g,g={}),l(d,{type:"element",tagName:p,properties:g||{},children:v||[]})}function a(d,p){return Nb(s,d,p)}function f(d){return Mv(s,d)}}function GB(e,t){e.position&&(t.position=OB(e))}function qB(e,t){let n=t;if(e&&e.data){const r=e.data.hName,i=e.data.hChildren,l=e.data.hProperties;typeof r=="string"&&(n.type==="element"?n.tagName=r:n={type:"element",tagName:r,properties:{},children:[]}),n.type==="element"&&l&&(n.properties={...n.properties,...l}),"children"in n&&n.children&&i!==null&&i!==void 0&&(n.children=i)}return n}function Nb(e,t,n){const r=t&&t.type;if(!r)throw new Error("Expected node, got `"+t+"`");return ef.call(e.handlers,r)?e.handlers[r](e,t,n):e.passThrough&&e.passThrough.includes(r)?"children"in t?{...t,children:Mv(e,t)}:t:e.unknownHandler?e.unknownHandler(e,t,n):KB(e,t)}function Mv(e,t){const n=[];if("children"in t){const r=t.children;let i=-1;for(;++i<r.length;){const l=Nb(e,r[i],t);if(l){if(i&&r[i-1].type==="break"&&(!Array.isArray(l)&&l.type==="text"&&(l.value=l.value.replace(/^\s+/,"")),!Array.isArray(l)&&l.type==="element")){const s=l.children[0];s&&s.type==="text"&&(s.value=s.value.replace(/^\s+/,""))}Array.isArray(l)?n.push(...l):n.push(l)}}}return n}function KB(e,t){const n=t.data||{},r="value"in t&&!(ef.call(n,"hProperties")||ef.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:Mv(e,t)};return e.patch(t,r),e.applyData(t,r)}function QB(e,t){const n=[];let r=-1;for(t&&n.push({type:"text",value:`
`});++r<e.length;)r&&n.push({type:"text",value:`
`}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:`
`}),n}function YB(e){const t=[];let n=-1;for(;++n<e.footnoteOrder.length;){const r=e.footnoteById[e.footnoteOrder[n]];if(!r)continue;const i=e.all(r),l=String(r.identifier).toUpperCase(),s=lu(l.toLowerCase());let a=0;const f=[];for(;++a<=e.footnoteCounts[l];){const g={type:"element",tagName:"a",properties:{href:"#"+e.clobberPrefix+"fnref-"+s+(a>1?"-"+a:""),dataFootnoteBackref:!0,className:["data-footnote-backref"],ariaLabel:e.footnoteBackLabel},children:[{type:"text",value:"↩"}]};a>1&&g.children.push({type:"element",tagName:"sup",children:[{type:"text",value:String(a)}]}),f.length>0&&f.push({type:"text",value:" "}),f.push(g)}const d=i[i.length-1];if(d&&d.type==="element"&&d.tagName==="p"){const g=d.children[d.children.length-1];g&&g.type==="text"?g.value+=" ":d.children.push({type:"text",value:" "}),d.children.push(...f)}else i.push(...f);const p={type:"element",tagName:"li",properties:{id:e.clobberPrefix+"fn-"+s},children:e.wrap(i,!0)};e.patch(r,p),t.push(p)}if(t.length!==0)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:e.footnoteLabelTagName,properties:{...JSON.parse(JSON.stringify(e.footnoteLabelProperties)),id:"footnote-label"},children:[{type:"text",value:e.footnoteLabel}]},{type:"text",value:`
`},{type:"element",tagName:"ol",properties:{},children:e.wrap(t,!0)},{type:"text",value:`
`}]}}function Mb(e,t){const n=VB(e,t),r=n.one(e,null),i=YB(n);return i&&r.children.push({type:"text",value:`
`},i),Array.isArray(r)?{type:"root",children:r}:r}const XB=function(e,t){return e&&"run"in e?JB(e,t):eU(e||t)},ZB=XB;function JB(e,t){return(n,r,i)=>{e.run(Mb(n,t),r,l=>{i(l)})}}function eU(e){return t=>Mb(t,e)}class Ls{constructor(t,n,r){this.property=t,this.normal=n,r&&(this.space=r)}}Ls.prototype.property={};Ls.prototype.normal={};Ls.prototype.space=null;function Lb(e,t){const n={},r={};let i=-1;for(;++i<e.length;)Object.assign(n,e[i].property),Object.assign(r,e[i].normal);return new Ls(n,r,t)}function Lg(e){return e.toLowerCase()}class dr{constructor(t,n){this.property=t,this.attribute=n}}dr.prototype.space=null;dr.prototype.boolean=!1;dr.prototype.booleanish=!1;dr.prototype.overloadedBoolean=!1;dr.prototype.number=!1;dr.prototype.commaSeparated=!1;dr.prototype.spaceSeparated=!1;dr.prototype.commaOrSpaceSeparated=!1;dr.prototype.mustUseProperty=!1;dr.prototype.defined=!1;let tU=0;const _e=Vo(),kt=Vo(),Fb=Vo(),ie=Vo(),tt=Vo(),Fl=Vo(),An=Vo();function Vo(){return 2**++tU}const Fg=Object.freeze(Object.defineProperty({__proto__:null,boolean:_e,booleanish:kt,commaOrSpaceSeparated:An,commaSeparated:Fl,number:ie,overloadedBoolean:Fb,spaceSeparated:tt},Symbol.toStringTag,{value:"Module"})),mh=Object.keys(Fg);class Lv extends dr{constructor(t,n,r,i){let l=-1;if(super(t,n),tS(this,"space",i),typeof r=="number")for(;++l<mh.length;){const s=mh[l];tS(this,mh[l],(r&Fg[s])===Fg[s])}}}Lv.prototype.defined=!0;function tS(e,t,n){n&&(e[t]=n)}const nU={}.hasOwnProperty;function uu(e){const t={},n={};let r;for(r in e.properties)if(nU.call(e.properties,r)){const i=e.properties[r],l=new Lv(r,e.transform(e.attributes||{},r),i,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(l.mustUseProperty=!0),t[r]=l,n[Lg(r)]=r,n[Lg(l.attribute)]=r}return new Ls(t,n,e.space)}const zb=uu({space:"xlink",transform(e,t){return"xlink:"+t.slice(5).toLowerCase()},properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}}),$b=uu({space:"xml",transform(e,t){return"xml:"+t.slice(3).toLowerCase()},properties:{xmlLang:null,xmlBase:null,xmlSpace:null}});function Bb(e,t){return t in e?e[t]:t}function Ub(e,t){return Bb(e,t.toLowerCase())}const Hb=uu({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:Ub,properties:{xmlns:null,xmlnsXLink:null}}),jb=uu({transform(e,t){return t==="role"?t:"aria-"+t.slice(4).toLowerCase()},properties:{ariaActiveDescendant:null,ariaAtomic:kt,ariaAutoComplete:null,ariaBusy:kt,ariaChecked:kt,ariaColCount:ie,ariaColIndex:ie,ariaColSpan:ie,ariaControls:tt,ariaCurrent:null,ariaDescribedBy:tt,ariaDetails:null,ariaDisabled:kt,ariaDropEffect:tt,ariaErrorMessage:null,ariaExpanded:kt,ariaFlowTo:tt,ariaGrabbed:kt,ariaHasPopup:null,ariaHidden:kt,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:tt,ariaLevel:ie,ariaLive:null,ariaModal:kt,ariaMultiLine:kt,ariaMultiSelectable:kt,ariaOrientation:null,ariaOwns:tt,ariaPlaceholder:null,ariaPosInSet:ie,ariaPressed:kt,ariaReadOnly:kt,ariaRelevant:null,ariaRequired:kt,ariaRoleDescription:tt,ariaRowCount:ie,ariaRowIndex:ie,ariaRowSpan:ie,ariaSelected:kt,ariaSetSize:ie,ariaSort:null,ariaValueMax:ie,ariaValueMin:ie,ariaValueNow:ie,ariaValueText:null,role:null}}),rU=uu({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:Ub,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:Fl,acceptCharset:tt,accessKey:tt,action:null,allow:null,allowFullScreen:_e,allowPaymentRequest:_e,allowUserMedia:_e,alt:null,as:null,async:_e,autoCapitalize:null,autoComplete:tt,autoFocus:_e,autoPlay:_e,blocking:tt,capture:null,charSet:null,checked:_e,cite:null,className:tt,cols:ie,colSpan:null,content:null,contentEditable:kt,controls:_e,controlsList:tt,coords:ie|Fl,crossOrigin:null,data:null,dateTime:null,decoding:null,default:_e,defer:_e,dir:null,dirName:null,disabled:_e,download:Fb,draggable:kt,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:_e,formTarget:null,headers:tt,height:ie,hidden:_e,high:ie,href:null,hrefLang:null,htmlFor:tt,httpEquiv:tt,id:null,imageSizes:null,imageSrcSet:null,inert:_e,inputMode:null,integrity:null,is:null,isMap:_e,itemId:null,itemProp:tt,itemRef:tt,itemScope:_e,itemType:tt,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:_e,low:ie,manifest:null,max:null,maxLength:ie,media:null,method:null,min:null,minLength:ie,multiple:_e,muted:_e,name:null,nonce:null,noModule:_e,noValidate:_e,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:_e,optimum:ie,pattern:null,ping:tt,placeholder:null,playsInline:_e,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:_e,referrerPolicy:null,rel:tt,required:_e,reversed:_e,rows:ie,rowSpan:ie,sandbox:tt,scope:null,scoped:_e,seamless:_e,selected:_e,shadowRootClonable:_e,shadowRootDelegatesFocus:_e,shadowRootMode:null,shape:null,size:ie,sizes:null,slot:null,span:ie,spellCheck:kt,src:null,srcDoc:null,srcLang:null,srcSet:null,start:ie,step:null,style:null,tabIndex:ie,target:null,title:null,translate:null,type:null,typeMustMatch:_e,useMap:null,value:kt,width:ie,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:tt,axis:null,background:null,bgColor:null,border:ie,borderColor:null,bottomMargin:ie,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:_e,declare:_e,event:null,face:null,frame:null,frameBorder:null,hSpace:ie,leftMargin:ie,link:null,longDesc:null,lowSrc:null,marginHeight:ie,marginWidth:ie,noResize:_e,noHref:_e,noShade:_e,noWrap:_e,object:null,profile:null,prompt:null,rev:null,rightMargin:ie,rules:null,scheme:null,scrolling:kt,standby:null,summary:null,text:null,topMargin:ie,valueType:null,version:null,vAlign:null,vLink:null,vSpace:ie,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:_e,disableRemotePlayback:_e,prefix:null,property:null,results:ie,security:null,unselectable:null}}),iU=uu({space:"svg",attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},transform:Bb,properties:{about:An,accentHeight:ie,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:ie,amplitude:ie,arabicForm:null,ascent:ie,attributeName:null,attributeType:null,azimuth:ie,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:ie,by:null,calcMode:null,capHeight:ie,className:tt,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:ie,diffuseConstant:ie,direction:null,display:null,dur:null,divisor:ie,dominantBaseline:null,download:_e,dx:null,dy:null,edgeMode:null,editable:null,elevation:ie,enableBackground:null,end:null,event:null,exponent:ie,externalResourcesRequired:null,fill:null,fillOpacity:ie,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:Fl,g2:Fl,glyphName:Fl,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:ie,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:ie,horizOriginX:ie,horizOriginY:ie,id:null,ideographic:ie,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:ie,k:ie,k1:ie,k2:ie,k3:ie,k4:ie,kernelMatrix:An,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:ie,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:ie,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:ie,overlineThickness:ie,paintOrder:null,panose1:null,path:null,pathLength:ie,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:tt,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:ie,pointsAtY:ie,pointsAtZ:ie,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:An,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:An,rev:An,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:An,requiredFeatures:An,requiredFonts:An,requiredFormats:An,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:ie,specularExponent:ie,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:ie,strikethroughThickness:ie,string:null,stroke:null,strokeDashArray:An,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:ie,strokeOpacity:ie,strokeWidth:null,style:null,surfaceScale:ie,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:An,tabIndex:ie,tableValues:null,target:null,targetX:ie,targetY:ie,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:An,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:ie,underlineThickness:ie,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:ie,values:null,vAlphabetic:ie,vMathematical:ie,vectorEffect:null,vHanging:ie,vIdeographic:ie,version:null,vertAdvY:ie,vertOriginX:ie,vertOriginY:ie,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:ie,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null}}),oU=/^data[-\w.:]+$/i,nS=/-[a-z]/g,lU=/[A-Z]/g;function uU(e,t){const n=Lg(t);let r=t,i=dr;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&n.slice(0,4)==="data"&&oU.test(t)){if(t.charAt(4)==="-"){const l=t.slice(5).replace(nS,aU);r="data"+l.charAt(0).toUpperCase()+l.slice(1)}else{const l=t.slice(4);if(!nS.test(l)){let s=l.replace(lU,sU);s.charAt(0)!=="-"&&(s="-"+s),t="data"+s}}i=Lv}return new i(r,t)}function sU(e){return"-"+e.toLowerCase()}function aU(e){return e.charAt(1).toUpperCase()}const rS={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"},cU=Lb([$b,zb,Hb,jb,rU],"html"),fU=Lb([$b,zb,Hb,jb,iU],"svg");function dU(e){if(e.allowedElements&&e.disallowedElements)throw new TypeError("Only one of `allowedElements` and `disallowedElements` should be defined");if(e.allowedElements||e.disallowedElements||e.allowElement)return t=>{Nv(t,"element",(n,r,i)=>{const l=i;let s;if(e.allowedElements?s=!e.allowedElements.includes(n.tagName):e.disallowedElements&&(s=e.disallowedElements.includes(n.tagName)),!s&&e.allowElement&&typeof r=="number"&&(s=!e.allowElement(n,r,l)),s&&typeof r=="number")return e.unwrapDisallowed&&n.children?l.children.splice(r,1,...n.children):l.children.splice(r,1),r})}}function pU(e){const t=e&&typeof e=="object"&&e.type==="text"?e.value||"":e;return typeof t=="string"&&t.replace(/[ \t\n\f\r]/g,"")===""}function hU(e){return e.join(" ").trim()}function gU(e,t){const n=t||{};return(e[e.length-1]===""?[...e,""]:e).join((n.padRight?" ":"")+","+(n.padLeft===!1?"":" ")).trim()}var Fv={exports:{}},iS=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,mU=/\n/g,vU=/^\s*/,yU=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,wU=/^:\s*/,xU=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,SU=/^[;\s]*/,EU=/^\s+|\s+$/g,CU=`
`,oS="/",lS="*",bo="",kU="comment",bU="declaration",_U=function(e,t){if(typeof e!="string")throw new TypeError("First argument must be a string");if(!e)return[];t=t||{};var n=1,r=1;function i(E){var S=E.match(mU);S&&(n+=S.length);var A=E.lastIndexOf(CU);r=~A?E.length-A:r+E.length}function l(){var E={line:n,column:r};return function(S){return S.position=new s(E),d(),S}}function s(E){this.start=E,this.end={line:n,column:r},this.source=t.source}s.prototype.content=e;function a(E){var S=new Error(t.source+":"+n+":"+r+": "+E);if(S.reason=E,S.filename=t.source,S.line=n,S.column=r,S.source=e,!t.silent)throw S}function f(E){var S=E.exec(e);if(S){var A=S[0];return i(A),e=e.slice(A.length),S}}function d(){f(vU)}function p(E){var S;for(E=E||[];S=g();)S!==!1&&E.push(S);return E}function g(){var E=l();if(!(oS!=e.charAt(0)||lS!=e.charAt(1))){for(var S=2;bo!=e.charAt(S)&&(lS!=e.charAt(S)||oS!=e.charAt(S+1));)++S;if(S+=2,bo===e.charAt(S-1))return a("End of comment missing");var A=e.slice(2,S-2);return r+=2,i(A),e=e.slice(S),r+=2,E({type:kU,comment:A})}}function v(){var E=l(),S=f(yU);if(S){if(g(),!f(wU))return a("property missing ':'");var A=f(xU),y=E({type:bU,property:uS(S[0].replace(iS,bo)),value:A?uS(A[0].replace(iS,bo)):bo});return f(SU),y}}function m(){var E=[];p(E);for(var S;S=v();)S!==!1&&(E.push(S),p(E));return E}return d(),m()};function uS(e){return e?e.replace(EU,bo):bo}var OU=_U;function Wb(e,t){var n=null;if(!e||typeof e!="string")return n;for(var r,i=OU(e),l=typeof t=="function",s,a,f=0,d=i.length;f<d;f++)r=i[f],s=r.property,a=r.value,l?t(s,a,r):a&&(n||(n={}),n[s]=a);return n}Fv.exports=Wb;Fv.exports.default=Wb;var TU=Fv.exports;const IU=io(TU),zg={}.hasOwnProperty,RU=new Set(["table","thead","tbody","tfoot","tr"]);function Vb(e,t){const n=[];let r=-1,i;for(;++r<t.children.length;)i=t.children[r],i.type==="element"?n.push(PU(e,i,r,t)):i.type==="text"?(t.type!=="element"||!RU.has(t.tagName)||!pU(i))&&n.push(i.value):i.type==="raw"&&!e.options.skipHtml&&n.push(i.value);return n}function PU(e,t,n,r){const i=e.options,l=i.transformLinkUri===void 0?L6:i.transformLinkUri,s=e.schema,a=t.tagName,f={};let d=s,p;if(s.space==="html"&&a==="svg"&&(d=fU,e.schema=d),t.properties)for(p in t.properties)zg.call(t.properties,p)&&DU(f,p,t.properties[p],e);(a==="ol"||a==="ul")&&e.listDepth++;const g=Vb(e,t);(a==="ol"||a==="ul")&&e.listDepth--,e.schema=s;const v=t.position||{start:{line:null,column:null,offset:null},end:{line:null,column:null,offset:null}},m=i.components&&zg.call(i.components,a)?i.components[a]:a,E=typeof m=="string"||m===Ve.Fragment;if(!LM.isValidElementType(m))throw new TypeError(`Component for name \`${a}\` not defined or is not renderable`);if(f.key=n,a==="a"&&i.linkTarget&&(f.target=typeof i.linkTarget=="function"?i.linkTarget(String(f.href||""),t.children,typeof f.title=="string"?f.title:null):i.linkTarget),a==="a"&&l&&(f.href=l(String(f.href||""),t.children,typeof f.title=="string"?f.title:null)),!E&&a==="code"&&r.type==="element"&&r.tagName!=="pre"&&(f.inline=!0),!E&&(a==="h1"||a==="h2"||a==="h3"||a==="h4"||a==="h5"||a==="h6")&&(f.level=Number.parseInt(a.charAt(1),10)),a==="img"&&i.transformImageUri&&(f.src=i.transformImageUri(String(f.src||""),String(f.alt||""),typeof f.title=="string"?f.title:null)),!E&&a==="li"&&r.type==="element"){const S=AU(t);f.checked=S&&S.properties?!!S.properties.checked:null,f.index=vh(r,t),f.ordered=r.tagName==="ol"}return!E&&(a==="ol"||a==="ul")&&(f.ordered=a==="ol",f.depth=e.listDepth),(a==="td"||a==="th")&&(f.align&&(f.style||(f.style={}),f.style.textAlign=f.align,delete f.align),E||(f.isHeader=a==="th")),!E&&a==="tr"&&r.type==="element"&&(f.isHeader=r.tagName==="thead"),i.sourcePos&&(f["data-sourcepos"]=LU(v)),!E&&i.rawSourcePos&&(f.sourcePosition=t.position),!E&&i.includeElementIndex&&(f.index=vh(r,t),f.siblingCount=vh(r)),E||(f.node=t),g.length>0?Ve.createElement(m,f,g):Ve.createElement(m,f)}function AU(e){let t=-1;for(;++t<e.children.length;){const n=e.children[t];if(n.type==="element"&&n.tagName==="input")return n}return null}function vh(e,t){let n=-1,r=0;for(;++n<e.children.length&&e.children[n]!==t;)e.children[n].type==="element"&&r++;return r}function DU(e,t,n,r){const i=uU(r.schema,t);let l=n;l==null||l!==l||(Array.isArray(l)&&(l=i.commaSeparated?gU(l):hU(l)),i.property==="style"&&typeof l=="string"&&(l=NU(l)),i.space&&i.property?e[zg.call(rS,i.property)?rS[i.property]:i.property]=l:i.attribute&&(e[i.attribute]=l))}function NU(e){const t={};try{IU(e,n)}catch{}return t;function n(r,i){const l=r.slice(0,4)==="-ms-"?`ms-${r.slice(4)}`:r;t[l.replace(/-([a-z])/g,MU)]=i}}function MU(e,t){return t.toUpperCase()}function LU(e){return[e.start.line,":",e.start.column,"-",e.end.line,":",e.end.column].map(String).join("")}const sS={}.hasOwnProperty,FU="https://github.com/remarkjs/react-markdown/blob/main/changelog.md",Qa={plugins:{to:"remarkPlugins",id:"change-plugins-to-remarkplugins"},renderers:{to:"components",id:"change-renderers-to-components"},astPlugins:{id:"remove-buggy-html-in-markdown-parser"},allowDangerousHtml:{id:"remove-buggy-html-in-markdown-parser"},escapeHtml:{id:"remove-buggy-html-in-markdown-parser"},source:{to:"children",id:"change-source-to-children"},allowNode:{to:"allowElement",id:"replace-allownode-allowedtypes-and-disallowedtypes"},allowedTypes:{to:"allowedElements",id:"replace-allownode-allowedtypes-and-disallowedtypes"},disallowedTypes:{to:"disallowedElements",id:"replace-allownode-allowedtypes-and-disallowedtypes"},includeNodeIndex:{to:"includeElementIndex",id:"change-includenodeindex-to-includeelementindex"}};function Gb(e){for(const l in Qa)if(sS.call(Qa,l)&&sS.call(e,l)){const s=Qa[l];console.warn(`[react-markdown] Warning: please ${s.to?`use \`${s.to}\` instead of`:"remove"} \`${l}\` (see <${FU}#${s.id}> for more info)`),delete Qa[l]}const t=Z6().use(uB).use(e.remarkPlugins||[]).use(ZB,{...e.remarkRehypeOptions,allowDangerousHtml:!0}).use(e.rehypePlugins||[]).use(dU,e),n=new db;typeof e.children=="string"?n.value=e.children:e.children!==void 0&&e.children!==null&&console.warn(`[react-markdown] Warning: please pass a string as \`children\` (not: \`${e.children}\`)`);const r=t.runSync(t.parse(n),n);if(r.type!=="root")throw new TypeError("Expected a `root` node");let i=Ve.createElement(Ve.Fragment,{},Vb({options:e,schema:cU,listDepth:0},r));return e.className&&(i=Ve.createElement("div",{className:e.className},i)),i}Gb.propTypes={children:Ce.string,className:Ce.string,allowElement:Ce.func,allowedElements:Ce.arrayOf(Ce.string),disallowedElements:Ce.arrayOf(Ce.string),unwrapDisallowed:Ce.bool,remarkPlugins:Ce.arrayOf(Ce.oneOfType([Ce.object,Ce.func,Ce.arrayOf(Ce.oneOfType([Ce.bool,Ce.string,Ce.object,Ce.func,Ce.arrayOf(Ce.any)]))])),rehypePlugins:Ce.arrayOf(Ce.oneOfType([Ce.object,Ce.func,Ce.arrayOf(Ce.oneOfType([Ce.bool,Ce.string,Ce.object,Ce.func,Ce.arrayOf(Ce.any)]))])),sourcePos:Ce.bool,rawSourcePos:Ce.bool,skipHtml:Ce.bool,includeElementIndex:Ce.bool,transformLinkUri:Ce.oneOfType([Ce.func,Ce.bool]),linkTarget:Ce.oneOfType([Ce.func,Ce.string]),transformImageUri:Ce.func,components:Ce.object};const zU=()=>we("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 24 24",strokeWidth:"1.5",stroke:"#ffffff",fill:"none",strokeLinecap:"round",strokeLinejoin:"round",children:[W("path",{stroke:"none",d:"M0 0h24v24H0z",fill:"none"}),W("circle",{cx:"12",cy:"12",r:"9"}),W("polyline",{points:"12 7 12 12 15 15"})]}),aS=()=>W("div",{className:"divider"}),$U=({item:e,inventoryType:t,style:n},r)=>{const i=vi(d=>d.inventory.additionalMetadata),l=I.useMemo(()=>dt[e.name],[e]),s=I.useMemo(()=>e.ingredients?Object.entries(e.ingredients).sort((d,p)=>d[1]-p[1]):null,[e]),a=e.metadata?.description||l?.description,f=l?.ammoName&&dt[l?.ammoName]?.label;return W(En,{children:l?we("div",{style:{...n},className:"tooltip-wrapper",ref:r,children:[we("div",{className:"tooltip-header-wrapper",children:[W("p",{children:e.metadata?.label||l.label||e.name}),t==="crafting"?we("div",{className:"tooltip-crafting-duration",children:[W(zU,{}),we("p",{children:[(e.duration!==void 0?e.duration:3e3)/1e3,"s"]})]}):W("p",{children:e.metadata?.type})]}),W(aS,{}),a&&W("div",{className:"tooltip-description",children:W(Gb,{className:"tooltip-markdown",children:a})}),t!=="crafting"?we(En,{children:[e.durability!==void 0&&we("p",{children:[ut.ui_durability,": ",Math.trunc(e.durability)]}),e.metadata?.ammo!==void 0&&we("p",{children:[ut.ui_ammo,": ",e.metadata.ammo]}),f&&we("p",{children:[ut.ammo_type,": ",f]}),e.metadata?.serial&&we("p",{children:[ut.ui_serial,": ",e.metadata.serial]}),e.metadata?.components&&e.metadata?.components[0]&&we("p",{children:[ut.ui_components,":"," ",(e.metadata?.components).map((d,p,g)=>p+1===g.length?dt[d]?.label:dt[d]?.label+", ")]}),e.metadata?.weapontint&&we("p",{children:[ut.ui_tint,": ",e.metadata.weapontint]}),i.map((d,p)=>W(I.Fragment,{children:e.metadata&&e.metadata[d.metadata]&&we("p",{children:[d.value,": ",e.metadata[d.metadata]]})},`metadata-${p}`))]}):W("div",{className:"tooltip-ingredients",children:s&&s.map(d=>{const[p,g]=[d[0],d[1]];return we("div",{className:"tooltip-ingredient",children:[W("img",{src:p?Dl(p):"none",alt:"item-image"}),W("p",{children:g>=1?`${g}x ${dt[p]?.label||p}`:g===0?`${dt[p]?.label||p}`:g<1&&`${g*100}% ${dt[p]?.label||p}`})]},`ingredient-${p}`)})})]}):we("div",{className:"tooltip-wrapper",ref:r,style:n,children:[W("div",{className:"tooltip-header-wrapper",children:W("p",{children:e.name})}),W(aS,{})]})})},BU=Ve.forwardRef($U),UU=()=>{const e=vi(a=>a.tooltip),{refs:t,context:n,floatingStyles:r}=Ev({middleware:[$k(),zk(),Fk({mainAxis:10,crossAxis:10})],open:e.open,placement:"right-start"}),{isMounted:i,styles:l}=Cv(n,{duration:200}),s=({clientX:a,clientY:f})=>{t.setPositionReference({getBoundingClientRect(){return{width:0,height:0,x:a,y:f,left:a,top:f,right:a,bottom:f}}})};return I.useEffect(()=>(window.addEventListener("mousemove",s),()=>{window.removeEventListener("mousemove",s)}),[]),W(En,{children:i&&e.item&&e.inventoryType&&W(Sv,{children:W(BU,{ref:t.setFloating,style:{...r,...l},item:e.item,inventoryType:e.inventoryType})})})},HU=e=>{const t=document.createElement("input");t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t)},$g=Ve.createContext({getItemProps:()=>({}),activeIndex:null,setActiveIndex:()=>{},setHasFocusInside:()=>{},isOpen:!1}),cS=Ve.forwardRef(({children:e,label:t,...n},r)=>{const i=vi(oe=>oe.contextMenu),[l,s]=I.useState(!1),[a,f]=I.useState(!1),[d,p]=I.useState(null),g=I.useRef([]),v=I.useRef([]),m=I.useContext($g),E=so(),S=Iz(),A=Wo(),y=Vk(),x=A!=null,{floatingStyles:k,refs:P,context:D}=Ev({nodeId:S,open:l,onOpenChange:s,placement:x?"right-start":"bottom-start",middleware:[Fk({mainAxis:x?0:4,alignmentAxis:x?-4:0}),$k(),zk()],whileElementsMounted:Z4}),{isMounted:T,styles:F}=Cv(D);I.useEffect(()=>{x||(i.coords&&(P.setPositionReference({getBoundingClientRect(){return{width:0,height:0,x:i.coords.x,y:i.coords.y,top:i.coords.y,right:i.coords.x,bottom:i.coords.y,left:i.coords.x}}}),s(!0)),i.coords||s(!1))},[i]);const z=Az(D,{enabled:x,delay:{open:75},handleClose:n6({blockPointerEvents:!0})}),G=jz(D,{event:"mousedown",toggle:!x,ignoreMouse:x}),q=Xz(D,{role:"menu"}),B=rb(D,{bubbles:!0}),Q=Yz(D,{listRef:g,activeIndex:d,nested:x,onNavigate:p}),U=e6(D,{listRef:v,onMatch:l?p:void 0,activeIndex:d}),{getReferenceProps:H,getFloatingProps:ee,getItemProps:le}=ib([z,G,q,B,Q,U]);return I.useEffect(()=>{if(!E)return;function oe(){s(!1)}function $(Z){Z.nodeId!==S&&Z.parentId===A&&s(!1)}return E.events.on("click",oe),E.events.on("menuopen",$),()=>{E.events.off("click",oe),E.events.off("menuopen",$)}},[E,S,A]),I.useEffect(()=>{l&&E&&E.events.emit("menuopen",{parentId:A,nodeId:S})},[E,l,S,A]),we(Rz,{id:S,children:[x&&we("button",{ref:vv([P.setReference,y.ref,r]),tabIndex:x?m.activeIndex===y.index?0:-1:void 0,role:x?"menuitem":void 0,"data-open":l?"":void 0,"data-nested":x?"":void 0,"data-focus-inside":a?"":void 0,className:x?"context-menu-item":"context-menu-list",...H(m.getItemProps({...n,onFocus(oe){n.onFocus?.(oe),f(!1),m.setHasFocusInside(!0)}})),children:[t,x&&W("span",{"aria-hidden":!0,style:{marginLeft:10,fontSize:10},children:"▶"})]}),W($g.Provider,{value:{activeIndex:d,setActiveIndex:p,getItemProps:le,setHasFocusInside:f,isOpen:l},children:W(bz,{elementsRef:g,labelsRef:v,children:T&&W(Sv,{children:W(nb,{lockScroll:!0,children:W(tb,{context:D,modal:!0,initialFocus:P.floating,children:W("div",{ref:P.setFloating,className:"context-menu-list",style:{...k,...F},...ee(),children:e})})})})})})]})}),Pi=Ve.forwardRef(({label:e,disabled:t,...n},r)=>{const i=I.useContext($g),l=Vk({label:t?null:e}),s=so(),a=l.index===i.activeIndex;return W("button",{...n,ref:vv([l.ref,r]),type:"button",role:"menuitem",className:"context-menu-item",tabIndex:a?0:-1,disabled:t,...i.getItemProps({onClick(f){n.onClick?.(f),s?.events.emit("click")},onFocus(f){n.onFocus?.(f),i.setHasFocusInside(!0)}}),children:e})}),yh=Ve.forwardRef((e,t)=>Wo()===null?W(Pz,{children:W(cS,{...e,ref:t})}):W(cS,{...e,ref:t})),jU=()=>{const t=vi(i=>i.contextMenu).item,n=i=>{if(t)switch(i&&i.action){case"use":av({name:t.name,slot:t.slot});break;case"give":vk({name:t.name,slot:t.slot});break;case"drop":gn(t)&&Ig({item:t,inventory:"player"});break;case"remove":ur("removeComponent",{component:i?.component,slot:i?.slot});break;case"removeAmmo":ur("removeAmmo",t.slot);break;case"copy":HU(i.serial||"");break;case"custom":ur("useButton",{id:(i?.id||0)+1,slot:t.slot});break}},r=i=>i.reduce((l,s,a)=>{if(s.group){const f=l.findIndex(d=>d.groupName===s.group);f!==-1?l[f].buttons.push({...s,index:a}):l.push({groupName:s.group,buttons:[{...s,index:a}]})}else l.push({groupName:null,buttons:[{...s,index:a}]});return l},[]);return W(En,{children:we(yh,{children:[W(Pi,{onClick:()=>n({action:"use"}),label:ut.ui_use||"Use"}),W(Pi,{onClick:()=>n({action:"give"}),label:ut.ui_give||"Give"}),W(Pi,{onClick:()=>n({action:"drop"}),label:ut.ui_drop||"Drop"}),t&&t.metadata?.ammo>0&&W(Pi,{onClick:()=>n({action:"removeAmmo"}),label:ut.ui_remove_ammo}),t&&t.metadata?.serial&&W(Pi,{onClick:()=>n({action:"copy",serial:t.metadata?.serial}),label:ut.ui_copy}),t&&t.metadata?.components&&t.metadata?.components.length>0&&W(yh,{label:ut.ui_removeattachments,children:t&&t.metadata?.components.map((i,l)=>W(Pi,{onClick:()=>n({action:"remove",component:i,slot:t.slot}),label:dt[i]?.label||""},l))}),(t&&t.name&&dt[t.name]?.buttons?.length||0)>0&&W(En,{children:t&&t.name&&r(dt[t.name]?.buttons).map((i,l)=>W(Ve.Fragment,{children:i.groupName?W(yh,{label:i.groupName,children:i.buttons.map(s=>W(Pi,{onClick:()=>n({action:"custom",id:s.index}),label:s.label},s.index))}):i.buttons.map(s=>W(Pi,{onClick:()=>n({action:"custom",id:s.index}),label:s.label},s.index))},l))})]})})},qb=e=>{const t=I.useRef(null);return W(ab,{in:e.in,nodeRef:t,classNames:"transition-fade",timeout:200,unmountOnExit:!0,children:W("span",{ref:t,children:e.children})})},WU=()=>{const[e,t]=I.useState(!1),n=iu();return Fr("setInventoryVisible",t),Fr("closeInventory",()=>{t(!1),n(mk()),n(Ju())}),b6(t),Fr("setupInventory",r=>{n(dk(r)),!e&&t(!0)}),Fr("refreshSlots",r=>n(e4(r))),Fr("displayMetadata",r=>{n(K3(r))}),we(En,{children:[W(qb,{in:e,children:we("div",{className:"inventory-wrapper",children:[W(M6,{}),W(i6,{}),W(N6,{}),W(UU,{}),W(jU,{})]})}),W(C6,{})]})},Bg=(e,t)=>({x:e.x-t.x,y:e.y-t.y}),VU=e=>{const t=e.getInitialClientOffset(),n=e.getInitialSourceClientOffset();return t===null||n===null||t.x===void 0||t.y===void 0?{x:0,y:0}:Bg(t,n)},GU=(e,t)=>{const n=e.getClientOffset();if(n===null)return null;if(!t.current||!t.current.getBoundingClientRect)return Bg(n,VU(e));const r=t.current.getBoundingClientRect(),i={x:r.width/2,y:r.height/2};return Bg(n,i)},qU=()=>{const e=I.useRef(null),{data:t,isDragging:n,currentOffset:r}=FF(i=>({data:i.getItem(),currentOffset:GU(i,e),isDragging:i.isDragging()}));return W(En,{children:n&&r&&t.item&&W("div",{className:"item-drag-preview",ref:e,style:{transform:`translate(${r.x}px, ${r.y}px)`,backgroundImage:t.image}})})},KU=e=>{const[t,n]=I.useState(!1),r=I.useCallback(s=>({key:a})=>{a===e&&n(s)},[e]),i=r(!0),l=r(!1);return I.useEffect(()=>(window.addEventListener("keydown",i),window.addEventListener("keyup",l),()=>{window.removeEventListener("keydown",i),window.removeEventListener("keyup",l)}),[i,l]),t},QU=()=>{const e=iu(),t=KU("Shift");return I.useEffect(()=>{e(Y3(t))},[t,e]),W(En,{})},YU=()=>{const e=iu(),t=mi();return Fr("init",({locale:n,items:r,leftInventory:i,imagepath:l})=>{for(const s in n)ut[s]=n[s];for(const s in r)dt[s]=r[s];F3(l),e(dk({leftInventory:i}))}),ur("uiLoaded",{}),Fr("closeInventory",()=>{t.dispatch({type:"dnd-core/END_DRAG"})}),we("div",{className:"app-wrapper",children:[W(WU,{}),W(qU,{}),W(QU,{})]})};addEventListener("dragstart",function(e){e.preventDefault()});const XU=(e=[])=>{const[t,n]=I.useState(e);return{add:r=>{n(i=>[...i,r])},remove:()=>{let r;return n(([i,...l])=>(r=i,l)),r},get values(){return t},get first(){return t[0]},get last(){return t[t.length-1]},get size(){return t.length}}},ZU=Ve.createContext(null),JU=Ve.forwardRef((e,t)=>{const n=e.item.item;return W("div",{className:"item-notification-item-box",style:{backgroundImage:`url(${Dl(n)||"none"}`,...e.style},ref:t,children:we("div",{className:"item-slot-wrapper",children:[W("div",{className:"item-notification-action-box",children:W("p",{children:e.item.text})}),W("div",{className:"inventory-slot-label-box",children:W("div",{className:"inventory-slot-label-text",children:n.metadata?.label||dt[n.name]?.label})})]})})}),e8=({children:e})=>{const t=XU(),n=r=>{const i=Ve.createRef(),l={id:Date.now(),item:r,ref:i};t.add(l);const s=setTimeout(()=>{t.remove(),clearTimeout(s)},2500)};return Fr("itemNotify",([r,i,l])=>{n({item:r,text:l?`${ut[i]} ${l}x`:`${ut[i]}`})}),we(ZU.Provider,{value:{add:n},children:[e,nu.createPortal(W(S6,{className:"item-notification-container",children:t.values.map((r,i)=>W(qb,{children:W(JU,{item:r.item,ref:r.ref})},`item-notification-${i}`))}),document.body)]})},Uu=document.getElementById("root");ck()&&(Uu.style.backgroundImage='url("https://i.imgur.com/3pzRj9n.png")',Uu.style.backgroundSize="cover",Uu.style.backgroundRepeat="no-repeat",Uu.style.backgroundPosition="center");kC(Uu).render(W(Ve.StrictMode,{children:W(UM,{store:Nn,children:W(pF,{backend:e3,options:{enableMouseEvents:!0},children:W(e8,{children:W(YU,{})})})})}));
